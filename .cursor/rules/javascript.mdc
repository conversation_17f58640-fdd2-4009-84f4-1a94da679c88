---
description: 
globs: *.tsx,*.ts,*.js,*.jsx,*.vue
alwaysApply: false
---
This comprehensive guide outlines best practices, conventions, and standards for development with modern web technologies including VueJS, NextJS, TypeScript, JavaScript, HTML, CSS, and UI frameworks.

# Development Philosophy
- Write clean, maintainable, and scalable code
- Follow SOLID principles
- Prefer functional and declarative programming patterns over imperative
- Emphasize type safety and static analysis
- Practice component-driven development
- Use functional and declarative programming patterns; avoid classes.
- Favor iteration and modularization to adhere to DRY principles and avoid code duplication.
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
- Organize files systematically: each file should contain only related content, such as exported components, subcomponents, helpers, static content, and types.

# Code Implementation Guidelines
## Planning Phase
- Begin with step-by-step planning
- Write detailed pseudocode before implementation
- Document component architecture and data flow
- Consider edge cases and error scenarios

# Code Style
- Use spaces for indentation
- Use single quotes for strings (except to avoid escaping)
- Omit semicolons (unless required for disambiguation)
- Eliminate unused variables
- Add space after keywords
- Add space before function declaration parentheses
- Always use strict equality (===) instead of loose equality (==)
- Space infix operators
- Add space after commas
- Keep else statements on the same line as closing curly braces
- Use curly braces for multi-line if statements
- Always handle error parameters in callbacks
- Limit line length to 120 characters
- Use trailing commas in multiline object/array literals

# Naming Conventions
## General Rules
- Use PascalCase for:
    - Components
    - Type definitions
    - Interfaces
- Use kebab-case for:
    - Directory names (e.g., components/auth-wizard)
    - File names (e.g., user-profile.tsx)
- Use camelCase for:
    - Variables
    - Functions
    - Methods
    - Composables / Hooks
    - Properties
    - Props
- Use UPPERCASE for:
    - Environment variables
    - Constants
    - Global configurations

## Specific Naming Patterns
- Prefix event handlers with 'handle': handleClick, handleSubmit
- Prefix boolean variables with verbs: isLoading, hasError, canSubmit
- Prefix custom composables with 'use': useAuth, useForm
- Use complete words over abbreviations except for:
    - err (error)
    - req (request)
    - res (response)
    - props (properties)
    - ref (reference)
- Favor named exports for functions.

# Vue Best Practices
## Component Architecture
- Use Vue 2.7.16.
- Define components using the defineComponent keyword with JSX syntax
- Extract reusable logic into custom composables(hooks).
- Implement proper component composition

## Vue Performance Optimization
- Wrap asynchronous components in Suspense with a fallback UI
- Use dynamic loading for non-critical components
- Optimize images: use WebP format, include size data, implement lazy loading
- Implement an optimized chunking strategy during the Vite build process, such as code splitting, to generate smaller bundle sizes
- Avoid inline function definitions in JSX
- Implement code splitting using dynamic imports
- Use functional components with TypeScript interfaces
- Leverage VueUse functions where applicable to enhance reactivity and performance

## Syntax and Formatting
- Prefer arrow functions over function declarations
- Use arrow functions for functional programming
- Always use the Vue Composition API script setup style

# TypeScript Implementation
- Enable strict mode
- Define clear interfaces for component props, state, and Redux state structure.
- Use type guards to handle potential undefined or null values safely.
- Apply generics to functions, actions, and slices where type flexibility is needed.
- Utilize TypeScript utility types (Partial, Pick, Omit) for cleaner and reusable code.
- Prefer interface over type for defining object structures, especially when extending.
- Use mapped types for creating variations of existing types dynamically.

# UI and Styling
Component Libraries
- Use Ant Design Vue for components and styling.
- Apply composition patterns to create modular, reusable components.

# Styling Guidelines
<!-- - Use Tailwind CSS for styling -->
<!-- - Use Tailwind CSS for utility-first, maintainable styling. -->
- Design with mobile-first, responsive principles for flexibility across devices.
- Implement dark mode using CSS variables or Tailwind’s dark mode features.
- Ensure color contrast ratios meet accessibility standards for readability.
- Maintain consistent spacing values to establish visual harmony.
- Define CSS or Less variables for theme colors and spacing to support easy theming and maintainability.

# State Management
## Local State
- Use useState for component-level state
- Implement useReducer for complex state
- Use useContext for shared state
- Implement proper state initialization

## Global State
- Use Redux Toolkit for global state
- Use createSlice to define state, reducers, and actions together.
- Avoid using createReducer and createAction unless necessary.
- Normalize state structure to avoid deeply nested data.
- Use selectors to encapsulate state access.
- Avoid large, all-encompassing slices; separate concerns by feature.


# Error Handling and Validation
## Form Validation
- Implement proper error messages.
- Use proper form libraries (e.g., Ant Design Vue Form).

## Error Boundaries
- Log caught errors to an external service (e.g., Sentry) for tracking and debugging.
- Design user-friendly fallback UIs to display when errors occur, keeping users informed without breaking the app.

# Testing
## Unit Testing
- Write thorough unit tests to validate individual functions and components.
- Use Jest(or Vitest) and Vue Test Utils v1(Vue 2) or v2(Vue 3) for reliable and efficient testing of Vue components.
- Follow patterns like Arrange-Act-Assert to ensure clarity and consistency in tests.
- Mock external dependencies and API calls to isolate unit tests.

## Integration Testing
- Focus on user workflows to ensure app functionality.
- Use Playwright(or Cypress) for testing.
- Set up and tear down test environments properly to maintain test independence.
- Use snapshot testing selectively to catch unintended UI changes without over-relying on it.
- Leverage testing utilities (e.g., screen in RTL) for cleaner and more readable tests.

# Accessibility (a11y)
## Core Requirements
- Use semantic HTML for meaningful structure.
- Apply accurate ARIA attributes where needed.
- Ensure full keyboard navigation support.
- Manage focus order and visibility effectively.
- Maintain accessible color contrast ratios.
- Follow a logical heading hierarchy.
- Make all interactive elements accessible.
- Provide clear and accessible error feedback.
- Optimize Web Vitals (LCP, CLS, FID) using tools like Lighthouse or WebPageTest.

# Security
- Implement input sanitization to prevent XSS attacks.
- Use DOMPurify for sanitizing HTML content.
- Use proper authentication methods.

# Internationalization (i18n)
- Use vue-i18n for translations
- Implement proper locale detection
- Use proper number and date formatting
- Implement proper RTL support
- Use proper currency formatting

# Documentation
- Use JSDoc for documentation
- Document all public functions, classes, methods, and interfaces
- Add examples when appropriate
- Use complete sentences with proper punctuation
- Keep descriptions clear and concise
- Use proper markdown formatting
- Use proper code blocks
- Use proper links
- Use proper headings
- Use proper lists