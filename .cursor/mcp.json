{"mcpServers": {"mastergo-mcp": {"command": "npx", "args": ["-y", "@mastergo/magic-mcp", "--token=<MASTERGO_API_TOKEN>", "--url=https://mastergo.com"], "env": {"MASTERGO_API_TOKEN": "mg_9d0093dd00cc44f4a7964961e3e5a1d0", "NPM_CONFIG_REGISTRY": "https://registry.npmmirror.com"}}, "API 文档": {"command": "npx", "args": ["-y", "apifox-mcp-server@latest", "--oas=http://i.test.greatld.com/insights/swagger-json"], "env": {"NPM_CONFIG_REGISTRY": "https://registry.npmmirror.com"}}}}