diff --git a/node_modules/ant-design-vue/es/_util/moment-util.js b/node_modules/ant-design-vue/es/_util/moment-util.js
index 2a1c223..2f53c70 100644
--- a/node_modules/ant-design-vue/es/_util/moment-util.js
+++ b/node_modules/ant-design-vue/es/_util/moment-util.js
@@ -1,5 +1,5 @@
 import interopDefault from './interopDefault';
-import * as moment from 'moment';
+import moment from 'moment';
 import warning from './warning';
 import isNil from 'lodash/isNil';
 
diff --git a/node_modules/ant-design-vue/es/calendar/index.js b/node_modules/ant-design-vue/es/calendar/index.js
index 9e24e67..d046764 100644
--- a/node_modules/ant-design-vue/es/calendar/index.js
+++ b/node_modules/ant-design-vue/es/calendar/index.js
@@ -3,7 +3,7 @@ import _slicedToArray from 'babel-runtime/helpers/slicedToArray';
 import PropTypes from '../_util/vue-types';
 import BaseMixin from '../_util/BaseMixin';
 import { getOptionProps, hasProp, initDefaultProps, getListeners } from '../_util/props-util';
-import * as moment from 'moment';
+import moment from 'moment';
 import FullCalendar from '../vc-calendar/src/FullCalendar';
 import Header from './Header';
 import LocaleReceiver from '../locale-provider/LocaleReceiver';
diff --git a/node_modules/ant-design-vue/es/locale-provider/index.js b/node_modules/ant-design-vue/es/locale-provider/index.js
index 657c8b6..284b877 100644
--- a/node_modules/ant-design-vue/es/locale-provider/index.js
+++ b/node_modules/ant-design-vue/es/locale-provider/index.js
@@ -1,6 +1,6 @@
 import _extends from 'babel-runtime/helpers/extends';
 import PropTypes from '../_util/vue-types';
-import * as moment from 'moment';
+import moment from 'moment';
 import interopDefault from '../_util/interopDefault';
 import { changeConfirmLocale } from '../modal/locale';
 import Base from '../base';
diff --git a/node_modules/ant-design-vue/es/vc-select/util.js b/node_modules/ant-design-vue/es/vc-select/util.js
index aa5d976..4bbcd05 100644
--- a/node_modules/ant-design-vue/es/vc-select/util.js
+++ b/node_modules/ant-design-vue/es/vc-select/util.js
@@ -202,7 +202,8 @@ export function saveRef(instance, name) {
 }
 
 export function generateUUID() {
-  if (process.env.NODE_ENV === 'test') {
+  // 兼容 Vitest 的测试模式: 避免快照由于时间变化生成不同的aria属性，导致生成的快照不一致
+  if (process.env.NODE_ENV === 'test' || process.env.MODE === 'test') {
     return 'test-uuid';
   }
   var d = new Date().getTime();
diff --git a/node_modules/ant-design-vue/lib/vc-select/util.js b/node_modules/ant-design-vue/lib/vc-select/util.js
index 27bacf7..d514416 100644
--- a/node_modules/ant-design-vue/lib/vc-select/util.js
+++ b/node_modules/ant-design-vue/lib/vc-select/util.js
@@ -237,7 +237,8 @@ function saveRef(instance, name) {
 }
 
 function generateUUID() {
-  if (process.env.NODE_ENV === 'test') {
+  // 兼容 Vitest 的测试模式: 避免快照由于时间变化生成不同的aria属性，导致生成的快照不一致
+  if (process.env.NODE_ENV === 'test' || process.env.MODE === 'test') {
     return 'test-uuid';
   }
   var d = new Date().getTime();
diff --git a/node_modules/ant-design-vue/lib/vc-select/util.js b/node_modules/ant-design-vue/lib/vc-select/util.js b/node_modules/ant-design-vue/lib/vc-select/util.js b/node_modules/ant-design-vue/lib/vc-select/util.js
new file mode 100644
index 0000000..e69de29
