import { getEmbedUrl } from '..';

vi.mock('@/shared/config/env', () => ({
  default: {
    KYC_HOME: 'KYS_HOME',
  },
}));

describe('getEmbedUrl', () => {
  const EMBED_HOST = 'KYS_HOME';

  test('should generate the correct URL', () => {
    const params = {
      accessKey: 'ACCESS_KEY',
      token: 'TOKEN',
      type: 'companyDetail',
      query: { keyNo: 'KEY_NO' },
    };
    const expectedUrl = `${EMBED_HOST}plugin-login?key=ACCESS_KEY&token=TOKEN&pluginSourceFrom=insights&returnUrl=%2FcompanyDetail%3Fno-redirect%3D%26cWidth%3D1220%26hideBackFlag%3DY%26hideExportFlag%3DY%26hideGenerateReportFlag%3DY%26hideOfflineExportFlag%3DY%26hideNameFlag%3DY%26hideCollectionsFlag%3DY%26hideJudgeDeepSearchFlag%3DY%26keyNo%3DKEY_NO`;
    // 调用函数并断言返回的URL与预期的URL相等
    expect(getEmbedUrl(params)).toBe(expectedUrl);
  });
});
