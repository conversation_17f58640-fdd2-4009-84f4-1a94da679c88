import env from '@/shared/config/env';

export type EmbedPageParams<P extends Record<string, string | number | undefined> | undefined> = {
  type: string;
  token: string;
  query?: P;
  accessKey: string;
};

const EMBED_HOST = env.KYC_HOME;

function objectToQuery(params: Record<string, string | number | undefined>) {
  return Object.keys(params)
    .map((k: string) => {
      const v = params[k] || '';
      return `${k}=${encodeURIComponent(v)}`;
    })
    .join('&');
}

/**
 * 获取专业版详情页链接
 * @param {string} type
 * @param {string} token
 * @param {string} accesskey
 * @param {object} payload
 * @returns
 */
export function getEmbedUrl<T extends Record<string, string | number | undefined>>(params: EmbedPageParams<T>) {
  const host = `${EMBED_HOST}plugin-login`;
  const query = {
    key: params.accessKey,
    token: params.token,
    pluginSourceFrom: 'insights', // 来源网站标识, 用于 nginx 明确跳转地址
    returnUrl: `/${params.type}?${objectToQuery({
      'no-redirect': '', // NOTE: nginx 不转发
      cWidth: '1220',
      hideBackFlag: 'Y', // 隐藏顶部返回区域
      hideExportFlag: 'Y', // 隐藏导出生成报告
      hideGenerateReportFlag: 'Y', // 隐藏详情页面的准入尽调模块
      hideOfflineExportFlag: 'Y', // 隐藏离线导出裁判文书
      hideNameFlag: 'Y', // 隐藏图表头部返回按钮
      hideCollectionsFlag: 'Y', // 隐藏分组弹框 https://www.teambition.com/task/6405b3a85cba8ff3fba3a8fa
      hideJudgeDeepSearchFlag: 'Y', // 隐藏裁判文书深度查询
      ...params.query,
    })}`, // /companyDetail?keyNo=9cce0780ab7644008b73bc2120479d31
  };
  // https://pro-plugin.qcc.com/plugin-login?key=<由企查查提供>&token=<由用户生成>&returnUrl=<相关功能页面URL>
  const url = `${host}?${objectToQuery(query)}`;
  return url;
}
