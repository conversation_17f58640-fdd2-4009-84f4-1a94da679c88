import { shallowMount } from '@vue/test-utils';
import { vi } from 'vitest';

import { flushPromises } from '@/test-utils/flush-promises';

import EmbedPage from '..';

vi.mock('vue-router/composables', () => ({
  useRoute: vi.fn(() => ({
    query: {
      title: 'TITLE',
    },
  })),
}));
vi.mock('@/shared/config/env', () => ({
  default: {
    KYC_HOME: 'KYS_HOME',
  },
}));
vi.mock('@/shared/services', () => ({
  auth: {
    getProToken: vi.fn().mockResolvedValue({ token: 'TOKEN', key: 'KEY' }),
  },
}));

describe('EmbedPage', () => {
  test('render', async () => {
    const wrapper = shallowMount<InstanceType<typeof EmbedPage>>(EmbedPage);
    await flushPromises();
    expect(wrapper).toMatchSnapshot();
  });
});
