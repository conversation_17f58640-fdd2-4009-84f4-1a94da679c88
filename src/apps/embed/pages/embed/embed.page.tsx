import { useRoute } from 'vue-router/composables';
import { computed, defineComponent, Ref, ref } from 'vue';

import { useFetchState } from '@/hooks/use-fetch-state';
import { useWebTitle } from '@/shared/composables/use-web-title';
import { auth as authService } from '@/shared/services';

import styles from './embed.page.module.less';
import { getEmbedUrl } from './utils/embed-url';

const EmbedPage = defineComponent({
  name: 'EmbedPage',
  setup() {
    const iframeRef: Ref<HTMLIFrameElement | null> = ref(null);

    const route = useRoute();
    useWebTitle(route.query?.title?.toString());
    const type = route?.params?.type; // 嵌入页面类型
    const { execute, result, isLoading } = useFetchState<typeof undefined, { token: string; key: string }>(authService.getProToken);
    execute();

    const iframeURL = computed(() => {
      if (!result.value?.token) {
        return '';
      }
      return getEmbedUrl({
        token: result.value.token,
        accessKey: result.value.key,
        type,
        // NOTE: 通过类型判断 `type` 从 `route.params` 中取值, 如有异常则返回 null
        query: route?.query as Record<string, string>,
      });
    });

    return {
      iframeRef,
      iframeURL,
      isLoading,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.body}>
          <div class={styles.iframe}>
            <iframe ref="iframeRef" src={this.iframeURL} allowfullscreen="true"></iframe>
          </div>
        </div>
      </div>
    );
  },
});

export default EmbedPage;
