import { isArray, isEqual, isNil, orderBy, get, omit } from 'lodash';

export const monitorRiskLevelMap = [
  {
    value: 2,
    icon: 'icon-icon_hongsedengji',
    label: '红色等级',
  },
  {
    value: 1,
    icon: 'icon-icon_huangsedengji',
    label: '黄色等级',
  },
  {
    value: 0,
    icon: 'icon-shape',
    label: '绿色等级',
  },
];
export const riskLevelMap = [
  {
    value: 2,
    icon: 'icon-icon_hongsedengji',
    label: '红色等级',
  },
  {
    value: 1,
    icon: 'icon-icon_huangsedengji',
    label: '黄色等级',
  },
  {
    value: 0,
    icon: 'icon-icon_lvsedengji',
    label: '绿色等级',
  },
];

export enum DimensionFieldCompareTypeEnums {
  Equal = 'Equal',
  NotEqual = 'NotEqual',
  GreaterThan = 'GreaterThan',
  LessThan = 'LessThan',
  GreaterThanOrEqual = 'GreaterThanOrEqual',
  LessThanOrEqual = 'LessThanOrEqual',
  Between = 'Between',
  ContainsAny = 'ContainsAny',
  ContainsAll = 'ContainsAll',
  ExceptAny = 'ExceptAny',
  ExceptAll = 'ExceptAll',
}

export const OperatorName = {
  [DimensionFieldCompareTypeEnums.GreaterThan]: '大于',
  [DimensionFieldCompareTypeEnums.GreaterThanOrEqual]: '大于等于',
  [DimensionFieldCompareTypeEnums.Equal]: '等于',
  [DimensionFieldCompareTypeEnums.LessThan]: '小于等于',
  [DimensionFieldCompareTypeEnums.LessThanOrEqual]: '小于',
  [DimensionFieldCompareTypeEnums.ContainsAny]: '包含任一',
  [DimensionFieldCompareTypeEnums.ContainsAll]: '包含全部',
  [DimensionFieldCompareTypeEnums.ExceptAny]: '任一不包含',
  [DimensionFieldCompareTypeEnums.ExceptAll]: '都不包含',
};

export const OperatorIcon = {
  [DimensionFieldCompareTypeEnums.GreaterThan]: '>',
  [DimensionFieldCompareTypeEnums.GreaterThanOrEqual]: '>=',
  // [DimensionFieldCompareTypeEnums.Equal]: '=',
  [DimensionFieldCompareTypeEnums.LessThan]: '<',
  [DimensionFieldCompareTypeEnums.LessThanOrEqual]: '<=',
  [DimensionFieldCompareTypeEnums.ExceptAll]: '都不包含',
  [DimensionFieldCompareTypeEnums.ExceptAny]: '不包含',
};

export const getCycleDes = (type, value) => {
  if (value === -1) {
    return '不限';
  }
  switch (type) {
    case 'GreaterThan':
    case 'GreaterThanOrEqual':
      return `近${value}年`;
    case 'LessThanOrEqual':
    case 'LessThan':
      return `${value}年前`;
    default:
      return '';
  }
};

// 输入框类型
export enum DimensionFieldInputTypeEnums {
  /** 文本框 */
  Text = 0,
  /** 下拉框单选 */
  Select = 1,
  /** 下拉多选 */
  MultiSelect = 2,
  /** 单选框 */
  Radio = 3,
  /** 复选框 */
  Checkbox = 4,
  /** 日期选择器 */
  DatePicker = 5,
  /** numberRange输入框 */
  NumberRange = 6,
}

// 数据类型
export enum DimensionFieldTypeEnums {
  String = 'String',
  Number = 'Number',
  Date = 'Date',
  Boolean = 'Boolean',
  Object = 'Object',
}

// 统计周期 不可以传0 -1 表示不限 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间；
// 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；
const labelTextMap = {
  GreaterThan: (year: number) => `近${year}年`,
  LessThanOrEqual: (year: number) => `${year}年前`,
};
//  对于type为cycle, naturalCycle的字段，options需要做相应的处理
export const getSelfOptions = (inputType, compareType: string, dimensionField: string, options: any[]) => {
  if (inputType !== 0 && ['cycle', 'naturalCycle'].includes(dimensionField) && ['GreaterThan', 'LessThanOrEqual'].includes(compareType)) {
    return options.map((value) => {
      let label = '不限';
      if (value !== -1) {
        label = labelTextMap[compareType](value);
      }
      return {
        value,
        label,
      };
    });
  }
  return options;
};

const getOptionLabel = (fieldValue, selfOptions) => {
  return fieldValue.reduce((arr, fv) => {
    const op = selfOptions.find((option) => isEqual(option.value, fv));
    arr.push(op?.label || op?.name);
    return arr;
  }, []);
};

// 0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃
export const ModelStatusMap = {
  0: {
    label: '无效',
    style: {
      color: '#F04040',
      backgroundColor: '#ffecec',
    },
  },
  1: {
    label: '启用',
    style: {
      color: '#00ad65',
      backgroundColor: '#E0F5EC',
    },
  },
  2: {
    label: '开发中',
    style: {
      color: '#F04040',
      backgroundColor: '#ffecec',
    },
  },
  3: {
    label: '待废弃',
    style: {
      color: '#F04040',
      backgroundColor: '#ffecec',
    },
  },
  4: {
    label: '已废弃',
    style: {
      color: '#F04040',
      backgroundColor: '#ffecec',
    },
  },
};

// 解构获取对应的字段配置
export const getStrategyFieldConfigs = (strategyField) => {
  // 维度名称 比较类型 维度字段 维度字段值 维度字段key 维度字段选项
  const { dimensionFieldName, compareType, dimensionField, fieldValue, dimensionFieldKey, options, accessScope } = strategyField;
  const { inputType } = dimensionField ?? {};
  const operateName = (!['cycle', 'isValid', 'naturalCycle'].includes(dimensionFieldKey) && OperatorIcon[compareType]) || '';
  const newOptions = getSelfOptions(inputType, compareType, dimensionFieldKey, options);
  const compareIcon = OperatorIcon[compareType];

  return {
    dimensionFieldName,
    compareType,
    dimensionField,
    fieldValue,
    dimensionFieldKey,
    options,
    inputType,
    accessScope,
    newOptions,
    compareIcon,
    operateName,
  };
};

// 获取渲染的策略字段值
const renderInputValue = (inputType, fieldValue, valueSetting, opKey?) => {
  const val = isArray(fieldValue) ? fieldValue : [fieldValue];
  const unit = valueSetting?.unit ?? '';
  if (inputType === 0) {
    return !isNil(val[0]) ? [`${OperatorIcon[valueSetting.compareType] || valueSetting.compareType || ''}${val[0]}${unit}`] : [];
  }
  if (inputType === 5) {
    const [v1, v2] = val;
    const before = v1 === null ? '不限' : v1;
    const after = v2 === null ? '不限' : v2;
    return [`${before} ~ ${after}${unit}`];
  }
  return getOptionLabel(val, getSelfOptions(inputType, valueSetting.compareType, opKey, valueSetting.options));
};

export const getObjectListConfig = (options) => {
  const optionSettingData = options[0];
  const renderKeyList = orderBy(Object.keys(omit(optionSettingData, ['shareChangeRateCompareType'])), ['sort'], ['asc']).reverse();
  return {
    optionSettingData,
    renderKeyList,
  };
};

// 获取渲染的策略字段值
export const getRenderStrategyFieldValues = ({ operateName, fieldValue, dimensionFieldKey, compareType, options, inputType }) => {
  let hitValue = fieldValue;
  if (['cycle', 'naturalCycle'].includes(dimensionFieldKey) && compareType) {
    hitValue = [getCycleDes(compareType, fieldValue[0])];
  } else if (options && !['cycle', 'naturalCycle'].includes(dimensionFieldKey) && isArray(options)) {
    // input的时候如果有2个值，说明是一个区间，需要特殊处理
    if (inputType === 0) {
      if (fieldValue.length === 1) {
        hitValue = renderInputValue(0, fieldValue, options?.[0]);
      } else if (fieldValue.length === 2) {
        hitValue = renderInputValue(5, fieldValue, options?.[0]);
      }
    } else if (inputType === 6) {
      //  这时候是组设置规则， 规则字段的要求再options里排序后确定展示顺序"
      const { optionSettingData, renderKeyList } = getObjectListConfig(options);
      hitValue = [];
      hitValue = fieldValue.reduce((arr, fv) => {
        const singleOpValue = renderKeyList.reduce<string[]>((strArr, opKey) => {
          const currentOpSetting = optionSettingData[opKey];
          const label = currentOpSetting?.label;
          const value = renderInputValue(currentOpSetting.inputType || 0, fv[opKey], currentOpSetting, opKey);
          return [...strArr, `${label}:${value}`];
        }, []);
        return [...arr, singleOpValue];
      }, hitValue);
    } else {
      const selfOptions = getSelfOptions(inputType, compareType, dimensionFieldKey, options);
      hitValue = [];
      hitValue = getOptionLabel(fieldValue, selfOptions);
    }
  }
  return hitValue.length ? `${operateName} ${hitValue.join('、')}` : '不限';
};
