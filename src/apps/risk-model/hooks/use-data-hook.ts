import { ref } from 'vue';
import { cloneDeep } from 'lodash';

const modelDetail: any = ref({});

/** *
 * groupId:
 * metricId:
 * updateData: 复制后需要替换的数据
 * changeData: 发生变更的数据
 * updateType: 变更类型
 */
const updateModelData = (groupId, metricId, updateData, changeData, updateType = 'hitStrategy') => {
  const orinData = cloneDeep(modelDetail.value);
  const metricData = orinData.groups.find((item) => item.groupId === groupId).groupMetrics.find((item) => item.metricsId === metricId);
  if (updateType) {
    if (updateData[0].newId) {
      metricData.metricsId = updateData[0].newId;
      metricData.metricEntity.metricsId = updateData[0].newId;
    }

    metricData.metricEntity.dimensionHitStrategies.forEach((stategy) => {
      stategy.metricsId = metricData.metricsId;
      const previousId = stategy.dimensionHitStrategyEntity.strategyId;
      const matchItem = updateData[0].strategyResults.find((item) => item.oldId === previousId);
      // dimensionHitStrategies 匹配更新
      if (matchItem?.newId) {
        stategy.dimensionHitStrategyEntity.strategyId = matchItem.newId;
        stategy.dimensionStrategyId = matchItem.newId;
        // fieldHitStrategy update
        ['must', 'should', 'must_not'].forEach((key) => {
          const strategyHits = stategy.dimensionHitStrategyEntity.fieldHitStrategy[key];
          if (strategyHits?.length) {
            stategy.dimensionHitStrategyEntity.fieldHitStrategy[key] = matchItem.fieldResults
              .filter((item) => strategyHits.includes(item.oldId))
              .map((item) => item.newId);
          }
        });
        // strategyFields id update
        stategy?.dimensionHitStrategyEntity?.strategyFields?.forEach((field) => {
          field.strategyId = matchItem.newId;
          const previousFieldId = field.id;
          const matchField = matchItem.fieldResults.find((res) => res.oldId === previousFieldId);
          if (matchField) {
            field.id = matchField.newId;
            // strategyFieldValues update
            if (updateType === 'dimensionStrategy' && previousFieldId === changeData.id) {
              field.fieldValue = changeData.fieldValue;
            }
          }
        });
      }
    });
    if (updateType === 'detailsJson') {
      updateData[0].newMetric.detailsJson = cloneDeep(changeData.detailsJson);
    }
    metricData.metricEntity.hitStrategy = updateData[0].newMetric.hitStrategy;
    metricData.metricEntity.detailsJson = updateData[0].newMetric.detailsJson;
    // update hitStrategy
    if (updateType === 'hitStrategy' && changeData) {
      const { status, scoreSettings } = changeData;
      metricData.metricEntity.hitStrategy[changeData.strategyIndex] = {
        ...metricData.metricEntity.hitStrategy[changeData.strategyIndex],
        scoreSettings,
        status,
      };
    }
    metricData.metricEntity = { ...metricData.metricEntity, ...updateData[0].newMetric };
  }
  modelDetail.value = { ...orinData };
};

export const useModelDataHook = () => {
  return {
    modelDetail,
    updateModelData,
  };
};
