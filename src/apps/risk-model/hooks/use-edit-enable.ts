import { ref } from 'vue';

import { setting } from '@/shared/services';

const bindingStategyKeys = ['bindMetricToGroupId', 'bindStrategyToMetricId', 'bindStrategyFieldToStrategyId'];
export const useEnableEdit = () => {
  const statusChecked = ref(false);
  // 判断是否能编辑
  const checkModelStatus = async (ids) => {
    const params = {
      riskModelId: ids[0],
      metricsId: ids[2],
      hitStrategyId: ids[3],
      hitStrategyFieldId: ids[4],
    };
    const res = await setting.checkEditStatus(params);
    return res;
  };

  // 复制模型
  const copyModel = async (modelStatus, ids, extraParams = {}) => {
    const targetIndex = modelStatus.findIndex((item) => !item.canBeEdit);
    const { targetResource } = modelStatus[targetIndex];
    const params = {
      riskModelId: ids[0],
      targetResource,
      ...extraParams,
    };
    for (let i = 1; i <= targetIndex; i++) {
      // 绑定上一层
      params[bindingStategyKeys[i - 1]] = ids[i];
    }

    const module = await setting.copyModelStrategy(params);
    return module;
  };

  return {
    statusChecked,
    checkModelStatus,
    copyModel,
  };
};
