import { useModelDataHook } from '../use-data-hook';
import { mockDetail } from './ mockDetail';

const { modelDetail, updateModelData } = useModelDataHook();

modelDetail.value = mockDetail;

describe('updateModelData', () => {
  beforeEach(() => {
    modelDetail.value = {
      groups: [
        {
          groupId: 'group1',
          groupMetrics: [
            {
              metricsId: 'metric1',
              metricEntity: {
                metricsId: 'metric1',
                dimensionHitStrategies: [
                  {
                    metricsId: 'metric1',
                    dimensionHitStrategyEntity: {
                      strategyId: 'strategy1',
                      fieldHitStrategy: {
                        must: ['field1'],
                        should: [],
                        must_not: [],
                      },
                      strategyFields: [
                        {
                          strategyId: 'strategy1',
                          id: 'field1',
                          fieldValue: 'value1',
                        },
                      ],
                    },
                    dimensionStrategyId: 'strategy1',
                  },
                ],
                hitStrategy: [
                  {
                    status: 'active',
                    scoreSettings: { min: 1, max: 10 },
                  },
                ],
                detailsJson: { key: 'value' },
              },
            },
          ],
        },
      ],
    };
  });

  it('should update metricsId and hitStrategy when updateType is hitStrategy', () => {
    const updateData = [
      {
        newId: 'metric2',
        newMetric: {
          hitStrategy: [{ status: 'inactive', scoreSettings: { min: 2, max: 20 } }],
          detailsJson: { key: 'newValue' },
        },
        strategyResults: [
          {
            oldId: 'strategy1',
            newId: 'strategy2',
            fieldResults: [
              {
                oldId: 'field1',
                newId: 'field2',
              },
            ],
          },
        ],
      },
    ];
    const changeData = { status: 'inactive', scoreSettings: { min: 2, max: 20 }, strategyIndex: 0 };

    updateModelData('group1', 'metric1', updateData, changeData);

    expect(modelDetail.value.groups[0].groupMetrics[0].metricsId).toBe('metric2');
    expect(modelDetail.value.groups[0].groupMetrics[0].metricEntity.metricsId).toBe('metric2');
    expect(modelDetail.value.groups[0].groupMetrics[0].metricEntity.hitStrategy[0].status).toBe('inactive');
    expect(modelDetail.value.groups[0].groupMetrics[0].metricEntity.hitStrategy[0].scoreSettings).toEqual({
      min: 2,
      max: 20,
    });
    expect(modelDetail.value.groups[0].groupMetrics[0].metricEntity.dimensionHitStrategies[0].metricsId).toBe('metric2');
    expect(modelDetail.value.groups[0].groupMetrics[0].metricEntity.dimensionHitStrategies[0].dimensionStrategyId).toBe('strategy2');
    expect(modelDetail.value.groups[0].groupMetrics[0].metricEntity.dimensionHitStrategies[0].dimensionHitStrategyEntity.strategyId).toBe(
      'strategy2'
    );
    expect(
      modelDetail.value.groups[0].groupMetrics[0].metricEntity.dimensionHitStrategies[0].dimensionHitStrategyEntity.fieldHitStrategy.must
    ).toEqual(['field2']);
    expect(
      modelDetail.value.groups[0].groupMetrics[0].metricEntity.dimensionHitStrategies[0].dimensionHitStrategyEntity.strategyFields[0]
        .strategyId
    ).toBe('strategy2');
    expect(
      modelDetail.value.groups[0].groupMetrics[0].metricEntity.dimensionHitStrategies[0].dimensionHitStrategyEntity.strategyFields[0].id
    ).toBe('field2');
  });

  it('should update only detailsJson when updateType is detailsJson', () => {
    const updateData = [
      {
        newMetric: {
          detailsJson: { key: 'newValue' },
        },
        strategyResults: [
          {
            oldId: 'strategy1',
            newId: 'strategy2',
            fieldResults: [
              {
                oldId: 'field1',
                newId: 'field2',
              },
            ],
          },
        ],
      },
    ];
    const changeData = { detailsJson: { key: 'newValue' } };

    updateModelData('group1', 'metric1', updateData, changeData, 'detailsJson');

    expect(modelDetail.value.groups[0].groupMetrics[0].metricsId).toBe('metric1');
    expect(modelDetail.value.groups[0].groupMetrics[0].metricEntity.metricsId).toBe('metric1');
    expect(modelDetail.value.groups[0].groupMetrics[0].metricEntity.detailsJson).toEqual({ key: 'newValue' });
  });

  it('should update strategyFields fieldValue when updateType is dimensionStrategy', () => {
    const updateData = [
      {
        newMetric: {
          detailsJson: { key: 'value' },
        },
        strategyResults: [
          {
            oldId: 'strategy1',
            newId: 'strategy2',
            fieldResults: [
              {
                oldId: 'field1',
                newId: 'field2',
              },
            ],
          },
        ],
      },
    ];
    const changeData = { id: 'field1', fieldValue: 'newValue' };

    updateModelData('group1', 'metric1', updateData, changeData, 'dimensionStrategy');

    expect(
      modelDetail.value.groups[0].groupMetrics[0].metricEntity.dimensionHitStrategies[0].dimensionHitStrategyEntity.strategyFields[0]
        .fieldValue
    ).toBe('newValue');
  });
});
