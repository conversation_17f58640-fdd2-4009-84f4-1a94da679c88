export const mockDetail = {
  modelType: 1,
  category: 1,
  modelId: 3549,
  modelName: '【企查查分指标】0327',
  product: 'SAAS_PRO',
  comment: '【企查查分指标】0327',
  createBy: 1,
  publishBy: 0,
  updateBy: null,
  createDate: '2025-03-27T12:22:36.000Z',
  updateDate: '2025-03-27T12:22:36.000Z',
  scoreStrategy: 1,
  branchTier: 0,
  branchCount: 1,
  branchCode: 'c8f5daa2-527c-4021-9270-f8a9e05669b5',
  orgId: 1,
  extendFrom: null,
  versionMajor: 1,
  versionMinor: 0,
  versionPatch: 0,
  status: 2,
  resultSetting: [
    {
      name: '高风险',
      level: 2,
      maxScore: null,
      mimScore: 75,
    },
    {
      name: '中高风险',
      level: 1,
      maxScore: 74,
      mimScore: 60,
    },
    {
      name: '中风险',
      level: 0,
      maxScore: 59,
      mimScore: 40,
    },
    {
      name: '中低风险',
      level: -1,
      maxScore: 39,
      mimScore: 20,
    },
    {
      name: '良好',
      level: -2,
      maxScore: 19,
      mimScore: null,
    },
  ],
  publishedDate: null,
  modifiedDate: null,
  deprecatedDate: null,
  deprecateStartDate: null,
  groups: [
    {
      category: 1,
      groupId: 50014080,
      extendFrom: null,
      createDate: '2025-03-27T12:22:37.000Z',
      updateDate: '2025-03-27T12:22:37.000Z',
      groupName: '经营风险',
      parentGroupId: null,
      isVirtual: null,
      detailsJson: null,
      orgId: 1,
      productCode: 'SAAS_PRO',
      publishBy: 0,
      publishedDate: null,
      riskLevel: 2,
      status: 1,
      comment: '经营风险',
      createBy: 1,
      updateBy: null,
      modelId: 3549,
      order: 1,
      groupMetrics: [
        {
          id: 164096,
          metricsId: 145247,
          status: 1,
          groupId: 50014080,
          order: 1,
          createDate: '2025-03-27T12:22:37.000Z',
          updateDate: '2025-03-27T12:22:37.000Z',
          metricEntity: {
            category: 1,
            metricsId: 145247,
            name: '经营状态非存续',
            riskLevel: 0,
            isVeto: 0,
            metricType: 0,
            score: 0,
            productCode: 'SAAS_PRO',
            status: 2,
            extendFrom: null,
            comment: '经营状态非存续',
            deprecatedDate: null,
            deprecateStartDate: null,
            createDate: '2025-03-27T12:22:37.000Z',
            updateDate: '2025-03-27T12:22:37.000Z',
            modifiedDate: null,
            publishBy: 0,
            publishedDate: null,
            createBy: 1,
            updateBy: null,
            hitStrategy: [
              {
                must: [232096],
                order: 0,
                status: 1,
                scoreSettings: {
                  maxScore: 80,
                  riskLevel: 2,
                },
                minimum_should_match: 1,
              },
            ],
            orgId: 1,
            detailsJson: {},
            dimensionHitStrategies: [
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229321,
                metricsId: 145247,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232096,
                createDate: '2025-03-27T12:22:37.000Z',
                updateDate: '2025-03-27T12:22:37.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                  category: 1,
                  strategyId: 232096,
                  dimensionId: 4466,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '经营状态非存续',
                  comment: '命中 经营状态非存续 BusinessAbnormal1',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4466,
                    key: 'BusinessAbnormal1',
                    name: '经营状态非存续',
                    createDate: '2025-01-16T13:38:21.000Z',
                    updateDate: '2025-01-16T13:38:21.000Z',
                    modifiedDate: null,
                    createBy: 102709,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'CompanyDetail',
                    sourcePath: null,
                    detailSource: null,
                    detailSourcePath: null,
                    typeCode: '2110',
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [],
                },
              },
            ],
          },
        },
        {
          id: 164097,
          metricsId: 145248,
          status: 1,
          groupId: 50014080,
          order: 2,
          createDate: '2025-03-27T12:22:37.000Z',
          updateDate: '2025-03-27T12:22:37.000Z',
          metricEntity: {
            category: 1,
            metricsId: 145248,
            name: '清算信息',
            riskLevel: 0,
            isVeto: 0,
            metricType: 0,
            score: 0,
            productCode: 'SAAS_PRO',
            status: 2,
            extendFrom: null,
            comment: '企业依法解散后清理公司债权债务的行为',
            deprecatedDate: null,
            deprecateStartDate: null,
            createDate: '2025-03-27T12:22:37.000Z',
            updateDate: '2025-03-27T12:22:37.000Z',
            modifiedDate: null,
            publishBy: 0,
            publishedDate: null,
            createBy: 1,
            updateBy: null,
            hitStrategy: [
              {
                must: [232097],
                order: 0,
                status: 1,
                scoreSettings: {
                  maxScore: 80,
                  riskLevel: 2,
                },
                minimum_should_match: 1,
              },
            ],
            orgId: 1,
            detailsJson: {},
            dimensionHitStrategies: [
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229322,
                metricsId: 145248,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232097,
                createDate: '2025-03-27T12:22:37.000Z',
                updateDate: '2025-03-27T12:22:37.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
                  category: 1,
                  strategyId: 232097,
                  dimensionId: 4498,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '清算信息',
                  comment: '有清算信息',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4498,
                    key: 'Liquidation',
                    name: '清算信息',
                    createDate: '2025-01-16T13:38:24.000Z',
                    updateDate: '2025-01-16T13:38:24.000Z',
                    modifiedDate: null,
                    createBy: 102709,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'EnterpriseLib',
                    sourcePath: '/api/ECILocal/GetLiquidationDetail',
                    detailSource: null,
                    detailSourcePath: null,
                    typeCode: null,
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [],
                },
              },
            ],
          },
        },
        {
          id: 164104,
          metricsId: 145256,
          status: 1,
          groupId: 50014080,
          order: 3,
          createDate: '2025-03-27T12:22:37.000Z',
          updateDate: '2025-03-27T12:22:37.000Z',
          metricEntity: {
            category: 1,
            metricsId: 145256,
            name: '股权冻结',
            riskLevel: 0,
            isVeto: 0,
            metricType: 0,
            score: 0,
            productCode: 'SAAS_PRO',
            status: 2,
            extendFrom: null,
            comment: '股权冻结',
            deprecatedDate: null,
            deprecateStartDate: null,
            createDate: '2025-03-27T12:22:37.000Z',
            updateDate: '2025-03-27T12:22:37.000Z',
            modifiedDate: null,
            publishBy: 0,
            publishedDate: null,
            createBy: 1,
            updateBy: null,
            hitStrategy: [
              {
                must: [232099],
                order: 0,
                status: 1,
                scoreSettings: {
                  maxScore: 8,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
            ],
            orgId: 1,
            detailsJson: {},
            dimensionHitStrategies: [
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229330,
                metricsId: 145256,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232099,
                createDate: '2025-03-27T12:22:37.000Z',
                updateDate: '2025-03-27T12:22:37.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template:
                    '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em><span class="#isHidden#">，冻结股权数额：<em class="#level#">#amountW#</em></span>',
                  category: 1,
                  strategyId: 232099,
                  dimensionId: 4428,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '股权冻结',
                  comment: '命中 有股权被冻结  股权数额 > 0',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [535970, 535989, 535992],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4428,
                    key: 'FreezeEquity',
                    name: '股权冻结',
                    createDate: '2025-01-16T13:38:17.000Z',
                    updateDate: '2025-01-16T13:38:17.000Z',
                    modifiedDate: null,
                    createBy: 102709,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'CreditES',
                    sourcePath: '/api/search/search-credit',
                    detailSource: null,
                    detailSourcePath: null,
                    typeCode: null,
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [
                    {
                      category: 1,
                      id: 535970,
                      strategyId: 232099,
                      orgId: 1,
                      dimensionId: 4428,
                      extendFrom: null,
                      comment: '股权数额大于等于20万元',
                      dimensionFieldId: 8842,
                      dimensionFieldKey: 'equityAmount',
                      dimensionFieldName: '股权数额',
                      options: [
                        {
                          max: 99999999,
                          min: 0,
                          unit: '万元',
                        },
                      ],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 0,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [0],
                      compareType: 'GreaterThan',
                      dimensionField: {
                        fieldId: 8842,
                        inputType: 0,
                        comment: '股权数额大于等于20万元',
                        fieldKey: 'equityAmount',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 0,
                        dimensionId: 4428,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '股权数额',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 535981,
                      strategyId: 232099,
                      orgId: 1,
                      dimensionId: 4428,
                      extendFrom: null,
                      comment: '排序字段',
                      dimensionFieldId: 8846,
                      dimensionFieldKey: 'sortField',
                      dimensionFieldName: '排序',
                      options: [],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 1,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [
                        {
                          field: 'liandate',
                          order: 'DESC',
                          fieldSnapshot: 'LianDate',
                        },
                      ],
                      compareType: 'Equal',
                      dimensionField: {
                        fieldId: 8846,
                        inputType: 0,
                        comment: '排序字段',
                        fieldKey: 'sortField',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Object',
                        isArray: 0,
                        fieldOrder: 10,
                        dimensionId: 4428,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '排序',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 535989,
                      strategyId: 232099,
                      orgId: 1,
                      dimensionId: 4428,
                      extendFrom: null,
                      comment: '数据范围 1:当前有效，0:历史, -1:不限',
                      dimensionFieldId: 8841,
                      dimensionFieldKey: 'isValid',
                      dimensionFieldName: '数据范围',
                      options: [
                        {
                          label: '当前有效',
                          value: 1,
                        },
                        {
                          label: '历史',
                          value: 0,
                        },
                        {
                          label: '不限',
                          value: -1,
                        },
                      ],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 0,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [1],
                      compareType: 'Equal',
                      dimensionField: {
                        fieldId: 8841,
                        inputType: 3,
                        comment: '数据范围 1:当前有效，0:历史, -1:不限',
                        fieldKey: 'isValid',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 1,
                        dimensionId: 4428,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '数据范围',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 535992,
                      strategyId: 232099,
                      orgId: 1,
                      dimensionId: 4428,
                      extendFrom: null,
                      comment:
                        '统计周期 不可以传0 -1 表示不限 compareType = Equal; 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间； 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；',
                      dimensionFieldId: 8836,
                      dimensionFieldKey: 'cycle',
                      dimensionFieldName: '统计周期',
                      options: [-1, 1, 2, 3],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 0,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [-1],
                      compareType: 'GreaterThan',
                      dimensionField: {
                        fieldId: 8836,
                        inputType: 3,
                        comment:
                          '统计周期 不可以传0 -1 表示不限 compareType = Equal; 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间； 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；',
                        fieldKey: 'cycle',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 1,
                        dimensionId: 4428,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '统计周期',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          id: 164105,
          metricsId: 145255,
          status: 1,
          groupId: 50014080,
          order: 4,
          createDate: '2025-03-27T12:22:37.000Z',
          updateDate: '2025-03-27T12:22:37.000Z',
          metricEntity: {
            category: 1,
            metricsId: 145255,
            name: '严重违法失信企业名录',
            riskLevel: 0,
            isVeto: 0,
            metricType: 0,
            score: 0,
            productCode: 'SAAS_PRO',
            status: 2,
            extendFrom: null,
            comment: '被列入严重违法失信企业名录',
            deprecatedDate: null,
            deprecateStartDate: null,
            createDate: '2025-03-27T12:22:37.000Z',
            updateDate: '2025-03-27T12:22:37.000Z',
            modifiedDate: null,
            publishBy: 0,
            publishedDate: null,
            createBy: 1,
            updateBy: null,
            hitStrategy: [
              {
                order: 0,
                should: [232100, 232109],
                status: 1,
                scoreSettings: {
                  maxScore: 80,
                  riskLevel: 2,
                },
                minimum_should_match: 1,
              },
            ],
            orgId: 1,
            detailsJson: {},
            dimensionHitStrategies: [
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229329,
                metricsId: 145255,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232100,
                createDate: '2025-03-27T12:22:37.000Z',
                updateDate: '2025-03-27T12:22:37.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
                  category: 1,
                  strategyId: 232100,
                  dimensionId: 4430,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '被列入严重违法失信企业名录',
                  comment: '被列入严重违法失信企业名录 CompanyCredit ',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4430,
                    key: 'CompanyCredit',
                    name: '被列入严重违法失信企业名录',
                    createDate: '2025-01-16T13:38:17.000Z',
                    updateDate: '2025-01-16T13:38:17.000Z',
                    modifiedDate: null,
                    createBy: 102709,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'EnterpriseLib',
                    sourcePath: '/api/QccSearch/List/SeriousViolation',
                    detailSource: null,
                    detailSourcePath: null,
                    typeCode: null,
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [
                    {
                      category: 1,
                      id: 535971,
                      strategyId: 232100,
                      orgId: 1,
                      dimensionId: 4430,
                      extendFrom: null,
                      comment: '排序字段',
                      dimensionFieldId: 8848,
                      dimensionFieldKey: 'sortField',
                      dimensionFieldName: '排序',
                      options: [],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 1,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [
                        {
                          field: 'AddDate',
                          order: 'DESC',
                          fieldSnapshot: 'AddDate',
                        },
                      ],
                      compareType: 'ContainsAny',
                      dimensionField: {
                        fieldId: 8848,
                        inputType: 0,
                        comment: '排序字段',
                        fieldKey: 'sortField',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Object',
                        isArray: 0,
                        fieldOrder: 3,
                        dimensionId: 4430,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '排序',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                  ],
                },
              },
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229331,
                metricsId: 145255,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232109,
                createDate: '2025-03-27T12:22:37.000Z',
                updateDate: '2025-03-27T12:22:37.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
                  category: 1,
                  strategyId: 232109,
                  dimensionId: 4434,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '被列入严重违法失信企业名录（历史）',
                  comment: '近3年 被列入严重违法失信企业名录（历史） CompanyCreditHistory  ',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4434,
                    key: 'CompanyCreditHistory',
                    name: '被列入严重违法失信企业名录（历史）',
                    createDate: '2025-01-16T13:38:17.000Z',
                    updateDate: '2025-01-16T13:38:17.000Z',
                    modifiedDate: null,
                    createBy: 102709,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'EnterpriseLib',
                    sourcePath: '/api/QccSearch/List/SeriousViolation',
                    detailSource: null,
                    detailSourcePath: null,
                    typeCode: null,
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [
                    {
                      category: 1,
                      id: 535990,
                      strategyId: 232109,
                      orgId: 1,
                      dimensionId: 4434,
                      extendFrom: null,
                      comment: '排序字段',
                      dimensionFieldId: 8850,
                      dimensionFieldKey: 'sortField',
                      dimensionFieldName: '排序',
                      options: [],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 1,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [
                        {
                          field: 'AddDate',
                          order: 'DESC',
                          fieldSnapshot: 'AddDate',
                        },
                      ],
                      compareType: 'ContainsAny',
                      dimensionField: {
                        fieldId: 8850,
                        inputType: 0,
                        comment: '排序字段',
                        fieldKey: 'sortField',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Object',
                        isArray: 0,
                        fieldOrder: 3,
                        dimensionId: 4434,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '排序',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          id: 164102,
          metricsId: 145252,
          status: 1,
          groupId: 50014080,
          order: 5,
          createDate: '2025-03-27T12:22:37.000Z',
          updateDate: '2025-03-27T12:22:37.000Z',
          metricEntity: {
            category: 1,
            metricsId: 145252,
            name: '破产重整',
            riskLevel: 0,
            isVeto: 0,
            metricType: 0,
            score: 0,
            productCode: 'SAAS_PRO',
            status: 2,
            extendFrom: null,
            comment: '破产重整',
            deprecatedDate: null,
            deprecateStartDate: null,
            createDate: '2025-03-27T12:22:37.000Z',
            updateDate: '2025-03-27T12:22:37.000Z',
            modifiedDate: null,
            publishBy: 0,
            publishedDate: null,
            createBy: 1,
            updateBy: null,
            hitStrategy: [
              {
                must: [232098],
                order: 0,
                status: 1,
                scoreSettings: {
                  maxScore: 80,
                  riskLevel: 2,
                },
                minimum_should_match: 1,
              },
            ],
            orgId: 1,
            detailsJson: {},
            dimensionHitStrategies: [
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229327,
                metricsId: 145252,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232098,
                createDate: '2025-03-27T12:22:37.000Z',
                updateDate: '2025-03-27T12:22:37.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
                  category: 1,
                  strategyId: 232098,
                  dimensionId: 4429,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '破产重整',
                  comment: '命中 破产重整 时间不限 有效',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [535966, 535986],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4429,
                    key: 'Bankruptcy',
                    name: '破产重整',
                    createDate: '2025-01-16T13:38:17.000Z',
                    updateDate: '2025-01-16T13:38:17.000Z',
                    modifiedDate: null,
                    createBy: 102709,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'CreditES',
                    sourcePath: '/api/search/search-credit',
                    detailSource: 'EnterpriseLib',
                    detailSourcePath: '/api/QccSearch/SmallSearch/BankRuptcy',
                    typeCode: null,
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [
                    {
                      category: 1,
                      id: 535966,
                      strategyId: 232098,
                      orgId: 1,
                      dimensionId: 4429,
                      extendFrom: null,
                      comment:
                        '统计周期 不可以传0 -1 表示不限 compareType = Equal; 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间； 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；',
                      dimensionFieldId: 8844,
                      dimensionFieldKey: 'cycle',
                      dimensionFieldName: '统计周期',
                      options: [-1, 1, 3, 5],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 0,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [-1],
                      compareType: 'GreaterThan',
                      dimensionField: {
                        fieldId: 8844,
                        inputType: 3,
                        comment:
                          '统计周期 不可以传0 -1 表示不限 compareType = Equal; 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间； 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；',
                        fieldKey: 'cycle',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 0,
                        dimensionId: 4429,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '统计周期',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 535977,
                      strategyId: 232098,
                      orgId: 1,
                      dimensionId: 4429,
                      extendFrom: null,
                      comment: '排序字段',
                      dimensionFieldId: 8847,
                      dimensionFieldKey: 'sortField',
                      dimensionFieldName: '排序',
                      options: [],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 1,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [
                        {
                          field: 'riskdate',
                          order: 'DESC',
                          fieldSnapshot: 'RiskDate',
                        },
                      ],
                      compareType: 'ContainsAny',
                      dimensionField: {
                        fieldId: 8847,
                        inputType: 0,
                        comment: '排序字段',
                        fieldKey: 'sortField',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Object',
                        isArray: 0,
                        fieldOrder: 10,
                        dimensionId: 4429,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '排序',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 535986,
                      strategyId: 232098,
                      orgId: 1,
                      dimensionId: 4429,
                      extendFrom: null,
                      comment: '数据范围 1:当前有效，0:历史, -1:不限',
                      dimensionFieldId: 8845,
                      dimensionFieldKey: 'isValid',
                      dimensionFieldName: '数据范围',
                      options: [
                        {
                          label: '当前有效',
                          value: 1,
                        },
                        {
                          label: '历史',
                          value: 0,
                        },
                        {
                          label: '不限',
                          value: -1,
                        },
                      ],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 1,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [1],
                      compareType: 'Equal',
                      dimensionField: {
                        fieldId: 8845,
                        inputType: 3,
                        comment: '数据范围 1:当前有效，0:历史, -1:不限',
                        fieldKey: 'isValid',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 1,
                        dimensionId: 4429,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '数据范围',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          id: 164099,
          metricsId: 145250,
          status: 1,
          groupId: 50014080,
          order: 6,
          createDate: '2025-03-27T12:22:37.000Z',
          updateDate: '2025-03-27T12:22:37.000Z',
          metricEntity: {
            category: 1,
            metricsId: 145250,
            name: '破产重整（历史）',
            riskLevel: 0,
            isVeto: 0,
            metricType: 0,
            score: 0,
            productCode: 'SAAS_PRO',
            status: 2,
            extendFrom: null,
            comment: '破产重整（历史）',
            deprecatedDate: null,
            deprecateStartDate: null,
            createDate: '2025-03-27T12:22:37.000Z',
            updateDate: '2025-03-28T08:27:40.000Z',
            modifiedDate: null,
            publishBy: 0,
            publishedDate: null,
            createBy: 1,
            updateBy: 103241,
            hitStrategy: [
              {
                must: [232103],
                order: 0,
                status: 1,
                scoreSettings: {
                  maxScore: 30,
                  riskLevel: 2,
                },
                minimum_should_match: 1,
              },
            ],
            orgId: 1,
            detailsJson: {
              scoreStrategy: 0,
            },
            dimensionHitStrategies: [
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229324,
                metricsId: 145250,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232103,
                createDate: '2025-03-27T12:22:37.000Z',
                updateDate: '2025-03-27T12:22:37.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
                  category: 1,
                  strategyId: 232103,
                  dimensionId: 4429,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '破产重整（历史）',
                  comment: '命中 破产重整 时间不限 失效',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [535969, 535987],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4429,
                    key: 'Bankruptcy',
                    name: '破产重整',
                    createDate: '2025-01-16T13:38:17.000Z',
                    updateDate: '2025-01-16T13:38:17.000Z',
                    modifiedDate: null,
                    createBy: 102709,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'CreditES',
                    sourcePath: '/api/search/search-credit',
                    detailSource: 'EnterpriseLib',
                    detailSourcePath: '/api/QccSearch/SmallSearch/BankRuptcy',
                    typeCode: null,
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [
                    {
                      category: 1,
                      id: 535969,
                      strategyId: 232103,
                      orgId: 1,
                      dimensionId: 4429,
                      extendFrom: null,
                      comment:
                        '统计周期 不可以传0 -1 表示不限 compareType = Equal; 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间； 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；',
                      dimensionFieldId: 8844,
                      dimensionFieldKey: 'cycle',
                      dimensionFieldName: '统计周期',
                      options: [-1, 1, 3, 5],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 0,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [-1],
                      compareType: 'GreaterThan',
                      dimensionField: {
                        fieldId: 8844,
                        inputType: 3,
                        comment:
                          '统计周期 不可以传0 -1 表示不限 compareType = Equal; 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间； 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；',
                        fieldKey: 'cycle',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 0,
                        dimensionId: 4429,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '统计周期',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 535979,
                      strategyId: 232103,
                      orgId: 1,
                      dimensionId: 4429,
                      extendFrom: null,
                      comment: '排序字段',
                      dimensionFieldId: 8847,
                      dimensionFieldKey: 'sortField',
                      dimensionFieldName: '排序',
                      options: [],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 1,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [
                        {
                          field: 'riskdate',
                          order: 'DESC',
                          fieldSnapshot: 'RiskDate',
                        },
                      ],
                      compareType: 'ContainsAny',
                      dimensionField: {
                        fieldId: 8847,
                        inputType: 0,
                        comment: '排序字段',
                        fieldKey: 'sortField',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Object',
                        isArray: 0,
                        fieldOrder: 10,
                        dimensionId: 4429,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '排序',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 535987,
                      strategyId: 232103,
                      orgId: 1,
                      dimensionId: 4429,
                      extendFrom: null,
                      comment: '数据范围 1:当前有效，0:历史, -1:不限',
                      dimensionFieldId: 8845,
                      dimensionFieldKey: 'isValid',
                      dimensionFieldName: '数据范围',
                      options: [
                        {
                          label: '当前有效',
                          value: 1,
                        },
                        {
                          label: '历史',
                          value: 0,
                        },
                        {
                          label: '不限',
                          value: -1,
                        },
                      ],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 1,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [0],
                      compareType: 'Equal',
                      dimensionField: {
                        fieldId: 8845,
                        inputType: 3,
                        comment: '数据范围 1:当前有效，0:历史, -1:不限',
                        fieldKey: 'isValid',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 1,
                        dimensionId: 4429,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '数据范围',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          id: 164108,
          metricsId: 145259,
          status: 1,
          groupId: 50014080,
          order: 7,
          createDate: '2025-03-27T12:22:38.000Z',
          updateDate: '2025-03-27T12:22:38.000Z',
          metricEntity: {
            category: 1,
            metricsId: 145259,
            name: '法定代表人限制高消费',
            riskLevel: 0,
            isVeto: 0,
            metricType: 0,
            score: 0,
            productCode: 'SAAS_PRO',
            status: 2,
            extendFrom: null,
            comment: '法定代表人限制高消费',
            deprecatedDate: null,
            deprecateStartDate: null,
            createDate: '2025-03-27T12:22:37.000Z',
            updateDate: '2025-03-27T12:22:37.000Z',
            modifiedDate: null,
            publishBy: 0,
            publishedDate: null,
            createBy: 1,
            updateBy: null,
            hitStrategy: [
              {
                order: 0,
                should: [232104],
                status: 1,
                scoreSettings: {
                  maxScore: 50,
                  riskLevel: 2,
                },
                minimum_should_match: 1,
              },
              {
                order: 3,
                should: [232111],
                status: 1,
                scoreSettings: {
                  maxScore: 5,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
              {
                order: 2,
                should: [232113],
                status: 1,
                scoreSettings: {
                  maxScore: 15,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
              {
                order: 1,
                should: [232114],
                status: 1,
                scoreSettings: {
                  maxScore: 10,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
            ],
            orgId: 1,
            detailsJson: {},
            dimensionHitStrategies: [
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229336,
                metricsId: 145259,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232104,
                createDate: '2025-03-27T12:22:37.000Z',
                updateDate: '2025-03-27T12:22:37.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template: '匹配到目标主体 <em class="#level#">#cycle#</em><em class="#level#">【#name#】 #count#条记录</em>',
                  category: 1,
                  strategyId: 232104,
                  dimensionId: 4471,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '限制高消费',
                  comment: '命中  当前法人 + 限制高消费',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [535972],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4471,
                    key: 'RestrictedConsumptionCurrent',
                    name: '被列入限制高消费名单',
                    createDate: '2025-01-16T13:38:21.000Z',
                    updateDate: '2025-01-16T13:38:21.000Z',
                    modifiedDate: null,
                    createBy: 102709,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'CreditES',
                    sourcePath: '/api/search/search-credit',
                    detailSource: null,
                    detailSourcePath: null,
                    typeCode: null,
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [
                    {
                      category: 1,
                      id: 535972,
                      strategyId: 232104,
                      orgId: 1,
                      dimensionId: 4471,
                      extendFrom: null,
                      comment: null,
                      dimensionFieldId: 8930,
                      dimensionFieldKey: 'targetInvestigation',
                      dimensionFieldName: '排查对象',
                      options: [
                        {
                          label: '当前法人',
                          value: 'Legal',
                        },
                        {
                          label: '历史法人',
                          value: 'HisLegal',
                        },
                        {
                          label: '企业本身',
                          value: 'Self',
                        },
                      ],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 2,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: ['Legal'],
                      compareType: 'ContainsAny',
                      dimensionField: {
                        fieldId: 8930,
                        inputType: 2,
                        comment: null,
                        fieldKey: 'targetInvestigation',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'String',
                        isArray: 1,
                        fieldOrder: 2,
                        dimensionId: 4471,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '排查对象',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 535983,
                      strategyId: 232104,
                      orgId: 1,
                      dimensionId: 4471,
                      extendFrom: null,
                      comment: '排序字段',
                      dimensionFieldId: 8929,
                      dimensionFieldKey: 'sortField',
                      dimensionFieldName: '排序',
                      options: [],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 1,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [
                        {
                          field: 'publishdate',
                          order: 'DESC',
                          fieldSnapshot: 'PublishDate',
                        },
                      ],
                      compareType: 'ContainsAny',
                      dimensionField: {
                        fieldId: 8929,
                        inputType: 0,
                        comment: '排序字段',
                        fieldKey: 'sortField',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Object',
                        isArray: 0,
                        fieldOrder: 1,
                        dimensionId: 4471,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '排序',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                  ],
                },
              },
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229337,
                metricsId: 145259,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232111,
                createDate: '2025-03-27T12:22:37.000Z',
                updateDate: '2025-03-27T12:22:37.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template: '匹配到目标主体 <em class="#level#">#cycle#</em><em class="#level#">【#name#】#count#条记录</em>',
                  category: 1,
                  strategyId: 232111,
                  dimensionId: 4475,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '历史限制高消费',
                  comment: '命中  历史法人 + 历史限制高消费 + 近3年',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [535995, 535998],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4475,
                    key: 'RestrictedConsumptionHistory',
                    name: '历史限制高消费',
                    createDate: '2025-01-16T13:38:22.000Z',
                    updateDate: '2025-01-16T13:38:22.000Z',
                    modifiedDate: null,
                    createBy: 102709,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'CreditES',
                    sourcePath: '/api/search/search-credit',
                    detailSource: null,
                    detailSourcePath: null,
                    typeCode: null,
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [
                    {
                      category: 1,
                      id: 535995,
                      strategyId: 232111,
                      orgId: 1,
                      dimensionId: 4475,
                      extendFrom: null,
                      comment: null,
                      dimensionFieldId: 8945,
                      dimensionFieldKey: 'targetInvestigation',
                      dimensionFieldName: '排查对象',
                      options: [
                        {
                          label: '当前法人',
                          value: 'Legal',
                        },
                        {
                          label: '历史法人',
                          value: 'HisLegal',
                        },
                        {
                          label: '企业本身',
                          value: 'Self',
                        },
                      ],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 2,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: ['HisLegal'],
                      compareType: 'ContainsAny',
                      dimensionField: {
                        fieldId: 8945,
                        inputType: 2,
                        comment: null,
                        fieldKey: 'targetInvestigation',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'String',
                        isArray: 1,
                        fieldOrder: 2,
                        dimensionId: 4475,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '排查对象',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 535996,
                      strategyId: 232111,
                      orgId: 1,
                      dimensionId: 4475,
                      extendFrom: null,
                      comment: '排序字段',
                      dimensionFieldId: 8946,
                      dimensionFieldKey: 'sortField',
                      dimensionFieldName: '排序',
                      options: [],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 1,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [
                        {
                          field: 'publishdate',
                          order: 'DESC',
                          fieldSnapshot: 'PublishDate',
                        },
                      ],
                      compareType: 'ContainsAny',
                      dimensionField: {
                        fieldId: 8946,
                        inputType: 0,
                        comment: '排序字段',
                        fieldKey: 'sortField',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Object',
                        isArray: 0,
                        fieldOrder: 10,
                        dimensionId: 4475,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '排序',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 535998,
                      strategyId: 232111,
                      orgId: 1,
                      dimensionId: 4475,
                      extendFrom: null,
                      comment:
                        '统计周期 不可以传0 -1 表示不限 compareType = Equal; 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间； 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；',
                      dimensionFieldId: 8948,
                      dimensionFieldKey: 'cycle',
                      dimensionFieldName: '统计周期',
                      options: [-1, 1, 3, 5],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 0,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [5],
                      compareType: 'GreaterThan',
                      dimensionField: {
                        fieldId: 8948,
                        inputType: 3,
                        comment:
                          '统计周期 不可以传0 -1 表示不限 compareType = Equal; 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间； 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；',
                        fieldKey: 'cycle',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 0,
                        dimensionId: 4475,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '统计周期',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                  ],
                },
              },
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229338,
                metricsId: 145259,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232113,
                createDate: '2025-03-27T12:22:38.000Z',
                updateDate: '2025-03-27T12:22:38.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template: '匹配到目标主体 <em class="#level#">#cycle#</em><em class="#level#">【#name#】#count#条记录</em>',
                  category: 1,
                  strategyId: 232113,
                  dimensionId: 4475,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '历史限制高消费',
                  comment: '命中  当前法人 + 历史限制高消费 + 近3年',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [535999, 536001],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4475,
                    key: 'RestrictedConsumptionHistory',
                    name: '历史限制高消费',
                    createDate: '2025-01-16T13:38:22.000Z',
                    updateDate: '2025-01-16T13:38:22.000Z',
                    modifiedDate: null,
                    createBy: 102709,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'CreditES',
                    sourcePath: '/api/search/search-credit',
                    detailSource: null,
                    detailSourcePath: null,
                    typeCode: null,
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [
                    {
                      category: 1,
                      id: 535999,
                      strategyId: 232113,
                      orgId: 1,
                      dimensionId: 4475,
                      extendFrom: null,
                      comment: null,
                      dimensionFieldId: 8945,
                      dimensionFieldKey: 'targetInvestigation',
                      dimensionFieldName: '排查对象',
                      options: [
                        {
                          label: '当前法人',
                          value: 'Legal',
                        },
                        {
                          label: '历史法人',
                          value: 'HisLegal',
                        },
                        {
                          label: '企业本身',
                          value: 'Self',
                        },
                      ],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 2,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: ['Legal'],
                      compareType: 'ContainsAny',
                      dimensionField: {
                        fieldId: 8945,
                        inputType: 2,
                        comment: null,
                        fieldKey: 'targetInvestigation',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'String',
                        isArray: 1,
                        fieldOrder: 2,
                        dimensionId: 4475,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '排查对象',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 536000,
                      strategyId: 232113,
                      orgId: 1,
                      dimensionId: 4475,
                      extendFrom: null,
                      comment: '排序字段',
                      dimensionFieldId: 8946,
                      dimensionFieldKey: 'sortField',
                      dimensionFieldName: '排序',
                      options: [],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 1,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [
                        {
                          field: 'publishdate',
                          order: 'DESC',
                          fieldSnapshot: 'PublishDate',
                        },
                      ],
                      compareType: 'ContainsAny',
                      dimensionField: {
                        fieldId: 8946,
                        inputType: 0,
                        comment: '排序字段',
                        fieldKey: 'sortField',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Object',
                        isArray: 0,
                        fieldOrder: 10,
                        dimensionId: 4475,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '排序',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 536001,
                      strategyId: 232113,
                      orgId: 1,
                      dimensionId: 4475,
                      extendFrom: null,
                      comment:
                        '统计周期 不可以传0 -1 表示不限 compareType = Equal; 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间； 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；',
                      dimensionFieldId: 8948,
                      dimensionFieldKey: 'cycle',
                      dimensionFieldName: '统计周期',
                      options: [-1, 1, 3, 5],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 0,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [5],
                      compareType: 'GreaterThan',
                      dimensionField: {
                        fieldId: 8948,
                        inputType: 3,
                        comment:
                          '统计周期 不可以传0 -1 表示不限 compareType = Equal; 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间； 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；',
                        fieldKey: 'cycle',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 0,
                        dimensionId: 4475,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '统计周期',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                  ],
                },
              },
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229339,
                metricsId: 145259,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232114,
                createDate: '2025-03-27T12:22:38.000Z',
                updateDate: '2025-03-27T12:22:38.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template: '匹配到目标主体 <em class="#level#">#cycle#</em><em class="#level#">【#name#】 #count#条记录</em>',
                  category: 1,
                  strategyId: 232114,
                  dimensionId: 4471,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '限制高消费',
                  comment: '命中  历史法人 + 限制高消费',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [536002],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4471,
                    key: 'RestrictedConsumptionCurrent',
                    name: '被列入限制高消费名单',
                    createDate: '2025-01-16T13:38:21.000Z',
                    updateDate: '2025-01-16T13:38:21.000Z',
                    modifiedDate: null,
                    createBy: 102709,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'CreditES',
                    sourcePath: '/api/search/search-credit',
                    detailSource: null,
                    detailSourcePath: null,
                    typeCode: null,
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [
                    {
                      category: 1,
                      id: 536002,
                      strategyId: 232114,
                      orgId: 1,
                      dimensionId: 4471,
                      extendFrom: null,
                      comment: null,
                      dimensionFieldId: 8930,
                      dimensionFieldKey: 'targetInvestigation',
                      dimensionFieldName: '排查对象',
                      options: [
                        {
                          label: '当前法人',
                          value: 'Legal',
                        },
                        {
                          label: '历史法人',
                          value: 'HisLegal',
                        },
                        {
                          label: '企业本身',
                          value: 'Self',
                        },
                      ],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 2,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: ['HisLegal'],
                      compareType: 'ContainsAny',
                      dimensionField: {
                        fieldId: 8930,
                        inputType: 2,
                        comment: null,
                        fieldKey: 'targetInvestigation',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'String',
                        isArray: 1,
                        fieldOrder: 2,
                        dimensionId: 4471,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '排查对象',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 536003,
                      strategyId: 232114,
                      orgId: 1,
                      dimensionId: 4471,
                      extendFrom: null,
                      comment: '排序字段',
                      dimensionFieldId: 8929,
                      dimensionFieldKey: 'sortField',
                      dimensionFieldName: '排序',
                      options: [],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 1,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [
                        {
                          field: 'publishdate',
                          order: 'DESC',
                          fieldSnapshot: 'PublishDate',
                        },
                      ],
                      compareType: 'ContainsAny',
                      dimensionField: {
                        fieldId: 8929,
                        inputType: 0,
                        comment: '排序字段',
                        fieldKey: 'sortField',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Object',
                        isArray: 0,
                        fieldOrder: 1,
                        dimensionId: 4471,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '排序',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          id: 164101,
          metricsId: 145253,
          status: 1,
          groupId: 50014080,
          order: 8,
          createDate: '2025-03-27T12:22:37.000Z',
          updateDate: '2025-03-27T12:22:37.000Z',
          metricEntity: {
            category: 1,
            metricsId: 145253,
            name: '欠税公告',
            riskLevel: 0,
            isVeto: 0,
            metricType: 0,
            score: 0,
            productCode: 'SAAS_PRO',
            status: 2,
            extendFrom: null,
            comment: '欠税公告',
            deprecatedDate: null,
            deprecateStartDate: null,
            createDate: '2025-03-27T12:22:37.000Z',
            updateDate: '2025-03-27T12:22:37.000Z',
            modifiedDate: null,
            publishBy: 0,
            publishedDate: null,
            createBy: 1,
            updateBy: null,
            hitStrategy: [
              {
                order: 0,
                should: [232101],
                status: 1,
                scoreSettings: {
                  maxScore: 5,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
            ],
            orgId: 1,
            detailsJson: {},
            dimensionHitStrategies: [
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229326,
                metricsId: 145253,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232101,
                createDate: '2025-03-27T12:22:37.000Z',
                updateDate: '2025-03-27T12:22:37.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template:
                    '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em><span class="#isHidden#">，欠税余额：<em class="#level#">#amountW#</em></span>',
                  category: 1,
                  strategyId: 232101,
                  dimensionId: 4436,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '欠税公告',
                  comment: '命中 欠税公告, 统计周期不限, 数据范围不限',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [535967, 535988],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4436,
                    key: 'TaxArrearsNotice',
                    name: '欠税公告',
                    createDate: '2025-01-16T13:38:18.000Z',
                    updateDate: '2025-01-16T13:38:18.000Z',
                    modifiedDate: null,
                    createBy: 102709,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'EnterpriseLib',
                    sourcePath: '/api/Tax/GetListOfOweNoticeNew',
                    detailSource: null,
                    detailSourcePath: null,
                    typeCode: null,
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [
                    {
                      category: 1,
                      id: 535967,
                      strategyId: 232101,
                      orgId: 1,
                      dimensionId: 4436,
                      extendFrom: null,
                      comment: '欠税金额 大于等于 1万元',
                      dimensionFieldId: 8862,
                      dimensionFieldKey: 'taxArrearsAmount',
                      dimensionFieldName: '欠税金额',
                      options: [
                        {
                          max: 99999999,
                          min: 0,
                          unit: '万元',
                        },
                      ],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 0,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [0],
                      compareType: 'GreaterThanOrEqual',
                      dimensionField: {
                        fieldId: 8862,
                        inputType: 0,
                        comment: '欠税金额 大于等于 1万元',
                        fieldKey: 'taxArrearsAmount',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 0,
                        dimensionId: 4436,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '欠税金额',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 535978,
                      strategyId: 232101,
                      orgId: 1,
                      dimensionId: 4436,
                      extendFrom: null,
                      comment: '排序字段',
                      dimensionFieldId: 8853,
                      dimensionFieldKey: 'sortField',
                      dimensionFieldName: '排序',
                      options: [],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 1,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [
                        {
                          field: 'liandate',
                          order: 'DESC',
                          fieldSnapshot: 'PublishDate',
                        },
                      ],
                      compareType: 'Equal',
                      dimensionField: {
                        fieldId: 8853,
                        inputType: 0,
                        comment: '排序字段',
                        fieldKey: 'sortField',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Object',
                        isArray: 0,
                        fieldOrder: 10,
                        dimensionId: 4436,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '排序',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 535988,
                      strategyId: 232101,
                      orgId: 1,
                      dimensionId: 4436,
                      extendFrom: null,
                      comment: '数据范围 1:当前有效，0:历史, -1:不限',
                      dimensionFieldId: 8854,
                      dimensionFieldKey: 'isValid',
                      dimensionFieldName: '数据范围',
                      options: [
                        {
                          label: '当前有效',
                          value: 1,
                        },
                        {
                          label: '历史',
                          value: 0,
                        },
                        {
                          label: '不限',
                          value: -1,
                        },
                      ],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 0,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [-1],
                      compareType: 'ContainsAny',
                      dimensionField: {
                        fieldId: 8854,
                        inputType: 3,
                        comment: '数据范围 1:当前有效，0:历史, -1:不限',
                        fieldKey: 'isValid',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 1,
                        dimensionId: 4436,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '数据范围',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          id: 164100,
          metricsId: 145251,
          status: 1,
          groupId: 50014080,
          order: 9,
          createDate: '2025-03-27T12:22:37.000Z',
          updateDate: '2025-03-27T12:22:37.000Z',
          metricEntity: {
            category: 1,
            metricsId: 145251,
            name: '环保处罚',
            riskLevel: 0,
            isVeto: 0,
            metricType: 0,
            score: 0,
            productCode: 'SAAS_PRO',
            status: 2,
            extendFrom: null,
            comment: '环保处罚',
            deprecatedDate: null,
            deprecateStartDate: null,
            createDate: '2025-03-27T12:22:37.000Z',
            updateDate: '2025-03-27T12:22:37.000Z',
            modifiedDate: null,
            publishBy: 0,
            publishedDate: null,
            createBy: 1,
            updateBy: null,
            hitStrategy: [
              {
                must: [232105],
                order: 0,
                status: 1,
                scoreSettings: {
                  maxScore: 2,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
            ],
            orgId: 1,
            detailsJson: {},
            dimensionHitStrategies: [
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229325,
                metricsId: 145251,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232105,
                createDate: '2025-03-27T12:22:37.000Z',
                updateDate: '2025-03-27T12:22:37.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template:
                    '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em><span class="#isHidden#">，罚款总金额：<em class="#level#">#amountW#</em></span>',
                  category: 1,
                  strategyId: 232105,
                  dimensionId: 4440,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '环保处罚',
                  comment: '命中 环保处罚 近5年 ',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [535976],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4440,
                    key: 'EnvironmentalPenalties',
                    name: '环保处罚',
                    createDate: '2025-01-16T13:38:18.000Z',
                    updateDate: '2025-01-16T13:38:18.000Z',
                    modifiedDate: null,
                    createBy: 102709,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'CreditES',
                    sourcePath: '/api/search/search-credit',
                    detailSource: null,
                    detailSourcePath: null,
                    typeCode: null,
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [
                    {
                      category: 1,
                      id: 535976,
                      strategyId: 232105,
                      orgId: 1,
                      dimensionId: 4440,
                      extendFrom: null,
                      comment:
                        '统计周期 不可以传0 -1 表示不限 compareType = Equal; 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间； 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；',
                      dimensionFieldId: 8879,
                      dimensionFieldKey: 'cycle',
                      dimensionFieldName: '统计周期',
                      options: [-1, 1, 3, 5],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 0,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [5],
                      compareType: 'GreaterThan',
                      dimensionField: {
                        fieldId: 8879,
                        inputType: 3,
                        comment:
                          '统计周期 不可以传0 -1 表示不限 compareType = Equal; 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间； 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；',
                        fieldKey: 'cycle',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 0,
                        dimensionId: 4440,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '统计周期',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 535984,
                      strategyId: 232105,
                      orgId: 1,
                      dimensionId: 4440,
                      extendFrom: null,
                      comment: '排序字段',
                      dimensionFieldId: 8882,
                      dimensionFieldKey: 'sortField',
                      dimensionFieldName: '排序',
                      options: [],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 1,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [
                        {
                          field: 'punishdate',
                          order: 'DESC',
                          fieldSnapshot: 'PunishDate',
                        },
                      ],
                      compareType: 'ContainsAny',
                      dimensionField: {
                        fieldId: 8882,
                        inputType: 0,
                        comment: '排序字段',
                        fieldKey: 'sortField',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Object',
                        isArray: 0,
                        fieldOrder: 10,
                        dimensionId: 4440,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '排序',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          id: 164098,
          metricsId: 145249,
          status: 1,
          groupId: 50014080,
          order: 10,
          createDate: '2025-03-27T12:22:37.000Z',
          updateDate: '2025-03-27T12:22:37.000Z',
          metricEntity: {
            category: 1,
            metricsId: 145249,
            name: '被列入税务非正常户',
            riskLevel: 0,
            isVeto: 0,
            metricType: 0,
            score: 0,
            productCode: 'SAAS_PRO',
            status: 2,
            extendFrom: null,
            comment: '企业被列入税收非正常户',
            deprecatedDate: null,
            deprecateStartDate: null,
            createDate: '2025-03-27T12:22:37.000Z',
            updateDate: '2025-03-27T12:22:37.000Z',
            modifiedDate: null,
            publishBy: 0,
            publishedDate: null,
            createBy: 1,
            updateBy: null,
            hitStrategy: [
              {
                must: [232102],
                order: 0,
                status: 1,
                scoreSettings: {
                  maxScore: 5,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
            ],
            orgId: 1,
            detailsJson: {},
            dimensionHitStrategies: [
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229323,
                metricsId: 145249,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232102,
                createDate: '2025-03-27T12:22:37.000Z',
                updateDate: '2025-03-27T12:22:37.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
                  category: 1,
                  strategyId: 232102,
                  dimensionId: 4484,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '被列入税务非正常户',
                  comment: '命中 被列入非正常户',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [535968],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4484,
                    key: 'TaxUnnormals',
                    name: '被列入税务非正常户',
                    createDate: '2025-01-16T13:38:23.000Z',
                    updateDate: '2025-01-16T13:38:23.000Z',
                    modifiedDate: null,
                    createBy: 102709,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'EnterpriseLib',
                    sourcePath: '/api/Risk/GetTaxUnnormals',
                    detailSource: null,
                    detailSourcePath: null,
                    typeCode: null,
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [
                    {
                      category: 1,
                      id: 535968,
                      strategyId: 232102,
                      orgId: 1,
                      dimensionId: 4484,
                      extendFrom: null,
                      comment: '数据范围 1:当前有效，0:历史, -1:不限',
                      dimensionFieldId: 8970,
                      dimensionFieldKey: 'isValid',
                      dimensionFieldName: '数据范围',
                      options: [
                        {
                          label: '当前有效',
                          value: 1,
                        },
                        {
                          label: '历史',
                          value: 0,
                        },
                        {
                          label: '不限',
                          value: -1,
                        },
                      ],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 1,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [1],
                      compareType: 'Equal',
                      dimensionField: {
                        fieldId: 8970,
                        inputType: 3,
                        comment: '数据范围 1:当前有效，0:历史, -1:不限',
                        fieldKey: 'isValid',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 1,
                        dimensionId: 4484,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '数据范围',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 535980,
                      strategyId: 232102,
                      orgId: 1,
                      dimensionId: 4484,
                      extendFrom: null,
                      comment: '排序字段',
                      dimensionFieldId: 8988,
                      dimensionFieldKey: 'sortField',
                      dimensionFieldName: '排序',
                      options: [],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 1,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [
                        {
                          field: 'courtdate',
                          order: 'DESC',
                          fieldSnapshot: 'courtdate',
                        },
                      ],
                      compareType: 'Equal',
                      dimensionField: {
                        fieldId: 8988,
                        inputType: 0,
                        comment: '排序字段',
                        fieldKey: 'sortField',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Object',
                        isArray: 0,
                        fieldOrder: 10,
                        dimensionId: 4484,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '排序',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          id: 164106,
          metricsId: 145257,
          status: 1,
          groupId: 50014080,
          order: 11,
          createDate: '2025-03-27T12:22:37.000Z',
          updateDate: '2025-03-27T12:22:37.000Z',
          metricEntity: {
            category: 1,
            metricsId: 145257,
            name: '被列入经营异常名录',
            riskLevel: 0,
            isVeto: 0,
            metricType: 0,
            score: 0,
            productCode: 'SAAS_PRO',
            status: 2,
            extendFrom: null,
            comment: '被列入经营异常名录(未移出)',
            deprecatedDate: null,
            deprecateStartDate: null,
            createDate: '2025-03-27T12:22:37.000Z',
            updateDate: '2025-03-27T12:22:37.000Z',
            modifiedDate: null,
            publishBy: 0,
            publishedDate: null,
            createBy: 1,
            updateBy: null,
            hitStrategy: [
              {
                must: [232106],
                order: 0,
                status: 1,
                scoreSettings: {
                  maxScore: 20,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
            ],
            orgId: 1,
            detailsJson: {},
            dimensionHitStrategies: [
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229332,
                metricsId: 145257,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232106,
                createDate: '2025-03-27T12:22:37.000Z',
                updateDate: '2025-03-27T12:22:37.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
                  category: 1,
                  strategyId: 232106,
                  dimensionId: 4485,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '被列入经营异常名录',
                  comment: '命中 被列入经营异常名录',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [535973, 535993],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4485,
                    key: 'BusinessAbnormal3',
                    name: '被列入经营异常名录',
                    createDate: '2025-01-16T13:38:23.000Z',
                    updateDate: '2025-01-16T13:38:23.000Z',
                    modifiedDate: null,
                    createBy: 102709,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'CreditES',
                    sourcePath: '/api/search/search-credit',
                    detailSource: null,
                    detailSourcePath: null,
                    typeCode: null,
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [
                    {
                      category: 1,
                      id: 535973,
                      strategyId: 232106,
                      orgId: 1,
                      dimensionId: 4485,
                      extendFrom: null,
                      comment: '列入原因',
                      dimensionFieldId: 8992,
                      dimensionFieldKey: 'businessAbnormalType',
                      dimensionFieldName: '经营异常类型',
                      options: [
                        {
                          label: '公示信息隐瞒真实情况/弄虚作假',
                          value: '0803',
                          esCode: '3',
                        },
                        {
                          label: '登记的住所/经营场所无法联系企业',
                          value: '0801',
                          esCode: '1',
                        },
                        {
                          label: '未在规定期限公示年度报告',
                          value: '0805',
                          esCode: '5',
                        },
                        {
                          label: '未按规定公示企业信息',
                          value: '0802',
                          esCode: '2',
                        },
                        {
                          label: '未在登记所从事经营活动',
                          value: '0804',
                          esCode: '4',
                        },
                        {
                          label: '商事主体名称不适宜',
                          value: '0806',
                          esCode: '6',
                        },
                        {
                          label: '其他原因',
                          value: '0807',
                          esCode: '7',
                        },
                      ],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 0,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: ['0801', '0802', '0803', '0804', '0805', '0806', '0807'],
                      compareType: 'ContainsAny',
                      dimensionField: {
                        fieldId: 8992,
                        inputType: 2,
                        comment: '列入原因',
                        fieldKey: 'businessAbnormalType',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'String',
                        isArray: 1,
                        fieldOrder: 0,
                        dimensionId: 4485,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '经营异常类型',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 535985,
                      strategyId: 232106,
                      orgId: 1,
                      dimensionId: 4485,
                      extendFrom: null,
                      comment: '排序字段',
                      dimensionFieldId: 8979,
                      dimensionFieldKey: 'sortField',
                      dimensionFieldName: '排序',
                      options: [],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 1,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [
                        {
                          field: 'occurrencedate',
                          order: 'DESC',
                          fieldSnapshot: 'CurrenceDate',
                        },
                      ],
                      compareType: 'ContainsAny',
                      dimensionField: {
                        fieldId: 8979,
                        inputType: 0,
                        comment: '排序字段',
                        fieldKey: 'sortField',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Object',
                        isArray: 0,
                        fieldOrder: 10,
                        dimensionId: 4485,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '排序',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 535993,
                      strategyId: 232106,
                      orgId: 1,
                      dimensionId: 4485,
                      extendFrom: null,
                      comment:
                        '统计周期 不可以传0 -1 表示不限 compareType = Equal; 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间； 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；',
                      dimensionFieldId: 8977,
                      dimensionFieldKey: 'cycle',
                      dimensionFieldName: '统计周期',
                      options: [-1, 1, 2, 3],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 0,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [-1],
                      compareType: 'GreaterThan',
                      dimensionField: {
                        fieldId: 8977,
                        inputType: 3,
                        comment:
                          '统计周期 不可以传0 -1 表示不限 compareType = Equal; 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间； 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；',
                        fieldKey: 'cycle',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 0,
                        dimensionId: 4485,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '统计周期',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          id: 164103,
          metricsId: 145254,
          status: 1,
          groupId: 50014080,
          order: 12,
          createDate: '2025-03-27T12:22:37.000Z',
          updateDate: '2025-03-27T12:22:37.000Z',
          metricEntity: {
            category: 1,
            metricsId: 145254,
            name: '被列入经营异常名录（历史）',
            riskLevel: 0,
            isVeto: 0,
            metricType: 0,
            score: 0,
            productCode: 'SAAS_PRO',
            status: 2,
            extendFrom: null,
            comment: '多次被列入经营异常名录(当下未列入)',
            deprecatedDate: null,
            deprecateStartDate: null,
            createDate: '2025-03-27T12:22:37.000Z',
            updateDate: '2025-03-27T12:22:37.000Z',
            modifiedDate: null,
            publishBy: 0,
            publishedDate: null,
            createBy: 1,
            updateBy: null,
            hitStrategy: [
              {
                must: [232107],
                order: 0,
                status: 1,
                scoreSettings: {
                  maxScore: 2,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
            ],
            orgId: 1,
            detailsJson: {},
            dimensionHitStrategies: [
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229328,
                metricsId: 145254,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232107,
                createDate: '2025-03-27T12:22:37.000Z',
                updateDate: '2025-03-27T12:22:37.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
                  category: 1,
                  strategyId: 232107,
                  dimensionId: 4439,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '被列入经营异常名录（历史）',
                  comment: '命中 被列入经营异常名录（历史） 近5年  >0次',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [535974, 535991],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4439,
                    key: 'OperationAbnormal',
                    name: '被列入经营异常名录（历史）',
                    createDate: '2025-01-16T13:38:18.000Z',
                    updateDate: '2025-01-16T13:38:18.000Z',
                    modifiedDate: null,
                    createBy: 102709,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'CreditES',
                    sourcePath: '/api/search/search-credit',
                    detailSource: null,
                    detailSourcePath: null,
                    typeCode: null,
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [
                    {
                      category: 1,
                      id: 535974,
                      strategyId: 232107,
                      orgId: 1,
                      dimensionId: 4439,
                      extendFrom: null,
                      comment: '命中记录条数 > 0',
                      dimensionFieldId: 8851,
                      dimensionFieldKey: 'hitCount',
                      dimensionFieldName: '记录条数',
                      options: [
                        {
                          max: 99999999,
                          min: 0,
                          unit: '条',
                        },
                      ],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 0,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [0],
                      compareType: 'GreaterThan',
                      dimensionField: {
                        fieldId: 8851,
                        inputType: 0,
                        comment: '命中记录条数 > 0',
                        fieldKey: 'hitCount',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 0,
                        dimensionId: 4439,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '记录条数',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 535982,
                      strategyId: 232107,
                      orgId: 1,
                      dimensionId: 4439,
                      extendFrom: null,
                      comment: '排序字段',
                      dimensionFieldId: 8857,
                      dimensionFieldKey: 'sortField',
                      dimensionFieldName: '排序',
                      options: [],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 1,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [
                        {
                          field: 'occurrencedate',
                          order: 'DESC',
                          fieldSnapshot: 'CurrenceDate',
                        },
                      ],
                      compareType: 'ContainsAny',
                      dimensionField: {
                        fieldId: 8857,
                        inputType: 0,
                        comment: '排序字段',
                        fieldKey: 'sortField',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Object',
                        isArray: 0,
                        fieldOrder: 10,
                        dimensionId: 4439,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '排序',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                    {
                      category: 1,
                      id: 535991,
                      strategyId: 232107,
                      orgId: 1,
                      dimensionId: 4439,
                      extendFrom: null,
                      comment:
                        '统计周期 不可以传0 -1 表示不限 compareType = Equal; 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间； 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；',
                      dimensionFieldId: 8855,
                      dimensionFieldKey: 'cycle',
                      dimensionFieldName: '统计周期',
                      options: [-1, 1, 3, 5],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 0,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [5],
                      compareType: 'GreaterThan',
                      dimensionField: {
                        fieldId: 8855,
                        inputType: 3,
                        comment:
                          '统计周期 不可以传0 -1 表示不限 compareType = Equal; 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间； 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；',
                        fieldKey: 'cycle',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 0,
                        dimensionId: 4439,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '统计周期',
                        createDate: '2025-01-16T13:38:42.000Z',
                        updateDate: '2025-01-16T13:38:42.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          id: 164107,
          metricsId: 145258,
          status: 1,
          groupId: 50014080,
          order: 13,
          createDate: '2025-03-27T12:22:37.000Z',
          updateDate: '2025-03-27T12:22:37.000Z',
          metricEntity: {
            category: 1,
            metricsId: 145258,
            name: '企查分',
            riskLevel: 0,
            isVeto: 0,
            metricType: 0,
            score: 0,
            productCode: 'SAAS_PRO',
            status: 2,
            extendFrom: null,
            comment: '企查分',
            deprecatedDate: null,
            deprecateStartDate: null,
            createDate: '2025-03-27T12:22:37.000Z',
            updateDate: '2025-03-27T12:22:37.000Z',
            modifiedDate: null,
            publishBy: 0,
            publishedDate: null,
            createBy: 1,
            updateBy: null,
            hitStrategy: [
              {
                must: [232108],
                order: 0,
                status: 1,
                scoreSettings: {
                  maxScore: 50,
                  riskLevel: 2,
                },
                minimum_should_match: 1,
              },
              {
                must: [232110],
                order: 2,
                status: 1,
                scoreSettings: {
                  maxScore: 1,
                  riskLevel: 0,
                },
                minimum_should_match: 1,
              },
              {
                must: [232112],
                order: 1,
                status: 1,
                scoreSettings: {
                  maxScore: 20,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
            ],
            orgId: 1,
            detailsJson: {},
            dimensionHitStrategies: [
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229333,
                metricsId: 145258,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232108,
                createDate: '2025-03-27T12:22:37.000Z',
                updateDate: '2025-03-27T12:22:37.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template: '匹配到目标主体 <em class="#level#">【#name#】 #count#分</em>',
                  category: 1,
                  strategyId: 232108,
                  dimensionId: 4506,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '企查分',
                  comment: '企查分 小于等于 499 ',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [535975],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4506,
                    key: 'QCCCreditRate',
                    name: '企查分',
                    createDate: '2025-03-18T09:24:38.000Z',
                    updateDate: '2025-03-18T09:24:38.000Z',
                    modifiedDate: null,
                    createBy: 1,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'EnterpriseLib',
                    sourcePath: null,
                    detailSource: null,
                    detailSourcePath: null,
                    typeCode: null,
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [
                    {
                      category: 1,
                      id: 535975,
                      strategyId: 232108,
                      orgId: 1,
                      dimensionId: 4506,
                      extendFrom: null,
                      comment: '企查分信用评分大于等于0分',
                      dimensionFieldId: 9369,
                      dimensionFieldKey: 'qccCreditScore',
                      dimensionFieldName: '企查分',
                      options: [
                        {
                          max: 10000,
                          min: 0,
                          unit: '分',
                        },
                      ],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 0,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [499],
                      compareType: 'LessThanOrEqual',
                      dimensionField: {
                        fieldId: 9369,
                        inputType: 0,
                        comment: '企查分信用评分大于等于0分',
                        fieldKey: 'qccCreditScore',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 0,
                        dimensionId: 4506,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '企查分',
                        createDate: '2025-03-18T09:24:43.000Z',
                        updateDate: '2025-03-18T09:24:43.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                  ],
                },
              },
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229334,
                metricsId: 145258,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232110,
                createDate: '2025-03-27T12:22:37.000Z',
                updateDate: '2025-03-27T12:22:37.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template: '匹配到目标主体 <em class="#level#">【#name#】 #count#分</em>',
                  category: 1,
                  strategyId: 232110,
                  dimensionId: 4506,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '企查分',
                  comment: '企查分 >= 750 ',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [535994],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4506,
                    key: 'QCCCreditRate',
                    name: '企查分',
                    createDate: '2025-03-18T09:24:38.000Z',
                    updateDate: '2025-03-18T09:24:38.000Z',
                    modifiedDate: null,
                    createBy: 1,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'EnterpriseLib',
                    sourcePath: null,
                    detailSource: null,
                    detailSourcePath: null,
                    typeCode: null,
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [
                    {
                      category: 1,
                      id: 535994,
                      strategyId: 232110,
                      orgId: 1,
                      dimensionId: 4506,
                      extendFrom: null,
                      comment: '企查分信用评分大于等于0分',
                      dimensionFieldId: 9369,
                      dimensionFieldKey: 'qccCreditScore',
                      dimensionFieldName: '企查分',
                      options: [
                        {
                          max: 10000,
                          min: 0,
                          unit: '分',
                        },
                      ],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 0,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [600],
                      compareType: 'GreaterThanOrEqual',
                      dimensionField: {
                        fieldId: 9369,
                        inputType: 0,
                        comment: '企查分信用评分大于等于0分',
                        fieldKey: 'qccCreditScore',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 0,
                        dimensionId: 4506,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '企查分',
                        createDate: '2025-03-18T09:24:43.000Z',
                        updateDate: '2025-03-18T09:24:43.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                  ],
                },
              },
              {
                template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
                id: 229335,
                metricsId: 145258,
                priority: 1,
                order: 0,
                isPreCondition: 1,
                dimensionStrategyId: 232112,
                createDate: '2025-03-27T12:22:37.000Z',
                updateDate: '2025-03-27T12:22:37.000Z',
                dimensionHitStrategyEntity: {
                  strategyRole: 1,
                  template: '匹配到目标主体 <em class="#level#">【#name#】 #count#分</em>',
                  category: 1,
                  strategyId: 232112,
                  dimensionId: 4506,
                  orgId: 1,
                  publishBy: 0,
                  publishedDate: null,
                  strategyName: '企查分',
                  comment: '企查分 500 到 599 ',
                  extendFrom: null,
                  createDate: '2025-03-27T12:22:37.000Z',
                  updateDate: '2025-03-27T12:22:37.000Z',
                  status: 2,
                  deprecatedDate: null,
                  deprecateStartDate: null,
                  modifiedDate: null,
                  createBy: 1,
                  updateBy: null,
                  fieldHitStrategy: {
                    must: [535997],
                    minimum_should_match: 1,
                  },
                  dimensionDef: {
                    type: 'generalItems',
                    dimensionId: 4506,
                    key: 'QCCCreditRate',
                    name: '企查分',
                    createDate: '2025-03-18T09:24:38.000Z',
                    updateDate: '2025-03-18T09:24:38.000Z',
                    modifiedDate: null,
                    createBy: 1,
                    updateBy: null,
                    status: 1,
                    extendFrom: null,
                    source: 'EnterpriseLib',
                    sourcePath: null,
                    detailSource: null,
                    detailSourcePath: null,
                    typeCode: null,
                    description: null,
                    deprecatedDate: null,
                    deprecateStartDate: null,
                  },
                  strategyFields: [
                    {
                      category: 1,
                      id: 535997,
                      strategyId: 232112,
                      orgId: 1,
                      dimensionId: 4506,
                      extendFrom: null,
                      comment: '企查分信用评分大于等于0分',
                      dimensionFieldId: 9369,
                      dimensionFieldKey: 'qccCreditScore',
                      dimensionFieldName: '企查分',
                      options: [
                        {
                          max: 10000,
                          min: 0,
                          unit: '分',
                        },
                      ],
                      searchType: 0,
                      createDate: '2025-03-27T12:22:37.000Z',
                      updateDate: '2025-03-27T12:22:37.000Z',
                      status: 2,
                      accessScope: 0,
                      createBy: 1,
                      updateBy: null,
                      deprecatedDate: null,
                      publishBy: 0,
                      publishedDate: null,
                      deprecateStartDate: null,
                      modifiedDate: null,
                      fieldValue: [500, 599],
                      compareType: 'Between',
                      dimensionField: {
                        fieldId: 9369,
                        inputType: 0,
                        comment: '企查分信用评分大于等于0分',
                        fieldKey: 'qccCreditScore',
                        defaultValue: null,
                        defaultCompareType: null,
                        dataType: 'Number',
                        isArray: 0,
                        fieldOrder: 0,
                        dimensionId: 4506,
                        deprecatedDate: null,
                        deprecateStartDate: null,
                        fieldName: '企查分',
                        createDate: '2025-03-18T09:24:43.000Z',
                        updateDate: '2025-03-18T09:24:43.000Z',
                        modifiedDate: null,
                        status: 1,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
      ],
    },
  ],
  distributedResource: [
    {
      isOrgDefault: 0,
      id: 29264,
      resourceType: 1,
      product: 'SAAS_PRO',
      branchCode: 'c8f5daa2-527c-4021-9270-f8a9e05669b5',
      resourceId: 3549,
      distributeStatus: 1,
      distributedBy: 1,
      orgId: 1003542,
      createDate: '2025-03-27T12:22:38.000Z',
      expireDate: null,
      updateDate: '2025-03-27T12:22:38.000Z',
    },
  ],
};
