import { useEnableEdit } from '@/apps/risk-model/hooks/use-edit-enable';
import { setting } from '@/shared/services';

vi.mock('vue', () => ({
  ref: vi.fn((value) => ({ value })),
}));

vi.mock('@/shared/services', () => ({
  setting: {
    checkEditStatus: vi.fn(),
    copyModelStrategy: vi.fn(),
  },
}));

describe('useEnableEdit', () => {
  const ids = [1, 2, 3, 4, 5];
  const modelStatusHappyPath = [
    { canBeEdit: true, targetResource: 'resource1' },
    { canBeEdit: true, targetResource: 'resource2' },
    { canBeEdit: false, targetResource: 'resource3' },
  ];
  const modelStatusEdgeCase1 = [{ canBeEdit: false, targetResource: 'resource1' }];
  const modelStatusEdgeCase2 = [
    { canBeEdit: true, targetResource: 'resource1' },
    { canBeEdit: true, targetResource: 'resource2' },
    { canBeEdit: true, targetResource: 'resource3' },
    { canBeEdit: false, targetResource: 'resource4' },
  ];
  const modelStatusEdgeCase3 = [];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return statusChecked as false initially', () => {
    const { statusChecked } = useEnableEdit();
    expect(statusChecked.value).toBe(false);
  });

  it('should call checkEditStatus with correct params', async () => {
    const { checkModelStatus } = useEnableEdit();
    const mockResponse = { canEdit: true };
    vi.mocked<any>(setting.checkEditStatus).mockResolvedValue(mockResponse);

    const result = await checkModelStatus(ids);
    expect(setting.checkEditStatus).toHaveBeenCalledWith({
      riskModelId: 1,
      metricsId: 3,
      hitStrategyId: 4,
      hitStrategyFieldId: 5,
    });
    expect(result).toEqual(mockResponse);
  });

  it('should call copyModelStrategy with correct params for happy path', async () => {
    const { copyModel } = useEnableEdit();
    const mockResponse = { id: 6 };
    vi.mocked<any>(setting.copyModelStrategy).mockResolvedValue(mockResponse);

    const result = await copyModel(modelStatusHappyPath, ids, { extra: 'param' });
    expect(setting.copyModelStrategy).toHaveBeenCalledWith({
      riskModelId: 1,
      targetResource: 'resource3',
      bindMetricToGroupId: 2,
      bindStrategyToMetricId: 3,
      extra: 'param',
    });
    expect(result).toEqual(mockResponse);
  });

  it('should call copyModelStrategy with correct params for edge case 1', async () => {
    const { copyModel } = useEnableEdit();
    const mockResponse = { id: 6 };
    vi.mocked<any>(setting.copyModelStrategy).mockResolvedValue(mockResponse);

    const result = await copyModel(modelStatusEdgeCase1, ids, { extra: 'param' });
    expect(setting.copyModelStrategy).toHaveBeenCalledWith({
      riskModelId: 1,
      targetResource: 'resource1',
      extra: 'param',
    });
    expect(result).toEqual(mockResponse);
  });

  it('should call copyModelStrategy with correct params for edge case 2', async () => {
    const { copyModel } = useEnableEdit();
    const mockResponse = { id: 6 };
    vi.mocked<any>(setting.copyModelStrategy).mockResolvedValue(mockResponse);

    const result = await copyModel(modelStatusEdgeCase2, ids, { extra: 'param' });
    expect(setting.copyModelStrategy).toHaveBeenCalledWith({
      riskModelId: 1,
      targetResource: 'resource4',
      bindMetricToGroupId: 2,
      bindStrategyToMetricId: 3,
      bindStrategyFieldToStrategyId: 4,
      extra: 'param',
    });
    expect(result).toEqual(mockResponse);
  });

  it('should call copyModelStrategy with correct params for edge case 2', async () => {
    const { copyModel } = useEnableEdit();
    const mockResponse = { id: 6 };
    vi.mocked<any>(setting.copyModelStrategy).mockResolvedValue(mockResponse);

    const result = await copyModel(modelStatusEdgeCase2, ids);
    expect(setting.copyModelStrategy).toHaveBeenCalledWith({
      riskModelId: 1,
      targetResource: 'resource4',
      bindMetricToGroupId: 2,
      bindStrategyToMetricId: 3,
      bindStrategyFieldToStrategyId: 4,
    });
    expect(result).toEqual(mockResponse);
  });
});
