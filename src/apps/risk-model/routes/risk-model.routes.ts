import type { RouteConfig } from 'vue-router';

import InsightsLayout from '@/shared/layouts/insights';
import { Permission } from '@/config/permissions.config';

export const baseModelRoutes = (namespace: string, baseUrl: string, name: string, modelType?: string) => (): RouteConfig[] => [
  {
    path: baseUrl,
    component: InsightsLayout,
    props: {
      pageTitle: `${name}`,
    },
    children: [
      {
        path: 'list',
        name: `risk-${namespace}-list`,
        component: () => import('../pages/risk-model-list'),
        meta: {
          title: `${name}列表`,
          permission: [baseUrl === '/monitor-model' ? Permission.MONITOR_MODEL_VIEW : Permission.INVESTIGATION_MODEL_VIEW],
        },
        props: {
          modelType,
        },
      },
      {
        path: 'detail/:riskModelId([0-9]+)',
        name: `risk-${namespace}-detail`,
        props: {
          default: true,
          modelType,
        },
        component: () => import('../pages/risk-model-detail'),
        meta: {
          title: `${name}详情`,
          permission: [baseUrl === '/monitor-model' ? Permission.MONITOR_MODEL_VIEW : Permission.INVESTIGATION_MODEL_VIEW],
        },
      },
    ],
  },
];

export const riskModelRoutes = baseModelRoutes('model', '/risk-model', '尽调模型', '1');
