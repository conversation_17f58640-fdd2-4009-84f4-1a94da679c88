import { mount, shallowMount } from '@vue/test-utils';
import { ref } from 'vue';
import { Breadcrumb, Spin, message } from 'ant-design-vue';
import { useRoute } from 'vue-router/composables';
import { setting as settingService } from '@/shared/services';
import RiskModelDetailPage from '..';
import ModelSettingSummary from '../widgets/model-setting-summary';
import ModelQuantitativeIndicators from '../widgets/model-quantitative-indicators';
import ModelDimensionSettings from '../widgets/model-dimenison-settings';

import { useModelDataHook } from '../../../hooks/use-data-hook';
import { flushPromises } from '@/test-utils/flush-promises';

vi.mock('@/shared/services', () => ({
  setting: {
    getModelDetail: vi.fn(),
    editNameSetting: vi.fn(),
  },
}));
vi.mock('vue-router/composables', () => ({
  useRoute: vi.fn(() => ({
    params: {
      riskModelId: '111',
    },
  })),
}));
vi.mock('../../../hooks/use-data-hook');

const modelDetail = {
  modelId: 'testModelId',
  modelName: 'Test Model',
  modelType: '1',
  status: 1,
  resultSetting: [],
  distributedResource: [{ distributeStatus: 1 }],
  comment: '',
};
describe('RiskModelDetailPage', () => {
  let modelDetailHook: any;
  let route: any;

  beforeEach(() => {
    modelDetailHook = {
      modelDetail: ref(modelDetail),
    };
    route = {
      params: {
        riskModelId: 'testRiskModelId',
      },
    };
    vi.mocked<any>(useModelDataHook).mockReturnValue(modelDetailHook);
    vi.mocked<any>(useRoute).mockReturnValue(route);
    vi.mocked<any>(settingService.getModelDetail).mockReturnValue(modelDetail);
    message.success = vi.fn();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders breadcrumb and model name when isExternal is false', async () => {
    const wrapper = mount(RiskModelDetailPage, {
      propsData: {
        isExternal: false,
        modelType: '1',
      },
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent(Breadcrumb).exists()).toBe(true);
    expect(wrapper.findComponent(Breadcrumb.Item).exists()).toBe(true);
    expect(wrapper.html()).toContain('尽调模型');
    expect(wrapper.html()).toContain('Test Model');
  });

  it('does not render breadcrumb when isExternal is true', async () => {
    const wrapper = mount(RiskModelDetailPage, {
      propsData: {
        isExternal: true,
        modelType: '1',
      },
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent(Breadcrumb).exists()).toBe(false);
  });

  it('renders Spin component when loading is true', async () => {
    modelDetailHook.modelDetail.value = {};
    vi.mocked<any>(settingService.getModelDetail).mockImplementation(() => new Promise(() => {}));
    const wrapper = mount(RiskModelDetailPage, {
      propsData: {
        isExternal: false,
        modelType: '1',
      },
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent(Spin).exists()).toBe(true);
  });

  it('renders ModelSettingSummary, ModelQuantitativeIndicators, and ModelDimensionSettings when loading is false and modelType is 1', async () => {
    const wrapper = mount(RiskModelDetailPage, {
      propsData: {
        isExternal: false,
        modelType: '1',
      },
    });

    await wrapper.vm.$nextTick();
    await flushPromises();

    expect(wrapper.findComponent(ModelSettingSummary).exists()).toBe(true);
    expect(wrapper.findComponent(ModelQuantitativeIndicators).exists()).toBe(true);
    expect(wrapper.findComponent(ModelDimensionSettings).exists()).toBe(true);
  });

  it('renders ModelSettingSummary and ModelDimensionSettings but not ModelQuantitativeIndicators when loading is false and modelType is 2', async () => {
    modelDetailHook.modelDetail.value.modelType = 2;
    const wrapper = mount(RiskModelDetailPage, {
      propsData: {
        isExternal: false,
        modelType: 2,
      },
    });

    await wrapper.vm.$nextTick();
    await flushPromises();
    expect(wrapper.findComponent(ModelSettingSummary).exists()).toBe(true);
    expect(wrapper.findComponent(ModelQuantitativeIndicators).exists()).toBe(false);
    expect(wrapper.findComponent(ModelDimensionSettings).exists()).toBe(true);
  });

  it('sets isMonitor to true when modelType is 2', async () => {
    modelDetailHook.modelDetail.value.modelType = 2;
    const wrapper = mount(RiskModelDetailPage, {
      propsData: {
        modelType: 2,
      },
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.vm.isMonitor).toBe(true);
  });

  it('sets isDisabled to true when status is 0', async () => {
    modelDetailHook.modelDetail.value.status = 0;
    const wrapper = mount(RiskModelDetailPage, {
      propsData: {
        modelType: '1',
      },
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.vm.isDisabled).toBe(true);
  });

  it('sets isDisabled to true when distributeStatus is 0', async () => {
    modelDetailHook.modelDetail.value.distributedResource[0].distributeStatus = 0;
    const wrapper = mount(RiskModelDetailPage, {
      propsData: {
        modelType: '1',
      },
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.vm.isDisabled).toBe(true);
  });

  it('sets isDisabled to true when status is 4', async () => {
    modelDetailHook.modelDetail.value.status = 4;
    const wrapper = mount(RiskModelDetailPage, {
      propsData: {
        modelType: '1',
      },
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.vm.isDisabled).toBe(true);
  });

  it('sets isDisabled to true when distributeStatus is 3', async () => {
    modelDetailHook.modelDetail.value.distributedResource[0].distributeStatus = 3;
    const wrapper = mount(RiskModelDetailPage, {
      propsData: {
        modelType: '1',
      },
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.vm.isDisabled).toBe(true);
  });

  it('calls updateModelName and updates modelDetail modelName when editNameSetting is successful', async () => {
    const newName = 'New Model Name';
    vi.mocked<any>(settingService.editNameSetting).mockResolvedValue(undefined);
    const wrapper = shallowMount(RiskModelDetailPage, {
      propsData: {
        modelType: '1',
      },
    });

    await wrapper.vm.updateModelName(newName);
    expect(settingService.editNameSetting).toHaveBeenCalledWith({
      modelId: 'testModelId',
      resultSetting: [],
      comment: '',
      modelName: newName,
    });
    expect(wrapper.vm.modelDetail.modelName).toBe(newName);
    expect(message.success).toHaveBeenCalledWith('模型名称保存成功');
  });
});
