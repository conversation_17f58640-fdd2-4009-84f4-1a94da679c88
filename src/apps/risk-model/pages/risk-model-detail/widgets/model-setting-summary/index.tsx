import { defineComponent } from 'vue';

import RiskModelStatusTag from '@/shared/components/risk-model-status-tag';

import styles from './model-setting-summary.module.less';
import NameChangeInput from './widgets/name-change-input';

const modelSettingSummary = defineComponent({
  name: 'modelSettingSummary',
  props: {
    detail: {
      type: Object,
      required: true,
    },
  },
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.title}>
          {/* 输入框 */}
          <NameChangeInput
            value={this.detail.modelName}
            onChange={(e) => {
              this.$emit('updateName', e);
            }}
          />
          {/*  模型状态 */}
          <RiskModelStatusTag status={this.detail.status} distributeStatus={this.detail?.distributedResource?.[0]?.distributeStatus} />
          {/*  定量指标概览 */}
          {/* <IndicatorOverview indicatorData={this.detail?.resultSetting} /> */}
        </div>
        <div data-testid="model-setting-summary" class={styles.comment}>
          {this.detail.comment}
        </div>
      </div>
    );
  },
});

export default modelSettingSummary;
