import { mount } from '@vue/test-utils';
import IndicatorOverview from '..';
import { Divider } from 'ant-design-vue';

describe('IndicatorOverview', () => {
  it('renders correctly with all indicator data provided', () => {
    const wrapper = mount(IndicatorOverview, {
      propsData: {
        indicatorData: {
          0: 'Value1',
          1: 'Value2',
          2: 'Value3',
        },
      },
    });

    expect(wrapper.exists()).toBe(true);
    expect(wrapper.findAll('[data-testid="indicator-item"]').length).toBe(3);
    expect(wrapper.find('[data-testid="indicator-name"]').text()).toBe('定量指标');
    expect(wrapper.find('[data-testid="indicator-value"]').text()).toBe('Value1');
    expect(wrapper.findAll('[data-testid="indicator-value"]').at(1).text()).toBe('Value2');
    expect(wrapper.findAll('[data-testid="indicator-value"]').at(2).text()).toBe('Value3');
    expect(wrapper.findAllComponents(Divider).length).toBe(2);
  });

  it('renders correctly with some indicator data missing', () => {
    const wrapper = mount(IndicatorOverview, {
      propsData: {
        indicatorData: {
          0: 'Value1',
          2: 'Value3',
        },
      },
    });
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.findAll('[data-testid="indicator-item"]').length).toBe(3);
    expect(wrapper.find('[data-testid="indicator-name"]').text()).toBe('定量指标');
    expect(wrapper.find('[data-testid="indicator-value"]').text()).toBe('Value1');
    expect(wrapper.findAll('[data-testid="indicator-value"]').at(1).text()).toBe('');
    expect(wrapper.findAll('[data-testid="indicator-value"]').at(2).text()).toBe('Value3');
    expect(wrapper.findAllComponents(Divider).length).toBe(2);
  });

  it('renders correctly with no indicator data', () => {
    const wrapper = mount(IndicatorOverview, {
      propsData: {
        indicatorData: {},
      },
    });

    expect(wrapper.exists()).toBe(true);
    expect(wrapper.findAll('[data-testid="indicator-item"]').length).toBe(3);
    expect(wrapper.find('[data-testid="indicator-name"]').text()).toBe('定量指标');
    expect(wrapper.find('[data-testid="indicator-value"]').text()).toBe('');
    expect(wrapper.findAll('[data-testid="indicator-value"]').at(1).text()).toBe('');
    expect(wrapper.findAll('[data-testid="indicator-value"]').at(2).text()).toBe('');
    expect(wrapper.findAllComponents(Divider).length).toBe(2);
  });

  it('renders correctly with undefined indicator data', () => {
    const wrapper = mount(IndicatorOverview, {
      propsData: {
        indicatorData: undefined,
      },
    });

    expect(wrapper.exists()).toBe(true);
    expect(wrapper.findAll('[data-testid="indicator-item"]').length).toBe(3);
    expect(wrapper.find('[data-testid="indicator-name"]').text()).toBe('定量指标');
    expect(wrapper.find('[data-testid="indicator-value"]').text()).toBe('');
    expect(wrapper.findAll('[data-testid="indicator-value"]').at(1).text()).toBe('');
    expect(wrapper.findAll('[data-testid="indicator-value"]').at(2).text()).toBe('');
    expect(wrapper.findAllComponents(Divider).length).toBe(2);
  });

  it('renders only one indicator item without dividers when there is only one item', () => {
    const wrapper = mount(IndicatorOverview, {
      propsData: {
        indicatorData: {
          0: 'Value1',
        },
      },
    });
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.findAll('[data-testid="indicator-item"]').length).toBe(3);
    expect(wrapper.find('[data-testid="indicator-name"]').text()).toBe('定量指标');
    expect(wrapper.find('[data-testid="indicator-value"]').text()).toBe('Value1');
    expect(wrapper.findAll('[data-testid="indicator-value"]').at(1).text()).toBe('');
    expect(wrapper.findAll('[data-testid="indicator-value"]').at(2).text()).toBe('');
    expect(wrapper.findAllComponents(Divider).length).toBe(2);
  });
});
