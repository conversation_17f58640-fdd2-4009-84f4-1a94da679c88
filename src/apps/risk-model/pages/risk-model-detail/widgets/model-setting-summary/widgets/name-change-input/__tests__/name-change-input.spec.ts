import { mount, shallowMount } from '@vue/test-utils';
import NameChangeInput from '..';
import { Input, message } from 'ant-design-vue';

describe('NameChangeInput', () => {
  it('renders correctly with initial value', () => {
    const wrapper = shallowMount(NameChangeInput, {
      propsData: {
        value: 'Initial Name',
        width: '300px',
      },
    });
    expect(wrapper.text()).toContain('Initial Name');
    expect(wrapper.find('.ant-input').exists()).toBe(false);
  });

  it('renders input when edit icon is clicked', async () => {
    const wrapper = shallowMount(NameChangeInput, {
      propsData: {
        value: 'Initial Name',
        width: '300px',
      },
      provide: {
        modelDetail: {
          status: 2,
        },
      },
    });
    await wrapper.vm.$nextTick();
    await wrapper.find('[data-testid="edit-icon"]').vm.$emit('click');
    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent(Input).exists()).toBe(true);
  });

  it('does not render edit icon when modelDetail.status is not 2', () => {
    const wrapper = shallowMount(NameChangeInput, {
      propsData: {
        value: 'Initial Name',
        width: '300px',
      },
      provide: {
        modelDetail: {
          status: 1,
        },
      },
    });
    expect(wrapper.find('q-icon').exists()).toBe(false);
  });

  it('cancels edit and resets input value when cancel button is clicked', async () => {
    const wrapper = mount(NameChangeInput, {
      propsData: {
        value: 'Initial Name',
        width: '300px',
      },
      provide: {
        modelDetail: {
          status: 2,
        },
      },
    });
    await wrapper.vm.$nextTick();
    await wrapper.find('[data-testid="edit-icon"]').vm.$emit('click');
    await wrapper.vm.$nextTick();
    const input = wrapper.findComponent(Input);
    input.vm.$emit('change', 'new Name');
    await wrapper.find('[data-testid="cancel-btn"]').trigger('click');
    expect(wrapper.findComponent(Input).exists()).toBe(false);
    expect(wrapper.text()).toContain('Initial Name');
  });

  it('emits change event with new input value when confirm button is clicked', async () => {
    const wrapper = shallowMount(NameChangeInput, {
      propsData: {
        value: 'Initial Name',
        width: '300px',
      },
      provide: {
        modelDetail: {
          status: 2,
        },
      },
    });
    wrapper.find('[data-testid="edit-icon"]').vm.$emit('click');
    await wrapper.vm.$nextTick();

    const input = wrapper.findComponent(Input);
    expect(input.exists()).toBe(true);

    // 修改input的值
    input.vm.$emit('change', { target: { value: 'new Name' } });

    const saveBtn = wrapper.find('[data-testid="save-btn"]');
    expect(saveBtn.exists()).toBe(true);
    saveBtn.vm.$emit('click');
    expect(wrapper.emitted().change).toBeTruthy();
  });

  it('shows error message when confirm button is clicked with empty input', async () => {
    const messgeSpy = vi.spyOn(message, 'error');
    const wrapper = shallowMount(NameChangeInput, {
      propsData: {
        value: 'Initial Name',
        width: '300px',
      },
      provide: {
        modelDetail: {
          status: 2,
        },
      },
    });
    await wrapper.find('[data-testid="edit-icon"]').vm.$emit('click');
    await wrapper.vm.$nextTick();
    const input = wrapper.findComponent(Input);
    input.vm.$emit('change', {
      target: { value: '' },
    });
    await wrapper.find('[data-testid="save-btn"]').vm.$emit('click');
    expect(messgeSpy).toHaveBeenCalledWith('名称不能为空');
  });

  it('renders input with different width when width prop is provided', async () => {
    const wrapper = shallowMount(NameChangeInput, {
      propsData: {
        value: 'Initial Name',
        width: '400px',
      },
      provide: {
        modelDetail: {
          status: 2,
        },
      },
    });
    await wrapper.vm.$nextTick();
    await wrapper.find('[data-testid="edit-icon"]').vm.$emit('click');
    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent(Input).attributes('style')).toContain('width: 400px');
  });

  it('does not render edit input when isDisabled is true', () => {
    const wrapper = shallowMount(NameChangeInput, {
      propsData: {
        value: 'Initial Name',
        width: '300px',
      },
      provide: {
        isDisabled: false,
      },
    });
    expect(wrapper.find('[data-testid="edit-icon"]').exists()).toBe(false);
    expect(wrapper.findComponent(Input).exists()).toBe(false);
  });
});
