import { computed, defineComponent, inject, ref } from 'vue';
import { Button, Input, message } from 'ant-design-vue';

import styles from './name-change-input.module.less';

const NameChangeInput = defineComponent({
  name: 'NameChangeInput',
  props: {
    value: {
      type: String,
      default: '',
    },
    width: {
      type: [String, Number],
      default: '300px',
    },
  },
  setup(props) {
    const isEdit = ref(false);
    const inputValue = ref(props.value);

    const disable = inject('isDisabled', false);

    const modelDetail = inject('modelDetail', {} as any);
    const nameValidator = computed(() => {
      if (inputValue.value?.trim() === '') {
        return false;
      }
      return true;
    });

    return {
      isEdit,
      disable,
      modelDetail,
      inputValue,
      nameValidator,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <q-icon type="icon-icon_fengxianmoxing1" style={{ fontSize: '24px', transform: 'translateY(-1px)', color: '#999' }} />
        {this.isEdit ? (
          <div class="flex items-center" style="gap: 5px">
            <Input
              style={{ width: this.width }}
              class={{ [styles.error]: !this.nameValidator }}
              placeholder="请输入名称"
              maxLength={30}
              value={this.inputValue}
              allowClear
              onChange={(e) => {
                this.inputValue = e.target?.value;
              }}
            />

            <Button
              type="link"
              data-testid="cancel-btn"
              class={styles.cancel}
              onClick={() => {
                this.inputValue = this.value;
                this.isEdit = false;
              }}
            >
              取消
            </Button>

            <Button
              type="link"
              data-testid="save-btn"
              onClick={() => {
                this.isEdit = false;
                if (!this.inputValue) {
                  message.error('名称不能为空');
                  return;
                }
                this.$emit('change', this.inputValue);
              }}
            >
              确定
            </Button>
          </div>
        ) : (
          <div>
            <span class={styles.default}>
              {this.value}
              {this.modelDetail.status === 2 && !this.disable && (
                <q-icon
                  type="icon-a-bianjixian1x"
                  data-testid="edit-icon"
                  onClick={() => {
                    this.isEdit = true;
                  }}
                ></q-icon>
              )}
            </span>
          </div>
        )}
      </div>
    );
  },
});

export default NameChangeInput;
