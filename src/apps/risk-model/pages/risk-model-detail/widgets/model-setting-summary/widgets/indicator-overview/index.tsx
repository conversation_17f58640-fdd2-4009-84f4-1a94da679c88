import { Divider } from 'ant-design-vue';
import { defineComponent } from 'vue';

import styles from './indicator-overview.module.less';

const IndicatorMap = {
  0: {
    name: '定量指标',
  },
  1: {
    name: '定性指标',
  },
  2: {
    name: '未启用',
  },
};

const IndicatorOverview = defineComponent({
  name: 'IndicatorOverview',
  props: {
    indicatorData: {
      type: Object,
      required: false,
    },
  },
  render() {
    return (
      <div class={styles.indicatorOverview}>
        <q-icon type="icon-shezhizhongxin" style="color: #999"></q-icon>
        {Object.keys(IndicatorMap).map((indictor, index) => {
          return [
            <div class={styles.indicatorItem} data-testid="indicator-item">
              <span data-testid="indicator-name" class={styles.indicatorName}>
                {IndicatorMap[indictor].name}
              </span>
              <span data-testid="indicator-value" class={styles.indicatorValue}>
                {this.indicatorData?.[indictor]}
              </span>
            </div>,
            index !== Object.keys(IndicatorMap).length - 1 && <Divider type="vertical" />,
          ];
        })}
      </div>
    );
  },
});

export default IndicatorOverview;
