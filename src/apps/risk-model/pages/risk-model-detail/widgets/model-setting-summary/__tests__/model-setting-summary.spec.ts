import { mount } from '@vue/test-utils';
import RiskModelStatusTag from '@/shared/components/risk-model-status-tag';
import modelSettingSummary from '..';
import NameChangeInput from '../widgets/name-change-input';

describe('modelSettingSummary', () => {
  it('renders correctly with complete detail', () => {
    const wrapper = mount(modelSettingSummary, {
      propsData: {
        detail: {
          modelName: 'Test Model',
          status: 'active',
          distributedResource: [{ distributeStatus: 'distributed' }],
          comment: 'This is a test comment',
        },
      },
    });

    expect(wrapper.exists()).toBe(true);
    expect(wrapper.findComponent(NameChangeInput).props('value')).toBe('Test Model');
    expect(wrapper.findComponent(RiskModelStatusTag).props('status')).toBe('active');
    expect(wrapper.findComponent(RiskModelStatusTag).props('distributeStatus')).toBe('distributed');
    expect(wrapper.text()).toContain('This is a test comment');
  });

  it('renders correctly with no distributedResource', () => {
    const wrapper = mount(modelSettingSummary, {
      propsData: {
        detail: {
          modelName: 'Test Model',
          status: 'active',
          comment: 'This is a test comment',
        },
      },
    });

    expect(wrapper.findComponent(RiskModelStatusTag).props('distributeStatus')).toBeUndefined();
  });

  it('renders correctly with empty distributedResource array', () => {
    const wrapper = mount(modelSettingSummary, {
      propsData: {
        detail: {
          modelName: 'Test Model',
          status: 'active',
          distributedResource: [],
          comment: 'This is a test comment',
        },
      },
    });

    expect(wrapper.findComponent(RiskModelStatusTag).props('distributeStatus')).toBeUndefined();
  });

  it('renders correctly with no comment', () => {
    const wrapper = mount(modelSettingSummary, {
      propsData: {
        detail: {
          modelName: 'Test Model',
          status: 'active',
          distributedResource: [{ distributeStatus: 'distributed' }],
        },
      },
    });

    expect(wrapper.find('[data-testid="model-setting-summary"]').text()).toBe('');
  });

  it('emits updateName event when NameChangeInput changes', async () => {
    const wrapper = mount(modelSettingSummary, {
      propsData: {
        detail: {
          modelName: 'Test Model',
          status: 'active',
          comment: 'This is a test comment',
        },
      },
    });

    const nameChangeInput = wrapper.findComponent(NameChangeInput);
    await nameChangeInput.vm.$emit('change', { target: { value: 'New Model Name' } });

    expect(wrapper.emitted('updateName')).toBeTruthy();
    expect(wrapper.emitted('updateName')).toEqual([[{ target: { value: 'New Model Name' } }]]);
  });

  it('renders correctly with empty detail', () => {
    const wrapper = mount(modelSettingSummary, {
      propsData: {
        detail: {},
      },
    });

    expect(wrapper.findComponent(NameChangeInput).props('value')).toBe('');
    expect(wrapper.findComponent(RiskModelStatusTag).props('status')).toBeUndefined();
    expect(wrapper.findComponent(RiskModelStatusTag).props('distributeStatus')).toBeUndefined();
    expect(wrapper.find('[data-testid="model-setting-summary"]').text()).toBe('');
  });
});
