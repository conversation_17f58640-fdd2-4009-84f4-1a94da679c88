import { defineComponent, inject } from 'vue';

import styles from './model-quantitative-indicators.module.less';
import IndictorLineSetting from './widgets/indictor-line-setting';
import { openQuantitativeIndicatorsSettingModel } from './widgets/quantitative-indicators-setting-model';

const INDICTOR_LINE_STYLE_MAP = {
  default: {
    2: {
      bgColor: '#a80000',
      pgwidth: '170px',
      mdwidth: '105px',
      desc: '该企业处于高风险等级，建议谨慎与之建立业务关系',
    },
    1: {
      bgColor: '#ff722d',
      pgwidth: '185px',
      mdwidth: '115px',
      desc: '该企业风险等级处于较高水平，建议采取进一步的强化尽职调查',
    },
    0: {
      bgColor: '#ffc043',
      pgwidth: '200px',
      mdwidth: '126px',
      desc: '该企业当前风险等级处于中等水平',
    },
    '-1': {
      bgColor: '#128BED',
      pgwidth: '215px',
      mdwidth: '138px',
      desc: '该企业当前时间点，存在个别风险特征项目',
    },
    '-2': {
      bgColor: '#00AD65',
      pgwidth: '230px',
      mdwidth: '150px',
      desc: '该企业当前时间点，暂无明显风险特征项目',
    },
  },
  tech: {
    2: {
      bgColor: '#00ad65',
      pgwidth: '170px',
      mdwidth: '105px',
      desc: '企业科创实力强劲，拥有多项核心技术，引领行业创新潮流',
    },
    1: {
      bgColor: '#128BED',
      pgwidth: '185px',
      mdwidth: '115px',
      desc: '企业在科创领域表现突出，能独立研发创新产品，有成功案例',
    },
    0: {
      bgColor: '#FFAA00',
      pgwidth: '200px',
      mdwidth: '126px',
      desc: '企业已建立科创体系，能完成常规研发任务，有一定技术积累',
    },
    '-1': {
      bgColor: '#FF722D',
      pgwidth: '215px',
      mdwidth: '138px',
      desc: '企业初涉科技创新，具备基本的研发意识与基础能力',
    },
    '-2': {
      bgColor: '#b30000',
      pgwidth: '230px',
      mdwidth: '150px',
      desc: '科创发展的健康性存在一定风险，建议采取进一步的强化尽职调查',
    },
    '-3': {
      bgColor: '#b30000',
      pgwidth: '245px',
      mdwidth: '150px',
      desc: '科创发展的健康性存在较大风险，建议谨慎与之建立业务关系',
    },
  },
};

const ModelQuantitativeIndicators = defineComponent({
  name: 'ModelQuantitativeIndicators',
  props: {
    detail: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const editModelQualities = async () => {
      const res = await openQuantitativeIndicatorsSettingModel(props.detail);
      emit('update', res);
    };

    const isMonitor = inject('isMonitor', false);
    const disable = inject('isDisabled', false);

    return {
      editModelQualities,
      isMonitor,
      disable,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.title}>
          <div>定量指标</div>
          {!this.isMonitor && !this.disable && this.detail.status === 2 && (
            <div class={styles.extra} onClick={this.editModelQualities} data-testid="edit-model-qualities">
              <q-icon type="icon-shezhizhongxin" color="#999"></q-icon>
              设置模型
            </div>
          )}
        </div>
        <div>
          {this.detail?.resultSetting?.map((item: any, index) => {
            // default | tech
            const modelSettingType = this.detail?.extendJson?.showScoreCard === 1 ? 'tech' : 'default';
            return (
              <IndictorLineSetting
                key={index}
                type={modelSettingType}
                mapping={INDICTOR_LINE_STYLE_MAP[modelSettingType]}
                isEdit={false}
                lineData={item}
              />
            );
          })}
        </div>
        {/* <div class={styles.tip}>
          <div class={styles.content}>
            若您的评分卡中任一「
            <q-icon type="icon-icon_hongsedengji" />
            红色等级」 被命中，模型结果将直接更新为
            <span class={styles.highRisk}> 「高风险」</span>
            ，请密切关注指标设置
          </div>
        </div> */}
      </div>
    );
  },
});

export default ModelQuantitativeIndicators;
