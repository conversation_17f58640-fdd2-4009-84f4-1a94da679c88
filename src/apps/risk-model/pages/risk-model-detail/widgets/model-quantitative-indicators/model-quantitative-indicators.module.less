.container{
  background: #F6FBFE;
  border: 1px solid #C4DFF5;
  padding: 15px;
  border-radius: 4px;
  margin-top: 15px;

  .title{
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 24px;
    line-height: 24px;
    font-size: 16px;
    font-weight: bold;

    .extra{
      color: #999;
      display: flex;
      padding: 0 5px;
      align-items: center;
      font-weight: normal;
      font-size: 14px;
      gap: 4px;
      cursor: pointer;

      &:hover{
        color: #1890FF;
      }
    }
  }

  .tip {
    padding-left: 350px;

    .content {
      padding: 0 10px;
      height: 32px;
      line-height: 32px;
      margin-top: 10px;
      border: 1px solid #FCC;
      border-radius: 4px;
      background: #FFF8F9;
      color: #666;

      :global{
        .anticon{
          color: #F04040;
          font-size: 18px;
          transform: translateY(2px);
        }
      }

      .highRisk{
        color: #B30000;
      }
    }
  }
}
