import { mount } from '@vue/test-utils';
import { defineComponent, inject } from 'vue';

import ModelQuantitativeIndicators from '../index';
import IndictorLineSetting from '../widgets/indictor-line-setting';
import { openQuantitativeIndicatorsSettingModel } from '../widgets/quantitative-indicators-setting-model';

vi.mock('../widgets/quantitative-indicators-setting-model', () => ({
  openQuantitativeIndicatorsSettingModel: vi.fn().mockResolvedValue({}),
}));

describe('ModelQuantitativeIndicators', () => {
  let wrapper: any;

  beforeEach(() => {
    wrapper = mount(ModelQuantitativeIndicators, {
      propsData: {
        detail: {},
      },
      provide: {
        isMonitor: false,
        isDisabled: false,
      },
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders with default props', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('[data-testid="edit-model-qualities"]').exists()).toBe(false);
  });

  it('renders "设置模型" button when detail.status is 2 and isMonitor and isDisabled are false', async () => {
    wrapper.setProps({ detail: { status: 2 } });
    await wrapper.vm.$nextTick();
    expect(wrapper.find('[data-testid="edit-model-qualities"]').exists()).toBe(true);
  });

  it('does not render "设置模型" button when detail.status is not 2', async () => {
    wrapper.setProps({ detail: { status: 1 } });
    await wrapper.vm.$nextTick();
    expect(wrapper.find('[data-testid="edit-model-qualities"]').exists()).toBe(false);
  });

  it('does not render "设置模型" button when isMonitor is true', async () => {
    wrapper.setProps({ detail: { status: 2 } });
    wrapper.vm.isMonitor = true;
    await wrapper.vm.$nextTick();
    expect(wrapper.find('[data-testid="edit-model-qualities"]').exists()).toBe(false);
  });

  it('does not render "设置模型" button when isDisabled is true', async () => {
    wrapper.setProps({ detail: { status: 2 } });
    wrapper.vm.disable = true;
    await wrapper.vm.$nextTick();
    expect(wrapper.find('[data-testid="edit-model-qualities"]').exists()).toBe(false);
  });

  it('renders IndictorLineSetting components when detail.resultSetting is provided', async () => {
    wrapper.setProps({ detail: { resultSetting: [{}, {}] } });
    await wrapper.vm.$nextTick();
    expect(wrapper.findAllComponents(IndictorLineSetting)).toHaveLength(2);
  });

  it('does not render IndictorLineSetting components when detail.resultSetting is not provided', async () => {
    wrapper.setProps({ detail: {} });
    await wrapper.vm.$nextTick();
    expect(wrapper.findAllComponents(IndictorLineSetting)).toHaveLength(0);
  });

  it('emits "update" event when "设置模型" button is clicked', async () => {
    wrapper.setProps({ detail: { status: 2 } });
    await wrapper.vm.$nextTick();
    wrapper.find('[data-testid="edit-model-qualities"]').trigger('click');
    await wrapper.vm.$nextTick();
    expect(wrapper.emitted().update).toBeTruthy();
  });

  it('calls openQuantitativeIndicatorsSettingModel with correct detail when "设置模型" button is clicked', async () => {
    const detail = { status: 2, some: 'data' };
    wrapper.setProps({ detail });
    await wrapper.vm.$nextTick();
    wrapper.find('[data-testid="edit-model-qualities"]').trigger('click');
    await wrapper.vm.$nextTick();
    expect(openQuantitativeIndicatorsSettingModel).toHaveBeenCalledWith(detail);
  });
});
