.container{
  display: flex;
  gap: 15px;
  align-items: center;
  height: 32px;
  padding: 0 10px;
  border: 1px solid #D8D8D8;
  border-radius: 2px;
  color: #999;
  background: #fff;
  transition: all 0.3s;

  &:hover{
    border-color: #128dff;
  }

  .number{
    color: #333;
    width: 43px;
    text-align: center;
  }

  .icon{
    cursor: pointer;
    color: #D8D8D8;

    &:hover{
      color: #999;
    }
  }
}

.disabled{
  color: #999;
  background: #f3f3f3;
  transition: all 0.3s;
  
  .number{
    color: #999;
  }

  &:hover{
    border-color: none;
  }

  .icon{
    opacity: 0;
    pointer-events: none;
  }
}