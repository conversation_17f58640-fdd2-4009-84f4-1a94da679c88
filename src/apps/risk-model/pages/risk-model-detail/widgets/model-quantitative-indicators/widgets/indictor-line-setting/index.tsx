import { defineComponent, inject, ref, watch } from 'vue';
import { Checkbox } from 'ant-design-vue';

import styles from './indictor-line-setting.module.less';
import NumberPeriodSetting from '../number-period-setting';

// 打分设置单元
const IndictorLineSetting = defineComponent({
  name: 'IndictorLineSetting',
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    lineData: {
      type: Object,
      default: () => ({}),
    },
    isFirst: {
      type: Boolean,
      default: false,
    },
    isLast: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'default', // default | tech
    },
    mapping: {
      type: Object,
      default: () => ({
        // 1: {
        //   bgColor: '#a80000',
        //   pgwidth: '170px',
        //   mdwidth: '105px',
        //   desc: '该企业处于高风险等级，建议谨慎与之建立业务关系',
        // },
        // 2: {
        //   bgColor: '#ff722d',
        //   pgwidth: '185px',
        //   mdwidth: '115px',
        //   desc: '该企业风险等级处于较高水平，建议采取进一步的强化尽职调查',
        // },
        // 3: {
        //   bgColor: '#ff5e71',
        //   pgwidth: '200px',
        //   mdwidth: '126px',
        //   desc: '该企业当前风险等级处于中等水平',
        // },
        // 4: {
        //   bgColor: '#128BED',
        //   pgwidth: '215px',
        //   mdwidth: '138px',
        //   desc: '该企业当前时间点，存在个别风险特征项目',
        // },
        // 5: {
        //   bgColor: '#00AD65',
        //   pgwidth: '230px',
        //   mdwidth: '150px',
        //   desc: '该企业当前时间点，暂无明显风险特征项目',
        // },
      }),
    },
  },

  setup(props, { emit }) {
    const curData = ref(props.lineData);

    const isMonitor = inject('isMonitor', false);

    const disabled = inject('isDisabled', false);
    watch(
      () => props.lineData,
      (val) => {
        curData.value = val;
      }
    );

    watch(curData.value, (val) => {
      emit('updateLineData', val);
    });

    return {
      curData,
      disabled,
      isMonitor,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        {/* {this.isEdit && (
          <Checkbox
            checked={true}
            onChange={(e) => {
              this.curData.active = e.target.checked;
            }}
          />
        )} */}
        <div class={[styles.trapezoidWrapper, this.curData.active ? styles.unActive : '', this.isEdit ? styles.minWidth : '']}>
          <div
            style={{
              width: this.isEdit ? this.mapping[this.lineData.level].mdwidth : this.mapping[this.lineData.level].pgwidth,
            }}
            class={[styles.bar, styles[`trapezoid${this.lineData.level}`]]}
          >
            <span class={styles.text}>{!this.isEdit && this.lineData.name}</span>
            <i class={styles.tail} style={{ backgroundColor: this.mapping[this.lineData.level].bgColor }}></i>
          </div>
        </div>

        <div style="flex: 1;" class="flex items-center justify-between">
          {this.isEdit ? (
            // 第一版不支持改名称
            // <Input
            //   style={{ width: '200px' }}
            //   allowClear
            //   value={this.curData.name}
            //   onChange={(e) => {
            //     this.curData.name = e.target.value;
            //   }}
            // />
            <div>{this.curData.name}</div>
          ) : (
            <div>{this.mapping[this.lineData.level].desc}</div>
          )}

          {!this.isMonitor ? (
            <NumberPeriodSetting
              isEdit={this.isEdit}
              disabled={this.disabled}
              isFirst={this.isFirst}
              isLast={this.isLast}
              value={[this.curData.mimScore, this.curData.maxScore]}
              onValueChange={(values) => {
                const [mimScore, maxScore] = values;
                this.curData = Object.assign(this.curData, { mimScore, maxScore });
              }}
            />
          ) : null}
        </div>
      </div>
    );
  },
});
export default IndictorLineSetting;
