.container{
  display: flex;
  align-items: center;
  height: 32px;
  line-height: 32px;
  margin-bottom: 5px;
  gap: 50px;

  :global{
    .ant-checkbox-wrapper{
      margin-right: 10px;
    }
  }

  .trapezoidWrapper{
    width:  300px;

    .bar {
      height: 32px;
      font-size: 18px;
      font-weight: bold;
      padding-left: 10px;
      position: relative;
      display: inline-block;

      .tail {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 4px;
        width: 100%;
        z-index: 1;
        -webkit-transform: perspective(60px) rotateX(7deg) scaleY(1.1) translateY(6px);
        -moz-transform: perspective(60px) rotateX(7deg) scaleY(1.1) translateY(6px);
        -ms-transform: perspective(60px) rotateX(7deg) scaleY(1.1) translateY(6px);
        -o-transform: perspective(60px) rotateX(7deg) scaleY(1.1) translateY(6px);
        transform: perspective(60px)rotateX(7deg) scaleY(1.1) translateY(6px);
        -webkit-transform-origin:bottom left;
        transform-origin:bottom left;
      }
    }

    .text{
      color: #fff;
      position: absolute;
      margin: auto 0;
      z-index: 2;
      transform: translateY(5px);
    }
  }

  .minWidth{
    width: 160px;
  }

  .unActive{
    .bar::after{
      background-color: #f3f3f3;
    }

    .text{
      z-index: 0;
    }
  }
}
