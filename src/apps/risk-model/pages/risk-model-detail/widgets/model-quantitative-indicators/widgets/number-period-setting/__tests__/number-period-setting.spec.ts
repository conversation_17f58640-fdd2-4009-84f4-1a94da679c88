import { mount } from '@vue/test-utils';
import { Input } from 'ant-design-vue';

import NumberPeriodSetting from '..';

describe('NumberPeriodSetting Component', () => {
  it('renders correctly in view mode', () => {
    const wrapper = mount(NumberPeriodSetting, {
      propsData: {
        isEdit: false,
        value: [10, 20],
      },
    });

    expect(wrapper.findAll('[data-testid="number-period-setting"]').at(0).text()).toBe('10');
    expect(wrapper.findAll('[data-testid="number-period-setting"]').at(1).text()).toBe('20');
  });

  it('renders correctly in view mode with null values', () => {
    const wrapper = mount(NumberPeriodSetting, {
      propsData: {
        isEdit: false,
        value: [null, null],
      },
    });

    expect(wrapper.findAll('[data-testid="number-period-setting"]').at(0).text()).toBe('-');
    expect(wrapper.findAll('[data-testid="number-period-setting"]').at(1).text()).toBe('-');
  });

  it('renders correctly in edit mode', () => {
    const wrapper = mount(NumberPeriodSetting, {
      propsData: {
        isEdit: true,
        value: [10, 20],
      },
    });

    const inputWappers = wrapper.findAllComponents(Input) as any;
    expect(inputWappers.at(0).vm.value).toBe(10);
    expect(inputWappers.at(1).vm.value).toBe(20);
  });

  it('renders correctly in edit mode with null values', () => {
    const wrapper = mount(NumberPeriodSetting, {
      propsData: {
        isEdit: true,
        value: [null, null],
      },
    });
    const inputWappers = wrapper.findAllComponents(Input) as any;
    expect(inputWappers.at(0).vm.value).toBe('-');
    expect(inputWappers.at(1).vm.value).toBe('-');
  });

  it('emits valueChange and change events with valid input in edit mode', async () => {
    const wrapper = mount(NumberPeriodSetting, {
      propsData: {
        isEdit: true,
        value: [null, null],
      },
    });

    await wrapper.findAllComponents(Input).at(0).setValue('15');
    await wrapper.findAllComponents(Input).at(0).trigger('blur');
    await wrapper.findAllComponents(Input).at(1).setValue('25');
    await wrapper.findAllComponents(Input).at(1).trigger('blur');

    expect(wrapper.emitted('valueChange')).toEqual([[[15, null]], [[15, 25]]]);
    expect(wrapper.emitted('change')).toEqual([[[15, null]], [[15, 25]]]);
  });

  it('emits valueChange and change events with null input in edit mode', async () => {
    const wrapper = mount(NumberPeriodSetting, {
      propsData: {
        isEdit: true,
        value: [10, 20],
      },
    });

    await wrapper.findAllComponents(Input).at(0).setValue('');
    await wrapper.findAllComponents(Input).at(0).trigger('blur');
    await wrapper.findAllComponents(Input).at(1).setValue('-');
    await wrapper.findAllComponents(Input).at(1).trigger('blur');

    expect(wrapper.emitted('valueChange')).toEqual([[[null, 20]], [[null, null]]]);
    expect(wrapper.emitted('change')).toEqual([[[null, 20]], [[null, null]]]);
  });
  it('clears values on clearValue click in edit mode', async () => {
    const wrapper = mount(NumberPeriodSetting, {
      propsData: {
        isEdit: true,
        value: [10, 20],
      },
    });

    await wrapper.find('[data-testid="number-period-setting-clear"]').trigger('click');

    expect(wrapper.emitted('valueChange')).toEqual(undefined);
    expect(wrapper.emitted('change')).toEqual(undefined);
  });

  it('disables second input when isFirst is true in edit mode', () => {
    const wrapper = mount(NumberPeriodSetting, {
      propsData: {
        isEdit: true,
        isFirst: true,
        value: [10, 20],
      },
    });

    expect(wrapper.findAllComponents(Input).at(1).attributes('disabled')).toBe('disabled');
  });

  it('disables first input when isLast is true in edit mode', () => {
    const wrapper = mount(NumberPeriodSetting, {
      propsData: {
        isEdit: true,
        isLast: true,
        value: [10, 20],
      },
    });

    expect(wrapper.findAllComponents(Input).at(0).attributes('disabled')).toBe('disabled');
  });

  it('disables both inputs when disabled is true in edit mode', () => {
    const wrapper = mount(NumberPeriodSetting, {
      propsData: {
        isEdit: true,
        disabled: true,
        value: [10, 20],
      },
    });

    expect(wrapper.findAllComponents(Input).at(0).attributes('disabled')).toBe('disabled');
    expect(wrapper.findAllComponents(Input).at(1).attributes('disabled')).toBe('disabled');
  });
});
