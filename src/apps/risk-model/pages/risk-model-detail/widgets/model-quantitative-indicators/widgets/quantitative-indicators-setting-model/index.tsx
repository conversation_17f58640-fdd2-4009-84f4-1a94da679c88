import { PropType, defineComponent, onMounted, ref } from 'vue';
import { cloneDeep } from 'lodash';
import { Modal, message } from 'ant-design-vue';

import { createPromiseDialog } from '@/components/promise-dialogs';
import { setting } from '@/shared/services';

import IndictorLineSetting from '../indictor-line-setting';

export function isInRatingRange(ranges) {
  const length = ranges.length;
  if (length === 0) {
    return false;
  }
  let lastEnd = Number(ranges[0][0]) - 1;

  let flag = true;
  for (let i = 0; i < ranges.length; i++) {
    const [start, end] = ranges[i];
    if ((Number(end) <= Number(start) && i !== length - 1) || Number(start) !== lastEnd + 1) {
      flag = false;
      return false;
    }
    lastEnd = end;
  }
  return flag;
}
export const QuantitativeIndicatorsSettingModel = defineComponent({
  name: 'QuantitativeIndicatorsSettingModel',
  props: {
    params: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}) as any,
    },
  },
  setup(props, { emit }) {
    const visible = ref(false);

    const settingData = ref(cloneDeep(props.params.resultSetting));

    // 判断
    const saveData = async () => {
      const rangeValues = settingData.value.map((item) => [item.mimScore, item.maxScore]).reverse();
      // 对于激活的指标，进行分值区间判断,区间值为100，且收尾相连
      if (!isInRatingRange(rangeValues)) {
        message.error('分值区间，设置错误');
        return;
      }
      // 上限和下限都不设置值
      settingData.value[settingData.value.length - 1].mimScore = null;
      settingData.value[0].maxScore = null;
      try {
        await setting.editNameSetting({
          modelId: props.params.modelId,
          resultSetting: settingData.value,
          comment: props.params.comment,
          modelName: props.params.modelName,
        });
        message.success('定量指标保存成功');
        emit('resolve', rangeValues);
      } catch (error) {
        message.error('定量指标保存失败');
      }
    };

    onMounted(() => {
      visible.value = true;
    });

    return {
      visible,
      settingData,
      saveData,
    };
  },
  render() {
    return (
      <Modal
        title="定量指标设置"
        width={600}
        destroyOnClose={true}
        visible={this.visible}
        onClose={() => {
          this.$emit('close');
          this.visible = false;
        }}
        onCancel={() => {
          this.$emit('close');
          this.visible = false;
        }}
        onOk={this.saveData}
      >
        {this.settingData.map((item, index) => (
          <IndictorLineSetting
            isEdit={true}
            isFirst={index === 0}
            isLast={index === this.settingData.length - 1}
            lineData={{ ...item, color: index + 1 }}
            onUpdateLineData={(data) => {
              this.settingData[index] = data;
            }}
          />
        ))}
      </Modal>
    );
  },
});

export const openQuantitativeIndicatorsSettingModel = createPromiseDialog(QuantitativeIndicatorsSettingModel);
