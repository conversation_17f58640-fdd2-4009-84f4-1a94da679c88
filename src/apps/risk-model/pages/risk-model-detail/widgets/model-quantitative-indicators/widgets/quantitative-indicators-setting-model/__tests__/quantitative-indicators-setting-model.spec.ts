import { mount } from '@vue/test-utils';
import { message } from 'ant-design-vue';

import { setting } from '@/shared/services';

import IndictorLineSetting from '../../indictor-line-setting';
import { isInRatingRange, QuantitativeIndicatorsSettingModel } from '..';

vi.mock('@/shared/services', () => ({
  setting: {
    editNameSetting: vi.fn(),
  },
}));
vi.mock('ant-design-vue', () => ({
  Modal: vi.fn(),
  message: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));
describe('isInRatingRange function', () => {
  it('should return false for an empty array', () => {
    expect(isInRatingRange([])).toBe(false);
  });

  it('should return true for a single valid range', () => {
    expect(isInRatingRange([[0, 100]])).toBe(true);
  });

  it('should return false for ranges that are not contiguous', () => {
    expect(
      isInRatingRange([
        [0, 50],
        [60, 100],
      ])
    ).toBe(false);
  });

  it('should return true for contiguous ranges', () => {
    expect(
      isInRatingRange([
        [0, 50],
        [51, 100],
      ])
    ).toBe(true);
  });

  it('should return false for ranges that overlap', () => {
    expect(
      isInRatingRange([
        [0, 50],
        [40, 100],
      ])
    ).toBe(false);
  });

  it('should return true for ranges that are in descending order but valid', () => {
    expect(isInRatingRange([[100, 100]])).toBe(true);
  });
});

describe('QuantitativeIndicatorsSettingModel component', () => {
  let wrapper;

  const mockParams = {
    modelId: '123',
    modelName: 'Test Model',
    resultSetting: [
      { mimScore: 51, maxScore: 100 },
      { mimScore: 0, maxScore: 50 },
    ],
    comment: 'test comment',
  };

  beforeEach(() => {
    wrapper = mount(QuantitativeIndicatorsSettingModel, {
      propsData: {
        params: mockParams,
      },
    });
  });

  it('should set visible to true on mount', () => {
    expect(wrapper.vm.visible).toBe(true);
  });

  it('should call message.error when saveData is called with invalid ranges', async () => {
    const mockInvalidParams = {
      ...mockParams,
      resultSetting: [
        { mimScore: 0, maxScore: 50 },
        { mimScore: 60, maxScore: 100 },
      ],
    };
    const mockWrapper = mount(QuantitativeIndicatorsSettingModel, {
      propsData: {
        params: mockInvalidParams,
      },
    });
    await mockWrapper.vm.saveData();
    expect(message.error).toHaveBeenCalledWith('分值区间，设置错误');
  });

  it('should call setting.editNameSetting and message.success when saveData is called with valid ranges', async () => {
    await wrapper.vm.saveData();
    expect(setting.editNameSetting).toHaveBeenCalled();
    expect(message.success).toHaveBeenCalledWith('定量指标保存成功');
  });

  it('should emit resolve with the correct range values when saveData is successful', async () => {
    const mockWrapper = mount(QuantitativeIndicatorsSettingModel, {
      propsData: {
        params: mockParams,
      },
    });
    await mockWrapper.vm.saveData();
    expect(mockWrapper.emitted('resolve')).toBeTruthy();
  });

  it('should call message.error when saveData fails', async () => {
    vi.mocked<any>(setting.editNameSetting).mockRejectedValue(new Error('Test error'));
    const mockWrapper = mount(QuantitativeIndicatorsSettingModel, {
      propsData: {
        params: mockParams,
      },
    });
    await mockWrapper.vm.saveData();
    expect(message.error).toHaveBeenCalledWith('定量指标保存失败');
  });
});
