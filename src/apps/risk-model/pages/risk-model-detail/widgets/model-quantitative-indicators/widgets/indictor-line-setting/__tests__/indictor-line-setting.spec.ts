import { mount, shallowMount } from '@vue/test-utils';

import IndictorLineSetting from '..';
import NumberPeriodSetting from '../../number-period-setting';

describe('IndictorLineSetting', () => {
  const trapezoidMap = {
    1: {
      bgColor: '#a80000',
      pgwidth: '170px',
      mdwidth: '105px',
      desc: '该企业处于高风险等级，建议谨慎与之建立业务关系',
    },
    2: {
      bgColor: '#ff722d',
      pgwidth: '185px',
      mdwidth: '115px',
      desc: '该企业风险等级处于较高水平，建议采取进一步的强化尽职调查',
    },
    3: {
      bgColor: '#ff5e71',
      pgwidth: '200px',
      mdwidth: '126px',
      desc: '该企业当前风险等级处于中等水平',
    },
    4: {
      bgColor: '#128BED',
      pgwidth: '215px',
      mdwidth: '138px',
      desc: '该企业当前时间点，存在个别风险特征项目',
    },
    5: {
      bgColor: '#00AD65',
      pgwidth: '230px',
      mdwidth: '150px',
      desc: '该企业当前时间点，暂无明显风险特征项目',
    },
  };

  it('renders correctly with default props', () => {
    const wrapper = shallowMount(IndictorLineSetting, {
      propsData: {
        lineData: {
          color: 1,
          name: 'Test Name',
        },
      },
    });
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.findComponent(NumberPeriodSetting).exists()).toBe(true);
  });

  it('renders correctly with isEdit true', () => {
    const wrapper = shallowMount(IndictorLineSetting, {
      propsData: {
        isEdit: true,
        lineData: {
          name: 'Test Name',
          color: 1,
          active: false,
        },
      },
      provide: {
        isMonitor: false,
        isDisabled: false,
      },
    });
    expect(wrapper.findComponent(NumberPeriodSetting).exists()).toBe(true);
    expect(wrapper.html()).toContain('Test Name');
    expect(wrapper.html()).toContain(trapezoidMap[1].mdwidth);
  });

  it('renders correctly with isEdit false and lineData name', () => {
    const wrapper = shallowMount(IndictorLineSetting, {
      propsData: {
        isEdit: false,
        lineData: {
          name: 'Test Name',
          color: 1,
          active: false,
        },
      },

      provide: {
        isMonitor: false,
        isDisabled: false,
      },
    });
    expect(wrapper.findComponent(NumberPeriodSetting).exists()).toBe(true);
    expect(wrapper.html()).toContain('Test Name');
    expect(wrapper.html()).toContain(trapezoidMap[1].pgwidth);
    expect(wrapper.html()).toContain(trapezoidMap[1].desc);
  });

  it('renders correctly with isEdit false and lineData active', () => {
    const wrapper = shallowMount(IndictorLineSetting, {
      propsData: {
        isEdit: false,
        lineData: {
          name: 'Test Name',
          color: 1,
          active: true,
        },
      },

      provide: {
        isMonitor: false,
        isDisabled: false,
      },
    });
    expect(wrapper.findComponent(NumberPeriodSetting).exists()).toBe(true);
    expect(wrapper.html()).toContain('Test Name');
    expect(wrapper.html()).toContain(trapezoidMap[1].pgwidth);
    expect(wrapper.html()).toContain(trapezoidMap[1].desc);
  });

  it('renders correctly with isEdit false and lineData color 2', () => {
    const wrapper = shallowMount(IndictorLineSetting, {
      propsData: {
        isEdit: false,
        lineData: {
          name: 'Test Name',
          color: 2,
          active: false,
        },
      },

      provide: {
        isMonitor: false,
        isDisabled: false,
      },
    });
    expect(wrapper.findComponent(NumberPeriodSetting).exists()).toBe(true);
    expect(wrapper.html()).toContain('Test Name');
    expect(wrapper.html()).toContain(trapezoidMap[2].pgwidth);
    expect(wrapper.html()).toContain(trapezoidMap[2].desc);
  });

  it('renders correctly with isEdit false and lineData color 3', () => {
    const wrapper = shallowMount(IndictorLineSetting, {
      propsData: {
        isEdit: false,
        lineData: {
          name: 'Test Name',
          color: 3,
          active: false,
        },
      },

      provide: {
        isMonitor: false,
        isDisabled: false,
      },
    });
    expect(wrapper.findComponent(NumberPeriodSetting).exists()).toBe(true);
    expect(wrapper.html()).toContain('Test Name');
    expect(wrapper.html()).toContain(trapezoidMap[3].pgwidth);
    expect(wrapper.html()).toContain(trapezoidMap[3].desc);
  });

  it('renders correctly with isEdit false and lineData color 4', () => {
    const wrapper = shallowMount(IndictorLineSetting, {
      propsData: {
        isEdit: false,
        lineData: {
          name: 'Test Name',
          color: 4,
          active: false,
        },
      },

      provide: {
        isMonitor: false,
        isDisabled: false,
      },
    });
    expect(wrapper.findComponent(NumberPeriodSetting).exists()).toBe(true);
    expect(wrapper.html()).toContain('Test Name');
    expect(wrapper.html()).toContain(trapezoidMap[4].pgwidth);
    expect(wrapper.html()).toContain(trapezoidMap[4].desc);
  });

  it('renders correctly with isEdit false and lineData color 5', () => {
    const wrapper = shallowMount(IndictorLineSetting, {
      propsData: {
        isEdit: false,
        lineData: {
          name: 'Test Name',
          color: 5,
          active: false,
        },
      },

      provide: {
        isMonitor: false,
        isDisabled: false,
      },
    });
    expect(wrapper.findComponent(NumberPeriodSetting).exists()).toBe(true);
    expect(wrapper.html()).toContain('Test Name');
    expect(wrapper.html()).toContain(trapezoidMap[5].pgwidth);
    expect(wrapper.html()).toContain(trapezoidMap[5].desc);
  });

  it('does not render NumberPeriodSetting when isMonitor is true', () => {
    const wrapper = shallowMount(IndictorLineSetting, {
      propsData: {
        isEdit: false,
        lineData: {
          name: 'Test Name',
          color: 1,
          active: false,
        },
      },

      provide: {
        isMonitor: true,
        isDisabled: false,
      },
    });
    expect(wrapper.findComponent(NumberPeriodSetting).exists()).toBe(false);
  });

  it('does not render NumberPeriodSetting when isDisabled is true', () => {
    const wrapper = shallowMount(IndictorLineSetting, {
      propsData: {
        isEdit: false,
        lineData: {
          name: 'Test Name',
          color: 1,
          active: false,
        },
      },

      provide: {
        isMonitor: false,
        isDisabled: true,
      },
    });
    expect(wrapper.findComponent(NumberPeriodSetting).exists()).toBe(true);
  });

  it('emits updateLineData when curData changes', async () => {
    const wrapper = mount(IndictorLineSetting, {
      propsData: {
        isEdit: false,
        lineData: {
          name: 'Test Name',
          color: 1,
          active: false,
        },
      },

      provide: {
        isMonitor: false,
        isDisabled: false,
      },
    });
    await wrapper.setData({ curData: { ...wrapper.vm.curData, mimScore: 10, maxScore: 20 } });
    expect(wrapper.emitted()).toHaveProperty('updateLineData');
    expect(wrapper.emitted().updateLineData).toEqual([[{ name: 'Test Name', color: 1, active: false, mimScore: 10, maxScore: 20 }]]);
  });

  it('updates curData when lineData prop changes', async () => {
    const wrapper = mount(IndictorLineSetting, {
      propsData: {
        isEdit: false,
        lineData: {
          name: 'Test Name',
          color: 1,
          active: false,
        },
      },

      provide: {
        isMonitor: false,
        isDisabled: false,
      },
    });
    await wrapper.setProps({ lineData: { name: 'New Test Name', color: 2, active: true } });
    expect(wrapper.vm.curData).toEqual({ name: 'New Test Name', color: 2, active: true });
  });

  it('renders correctly with isFirst true', () => {
    const wrapper = shallowMount(IndictorLineSetting, {
      propsData: {
        isEdit: true,
        lineData: {
          name: 'Test Name',
          color: 1,
          active: false,
        },
        isFirst: true,
      },

      provide: {
        isMonitor: false,
        isDisabled: false,
      },
    });
    expect(wrapper.findComponent(NumberPeriodSetting).props().isFirst).toBe(true);
  });

  it('renders correctly with isLast true', () => {
    const wrapper = shallowMount(IndictorLineSetting, {
      propsData: {
        isEdit: true,
        lineData: {
          name: 'Test Name',
          color: 1,
          active: false,
        },
        isLast: true,
      },

      provide: {
        isMonitor: false,
        isDisabled: false,
      },
    });
    expect(wrapper.findComponent(NumberPeriodSetting).props().isLast).toBe(true);
  });

  it('renders correctly with mimScore and maxScore in lineData', () => {
    const wrapper = shallowMount(IndictorLineSetting, {
      propsData: {
        isEdit: false,
        lineData: {
          name: 'Test Name',
          color: 1,
          active: false,
          mimScore: 10,
          maxScore: 20,
        },
      },

      provide: {
        isMonitor: false,
        isDisabled: false,
      },
    });
    expect(wrapper.findComponent(NumberPeriodSetting).props().value).toEqual([10, 20]);
  });

  it('renders correctly with mimScore and maxScore undefined in lineData', () => {
    const wrapper = shallowMount(IndictorLineSetting, {
      propsData: {
        isEdit: false,
        lineData: {
          name: 'Test Name',
          color: 1,
          active: false,
        },
      },

      provide: {
        isMonitor: false,
        isDisabled: false,
      },
    });
    expect(wrapper.findComponent(NumberPeriodSetting).props().value).toEqual([undefined, undefined]);
  });

  it('renders correctly with mimScore undefined and maxScore in lineData', () => {
    const wrapper = shallowMount(IndictorLineSetting, {
      propsData: {
        isEdit: false,
        lineData: {
          name: 'Test Name',
          color: 1,
          active: false,
          maxScore: 20,
        },
      },

      provide: {
        isMonitor: false,
        isDisabled: false,
      },
    });
    expect(wrapper.findComponent(NumberPeriodSetting).props().value).toEqual([undefined, 20]);
  });

  it('renders correctly with mimScore and maxScore undefined in lineData', () => {
    const wrapper = shallowMount(IndictorLineSetting, {
      propsData: {
        isEdit: false,
        lineData: {
          name: 'Test Name',
          color: 1,
          active: false,
          mimScore: 10,
        },
      },

      provide: {
        isMonitor: false,
        isDisabled: false,
      },
    });
    expect(wrapper.findComponent(NumberPeriodSetting).props().value).toEqual([10, undefined]);
  });
});
