import { mount } from '@vue/test-utils';
import ModelDimensionSettings from '..';
import DimensionDetail from '../widgets/dimension-detail';
import { openModelSetting } from '../widgets/model-setting';

vi.mock('../widgets/model-setting', () => ({
  openModelSetting: vi.fn(),
}));

describe('ModelDimensionSettings', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = mount(ModelDimensionSettings, {
      propsData: {
        modelData: {
          groups: [
            { id: 1, name: 'Group 1' },
            { id: 2, name: 'Group 2' },
          ],
        },
      },
    });
  });

  it('renders the component with the correct title and group count', () => {
    expect(wrapper.text()).toContain('模型指标');
  });

  it('renders the DimensionDetail component with the correct modelData', () => {
    const dimensionDetail = wrapper.findComponent(DimensionDetail);
    expect(dimensionDetail.exists()).toBe(true);
    expect(dimensionDetail.props('modelData')).toEqual({
      groups: [
        { id: 1, name: 'Group 1' },
        { id: 2, name: 'Group 2' },
      ],
    });
  });

  it('calls openModelSetting when editSettingModal is invoked', async () => {
    vi.mocked<any>(openModelSetting).mockResolvedValue({ updated: true });

    await wrapper.vm.editSettingModal();

    expect(openModelSetting).toHaveBeenCalledWith({
      groups: [
        { id: 1, name: 'Group 1' },
        { id: 2, name: 'Group 2' },
      ],
    });
  });

  it('emits updateModel event with the result from openModelSetting', async () => {
    vi.mocked<any>(openModelSetting).mockResolvedValue({ updated: true });

    await wrapper.vm.editSettingModal();

    expect(wrapper.emitted()).toHaveProperty('updateModel');
    expect(wrapper.emitted()['updateModel']).toEqual([[{ updated: true }]]);
  });
});
