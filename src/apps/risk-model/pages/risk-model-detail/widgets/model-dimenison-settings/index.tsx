import { defineComponent } from 'vue';

import ModelIcon from '@/assets/images/model-icon.svg';

import styles from './model-dimenison-settings.module.less';
import { openModelSetting } from './widgets/model-setting';
import DimensionDetail from './widgets/dimension-detail';

const ModelDimensionSettings = defineComponent({
  name: 'ModelDimensionSettings',
  props: {
    modelData: {
      type: Object,
      default: () => ({}),
    },
  },
  model: {
    prop: 'modelData',
    event: 'updateModel',
  },
  setup(props, { emit }) {
    const editSettingModal = async () => {
      const res = await openModelSetting(props.modelData);
      emit('updateModel', res);
    };

    return {
      editSettingModal,
    };
  },
  render() {
    const { modelData } = this;
    return (
      <div class={styles.container}>
        <div class={styles.bg}>
          <div class={styles.title}>
            <img src={ModelIcon} alt="模型图标" />
            <span>模型指标</span>
            {modelData.groups?.length}
          </div>
          {/* <div class={styles.extra} onClick={this.editSettingModal}>
            <q-icon class={styles.icon} type="icon-icon_fengxianmoxing1"></q-icon>
            新建分组
          </div> */}
        </div>
        {/* 设置详情 */}
        <DimensionDetail modelData={modelData} />
      </div>
    );
  },
});

export default ModelDimensionSettings;
