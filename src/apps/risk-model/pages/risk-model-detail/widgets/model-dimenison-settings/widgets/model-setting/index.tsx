import { PropType, defineComponent, onMounted, ref } from 'vue';
import { cloneDeep } from 'lodash';
import { Modal } from 'ant-design-vue';

import { createPromiseDialog } from '@/components/promise-dialogs';

// 模型指标设置弹窗
export const ModelSetting = defineComponent({
  name: 'ModelSetting',
  props: {
    params: {
      type: Array as PropType<any[]>,
    },
  },
  setup(props, { emit }) {
    const visible = ref(false);

    const modelData = ref(cloneDeep(props.params));

    // 判断
    const saveData = () => {
      emit('resolve', modelData.value);
    };

    onMounted(() => {
      visible.value = true;
    });

    return {
      visible,
      modelData,
      saveData,
    };
  },
  render() {
    return (
      <Modal
        title="模型指标设置"
        width={900}
        destroyOnClose={true}
        visible={this.visible}
        onClose={() => {
          this.$emit('close');
          this.visible = false;
        }}
        onCancel={() => {
          this.$emit('close');
          this.visible = false;
        }}
        onOk={this.saveData}
      ></Modal>
    );
  },
});

export const openModelSetting = createPromiseDialog(ModelSetting);
