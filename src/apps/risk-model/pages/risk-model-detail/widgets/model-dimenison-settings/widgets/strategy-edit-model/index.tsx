import { PropType, defineComponent, onMounted, ref } from 'vue';
import { cloneDeep } from 'lodash';
import { Modal } from 'ant-design-vue';

import { createPromiseDialog } from '@/components/promise-dialogs';

import styles from './strategy-edit-model.module.less';
import StrategySettingDetail from '../strategy-setting-detail';

// 策略修改弹窗
const StrategyEditModal = defineComponent({
  name: 'StrategyEditModal',
  props: {
    params: {
      type: Object as PropType<any>,
    },
  },
  setup(props) {
    const visible = ref(false);

    const metricEntityData = ref(cloneDeep(props.params));

    onMounted(() => {
      visible.value = true;
    });

    return {
      visible,
      metricEntityData,
    };
  },
  render() {
    const { metricEntityData } = this;
    const { passIds = [] } = metricEntityData;
    const { modelId, metricsId, groupId } = passIds;
    return (
      <Modal
        title={`${this.metricEntityData.metricEntity?.name}-设置`}
        width={900}
        class={styles.modal}
        destroyOnClose={true}
        visible={this.visible}
        onClose={() => {
          this.$emit('resolve', this.metricEntityData);
          this.$emit('close');
          this.visible = false;
        }}
        onCancel={() => {
          this.$emit('resolve', this.metricEntityData);
          this.$emit('close');
          this.visible = false;
        }}
        footer={null}
      >
        <StrategySettingDetail
          modelId={modelId}
          metricsId={metricsId}
          groupId={groupId}
          isMonitor={this.params.isMonitor}
          metricEntity={metricEntityData.metricEntity}
          isEdit={true}
          onEditStrategy={(data) => {
            this.metricEntityData.metricEntity = data;
          }}
        />
      </Modal>
    );
  },
});

export const openStrategyEditModal = createPromiseDialog(StrategyEditModal);
