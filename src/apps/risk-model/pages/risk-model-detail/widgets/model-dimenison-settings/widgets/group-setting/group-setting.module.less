.container{
  margin-bottom: 15px;

  .title{
    display: flex;
    height: 32px;
    margin-bottom: 5px;
    font-weight: bold;
    align-items: center;
    gap: 5px;
    cursor: pointer;

    &:hover{
      color: #1890ff;

      :global{
        .anticon{
          color: #1890ff !important;
        }
      }
    }
  }

  .metricContainer{
    display: flex;
    align-items: center;

  
   .metric{
    flex: 1;
    margin-left: 10px;
   }

   &:not(:first-child){
    .metric{
      border-top: none;
    }
   }
  }

}