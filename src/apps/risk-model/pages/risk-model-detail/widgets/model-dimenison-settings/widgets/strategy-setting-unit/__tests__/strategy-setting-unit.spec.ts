import { Checkbox } from 'ant-design-vue';
import { mount } from '@vue/test-utils';
import { getStrategyFieldConfigs, getObjectListConfig, getRenderStrategyFieldValues } from '@/apps/risk-model/config';
import InputWrapper from '../components/input-wrapper';
import SelectWrapper from '../components/select-wrapper';
import RadioWrapper from '../components/radio-wrapper';
import NumberPeriodSetting from '../../../../model-quantitative-indicators/widgets/number-period-setting';

import StrategySettingUnit from '..';

vi.mock('@/apps/risk-model/config', () => ({
  getStrategyFieldConfigs: vi.fn(),
  getObjectListConfig: vi.fn(),
  getRenderStrategyFieldValues: vi.fn(),
  OperatorIcon: {
    Equals: '<span>=</span>',
    Between: '<span>≤</span>',
  },
}));

describe('StrategySettingUnit', () => {
  const strategyFieldMock = {
    dimensionFieldName: 'Test Field',
    compareType: 'LessThan',
    dimensionField: {
      fieldId: 8768,
      inputType: 0,
      comment: '变更前持股比例>50%',
      fieldKey: 'beforeContent',
      defaultValue: null,
      defaultCompareType: null,
      dataType: 'Number',
      isArray: 0,
      fieldOrder: 7,
      dimensionId: 4416,
      deprecatedDate: null,
      deprecateStartDate: null,
      fieldName: '变更前持股比例',
      createDate: '2025-01-16T13:38:41.000Z',
      updateDate: '2025-01-16T13:38:41.000Z',
      modifiedDate: null,
      status: 1,
    },
    fieldValue: [50],
    dimensionFieldKey: 'beforeContent',
    options: [
      {
        max: 100,
        min: 0,
        unit: '%',
      },
    ],
    inputType: 0,
    accessScope: 1,
    newOptions: [
      {
        max: 100,
        min: 0,
        unit: '%',
      },
    ],
    compareIcon: '<',
    operateName: '',
  };

  beforeEach(() => {
    vi.mocked<any>(getStrategyFieldConfigs).mockReturnValue(strategyFieldMock);
  });

  it('renders correctly with happy path', () => {
    const wrapper = mount(StrategySettingUnit, {
      propsData: {
        strategyField: strategyFieldMock,
        modelId: 'model1',
        metricsId: 'metrics1',
        strategyId: 'strategy1',
        groupId: 'group1',
      },
    });

    expect(wrapper.find('[data-testid="strategy-item"]').text()).toContain('Test Field:');
    expect(wrapper.findComponent(InputWrapper).exists()).toBe(true);
  });

  it('renders correctly when accessScope is 2', () => {
    vi.mocked<any>(getStrategyFieldConfigs).mockReturnValue({
      ...strategyFieldMock,
      accessScope: 2,
    });

    vi.mocked<any>(getRenderStrategyFieldValues).mockReturnValue('hitValue');

    const wrapper = mount(StrategySettingUnit, {
      propsData: {
        strategyField: strategyFieldMock,
        modelId: 'model1',
        metricsId: 'metrics1',
        strategyId: 'strategy1',
        groupId: 'group1',
      },
    });

    expect(wrapper.findComponent(InputWrapper).exists()).toBe(false);
    expect(wrapper.text()).toContain('hitValue');
  });

  it('renders NumberPeriodSetting when inputType is 0 and compareType is Between', () => {
    vi.mocked<any>(getStrategyFieldConfigs).mockReturnValue({
      ...strategyFieldMock,
      compareType: 'Between',
    });

    const wrapper = mount(StrategySettingUnit, {
      propsData: {
        strategyField: strategyFieldMock,
        modelId: 'model1',
        metricsId: 'metrics1',
        strategyId: 'strategy1',
        groupId: 'group1',
      },
    });

    expect(wrapper.findComponent(NumberPeriodSetting).exists()).toBe(true);
  });

  it('renders SelectWrapper when inputType is 1', () => {
    vi.mocked<any>(getStrategyFieldConfigs).mockReturnValue({
      ...strategyFieldMock,
      inputType: 1,
    });

    const wrapper = mount(StrategySettingUnit, {
      propsData: {
        strategyField: strategyFieldMock,
        modelId: 'model1',
        metricsId: 'metrics1',
        strategyId: 'strategy1',
        groupId: 'group1',
      },
    });

    expect(wrapper.findComponent(SelectWrapper).exists()).toBe(true);
  });

  it('renders RadioWrapper when inputType is 3', () => {
    vi.mocked<any>(getStrategyFieldConfigs).mockReturnValue({
      ...strategyFieldMock,
      inputType: 3,
    });

    const wrapper = mount(StrategySettingUnit, {
      propsData: {
        strategyField: strategyFieldMock,
        modelId: 'model1',
        metricsId: 'metrics1',
        strategyId: 'strategy1',
        groupId: 'group1',
      },
    });

    expect(wrapper.findComponent(RadioWrapper).exists()).toBe(true);
  });

  it('renders Checkbox.Group when inputType is 4', () => {
    vi.mocked<any>(getStrategyFieldConfigs).mockReturnValue({
      ...strategyFieldMock,
      inputType: 4,
      newOptions: [
        {
          label: 'Option 1',
          value: 'option1',
        },
        {
          label: 'Option 2',
          value: 'option2',
        },
      ],
    });

    const wrapper = mount(StrategySettingUnit, {
      propsData: {
        strategyField: strategyFieldMock,
        modelId: 'model1',
        metricsId: 'metrics1',
        strategyId: 'strategy1',
        groupId: 'group1',
      },
    });

    expect(wrapper.findComponent(Checkbox).exists()).toBe(true);
  });

  it('renders correctly when inputType is 6', () => {
    vi.mocked<any>(getStrategyFieldConfigs).mockReturnValue({
      ...strategyFieldMock,
      inputType: 6,
      fieldValue: [{ key1: 'value1' }],
      options: [{ key: 'key1', label: 'Label 1', inputType: 0, compareType: 'Equals' }],
    });
    vi.mocked<any>(getObjectListConfig).mockReturnValue({
      optionSettingData: {
        key1: { label: 'Label 1', inputType: 0, compareType: 'Equals' },
      },
      renderKeyList: ['key1'],
    });

    const wrapper = mount(StrategySettingUnit, {
      propsData: {
        strategyField: strategyFieldMock,
        modelId: 'model1',
        metricsId: 'metrics1',
        strategyId: 'strategy1',
        groupId: 'group1',
      },
    });

    expect(wrapper.findComponent(InputWrapper).exists()).toBe(true);
  });

  it('renders correctly when fieldValue is an array', () => {
    vi.mocked<any>(getStrategyFieldConfigs).mockReturnValue({
      ...strategyFieldMock,
      inputType: 6,
      fieldValue: [{ key1: ['value1', 'value2'] }],
      options: [{ key: 'key1', label: 'Label 1', inputType: 0, compareType: 'Equals' }],
    });
    vi.mocked<any>(getObjectListConfig).mockReturnValue({
      optionSettingData: {
        key1: { label: 'Label 1', inputType: 0, compareType: 'Equals' },
      },
      renderKeyList: ['key1'],
    });

    const wrapper = mount(StrategySettingUnit, {
      propsData: {
        strategyField: strategyFieldMock,
        modelId: 'model1',
        metricsId: 'metrics1',
        strategyId: 'strategy1',
        groupId: 'group1',
      },
    });

    expect(wrapper.findComponent(InputWrapper).exists()).toBe(true);
  });

  it('renders null when inputType is not recognized', () => {
    vi.mocked<any>(getStrategyFieldConfigs).mockReturnValue({
      ...strategyFieldMock,
      inputType: 99, // unrecognized input type
    });

    const wrapper = mount(StrategySettingUnit, {
      propsData: {
        strategyField: strategyFieldMock,
        modelId: 'model1',
        metricsId: 'metrics1',
        strategyId: 'strategy1',
        groupId: 'group1',
      },
    });

    expect(wrapper.findComponent(InputWrapper).exists()).toBe(false);
  });
});
