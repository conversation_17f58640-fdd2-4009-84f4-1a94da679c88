.container{
  display: flex;
  align-items: center;
  border: 1px solid #d8d8d8;
  background: #fff;
  font-size: 13px;
  padding-right: 10px;

  &:hover{
    background: #f2f6fa;
  }

  .metricName{
    width: 345px;
    padding: 9px 10px;
    font-weight: bold;


    .pushtime{
      margin-top: 5px;
      display: flex;
      align-items: center;
      font-weight: normal;

      &>span{
        margin-right: 8px;
      }
      
      :global{
        span.ant-radio + *{
          padding-right: 16px;
        }
      }
    }
  }

  .metricData{
    flex: 1;
    display: flex;
    border-left: 1px solid #d8d8d8;
    padding-right: 5px;
  }

  .editIcon{
    display: flex;
    align-self: center;
    color: #999;
    cursor: pointer;
    font-size: 16px;

    &:hover{
      color: #1890ff;
    }
  }

  .disabled{
    color: #ddd !important;

    &:hover{
      cursor: not-allowed;
    }
  }
}
