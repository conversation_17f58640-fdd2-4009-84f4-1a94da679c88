import { mount, shallowMount } from '@vue/test-utils';
import { Checkbox } from 'ant-design-vue';
import CheckboxWrapper from '../components/checkbox-wrapper';

describe('CheckboxWrapper', () => {
  it('renders Checkbox.Group with correct value', () => {
    const wrapper = shallowMount(CheckboxWrapper, {
      propsData: {
        value: ['test'],
      },
    });

    expect(wrapper.findComponent(Checkbox.Group).props('value')).toBe('test');
  });

  it('renders with empty value', () => {
    const wrapper = shallowMount(CheckboxWrapper, {
      propsData: {
        value: [],
      },
    });

    expect(wrapper.findComponent(Checkbox.Group).props('value')).toBeUndefined();
  });
});
