import { mount } from '@vue/test-utils';
import StrategyPresentation from '@/apps/risk-model/pages/risk-model-detail/widgets/model-dimenison-settings/widgets/strategy-presentation/index';
import { getRenderStrategyFieldValues, getStrategyFieldConfigs } from '@/apps/risk-model/config';

vi.mock('@/apps/risk-model/config', () => ({
  getRenderStrategyFieldValues: vi.fn(),
  getStrategyFieldConfigs: vi.fn(),
}));

describe('StrategyPresentation', () => {
  it('renders correctly with valid data', () => {
    vi.mocked<any>(getStrategyFieldConfigs).mockReturnValue({
      dimensionFieldName: 'Test Field',
      compareType: 'eq',
      fieldValue: 'testValue',
      dimensionFieldKey: 'testKey',
      options: [],
      inputType: 'text',
      operateName: 'testOperate',
    });

    vi.mocked(getRenderStrategyFieldValues).mockReturnValue('Rendered Value');

    const wrapper = mount(StrategyPresentation, {
      propsData: {
        strategyField: {
          fieldKey: 'testKey',
          fieldValue: 'testValue',
        },
      },
    });

    expect(wrapper.text()).toContain('Test Field:Rendered Value');
  });

  it('renders correctly with empty strategyField', () => {
    vi.mocked<any>(getStrategyFieldConfigs).mockReturnValue({
      dimensionFieldName: '',
      compareType: '',
      fieldValue: '',
      dimensionFieldKey: '',
      options: [],
      inputType: '',
      operateName: '',
    });

    vi.mocked(getRenderStrategyFieldValues).mockReturnValue('');

    const wrapper = mount(StrategyPresentation, {
      propsData: {
        strategyField: {},
      },
    });

    expect(wrapper.text()).toContain(':');
  });

  it('renders correctly with null strategyField', () => {
    vi.mocked<any>(getStrategyFieldConfigs).mockReturnValue({
      dimensionFieldName: '',
      compareType: '',
      fieldValue: '',
      dimensionFieldKey: '',
      options: [],
      inputType: '',
      operateName: '',
    });

    vi.mocked(getRenderStrategyFieldValues).mockReturnValue('');

    const wrapper = mount(StrategyPresentation, {
      propsData: {
        strategyField: null,
      },
    });

    expect(wrapper.text()).toContain(':');
  });

  it('renders correctly with undefined strategyField', () => {
    vi.mocked<any>(getStrategyFieldConfigs).mockReturnValue({
      dimensionFieldName: '',
      compareType: '',
      fieldValue: '',
      dimensionFieldKey: '',
      options: [],
      inputType: '',
      operateName: '',
    });

    vi.mocked(getRenderStrategyFieldValues).mockReturnValue('');

    const wrapper = mount(StrategyPresentation, {
      propsData: {
        strategyField: undefined,
      },
    });

    expect(wrapper.text()).toContain(':');
  });

  it('renders correctly with empty options and inputType', () => {
    vi.mocked<any>(getStrategyFieldConfigs).mockReturnValue({
      dimensionFieldName: 'Test Field',
      compareType: 'eq',
      fieldValue: 'testValue',
      dimensionFieldKey: 'testKey',
      options: [],
      inputType: '',
      operateName: 'testOperate',
    });

    vi.mocked(getRenderStrategyFieldValues).mockReturnValue('Rendered Value');

    const wrapper = mount(StrategyPresentation, {
      propsData: {
        strategyField: {
          fieldKey: 'testKey',
          fieldValue: 'testValue',
        },
      },
    });

    expect(wrapper.text()).toContain('Test Field:Rendered Value');
  });

  it('renders correctly with non-empty options', () => {
    vi.mocked<any>(getStrategyFieldConfigs).mockReturnValue({
      dimensionFieldName: 'Test Field',
      compareType: 'eq',
      fieldValue: 'testValue',
      dimensionFieldKey: 'testKey',
      options: ['option1', 'option2'],
      inputType: 'select',
      operateName: 'testOperate',
    });

    vi.mocked(getRenderStrategyFieldValues).mockReturnValue('Rendered Value');

    const wrapper = mount(StrategyPresentation, {
      propsData: {
        strategyField: {
          fieldKey: 'testKey',
          fieldValue: 'testValue',
        },
      },
    });

    expect(wrapper.text()).toContain('Test Field:Rendered Value');
  });
});
