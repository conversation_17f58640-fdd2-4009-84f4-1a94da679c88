/* eslint-disable vue/no-mutating-props */
/* eslint-disable no-constant-condition */
import { defineComponent, ref } from 'vue';
import Draggable from 'vuedraggable';

import QIcon from '@/components/global/q-icon';

import MetricDetail from '../metric-detai';
import styles from './group-setting.module.less';

// 分组设置
const GroupSetting = defineComponent({
  name: 'GroupSetting',
  props: {
    groupData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const collapsed = ref(false);
    const toggleCollapsed = () => {
      collapsed.value = !collapsed.value;
    };

    const handleSort = () => {
      console.log(props.groupData);
    };

    return {
      collapsed,
      toggleCollapsed,
      handleSort,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.title} data-testid="group-setting-title" onClick={this.toggleCollapsed}>
          <q-icon
            data-testid="group-setting-icon"
            type={!this.collapsed ? 'icon-a-shixinxia1x' : 'icon-a-shixinyou1x'}
            style={{ color: this.collapsed ? '#128dff' : '#333' }}
          />
          <span class={styles.name}>{this.groupData.groupName}</span>
          {this.groupData.groupMetrics?.length}
        </div>
        <Draggable
          v-show={!this.collapsed}
          v-model={this.groupData.groupMetrics}
          handle=".drag-handle"
          animation="300"
          onChange={this.handleSort}
        >
          {this.groupData.groupMetrics?.map((metric, index) => (
            <div class={styles.metricContainer}>
              <QIcon class={[styles.icon, !false ? 'drag-handle' : 'drag-handle-disabled']} type="icon-icon_tuodong" />
              <MetricDetail
                class={styles.metric}
                metricData={metric}
                modelId={this.groupData.modelId}
                groupId={this.groupData.groupId}
                onChange={(data) => {
                  this.groupData.groupMetrics[index] = data;
                  this.$emit('update', this.groupData);
                }}
              />
            </div>
          ))}
        </Draggable>
      </div>
    );
  },
});
export default GroupSetting;
