import { defineComponent, inject, nextTick } from 'vue';
import { Modal } from 'ant-design-vue';

import { useEnableEdit } from '@/apps/risk-model/hooks/use-edit-enable';
import { useModelDataHook } from '@/apps/risk-model/hooks/use-data-hook';
import { setting } from '@/shared/services';
import QTag from '@/components/global/q-tag';

import styles from './metric-detail.module.less';
import StrategySettingDetail from '../strategy-setting-detail';
import { openStrategyEditModal } from '../strategy-edit-model';

// 指标详情
const MetricDetail = defineComponent({
  name: 'MetricDetail',
  props: {
    metricData: {
      type: Object,
      default: () => ({}),
    },
    modelId: {
      type: [String, Number],
    },
    groupId: {
      type: [String, Number],
    },
  },
  setup(props, { emit }) {
    const { checkModelStatus, copyModel } = useEnableEdit();
    const { updateModelData } = useModelDataHook();

    // 先复制再更新
    const updateData = async (statusData, strategy, cb?, type?) => {
      if (!statusData[1].canBeEdit) {
        const copyModelData = await copyModel(statusData, [props.modelId, props.groupId, props.metricData.metricsId], {
          strategyIds: props.metricData.metricEntity.dimensionHitStrategies.map((item) => item.dimensionHitStrategyEntity.strategyId),
          metricsIds: [props.metricData.metricsId],
          needReplace: 1,
        });
        if (!statusData[0].canBeEdit) {
          const hrefArr = window.location.href.split('/detail/');
          hrefArr[1] = copyModelData.newId;
          const urlhref = hrefArr.join('/detail/');
          window.open(urlhref);
          return;
        }
        updateModelData(props.groupId, props.metricData.metricsId, copyModelData, strategy, type);
      }
      // 回调保存的函数，保证数据已经更新，保证先copy再保存
      nextTick(() => cb?.());
    };
    const checkCopyModel = async ({ cb, strategy, type }: { cb?: any; strategy?: any; type?: any }) => {
      const statusData = await checkModelStatus([props.modelId, props.groupId, props.metricData.metricsId]);
      if (!statusData[0].canBeEdit) {
        Modal.confirm({
          title: '提示',
          content: '当前模型不可编辑，是否为您打开新的副本模型？',
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            updateData(statusData, strategy, cb, type);
          },
        });
      } else {
        updateData(statusData, strategy, cb, type);
      }
    };

    const isMonitor = inject('isMonitor', false);
    const disabled = inject('isDisabled', false);
    const handleEdit = async () => {
      // 在点击打开弹窗的时候就进行check metic，如果没有可编辑的字段，则进行复制metic
      await checkCopyModel({
        cb: async () => {
          const res = await openStrategyEditModal({
            ...props.metricData,
            passIds: { modelId: props.modelId, metricsId: props.metricData.metricsId, groupId: props.groupId },
            isMonitor,
          });
          emit('change', res);
          updateModelData(props.groupId, props.metricData.metricsId, res, '', '');
        },
      });
    };

    const saveDetailsJSON = async (status) => {
      checkCopyModel({
        cb: async () => {
          await setting.updateMetricData(props.metricData.metricEntity);
        },
        strategy: {
          detailsJson: {
            ...props.metricData.metricEntity.detailsJson,
            dynamicStrategy: {
              ...props.metricData.metricEntity.detailsJson.dynamicStrategy,
              allowRepeatedHits: status,
            },
          },
        },
        type: 'detailsJson',
      });
    };

    return {
      isMonitor,
      disabled,
      handleEdit,
      checkCopyModel,
      saveDetailsJSON,
    };
  },
  render() {
    const { metricEntity } = this.metricData;
    if (!metricEntity) return null;
    const { dimensionHitStrategies, detailsJson } = metricEntity;
    if (!dimensionHitStrategies?.length) {
      return null;
    }
    const { dimensionHitStrategyEntity } = dimensionHitStrategies[0];
    const { strategyFields } = dimensionHitStrategyEntity;
    const validFields = strategyFields.filter((item) => item.accessScope !== 1);
    const editDisabled = dimensionHitStrategies.length === 1 && validFields.length === 0;
    return (
      <div class={styles.container}>
        <div class={styles.metricName}>
          <div class="flex items-center">
            {metricEntity.name}
            {this.isMonitor && (
              <QTag
                ghost={true}
                size="small"
                style={{
                  marginLeft: '5px',
                  padding: '1px 4px',
                  background: metricEntity.metricType === 4 ? '#F0EBFF' : '#E0F4F8',
                  color: metricEntity.metricType === 4 ? '#845FFF' : '#00A3CC',
                }}
              >
                {metricEntity.metricType === 4 ? '业务类' : '监管类'}
              </QTag>
            )}
          </div>
          {/* <div class={styles.pushtime}>
              <span>推送设置:</span>
              <Radio.Group
                value={detailsJson.dynamicStrategy.allowRepeatedHits}
                onChange={(e) => {
                  detailsJson.dynamicStrategy.allowRepeatedHits = e.target.value;
                  this.saveDetailsJSON(e.target.value);
                }}
                options={[
                  { label: '单次', value: false },
                  { label: '连续', value: true },
                ]}
              />
            </div> */}
        </div>
        <div class={styles.metricData}>
          <StrategySettingDetail
            modelId={this.modelId}
            metricsId={this.metricData.metricsId}
            groupId={this.groupId}
            isMonitor={this.isMonitor}
            metricEntity={metricEntity}
            onCheckStatus={this.checkCopyModel}
          />
        </div>
        {!this.disabled && (
          <div
            data-testid="edit-icon"
            class={[styles.editIcon, editDisabled && styles.disabled]}
            onClick={() => {
              if (!editDisabled) {
                this.handleEdit();
              }
            }}
          >
            <q-icon type="icon-shezhizhongxin" />
          </div>
        )}
      </div>
    );
  },
});
export default MetricDetail;
