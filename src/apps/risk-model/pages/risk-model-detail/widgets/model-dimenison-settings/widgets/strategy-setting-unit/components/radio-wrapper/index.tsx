import { PropType, defineComponent } from 'vue';
import { Radio } from 'ant-design-vue';

import { createFunctionalEventEmitter } from '@/utils/component';

const RadioWrapper = defineComponent({
  functional: true,
  props: {
    value: {
      type: Array as PropType<[string, number][]>,
      default: () => [],
    },
  },
  render(h, ctx) {
    const { props, listeners } = ctx;
    const emitters = createFunctionalEventEmitter(listeners);

    return (
      <Radio.Group
        value={props.value[0]}
        {...{
          props: {
            ...ctx.data.attrs,
          },
        }}
        onChange={(e) => {
          emitters('change')([e.target.value]);
        }}
      />
    );
  },
});

export default RadioWrapper;
