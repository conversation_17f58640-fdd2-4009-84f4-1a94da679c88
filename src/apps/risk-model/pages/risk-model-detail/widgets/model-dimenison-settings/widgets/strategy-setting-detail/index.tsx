/* eslint-disable camelcase */
import { defineComponent, inject, nextTick, ref, watch } from 'vue';
import { InputNumber, message, Spin, Icon } from 'ant-design-vue';
import { flattenDeep, sortBy, throttle } from 'lodash';

import QSwitch from '@/components/global/q-switch';
import { setting } from '@/shared/services';
import { useEnableEdit } from '@/apps/risk-model/hooks/use-edit-enable';
import { useModelDataHook } from '@/apps/risk-model/hooks/use-data-hook';

import styles from './strategy-setting-detail.module.less';
import RiskLevelDropDown from '../risk-level-drop-down';
import StrategyPresentation from '../strategy-presentation';
import StrategySettingUnit from '../strategy-setting-unit';

// 策略详情
const StrategySettingDetail = defineComponent({
  name: 'StrategySettingDetail',
  props: {
    metricEntity: {
      type: Object,
      required: true,
    },
    // 判断是field显示详情还是修改弹窗
    isEdit: {
      type: Boolean,
      default: false,
    },
    // 判断是不是监控维度
    isMonitor: {
      type: Boolean,
      default: false,
    },
    modelId: {
      type: [String, Number],
    },
    groupId: {
      type: [String, Number],
    },
    metricsId: {
      type: [String, Number],
    },
  },
  setup(props, { emit }) {
    const { copyModel, checkModelStatus } = useEnableEdit();

    const { updateModelData } = useModelDataHook();

    const metricData = ref(props.metricEntity);
    // 保存中的状态
    const isSaving = ref(false);

    const disable = inject('isDisabled', false);

    const isTech = inject('isTech', false);
    // 保存hitStrategy
    const saveHitStrategy = async (params?: any) => {
      const fn = params ? setting.updateStrategyField : setting.updateMetricData;
      try {
        isSaving.value = true;
        // console.log(params || metricData.value);
        await fn(params || metricData.value);
        // message.success('保存成功');
      } catch (error) {
        message.error('保存失败');
      } finally {
        isSaving.value = false;
      }
    };

    watch(
      () => props.metricEntity,
      (newVal) => {
        metricData.value = newVal;
      }
    );
    const saveHitStrategyThrottle = throttle(saveHitStrategy, 3000);

    // 修改hitStrategy
    const editHitStrategy = async (cb, strategy = null) => {
      if (!props.isEdit) {
        emit('checkStatus', { cb, strategy });
      } else {
        await cb?.();
      }
    };
    const onEditHitStrategy = throttle(editHitStrategy, 2000);
    // const onEditHitStrategy = editHitStrategy;

    // 策略设置
    const editStrategy = async (strategyFiledId, callback, extra) => {
      const { strategyIds, strategyFields } = extra;
      const matchFiled = strategyFields.find((item) => item.id === strategyFiledId);
      const ids = [props.modelId, props.groupId, props.metricsId, strategyIds[0], strategyFiledId];
      const modelStatus = (await checkModelStatus(ids)) as any;
      // 判断是不是都是可编辑的状态
      const isAllEdit = modelStatus.every((item) => item.canBeEdit);
      if (!isAllEdit) {
        const stragetyFieldFalse = modelStatus[2].canBeEdit;
        let basicParams = {
          riskModelId: props.modelId,
          metricsIds: [props.metricsId],
          strategyIds,
          needReplace: 1,
          bindMetricToGroupId: props.groupId,
          bindStrategyToMetricId: props.metricsId,
        };
        if (stragetyFieldFalse) {
          basicParams = Object.assign(basicParams, {
            strategyFieldIds: [strategyFiledId],
            bindStrategyFieldToStrategyId: extra.strategyIds[0],
          });
        }
        const modules = await copyModel(modelStatus, ids, basicParams);
        // openDrawer打开的弹窗，需要手动更新数据
        metricData.value?.dimensionHitStrategyEntity?.strategyFields?.forEach((field) => {
          field.strategyId = modules.newMetric[0].newId;
          const previousFieldId = field.id;
          const matchField = modules.newMetric[0].fieldResults.find((res) => res.oldId === previousFieldId);
          field.id = matchField.newId;
        });
        metricData.value = Object.assign(metricData.value, modules.newMetric[0]);
        callback?.(modules.strategyResults[0]);
        // 更新原始数据
        updateModelData(props.groupId, props.metricsId, [modules], matchFiled, 'dimensionStrategy');
        // 回调保存的函数，保证数据已经更新，保证先copy再保存
        const { newId } = modules.strategyResults[0];
        strategyFields.forEach((field) => {
          field.strategyId = newId;
        });
        await saveHitStrategyThrottle(matchFiled);
      } else {
        updateModelData(props.groupId, props.metricsId, '', '', '');
      }
      nextTick(() => {
        saveHitStrategyThrottle(matchFiled);
      });
    };
    return {
      metricData,
      onEditHitStrategy,
      saveHitStrategy,
      saveHitStrategyThrottle,
      editStrategy,
      isSaving,
      disable,
      isTech,
    };
  },
  render() {
    const { metricData, isEdit, modelId, metricsId, groupId } = this;
    const { dimensionHitStrategies, hitStrategy } = metricData;

    const logicLabelMap = {
      0: '满足以下 <em>所有</em> 规则',
      1: '满足以下 <em>任一</em> 规则',
      2: '排除以下所有规则',
    };
    const renderHitStrategy = (selectedConditions: any[]) => {
      const content = selectedConditions.map((condition: any) => {
        const { dimensionHitStrategyEntity } = condition;
        // 维度名称展示
        const dimensionDefName = dimensionHitStrategyEntity.strategyName;
        // 维度key，特殊维度key不展示
        const dimensionDefKey = dimensionHitStrategyEntity.dimensionDef?.key;
        // 更新dimensionStrategyId（因为更新了hitStrategy）和strategyFields,保持id一致
        const updateStrategy = (strategyModelData) => {
          const { fieldResults, newId } = strategyModelData;
          condition.dimensionStrategyId = newId;
          condition?.dimensionHitStrategyEntity?.strategyFields?.forEach((strategyField) => {
            strategyField.strategyId = newId;
            const matchObj = fieldResults.find((item) => item.oldId === strategyField.id);
            if (matchObj) {
              strategyField.id = matchObj.newId;
              strategyField.oldId = matchObj.oldId;
            }
          });
        };
        // 需要展示的策略字段
        const validFields = sortBy(
          condition.dimensionHitStrategyEntity.strategyFields.filter((item) => item.accessScope !== 1),
          'dimensionField.fieldOrder'
        );

        if (isEdit) {
          return (
            <div data-testid="settingMetricHitStrategy" class={styles.settingMetricHitStrategy}>
              <div class={styles.strategyTitleEdit}>
                {dimensionDefName} {validFields?.length ? ':' : ''}
              </div>
              {validFields.map((strategyField, index) => {
                return (
                  <StrategySettingUnit
                    style={{ margin: '10px 0 0 15px' }}
                    key={`${dimensionHitStrategyEntity.strategyId}-${strategyField.id}`}
                    modelId={modelId}
                    groupId={groupId}
                    metricsId={metricsId}
                    strategyId={dimensionHitStrategyEntity.strategyId}
                    strategyField={strategyField}
                    onChange={(data) => {
                      // openDrawer打开的弹窗，需要更新数据
                      this.editStrategy(data.id, updateStrategy, {
                        strategyIds: [data.strategyId],
                        strategyFields: condition.dimensionHitStrategyEntity.strategyFields,
                      });
                    }}
                  >
                    <span slot="index">{index + 1} 、</span>
                  </StrategySettingUnit>
                );
              })}
            </div>
          );
        }
        return (
          <div class={styles.metricHitStrategy} data-testid="metricHitStrategy">
            <div class={styles.strategyTitle}>
              {dimensionDefName} {validFields?.length ? ':' : ''}
            </div>
            {validFields?.map((strategyField) => {
              // 展示页面
              return <StrategyPresentation strategyField={strategyField} />;
            })}
          </div>
        );
      });
      return content;
    };

    const editModeRenderer = (strategy, strategyIndex) => {
      // const { scoreSettings, allConditionsGroup } = strategy;
      const { must, should, must_not, scoreSettings } = strategy;
      // 命中条件
      const mustGroup = Array.isArray(must)
        ? dimensionHitStrategies.filter(({ dimensionStrategyId }) => must.includes(dimensionStrategyId))
        : [];
      const shouldGroup = Array.isArray(should)
        ? dimensionHitStrategies.filter(({ dimensionStrategyId }) => should.includes(dimensionStrategyId))
        : [];
      const mustNotGroup = Array.isArray(must_not)
        ? dimensionHitStrategies.filter(({ dimensionStrategyId }) => must_not.includes(dimensionStrategyId))
        : [];
      // 展示时显示全部条件
      const allConditions = [mustGroup, shouldGroup, mustNotGroup];

      return allConditions.map((conditions: any[], index: number) => {
        if (conditions?.length === 0) {
          return null;
        }

        return (
          <div class={styles.logicGroup} key={index}>
            <header class={styles.header} domPropsInnerHTML={`指标策略${strategyIndex + 1}: ${logicLabelMap[index]}`}>
              {/* 满足以下 <em>${index}</em> 规则 */}
            </header>
            <div class={styles.body}>
              <div class={styles.metricItem}>
                <div class={styles.strategyWrapper}>
                  <div>{renderHitStrategy(conditions)}</div>
                </div>
                <div class={styles.settingMetricAction}>
                  <QSwitch
                    checked={!!strategy.status}
                    onChange={(checked) => {
                      this.onEditHitStrategy(
                        async () => {
                          strategy.status = Number(checked);
                          await this.saveHitStrategyThrottle();
                        },
                        { ...strategy, strategyIndex, status: Number(checked) }
                      );
                    }}
                  />
                  <RiskLevelDropDown
                    value={scoreSettings.riskLevel}
                    onChange={(value) => {
                      this.onEditHitStrategy(
                        async () => {
                          scoreSettings.riskLevel = value;
                          await this.saveHitStrategyThrottle();
                        },
                        {
                          ...strategy,
                          strategyIndex,
                          scoreSettings: { riskLevel: value, maxScore: scoreSettings.maxScore },
                        }
                      );
                    }}
                  />
                  {!this.isMonitor ? (
                    <InputNumber
                      value={scoreSettings.maxScore}
                      formatter={(value) => `${value}  分`}
                      onChange={(e) => {
                        this.onEditHitStrategy(
                          async () => {
                            scoreSettings.maxScore = e;
                            await this.saveHitStrategyThrottle();
                          },
                          {
                            ...strategy,
                            strategyIndex,
                            scoreSettings: { riskLevel: scoreSettings.riskLevel, maxScore: e },
                          }
                        );
                      }}
                    />
                  ) : null}
                </div>
              </div>
            </div>
          </div>
        );
      });
    };

    const viewModeRenderer = (strategy, strategyIndex) => {
      const { must, should, must_not, scoreSettings } = strategy;
      // 命中条件
      const mustGroup = Array.isArray(must)
        ? dimensionHitStrategies.filter(({ dimensionStrategyId }) => must.includes(dimensionStrategyId))
        : [];
      const shouldGroup = Array.isArray(should)
        ? dimensionHitStrategies.filter(({ dimensionStrategyId }) => should.includes(dimensionStrategyId))
        : [];
      const mustNotGroup = Array.isArray(must_not)
        ? dimensionHitStrategies.filter(({ dimensionStrategyId }) => must_not.includes(dimensionStrategyId))
        : [];

      return (
        <div class={styles.metricItem}>
          {/* 显示或者编辑策略 */}
          <div class={styles.strategyWrapper}>
            {[mustGroup, shouldGroup, mustNotGroup].map((conditions: any[], index: number) => {
              // 需要展示的策略字段
              const totalFields = flattenDeep(conditions.map((stategy) => stategy.dimensionHitStrategyEntity.strategyFields));
              const validFields = totalFields.filter((item) => item.accessScope !== 1);
              if (conditions?.length === 0) {
                return null;
              }
              return (
                <div class={styles.strategyGroup2}>
                  <header
                    class={styles.header}
                    domPropsInnerHTML={`指标策略${hitStrategy?.length > 1 ? strategyIndex + 1 : ''}: ${logicLabelMap[index]}`}
                  />
                  {renderHitStrategy(conditions)}
                </div>
              );
            })}
          </div>
          {/* 按钮区域 */}
          <div class={styles.metricAction}>
            <QSwitch
              checked={!!strategy.status}
              disabled={this.disable}
              onChange={(checked) => {
                this.onEditHitStrategy(
                  async () => {
                    strategy.status = Number(checked);
                    await this.saveHitStrategyThrottle();
                  },
                  { ...strategy, strategyIndex, status: Number(checked) }
                );
              }}
            />
            {!this.isTech && (
              <RiskLevelDropDown
                value={scoreSettings.riskLevel}
                disabled={this.disable}
                onChange={(value) => {
                  this.onEditHitStrategy(
                    async () => {
                      scoreSettings.riskLevel = value;
                      await this.saveHitStrategyThrottle();
                    },
                    {
                      ...strategy,
                      strategyIndex,
                      scoreSettings: { riskLevel: value, maxScore: scoreSettings.maxScore },
                    }
                  );
                }}
              />
            )}

            {!this.isMonitor && (
              <InputNumber
                value={scoreSettings.maxScore}
                formatter={(value) => `${value}  分`}
                disabled={this.disable}
                onChange={(e) => {
                  this.onEditHitStrategy(
                    async () => {
                      scoreSettings.maxScore = e;
                      await this.saveHitStrategyThrottle();
                    },
                    {
                      ...strategy,
                      strategyIndex,
                      scoreSettings: { riskLevel: scoreSettings.riskLevel, maxScore: e },
                    }
                  );
                }}
              />
            )}
          </div>
        </div>
      );
    };

    return (
      <div class={styles.metricContent}>
        <Spin spinning={this.isSaving}>
          <Icon style="color: #bbb; font-size: 16px;" type="sync" spin slot="indicator" />
          {sortBy(hitStrategy, 'order')?.map((strategy: any, hindex) => {
            return (
              <div class={styles.strategyGroup} key={hindex} data-testid="strategyGroup">
                {isEdit ? editModeRenderer(strategy, hindex) : viewModeRenderer(strategy, hindex)}
              </div>
            );
          })}
        </Spin>
      </div>
    );
  },
});

export default StrategySettingDetail;
