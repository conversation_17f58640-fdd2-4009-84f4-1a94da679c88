import { defineComponent } from 'vue';

import { getRenderStrategyFieldValues, getStrategyFieldConfigs } from '@/apps/risk-model/config';

import styles from './strategy-presentation.module.less';

// 策略内容展示组件
const StrategyPresentation = defineComponent({
  name: 'StrategyPresentation',
  props: {
    strategyField: {
      type: Object,
    },
  },
  render() {
    // 解构，获取对应的 字段名，字段值，字段类型，字段options
    const { dimensionFieldName, compareType, fieldValue, dimensionFieldKey, options, inputType, operateName } = getStrategyFieldConfigs(
      this.strategyField
    );
    // if (this.strategyField?.id === 726009) {
    //   debugger;
    // }
    const hitValue = getRenderStrategyFieldValues({
      operateName,
      fieldValue,
      dimensionFieldKey,
      compareType,
      options,
      inputType,
    });

    return (
      <div class={styles.hitStrategyItem}>
        {dimensionFieldName}:<span class={styles.hitStrategyValue}>{hitValue}</span>
      </div>
    );
  },
});

export default StrategyPresentation;
