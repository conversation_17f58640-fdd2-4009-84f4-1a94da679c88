import { mount } from '@vue/test-utils';
import Draggable from 'vuedraggable';
import GroupSetting from '..';
import MetricDetail from '../../metric-detai';

describe('GroupSetting', () => {
  it('renders groupName and groupMetrics length', () => {
    const groupData = {
      groupName: 'Test Group',
      groupMetrics: [{ id: 1 }, { id: 2 }],
      modelId: 'model1',
      groupId: 'group1',
    };
    const wrapper = mount(GroupSetting, {
      propsData: { groupData },
    });
    expect(wrapper.text()).toBe('Test Group2');
    expect(wrapper.findAllComponents(MetricDetail)).toHaveLength(2);
  });

  it('toggles collapsed state on title click', async () => {
    const groupData = {
      groupName: 'Test Group',
      groupMetrics: [{ id: 1 }, { id: 2 }],
      modelId: 'model1',
      groupId: 'group1',
    };
    const wrapper = mount(GroupSetting, {
      propsData: { groupData },
    });
    expect(wrapper.vm.collapsed).toBe(false);
    await wrapper.find('[data-testid="group-setting-title"]').trigger('click');
    expect(wrapper.vm.collapsed).toBe(true);
  });

  it('renders Draggable component when not collapsed', () => {
    const groupData = {
      groupName: 'Test Group',
      groupMetrics: [{ id: 1 }, { id: 2 }],
      modelId: 'model1',
      groupId: 'group1',
    };
    const wrapper = mount(GroupSetting, {
      propsData: { groupData },
    });
    expect(wrapper.findComponent(Draggable).exists()).toBe(true);
  });

  it('does not render Draggable component when collapsed', async () => {
    const groupData = {
      groupName: 'Test Group',
      groupMetrics: [{ id: 1 }, { id: 2 }],
      modelId: 'model1',
      groupId: 'group1',
    };
    const wrapper = mount(GroupSetting, {
      propsData: { groupData },
    });
    await wrapper.find('[data-testid="group-setting-title"]').trigger('click');
    expect(wrapper.findComponent(Draggable).exists()).toBe(true);
    expect(wrapper.vm.collapsed).toBe(true);
  });

  it('renders MetricDetail component for each groupMetric', () => {
    const groupData = {
      groupName: 'Test Group',
      groupMetrics: [{ id: 1 }, { id: 2 }],
      modelId: 'model1',
      groupId: 'group1',
    };
    const wrapper = mount(GroupSetting, {
      propsData: { groupData },
    });
    expect(wrapper.findAllComponents(MetricDetail).length).toBe(2);
  });

  it('emits update event with updated groupData when MetricDetail changes', async () => {
    const groupData = {
      groupName: 'Test Group',
      groupMetrics: [{ id: 1 }, { id: 2 }],
      modelId: 'model1',
      groupId: 'group1',
    };
    const wrapper = mount(GroupSetting, {
      propsData: { groupData },
    });

    const metricDetailWrapper = wrapper.findComponent(MetricDetail);
    await metricDetailWrapper.vm.$emit('change', { id: 1, updated: true });

    expect(wrapper.emitted().update[0][0]).toEqual({
      groupName: 'Test Group',
      groupMetrics: [{ id: 1, updated: true }, { id: 2 }],
      modelId: 'model1',
      groupId: 'group1',
    });
  });

  it('handles empty groupMetrics array', () => {
    const groupData = {
      groupName: 'Test Group',
      groupMetrics: [],
      modelId: 'model1',
      groupId: 'group1',
    };
    const wrapper = mount(GroupSetting, {
      propsData: { groupData },
    });

    expect(wrapper.text()).toContain('0');
    expect(wrapper.findComponent(Draggable).exists()).toBe(true);
    expect(wrapper.findComponent(MetricDetail).exists()).toBe(false);
  });

  it('renders correct QIcon type when collapsed', async () => {
    const groupData = {
      groupName: 'Test Group',
      groupMetrics: [{ id: 1 }],
      modelId: 'model1',
      groupId: 'group1',
    };
    const wrapper = mount(GroupSetting, {
      propsData: { groupData },
    });

    await wrapper.find('[data-testid="group-setting-title"]').trigger('click');
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.collapsed).toBe(true);
  });
});
