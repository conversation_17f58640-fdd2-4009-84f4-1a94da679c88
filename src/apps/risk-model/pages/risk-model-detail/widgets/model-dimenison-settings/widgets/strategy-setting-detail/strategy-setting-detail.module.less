@indent-line-color: #d8d8d8;
@indent-line-width: 1px;
@indent-line-size: 17px;

.metricContent {
  flex: 1;

  .strategyGroup {
    .metricItem {
      display: flex;
      flex: 1;

      &:hover{
        background: #e2f1fd;
      }

      .strategyWrapper {
        flex: 1;
        border-right: 1px solid #d8d8d8;
        min-height: 38px;
      }

      .settingMetricHitStrategy,
      .metricHitStrategy {
        position: relative;
        flex: 1;
        min-height: 38px;
      }

      .metricHitStrategy {
        padding: 9px 15px;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }

      // .settingMetricAction::after,
      // .settingMetricHitStrategy::after,
      .metricAction::after,
      .metricHitStrategy::after {
        height: 1px;
        position: absolute;
        content: '';
        bottom: 0;
        left: 10px;
        width: calc(100% - 32px);
        background: #d8d8d8;
      }

      .metricAction::after {
        width: calc(100% - 27px);
      }

      .settingMetricAction::after,
      .settingMetricHitStrategy::after {
        left: 0;
        width: 100%;
      }

      .settingMetricAction,
      .metricAction {
        position: relative;
        padding: 0 15px;
        width: 212px;
        display: flex;
        align-items: center;
        gap: 10px;
        justify-content: flex-end;
      }

      .strategyTitleEdit {
        position: relative;
        min-height: 32px;
        display: flex;
        align-items: center;
        font-weight: bold;

        &::after{
          position: absolute;
          content: '';
          width: 20px;
          height: @indent-line-size;
          top: 0;
          left: -25px;
          border-bottom: @indent-line-width solid @indent-line-color;
        }
      }

      .strategyTitle {
        display: flex;
        align-items: center;
      }
    }

    &:last-child {
    // .settingMetricAction::after,
    // .settingMetricHitStrategy::after,
      .metricAction::after,
      .metricHitStrategy::after {
        display: none;
      }
    }
  }
}

.strategyGroup2{
  .header {
    font-weight: 700;
    padding: 6px 10px 0;
    border-radius: 4px;
    min-height: 32px;
    
    em {
      background: #E2F1FD;
      color: #128bed;
      padding: 2px 5px;
    }
  }

  
}

// 命中策略逻辑分组
.logicGroup {
  margin-top: 15px;

  .header {
    font-weight: 700;
    padding: 6px 8px;
    background: #f6fbfe;
    border: 1px solid #c4dff5;
    border-radius: 4px;
    min-height: 32px;

    em {
      background: #E2F1FD;
      color: #128bed;
      padding: 2px 5px;
    }
  }

  .body {
    padding: 10px 0 0 10px;
    position: relative;

    .metricItem:hover{
      background: #fff;
    }

    &::after {
      position: absolute;
      content: '';
      height: 100%;
      top: 0;
      left: 14px;
      bottom: 0;
      border-left: @indent-line-width solid @indent-line-color;
    }

    > div {
      padding-left: @indent-line-size + 12px;
      position: relative;
      content: '';
    }
  }
}
