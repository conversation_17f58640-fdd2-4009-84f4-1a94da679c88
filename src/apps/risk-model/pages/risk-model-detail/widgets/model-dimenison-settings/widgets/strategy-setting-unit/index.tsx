import { PropType, defineComponent } from 'vue';
import { Checkbox } from 'ant-design-vue';
import { isArray } from 'lodash';

import { getObjectListConfig, getRenderStrategyFieldValues, getStrategyFieldConfigs, OperatorIcon } from '@/apps/risk-model/config';

import styles from './strategy-setting-unit.module.less';
import InputWrapper from './components/input-wrapper';
import SelectWrapper from './components/select-wrapper';
import RadioWrapper from './components/radio-wrapper';
import NumberPeriodSetting from '../../../model-quantitative-indicators/widgets/number-period-setting';

const InputWidgetMap = {
  0: InputWrapper,
  1: SelectWrapper,
  2: SelectWrapper,
  3: RadioWrapper,
  4: Checkbox.Group,
  5: NumberPeriodSetting,
};
// 策略设置单元
const StrategySettingUnit = defineComponent({
  name: 'StrategySettingUnit',
  props: {
    strategyField: {
      type: Object as PropType<any>,
    },
    modelId: {
      type: [String, Number],
    },
    metricsId: {
      type: [String, Number],
    },
    strategyId: {
      type: [String, Number],
    },
    groupId: {
      type: [String, Number],
    },
  },
  render() {
    const { strategyField } = this;
    const {
      dimensionFieldName,
      compareType,
      accessScope,
      fieldValue,
      dimensionFieldKey,
      options,
      inputType,
      newOptions,
      compareIcon,
      operateName,
    } = getStrategyFieldConfigs(strategyField);

    const chooseInputWidget = (inputTypeCurrent, compareTypeCurrent) => {
      if (inputTypeCurrent === 0 && compareTypeCurrent === 'Between') {
        return InputWidgetMap[5];
      }
      return InputWidgetMap[inputTypeCurrent];
    };
    const renderSettingComponent = () => {
      if (accessScope === 2) {
        const hitValue = getRenderStrategyFieldValues({
          operateName,
          fieldValue,
          dimensionFieldKey,
          compareType,
          options,
          inputType,
        });
        return hitValue;
      }
      // input的时候如果有2个值，说明是一个区间，需要特殊处理
      const InputWidget = chooseInputWidget(inputType, compareType);
      if (InputWidget) {
        return (
          <InputWidget
            options={newOptions}
            value={fieldValue}
            inputType={inputType}
            isEdit={true}
            prefix={compareIcon}
            extra={newOptions[0]}
            onChange={(value) => {
              strategyField.fieldValue = value;
              this.$emit('change', strategyField);
            }}
          />
        );
      }
      // 当类型为6的时候 fieldValue是对象list,做特殊处理
      if (inputType === 6) {
        const { optionSettingData, renderKeyList } = getObjectListConfig(options);
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            {fieldValue.map((valueObj) => {
              return (
                <div class="flex flex-row" style={{ gap: '10px', flexWrap: 'wrap' }}>
                  {renderKeyList.map((key) => {
                    const {
                      label,
                      max,
                      min,
                      unit,
                      inputType: opInputType,
                      compareType: opCompareType,
                      options: opOptions,
                    } = optionSettingData[key];
                    const cpIcon = OperatorIcon[opCompareType];
                    const comp = chooseInputWidget(opInputType, opCompareType);
                    // 判断value是不是一个array，组件都是默认array去处理的，所以需要保持一致（因为主维度返回的都是array）
                    const valueIsArray = isArray(valueObj[key]);
                    const currentValue = valueIsArray ? valueObj[key] : [valueObj[key]];
                    return (
                      <div class={['flex flex-row items-center', styles.compWithName]}>
                        <span>{label}:</span>
                        <comp
                          value={currentValue}
                          inputType={inputType}
                          isEdit={true}
                          prefix={cpIcon}
                          options={opOptions}
                          extra={{
                            min,
                            max,
                            unit,
                          }}
                          onChange={(value) => {
                            valueObj[key] = valueIsArray ? value : value[0];
                            this.$emit('change', strategyField);
                          }}
                        />
                      </div>
                    );
                  })}
                </div>
              );
            })}
          </div>
        );
      }
      return null;
    };
    return (
      <div class={styles.container}>
        <div class={styles.strategyItem} data-testid="strategy-item">
          {this.$slots.index}
          {dimensionFieldName}:{' '}
        </div>
        <div class={styles.strategySetting} data-testid="strategy-setting-unit">
          {renderSettingComponent()}
        </div>
      </div>
    );
  },
});

export default StrategySettingUnit;
