import { mount } from '@vue/test-utils';
import RadioWrapper from '@/apps/risk-model/pages/risk-model-detail/widgets/model-dimenison-settings/widgets/strategy-setting-unit/components/radio-wrapper/index';
import { Radio } from 'ant-design-vue';

describe('RadioWrapper', () => {
  it('renders a Radio.Group with the correct value', () => {
    const wrapper = mount(RadioWrapper, {
      propsData: {
        value: [1],
      },
    });

    expect(wrapper.findComponent(Radio.Group).props('value')).toBe(1);
  });

  it('emits a change event with the correct value when a radio option is selected', async () => {
    const wrapper = mount(RadioWrapper, {
      propsData: {
        value: [1],
      },
    });

    const radioGroup = wrapper.findComponent(Radio.Group);
    await radioGroup.vm.$emit('change', { target: { value: 'option2' } });

    expect(wrapper.emitted().change).toBeTruthy();
    expect(wrapper.emitted().change).toEqual([[{ target: { value: 'option2' } }]]);
  });

  it('handles empty value prop array', () => {
    const wrapper = mount(RadioWrapper, {
      propsData: {
        value: [],
      },
    });

    expect(wrapper.findComponent(Radio.Group).props('value')).toBeUndefined();
  });
});
