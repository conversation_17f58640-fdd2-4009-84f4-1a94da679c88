import { mount } from '@vue/test-utils';
import InputWrapper from '../components/input-wrapper';
import { InputNumber } from 'ant-design-vue';

describe('InputWrapper', () => {
  it('renders with default props', () => {
    const wrapper = mount(InputWrapper, {
      propsData: {
        value: [50],
      },
    });
    expect(wrapper.findComponent(InputNumber).props('value')).toBe(50);
    expect(wrapper.findComponent(InputNumber).props('prefix')).toBeUndefined();
    expect(wrapper.findComponent(InputNumber).props('unit')).toBeUndefined();
    expect(wrapper.findComponent(InputNumber).props('min')).toBe(0);
    expect(wrapper.findComponent(InputNumber).props('max')).toBe(100);
  });

  it('renders with custom prefix', () => {
    const wrapper = mount(InputWrapper, {
      propsData: {
        value: [['test', 50]],
        prefix: 'custom',
      },
    });
    expect(wrapper.findComponent(InputNumber).props('prefix')).toBeUndefined();
  });

  it('renders with custom extra unit', () => {
    const wrapper = mount(InputWrapper, {
      propsData: {
        value: [['test', 50]],
        extra: { unit: 'kg' },
      },
    });
    expect(wrapper.findComponent(InputNumber).props('unit')).toBeUndefined();
  });

  it('renders with custom extra min and max', () => {
    const wrapper = mount(InputWrapper, {
      propsData: {
        value: [['test', 50]],
        extra: { min: 10, max: 90 },
      },
    });
    expect(wrapper.findComponent(InputNumber).props('min')).toBe(10);
    expect(wrapper.findComponent(InputNumber).props('max')).toBe(90);
  });

  it('emits change event with new value', async () => {
    const wrapper = mount(InputWrapper, {
      propsData: {
        value: [['test', 50]],
      },
    });
    await wrapper.findComponent(InputNumber).vm.$emit('change', 75);
    expect(wrapper.emitted().change).toBeTruthy();
    expect(wrapper.emitted().change).toEqual([[75]]);
  });
});
