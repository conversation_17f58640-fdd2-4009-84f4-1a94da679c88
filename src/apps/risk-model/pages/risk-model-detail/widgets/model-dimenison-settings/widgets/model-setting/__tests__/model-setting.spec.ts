import { mount } from '@vue/test-utils';
import { cloneDeep } from 'lodash';
import { ModelSetting } from '..';
import { Modal } from 'ant-design-vue';

describe('ModelSetting Component', () => {
  it('renders Modal with correct title and width', () => {
    const wrapper = mount(ModelSetting, {
      propsData: {
        params: [],
      },
    });

    const modal = wrapper.findComponent(Modal);
    expect(modal.exists()).toBe(true);
    expect(modal.props('title')).toBe('模型指标设置');
    expect(modal.props('width')).toBe(900);
  });

  it('sets visible to true onMounted', async () => {
    const wrapper = mount(ModelSetting, {
      propsData: {
        params: [],
      },
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.vm.visible).toBe(true);
  });

  it('clones params into modelData', () => {
    const params = [{ key: 'value' }];
    const wrapper = mount(ModelSetting, {
      propsData: {
        params,
      },
    });

    expect(wrapper.vm.modelData).toEqual(cloneDeep(params));
  });

  it('emits resolve event with modelData when saveData is called', () => {
    const params = [{ key: 'value' }];
    const wrapper = mount(ModelSetting, {
      propsData: {
        params,
      },
    });

    wrapper.vm.saveData();
    expect(wrapper.emitted('resolve')).toBeTruthy();
    expect(wrapper.emitted('resolve')![0]).toEqual([cloneDeep(params)]);
  });

  it('emits close event and sets visible to false when onClose is called', async () => {
    const wrapper = mount(ModelSetting, {
      propsData: {
        params: [],
      },
    });

    await wrapper.vm.$nextTick();
    const modal = wrapper.findComponent(Modal);
    modal.vm.$emit('close');
    await wrapper.vm.$nextTick();

    expect(wrapper.emitted('close')).toBeTruthy();
    expect(wrapper.vm.visible).toBe(false);
  });

  it('emits close event and sets visible to false when onCancel is called', async () => {
    const wrapper = mount(ModelSetting, {
      propsData: {
        params: [],
      },
    });

    await wrapper.vm.$nextTick();
    const modal = wrapper.findComponent(Modal);
    modal.vm.$emit('close');
    await wrapper.vm.$nextTick();

    expect(wrapper.emitted('close')).toBeTruthy();
    expect(wrapper.vm.visible).toBe(false);
  });

  it('handles empty params array', () => {
    const wrapper = mount(ModelSetting, {
      propsData: {
        params: [],
      },
    });

    expect(wrapper.vm.modelData).toEqual([]);
  });

  it('handles null params', () => {
    const wrapper = mount(ModelSetting, {
      propsData: {
        params: null,
      },
    });

    expect(wrapper.vm.modelData).toEqual(null);
  });

  it('handles undefined params', () => {
    const wrapper = mount(ModelSetting, {
      propsData: {
        params: undefined,
      },
    });

    expect(wrapper.vm.modelData).toEqual(undefined);
  });
});
