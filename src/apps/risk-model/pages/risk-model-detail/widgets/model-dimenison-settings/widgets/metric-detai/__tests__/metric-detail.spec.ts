import { shallowMount } from '@vue/test-utils';
import MetricDetail from '../../metric-detai';
import { flushPromises } from '@/test-utils/flush-promises';
import QTag from '@/components/global/q-tag';
import StrategySettingDetail from '../../strategy-setting-detail';
import { useEnableEdit } from '@/apps/risk-model/hooks/use-edit-enable';

vi.mock('@/shared/services');
vi.mock('@/apps/risk-model/hooks/use-edit-enable', () => ({
  useEnableEdit: vi.fn().mockReturnValue({
    checkModelStatus: vi.fn(),
    copyModel: vi.fn(),
  }),
  useModelDataHook: vi.fn().mockReturnValue({
    updateModelData: vi.fn(),
  }),
  setting: {
    updateMetricData: vi.fn(),
  },
  openStrategyEditModal: vi.fn(),
}));

describe('MetricDetail', () => {
  const mockMetricData = {
    metricsId: 1,
    metricEntity: {
      name: 'Example Metric',
      metricType: 4,
      dimensionHitStrategies: [
        {
          dimensionHitStrategyEntity: {
            strategyFields: [{ accessScope: 0 }],
          },
        },
      ],
      detailsJson: {
        dynamicStrategy: {
          allowRepeatedHits: false,
        },
      },
    },
  };

  const mockModelId = 1;
  const mockGroupId = 1;

  const createWrapper = (props: any = {}) => {
    return shallowMount(MetricDetail, {
      propsData: {
        metricData: mockMetricData,
        modelId: mockModelId,
        groupId: mockGroupId,
        ...props,
      },
      provide: {
        isMonitor: false,
        isDisabled: false,
      },
    });
  };

  it('renders correctly with metricData', async () => {
    const wrapper = createWrapper();
    await flushPromises();
    expect(wrapper.find('.flex.items-center').text()).toContain(mockMetricData.metricEntity.name);
    expect(wrapper.findComponent(QTag).exists()).toBe(false);
    expect(wrapper.findComponent(StrategySettingDetail).exists()).toBe(true);
    expect(wrapper.find('[data-testid="edit-icon"]').exists()).toBe(true);
  });

  it('does not render when dimensionHitStrategies is empty', async () => {
    const wrapper = createWrapper({
      metricData: { ...mockMetricData, metricEntity: { ...mockMetricData.metricEntity, dimensionHitStrategies: [] } },
    });
    await flushPromises();
    expect(wrapper.html()).toBe('');
  });

  it('handleEdit calls checkCopyModel and copies model when metric cannot be edited', async () => {
    const mockStrategyFields = [{ accessScope: 1 }];
    const mockDimensionHitStrategies = [{ dimensionHitStrategyEntity: { strategyFields: mockStrategyFields } }];
    const mockMetricEntity = { ...mockMetricData.metricEntity, dimensionHitStrategies: mockDimensionHitStrategies };
    const mockMetricDataWithEntity = { ...mockMetricData, metricEntity: mockMetricEntity };

    vi.mocked<any>(useEnableEdit).mockReturnValue({
      checkModelStatus: vi.fn().mockResolvedValue([{ canBeEdit: false }, { canBeEdit: false }]),
      copyModel: vi.fn().mockResolvedValue({ newId: 2 }),
    });

    const wrapper = createWrapper({ metricData: mockMetricDataWithEntity });
    await flushPromises();

    const handleEditSpy = vi.spyOn(wrapper.vm as any, 'handleEdit');
    await wrapper.find('[data-testid="edit-icon"]').trigger('click');

    expect(handleEditSpy).not.toHaveBeenCalled();
  });

  it('saveDetailsJSON calls checkCopyModel and updates metric data', async () => {
    vi.mocked<any>(useEnableEdit).mockReturnValue({
      checkModelStatus: vi.fn().mockResolvedValue([{ canBeEdit: true }, { canBeEdit: true }]),
      copyModel: vi.fn(),
    });

    const wrapper = createWrapper();
    await flushPromises();

    const saveDetailsJSONSpy = vi.spyOn(wrapper.vm as any, 'saveDetailsJSON');
    await wrapper.vm.saveDetailsJSON(true);

    expect(saveDetailsJSONSpy).toHaveBeenCalledWith(true);
    expect(useEnableEdit().checkModelStatus).toHaveBeenCalledWith([mockModelId, mockGroupId, mockMetricData.metricsId]);
  });

  it('saveDetailsJSON calls checkCopyModel and copies model when metric cannot be edited', async () => {
    vi.mocked<any>(useEnableEdit).mockReturnValue({
      checkModelStatus: vi.fn().mockResolvedValue([{ canBeEdit: false }, { canBeEdit: false }]),
      copyModel: vi.fn().mockResolvedValue({ newId: 2 }),
    });

    const wrapper = createWrapper();
    await flushPromises();

    const saveDetailsJSONSpy = vi.spyOn(wrapper.vm as any, 'saveDetailsJSON');
    await wrapper.vm.saveDetailsJSON(true);

    expect(saveDetailsJSONSpy).toHaveBeenCalledWith(true);
    expect(useEnableEdit().checkModelStatus).toHaveBeenCalledWith([mockModelId, mockGroupId, mockMetricData.metricsId]);
  });

  // it('renders edit icon disabled when there are no valid fields', async () => {
  //    vi.mocked<any>(useEnableEdit).mockReturnValue({
  //     checkModelStatus: vi.fn().mockResolvedValue([{ canBeEdit: true }, { canBeEdit: true }]),
  //     copyModel: vi.fn(),
  //   });
  //   const mockStrategyFields = [{ accessScope: 1 }];
  //   const mockDimensionHitStrategies = [
  //     { dimensionHitStrategyEntity: { strategyFields: mockStrategyFields } },
  //   ];
  //   const mockMetricEntity = { ...mockMetricData.metricEntity, dimensionHitStrategies: mockDimensionHitStrategies };
  //   const mockMetricDataWithEntity = { ...mockMetricData, metricEntity: mockMetricEntity };

  //   const wrapper = createWrapper({ metricData: mockMetricDataWithEntity });
  //   await flushPromises();

  //   const iconWrapper = wrapper.find('[data-testid="edit-icon"]');
  //   expect((iconWrapper.exists())).toBe(true);
  // });

  // it('renders edit icon enabled when there are valid fields', async () => {
  //   vi.mocked<any>(useEnableEdit).mockReturnValue({
  //     checkModelStatus: vi.fn().mockResolvedValue([{ canBeEdit: false }, { canBeEdit: false }]),
  //     copyModel: vi.fn().mockResolvedValue({ newId: 2 }),
  //   });
  //   const mockStrategyFields = [{ accessScope: 0 }];
  //   const mockDimensionHitStrategies = [
  //     { dimensionHitStrategyEntity: { strategyFields: mockStrategyFields } },
  //   ];
  //   const mockMetricEntity = { ...mockMetricData.metricEntity, dimensionHitStrategies: mockDimensionHitStrategies };
  //   const mockMetricDataWithEntity = { ...mockMetricData, metricEntity: mockMetricEntity };

  //   const wrapper = createWrapper({ metricData: mockMetricDataWithEntity });
  //   await flushPromises();

  //   expect(wrapper.find('[data-testid="edit-icon"]').exists()).toBe(true);
  // });

  // it('renders QTag based on metricType', async () => {
  //   const mockMetricEntity = { ...mockMetricData.metricEntity, metricType: 5 };
  //   const mockMetricDataWithEntity = { ...mockMetricData, metricEntity: mockMetricEntity };

  //   const wrapper = createWrapper({ metricData: mockMetricDataWithEntity });
  //   await flushPromises();

  //   expect(wrapper.findComponent(QTag).exists()).toBe(false);
  // });
});
