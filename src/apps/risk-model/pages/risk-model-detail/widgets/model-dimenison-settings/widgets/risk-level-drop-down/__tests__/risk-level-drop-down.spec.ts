import { Dropdown } from 'ant-design-vue';
import { mount } from '@vue/test-utils';
import RiskLevelDropDown from '..';

describe('RiskLevelDropDown', () => {
  it('renders with default props', () => {
    const wrapper = mount(RiskLevelDropDown);
    expect(wrapper.find(Dropdown).exists()).toBe(true);
  });
  it('renders with disabled prop set to true', () => {
    const wrapper = mount(RiskLevelDropDown, {
      propsData: { disabled: true },
    });
    expect(wrapper.find('[data-testid="icon-down-arrow"]').exists()).toBe(false);
  });

  it('toggles visibility on click', async () => {
    const wrapper = mount(RiskLevelDropDown);
    await wrapper.find('[data-test="risk-level-drop-down"]').trigger('click');
    expect(wrapper.vm.visible).toBe(true);
    await wrapper.find('[data-test="risk-level-drop-down"]').trigger('click');
    expect(wrapper.vm.visible).toBe(false);
  });

  it('does not emit change event when disabled', async () => {
    const wrapper = mount(RiskLevelDropDown, {
      propsData: { disabled: true },
    });
    await wrapper.find('[data-test="risk-level-drop-down"]').trigger('click');
    expect(wrapper.emitted()).not.toHaveProperty('change');
  });
});
