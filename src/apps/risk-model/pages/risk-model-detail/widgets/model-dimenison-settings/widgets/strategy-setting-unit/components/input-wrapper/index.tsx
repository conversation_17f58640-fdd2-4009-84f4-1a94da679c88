import { InputNumber } from 'ant-design-vue';
import { PropType, defineComponent } from 'vue';

import { createFunctionalEventEmitter } from '@/utils/component';

const InputWrapper = defineComponent({
  functional: true,
  props: {
    value: {
      type: Array as PropType<[string, number][]>,
      default: () => [],
    },
    prefix: {
      type: String,
      default: '',
    },
    extra: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}) as Record<string, any>,
    },
  },
  render(h, ctx) {
    const { props, listeners } = ctx;
    const emitters = createFunctionalEventEmitter(listeners);
    const min = props.extra?.min || 0;
    const max = props.extra?.max || 100;
    const unit = props.extra?.unit || '';
    return (
      <InputNumber
        value={props.value[0]}
        style={{ width: '180px' }}
        min={min}
        max={max}
        step={1}
        precision={0}
        formatter={(value) => `${props.prefix}  ${Math.round(value)}  ${unit}`}
        {...{
          props: {
            ...ctx.data.attrs,
          },
        }}
        onChange={(e) => {
          emitters('change')([e]);
        }}
      />
    );
  },
});

export default InputWrapper;
