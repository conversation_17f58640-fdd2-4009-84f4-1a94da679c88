import { defineComponent } from 'vue';

import GroupSetting from '../group-setting';
import styles from './dimension-detail.module.less';

// 维度详情
const DimensionDetail = defineComponent({
  name: 'DimensionDetail',
  props: {
    modelData: {
      type: Object,
    },
  },
  render() {
    return <div class={styles.container}>{this.modelData?.groups?.map((group) => <GroupSetting groupData={group} />)}</div>;
  },
});
export default DimensionDetail;
