import { PropType, defineComponent } from 'vue';

import { createFunctionalEventEmitter } from '@/utils/component';
import FilterSelect from '@/components/filter-select';

const SelectWrapper = defineComponent({
  functional: true,
  props: {
    value: {
      type: Array as PropType<[number | string][]>,
    },
    inputType: {
      type: Number,
    },
  },
  render(h, ctx) {
    const { props, listeners } = ctx;
    const emitters = createFunctionalEventEmitter(listeners);
    return (
      <FilterSelect
        style={{ width: 'calc(100% - 10px)', minWidth: 'unset' }}
        defaultValue={props.value}
        {...{
          props: {
            ...ctx.data.attrs,
          },
        }}
        allowClear
        maxTagTextLength={10}
        mode={props.inputType === 2 ? 'multiple' : ''}
        maxTagCount={1}
        onChange={(e) => {
          emitters('change')(e);
        }}
      />
    );
  },
});

export default SelectWrapper;
