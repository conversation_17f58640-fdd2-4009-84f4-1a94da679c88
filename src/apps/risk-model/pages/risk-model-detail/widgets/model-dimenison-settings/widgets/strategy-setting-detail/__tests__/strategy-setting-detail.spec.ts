import { mount } from '@vue/test-utils';
import { Input, message } from 'ant-design-vue';
import StrategySettingDetail from '@/apps/risk-model/pages/risk-model-detail/widgets/model-dimenison-settings/widgets/strategy-setting-detail/index';
import { useEnableEdit } from '@/apps/risk-model/hooks/use-edit-enable';
import { useModelDataHook } from '@/apps/risk-model/hooks/use-data-hook';
import { flushPromises } from '@/test-utils/flush-promises';
import StrategySettingUnit from '../../strategy-setting-unit';
import QSwitch from '@/components/global/q-switch';
import RiskLevelDropDown from '../../risk-level-drop-down';
import StrategyPresentation from '../../strategy-presentation';
import { setting } from '@/shared/services';

vi.mock('@/shared/services', () => ({
  setting: {
    updateStrategyField: vi.fn(),
    updateMetricData: vi.fn(),
  },
}));

vi.mock('@/apps/risk-model/hooks/use-edit-enable', () => ({
  useEnableEdit: vi.fn().mockReturnValue({
    copyModel: vi.fn(),
    checkModelStatus: vi.fn(),
  }),
}));

vi.mock('@/apps/risk-model/hooks/use-data-hook', () => ({
  useModelDataHook: vi.fn().mockReturnValue({
    updateModelData: vi.fn(),
  }),
}));

describe('StrategySettingDetail', () => {
  let wrapper;

  const mockMetricEntity = {
    dimensionHitStrategies: [
      {
        dimensionStrategyId: '1',
        dimensionHitStrategyEntity: {
          strategyName: 'Strategy 1',
          strategyFields: [{ id: '1', accessScope: 0, dimensionField: { fieldOrder: 1 } }],
        },
      },
      {
        dimensionStrategyId: '2',
        dimensionHitStrategyEntity: {
          strategyName: 'Strategy 2',
          strategyFields: [{ id: '2', accessScope: 0, dimensionField: { fieldOrder: 2 } }],
        },
      },
    ],
    hitStrategy: [
      { order: 1, must: ['1'], scoreSettings: { riskLevel: 'low', maxScore: 10 }, status: true },
      { order: 2, should: ['2'], scoreSettings: { riskLevel: 'high', maxScore: 20 }, status: false },
    ],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked<any>(useEnableEdit).mockReturnValue({
      copyModel: vi.fn().mockResolvedValue({ newMetric: [{ newId: 'newMetricId' }], strategyResults: [{ newId: 'newStrategyId' }] }),
      checkModelStatus: vi.fn().mockResolvedValue([{ canBeEdit: true }, { canBeEdit: true }, { canBeEdit: true }]),
    });
    vi.mocked<any>(useModelDataHook).mockReturnValue({
      updateModelData: vi.fn(),
    });
  });

  describe('Happy Path', () => {
    it('should render edit mode correctly', async () => {
      wrapper = mount(StrategySettingDetail, {
        propsData: {
          metricEntity: mockMetricEntity,
          isEdit: true,
        },
      });

      await flushPromises();

      expect(wrapper.findAll('[data-testid="settingMetricHitStrategy"]').length).toBe(2);
      expect(wrapper.findAllComponents(StrategySettingUnit).length).toBe(2);
      expect(wrapper.findAllComponents(QSwitch).length).toBe(2);
      expect(wrapper.findAllComponents(RiskLevelDropDown).length).toBe(2);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty metricEntity', async () => {
      wrapper = mount(StrategySettingDetail, {
        propsData: {
          metricEntity: {},
          isEdit: true,
        },
      });

      await flushPromises();

      expect(wrapper.findAll('[data-testid="settingMetricHitStrategy"]').length).toBe(0);
      expect(wrapper.findAllComponents(StrategySettingUnit).length).toBe(0);
    });

    it('should handle empty dimensionHitStrategies', async () => {
      wrapper = mount(StrategySettingDetail, {
        propsData: {
          metricEntity: { dimensionHitStrategies: [] },
          isEdit: true,
        },
      });

      await flushPromises();

      expect(wrapper.findAll('[data-testid="settingMetricHitStrategy"]').length).toBe(0);
      expect(wrapper.findAllComponents(StrategySettingUnit).length).toBe(0);
    });

    it('should handle empty hitStrategy', async () => {
      wrapper = mount(StrategySettingDetail, {
        propsData: {
          metricEntity: { hitStrategy: [] },
          isEdit: true,
        },
      });

      await flushPromises();

      expect(wrapper.findAll('[data-testid="strategyGroup"]').length).toBe(0);
    });

    it('should handle strategy with no conditions', async () => {
      wrapper = mount(StrategySettingDetail, {
        propsData: {
          metricEntity: {
            dimensionHitStrategies: [],
            hitStrategy: [{ order: 1, must: [], scoreSettings: { riskLevel: 'low', maxScore: 10 }, status: true }],
          },
          isEdit: true,
        },
      });

      await flushPromises();

      expect(wrapper.findAll('[data-testid="strategyGroup"]').length).toBe(1);
      expect(wrapper.findAll('[data-testid="settingMetricHitStrategy"]').length).toBe(0);
    });

    it('should handle non-editable strategy', async () => {
      vi.mocked<any>(useEnableEdit).mockReturnValue({
        copyModel: vi.fn(),
        checkModelStatus: vi.fn().mockResolvedValue([{ canBeEdit: false }, { canBeEdit: false }, { canBeEdit: false }]),
      });

      wrapper = mount(StrategySettingDetail, {
        propsData: {
          metricEntity: mockMetricEntity,
          isEdit: true,
        },
      });

      await flushPromises();

      expect(wrapper.findAll('[data-testid="settingMetricHitStrategy"]').length).toBe(2);
      expect(wrapper.findAllComponents(StrategySettingUnit).length).toBe(2);
    });

    it('should handle saving hit strategy', async () => {
      wrapper = mount(StrategySettingDetail, {
        propsData: {
          metricEntity: mockMetricEntity,
          isEdit: true,
        },
      });

      await flushPromises();

      wrapper.vm.saveHitStrategyThrottle();
      expect(wrapper.vm.isSaving).toBe(true);

      await flushPromises();

      expect(wrapper.vm.isSaving).toBe(false);
    });

    it('should handle saving hit strategy failure', async () => {
      vi.mocked<any>(useEnableEdit).mockReturnValue({
        copyModel: vi.fn().mockRejectedValue(new Error('Save failed')),
        checkModelStatus: vi.fn().mockResolvedValue([{ canBeEdit: false }, { canBeEdit: false }, { canBeEdit: false }]),
      });
      vi.mocked(setting.updateStrategyField).mockRejectedValue(new Error('Save failed'));
      vi.mocked(setting.updateMetricData).mockRejectedValue(new Error('Save failed'));
      wrapper = mount(StrategySettingDetail, {
        propsData: {
          metricEntity: mockMetricEntity,
          isEdit: true,
        },
      });

      await flushPromises();

      wrapper.vm.saveHitStrategyThrottle();
      const messageSpy = vi.spyOn(message, 'error');

      await flushPromises();

      expect(messageSpy).toHaveBeenCalled();
    });

    it('should handle monitor mode', async () => {
      wrapper = mount(StrategySettingDetail, {
        propsData: {
          metricEntity: mockMetricEntity,
          isEdit: true,
          isMonitor: true,
        },
      });

      await flushPromises();

      expect(wrapper.findAll('.ant-input-number').length).toBe(0);
    });
  });
});
