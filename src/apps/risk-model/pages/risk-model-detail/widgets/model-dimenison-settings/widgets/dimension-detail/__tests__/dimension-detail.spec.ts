import { mount, shallowMount } from '@vue/test-utils';
import DimensionDetail from '../index';
import GroupSetting from '../../group-setting';

describe('DimensionDetail', () => {
  it('renders correctly with full modelData', () => {
    const modelData = {
      groups: [
        { id: 1, name: 'Group 1' },
        { id: 2, name: 'Group 2' },
      ],
    };
    const wrapper = mount(DimensionDetail, {
      propsData: { modelData },
    });

    expect(wrapper.findAllComponents(GroupSetting)).toHaveLength(2);
  });

  it('renders correctly with empty groups', () => {
    const modelData = {
      groups: [],
    };
    const wrapper = shallowMount(DimensionDetail, {
      propsData: { modelData },
    });

    expect(wrapper.findAllComponents(GroupSetting)).toHaveLength(0);
  });

  it('renders correctly with no groups', () => {
    const modelData = {};
    const wrapper = shallowMount(DimensionDetail, {
      propsData: { modelData },
    });

    expect(wrapper.findAllComponents(GroupSetting)).toHaveLength(0);
  });

  it('renders correctly with null modelData', () => {
    const wrapper = shallowMount(DimensionDetail, {
      propsData: { modelData: null },
    });

    expect(wrapper.findAllComponents(GroupSetting)).toHaveLength(0);
  });

  it('renders correctly with undefined modelData', () => {
    const wrapper = shallowMount(DimensionDetail, {
      propsData: { modelData: undefined },
    });

    expect(wrapper.findAllComponents(GroupSetting)).toHaveLength(0);
  });

  it('renders correctly with single group', () => {
    const modelData = {
      groups: [{ id: 1, name: 'Group 1' }],
    };
    const wrapper = mount(DimensionDetail, {
      propsData: { modelData },
    });

    expect(wrapper.findAllComponents(GroupSetting)).toHaveLength(1);
  });
});
