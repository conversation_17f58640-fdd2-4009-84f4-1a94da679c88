.modelCollapseItem{
  margin-bottom: 15px;

  .modelName{
    height: 32px;
    line-height: 32px;
    font-size: 16px;
    font-weight: bold;
  }

  :global{
    .drag-handle{
      transform: translate(-24px, 1px);
      color: #bbb;
      font-size: 16px;
    }

    .ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-arrow{
      transform: translate(20px, -50%);
    }

    .ant-collapse{
       border: none;

      .ant-collapse-item2 {
        .ant-collapse-header{
          padding: 0;
          padding-left: 0;

          .ant-collapse-arrow{
            transform: translate(20px, -6px);
            z-index: 100;
            color: #bbb;

            &:hover{
              color: #1890ff;
            }
          }
        }
      }
    }

    .ant-collapse-content > .ant-collapse-content-box{
      padding: 16px 15px 0 16px;
    }

    .ant-collapse-borderless{
      background-color: #F2F8FE;

      .ant-collapse-item{
        border-bottom: none;

      }
    }

    .ant-spin-spinning{
      height: 300px !important;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .ant-spin-dot{
        transform: unset !important;
      }
    }
  }

  .pagegation{
    background: transparent;
    display: flex;
    margin-bottom: 15px;
    flex-direction: row-reverse;
  }

  .loadingHeight{
    height: 400px;

    :global{
      .ant-collapse-content-box{
        height: 400px !important;
      }
    }
  }

  .modelList {
    @media (min-width: 1680px) {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 0 16px;
    }
  }
}
