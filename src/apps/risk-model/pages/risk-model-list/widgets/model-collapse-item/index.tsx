import { defineComponent, reactive, ref } from 'vue';
import { Collapse, Spin } from 'ant-design-vue';
import moment from 'moment';

import { setting } from '@/shared/services';
import QIcon from '@/components/global/q-icon';

import ModelItem from '../model-item';
import styles from './model-collapse-item.module.less';

const ModelCollapseItem = defineComponent({
  props: {
    modelData: {
      type: Object,
      default: () => ({}),
    },
    isExternal: {
      type: Boolean,
    },
    modelType: {
      type: String,
    },
  },
  setup(props) {
    // 模型分组
    const modelCopyList = ref<any[]>([]);

    const showList = ref(false);
    const pagination = reactive({
      current: 1,
      pageSize: 100,
      total: 0,
      pageSizeOptions: ['5', '10'],
    });
    const getModelCopyList = async () => {
      showList.value = false;
      modelCopyList.value = [];
      try {
        const params = {
          riskModelId: props.modelData.modelId,
          product: 'SAAS_PRO',
          pageIndex: pagination.current,
          pageSize: pagination.pageSize,
          branchCode: props.modelData.branchCode,
          // 排查当前版本的模型
          // excludedModelsIds: [props.modelData.modelId],
        };
        const res = await setting.getModelCopyLists(params);
        modelCopyList.value = res.data || [];
        pagination.total = res.total || 0;
      } catch (error) {
        console.log(error);
      } finally {
        showList.value = true;
      }
    };

    const handlePageChange = (current: number, pageSize: number) => {
      Object.assign(pagination, { current, pageSize });
      getModelCopyList();
    };
    const handlePageSizeChange = (current: number, pageSize: number) => {
      Object.assign(pagination, { current: 1, pageSize });
      getModelCopyList();
    };
    return {
      modelCopyList,
      showList,
      pagination,
      getModelCopyList,
      handlePageChange,
      handlePageSizeChange,
    };
  },

  render() {
    const { modelData, modelCopyList, showList, getModelCopyList } = this;
    return (
      <div key={modelData.modelId} class={[styles.modelCollapseItem, showList ? styles.show : styles.loadingHeight]}>
        <Collapse
          showArrow={true}
          bordered={false}
          defaultActiveKey={modelData.modelId}
          onChange={async () => {
            if (this.showList === false) {
              this.pagination.current = 1;
              await getModelCopyList();
            } else {
              this.showList = false;
            }
          }}
        >
          <Collapse.Panel key={modelData.modelId}>
            <div slot="header" class={styles.modelName}>
              <QIcon class="drag-handle shrink-0" type="icon-icon_tuodong" />
              {modelData.modelName}
              <div style={{ float: 'right', fontSize: '14px', fontWeight: 'normal', color: '#999' }}>
                {/* {modelData.updateBy && <span style={{ marginRight: '10px' }}>操作人： {modelData.updateBy}</span>} */}
                <span>更新时间：{moment(modelData.updateDate).format('YYYY-MM-DD HH:mm:ss')}</span>
              </div>
            </div>
            <Spin spinning={!showList && !modelCopyList.length} wrapperClassName={styles.modelCopyList}>
              <div class={styles.modelList}>
                {modelCopyList.length > 0
                  ? modelCopyList.map((item) => (
                      <ModelItem
                        v-model={item}
                        isExternal={this.isExternal}
                        modelType={this.modelType}
                        key={item.modelId}
                        showDragger={false}
                        onChange={(data) => {
                          item = Object.assign(item, data);
                          this.getModelCopyList();
                        }}
                      />
                    ))
                  : null}
              </div>
            </Spin>
          </Collapse.Panel>
        </Collapse>
      </div>
    );
  },
});
export default ModelCollapseItem;
