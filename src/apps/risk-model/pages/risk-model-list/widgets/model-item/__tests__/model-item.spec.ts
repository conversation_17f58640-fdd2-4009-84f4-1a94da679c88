import { Wrapper, mount } from '@vue/test-utils';
import { Popconfirm, Spin, Tooltip } from 'ant-design-vue';

import ModelItem from '..';
import QIcon from '@/components/global/q-icon';
import QTag from '@/components/global/q-tag';
import RiskAction from '@/shared/components/risk-action';
import RiskModelStatusTag from '@/shared/components/risk-model-status-tag';
import { setting } from '@/shared/services';
import { flushPromises } from '@/test-utils/flush-promises';

const mockRouter = {
  push: vi.fn(),
};

const mockTrack = vi.fn();

vi.mock('vue-router/composables', () => ({
  useRouter: vi.fn(() => mockRouter),
}));

vi.mock('@/config/tracking-events', () => ({
  useTrack: vi.fn(() => mockTrack),
  createTrackEvent: vi.fn(() => 'trackEvent'),
}));

vi.mock('@/shared/services', () => ({
  setting: {
    updateModelStatus: vi.fn().mockResolvedValue({}),
    publishModel: vi.fn().mockResolvedValue({}),
    deprecate: vi.fn().mockResolvedValue({}),
  },
}));

describe('ModelItem', () => {
  let wrapper: any;

  const defaultProps = {
    value: {
      modelId: '123',
      modelName: 'Test Model',
      status: 0,
      branchTier: 1,
      distributedResource: [{ distributeStatus: 0 }],
    },
    isExternal: false,
    modelType: '1',
    showDragger: true,
  };

  beforeEach(() => {
    wrapper = mount(ModelItem, {
      propsData: defaultProps,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders correctly with default props', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.findComponent(Spin).exists()).toBe(true);
    expect(wrapper.findComponent(Tooltip).exists()).toBe(true);
    expect(wrapper.findComponent(QTag).exists()).toBe(true);
    expect(wrapper.findComponent(RiskModelStatusTag).exists()).toBe(true);
    expect(wrapper.findComponent(RiskAction).exists()).toBe(true);
    expect(wrapper.findComponent(Popconfirm).exists()).toBe(true);
  });

  it('renders Popconfirm when status is 1 and not deprecated', async () => {
    await wrapper.setProps({
      value: {
        ...defaultProps.value,
        status: 1,
      },
    });

    expect(wrapper.findComponent(Popconfirm).exists()).toBe(true);
  });

  it('renders correct icon and text based on isDeprecated', async () => {
    const editButton = wrapper.find('[data-testid="view-model"]');
    // expect(editButton.attributes('icon')).toBe('icon-a-bianjigenjin1x');
    expect(editButton.text()).toBe('编辑');

    await wrapper.setProps({
      value: {
        ...defaultProps.value,
        status: 4,
      },
    });

    // expect(wrapper.findComponent(RiskAction).attributes('icon')).toBe('icon-liulan');
    expect(wrapper.findComponent(RiskAction).text()).toBe('查看');
  });

  it('renders Popconfirm for publish when not deprecated', async () => {
    expect(wrapper.findComponent(Popconfirm).exists()).toBe(true);

    await wrapper.setProps({
      value: {
        ...defaultProps.value,
        distributedResource: [{ distributeStatus: 3 }],
      },
    });

    expect(wrapper.findAllComponents(Popconfirm).exists()).toBe(false);
  });

  it('calls handleGoUpdate when title is clicked', async () => {
    const handleGoUpdateSpy = vi.spyOn(wrapper.vm, 'handleGoUpdate');
    await wrapper.find('[data-testid="model-name"]').trigger('click');

    expect(handleGoUpdateSpy).toHaveBeenCalled();
    expect(mockRouter.push).toHaveBeenCalledWith({ path: '/investigation/models/detail/123' });
  });

  it('calls actionWapper with checkDeprecate when废弃 is clicked', async () => {
    await wrapper.setProps({
      value: {
        ...defaultProps.value,
        status: 1,
      },
    });

    const actionWapperSpy = vi.spyOn(wrapper.vm, 'actionWapper');
    await wrapper.findComponent(Popconfirm).vm.$emit('confirm');

    expect(actionWapperSpy).toHaveBeenCalledWith(wrapper.vm.checkDeprecate, undefined);
    expect(setting.deprecate).toHaveBeenCalledWith('123');
  });

  it('calls handlePublishModel when publish is clicked', async () => {
    const actionWapperSpy = vi.spyOn(wrapper.vm, 'actionWapper');
    await wrapper.findComponent(Popconfirm).vm.$emit('confirm');

    expect(actionWapperSpy).toHaveBeenCalledWith(wrapper.vm.handlePublishModel, undefined);
    expect(setting.publishModel).toHaveBeenCalledWith({ riskModelId: '123' });
  });

  it('renders with isExternal true', async () => {
    await wrapper.setProps({
      isExternal: true,
    });

    await wrapper.find('[data-testid="model-name"]').trigger('click');

    expect(mockRouter.push).toHaveBeenCalledWith({ path: '/external/investigation/models/detail/123' });
  });

  it('renders with showDragger true', async () => {
    expect(wrapper.html()).toMatchSnapshot();
  });
  it('renders with showDragger false', async () => {
    wrapper.setProps({
      showDragger: false,
    });
    await flushPromises();
    expect(wrapper.html()).toMatchSnapshot();
  });

  it('renders with status 4 and distributeStatus 3', async () => {
    await wrapper.setProps({
      value: {
        ...defaultProps.value,
        status: 4,
        distributedResource: [{ distributeStatus: 3 }],
      },
    });

    expect(wrapper.findComponent(QIcon).attributes('type')).toBe('icon-icon_tuodong');
    expect(wrapper.findComponent(RiskAction).text()).toBe('查看');
  });

  it('renders with comment', async () => {
    await wrapper.setProps({
      value: {
        ...defaultProps.value,
        comment: 'This is a test comment',
      },
    });

    expect(wrapper.find('[data-testid="model-desc"]').text()).toBe('This is a test comment');
  });

  it('renders without comment', async () => {
    await wrapper.setProps({
      value: {
        ...defaultProps.value,
        comment: '',
      },
    });
    await flushPromises();
    expect(wrapper.find('[data-testid="model-desc"]').isVisible()).toBe(false);
  });
});
