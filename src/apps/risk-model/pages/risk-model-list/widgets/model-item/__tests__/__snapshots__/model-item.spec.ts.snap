// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`ModelItem > renders with showDragger false 1`] = `
<div class="root">
  <div class="ant-spin-nested-loading spin flex flex-col flex-1">
    <div class="ant-spin-container">
      <div class="flex items-center justify-between">
        <div data-testid="model-name" class="title">
          <q-icon-stub type="icon-icon_fengxianmoxing1"></q-icon-stub><span class="text">Test Model</span>
          <div class="container small default" style="font-weight: normal; font-size: 12px; background-color: #1890ff; color: #fff;">扩展模型</div>
        </div>
        <div class="flex items-center">
          <div class="container disable" style="margin-right: 10px;">已禁用</div>
          <div data-testid="view-model" class="container withIcon text risk-action-wrapper"><span data-testid="" class="icon"><q-icon-stub type="icon-a-bianjixian1x"></q-icon-stub></span><span>编辑</span></div>
          <div data-testid="publish-model" class="container withIcon text risk-action-wrapper"><span data-testid="" class="icon"><q-icon-stub type="icon-gengxinshuomingicon"></q-icon-stub></span><span>发布</span></div>
        </div>
      </div>
      <div data-testid="model-desc" class="text-#999 flex desc" style="display: none;">-</div>
    </div>
  </div>
</div>
`;

exports[`ModelItem > renders with showDragger true 1`] = `
<div class="root">
  <q-icon-stub type="icon-icon_tuodong"></q-icon-stub>
  <div class="ant-spin-nested-loading spin flex flex-col flex-1">
    <div class="ant-spin-container">
      <div class="flex items-center justify-between">
        <div data-testid="model-name" class="title">
          <q-icon-stub type="icon-icon_fengxianmoxing1"></q-icon-stub><span class="text">Test Model</span>
          <div class="container small default" style="font-weight: normal; font-size: 12px; background-color: #1890ff; color: #fff;">扩展模型</div>
        </div>
        <div class="flex items-center">
          <div class="container disable" style="margin-right: 10px;">已禁用</div>
          <div data-testid="view-model" class="container withIcon text risk-action-wrapper"><span data-testid="" class="icon"><q-icon-stub type="icon-a-bianjixian1x"></q-icon-stub></span><span>编辑</span></div>
          <div data-testid="publish-model" class="container withIcon text risk-action-wrapper"><span data-testid="" class="icon"><q-icon-stub type="icon-gengxinshuomingicon"></q-icon-stub></span><span>发布</span></div>
        </div>
      </div>
      <div data-testid="model-desc" class="text-#999 flex desc" style="display: none;">-</div>
    </div>
  </div>
</div>
`;
