.root {
  display: flex;
  align-items: center;
  gap: 25px;
  padding: 15px;
  border-radius: 4px;
  background-color: #fff;
  margin-bottom: 15px;
  position: relative;

  &:hover {
    background-color: #e2f1fd;

    .title {
      color: #128bed;
    }
  }

  .title {
    line-height: 24px;
    font-weight: bold;
    color: #333;
    display: flex;
    align-items: center;

    &:hover {
      cursor: pointer;
    }

    .text {
      margin-right: 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 30em;

      @media (min-width: 1680px) {
        max-width: 20em;
      }
    }

    :global {
      .anticon {
        font-size: 20px;
        margin-right: 5px;
      }
    }
  }

  .desc {
    margin: 5px 125px 0 2px;
  }

  :global {
    .ant-tag {
      box-sizing: border-box;
      line-height: 22px;
      height: 22px;
      color: #666;
      font-size: 12px;
      padding: 0 8px;
    }

    .risk-action-wrapper {
      padding-right: 20px;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        right: 7px;
        top: 4px;
        width: 1px;
        height: calc(100% - 8px);
        background-color: #d8d8d8;
      }

      &:last-child {
        padding-right: 0;

        &::after {
          display: none;
        }
      }
    }
  }

  .spin {
    height: 50px;

    :global {
      .ant-spin-spinning {
        height: 50px !important;
        max-height: auto !important;

        .ant-spin-dot {
          margin: -15px -50px !important;
        }
      }
    }
  }
}
