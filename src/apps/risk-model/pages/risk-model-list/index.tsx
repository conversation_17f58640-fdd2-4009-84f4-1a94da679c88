import { defineComponent, reactive, ref } from 'vue';
import { Pagination } from 'ant-design-vue';
import Draggable from 'vuedraggable';

import { setting as settingService } from '@/shared/services';
import HeroicLayout from '@/shared/layouts/heroic';
import Card from '@/shared/components/card';

import styles from './model-list.module.less';
import ModelCollapseItem from './widgets/model-collapse-item';

const RiskModelListPage = defineComponent({
  name: 'RiskModelListPage',
  props: {
    /**
     * 是否为嵌入页面
     */
    isExternal: {
      type: Boolean,
      default: false,
    },
    /**
     * 模型类型
     */
    modelType: {
      type: String,
      default: '1',
    },
  },
  setup(props) {
    const modelList = ref([]);
    const loading = ref(false);
    const pagination = reactive({
      pageSize: 10,
      current: 1,
      total: 0,
      pageSizeOptions: ['5', '10', '15'],
    });

    const getModelList = async () => {
      loading.value = true;
      try {
        const params = {
          modelType: parseInt(props.modelType, 10), // 模型类型
          // modelName: 'string',
          product: 'SAAS_PRO',
          // 0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃
          // status: 0,
          branchCode: 0,
          onlyTier: true,
          pageIndex: pagination.current,
          pageSize: pagination.pageSize,
        };
        const res = await settingService.getModelLists(params);
        modelList.value = (res.data || []).filter((item) => item?.extendJson?.showScoreCard !== 1);
        pagination.total = res.total ?? 0;
      } catch (error) {
        modelList.value = [];
      } finally {
        loading.value = false;
      }
    };
    const handleSort = () => {
      // saveData();
    };

    const handlePageChange = (current: number, pageSize: number) => {
      Object.assign(pagination, { current, pageSize });
      getModelList();
    };
    const handlePageSizeChange = (current: number, pageSize: number) => {
      Object.assign(pagination, { current: 1, pageSize });
      getModelList();
    };
    getModelList();

    return {
      loading,
      modelList,
      pagination,
      getModelList,
      handleSort,
      handlePageChange,
      handlePageSizeChange,
    };
  },
  render() {
    return (
      <HeroicLayout loading={this.loading}>
        <Card title={this.$route?.meta?.title}>
          <div class={styles.container}>
            <Draggable v-model={this.modelList} handle=".drag-handle" animation="300" onChange={this.handleSort}>
              {this.modelList.map((item: any) => {
                return (
                  <ModelCollapseItem
                    modelData={item}
                    isExternal={this.isExternal}
                    modelType={this.modelType}
                    key={item.modelId}
                    onChange={(data) => {
                      item.status = data;
                    }}
                  />
                );
              })}
            </Draggable>

            {this.pagination.total > this.pagination.pageSize ? (
              <div class={styles.pagination}>
                <Pagination
                  onChange={this.handlePageChange}
                  onShowSizeChange={this.handlePageSizeChange}
                  {...{ props: this.pagination }}
                  showSizeChanger={true}
                />
              </div>
            ) : null}
          </div>
        </Card>
      </HeroicLayout>
    );
  },
});

export default RiskModelListPage;
