import { defineComponent } from 'vue';
import { Button } from 'ant-design-vue';

import NotFoundIcon from './assets/error-404.png';
import PermissionDeniedIcon from './assets/error-403.png';
import ServerErrorIcon from './assets/error-500.png';
import styles from './generic-error.page.module.less';

const ERROR_ICON_MAP = {
  403: PermissionDeniedIcon,
  404: NotFoundIcon,
  500: ServerErrorIcon,
};

const ERROR_MESSAGE_MAP = {
  403: {
    zh: '抱歉，您暂时没有权限访问该页面',
    en: `Sorry, you are not allowed to access this page. Please contact your compliance BP for the access if necessary.`,
  },
  404: {
    zh: '抱歉，您访问的页面不存在，请检查网址是否正确',
    en: `Sorry, the page you visited does not exist, please check if the URL is correct`,
  },
  500: {
    zh: '抱歉，服务器繁忙，请稍后再试',
    en: `Sorry, the server is busy, please try again later`,
  },
  goHome: {
    zh: '返回首页',
    en: 'Back to Home',
  },
};

const GenericErrorPage = defineComponent({
  name: 'GenericErrorPage',

  props: {
    isExternal: {
      type: Boolean,
      default: false,
    },
    errorCode: {
      type: Number,
      default: 404,
    },
    lang: {
      type: String,
      default: 'zh',
    },
  },

  setup() {
    const handleBackHome = () => {
      window.location.href = '/';
    };

    return {
      handleBackHome,
    };
  },

  render() {
    const icon = ERROR_ICON_MAP[this.errorCode];
    const message = ERROR_MESSAGE_MAP[this.errorCode][this.lang];
    const goHomeText = ERROR_MESSAGE_MAP.goHome[this.lang];
    return (
      <div class={styles.container}>
        <div class={styles.image}>
          <img width={240} src={icon} />
        </div>
        <p class={styles.description}>{message}</p>
        <div v-show={!this.isExternal}>
          <Button type="primary" onClick={this.handleBackHome}>
            {goHomeText}
          </Button>
        </div>
      </div>
    );
  },
});

export default GenericErrorPage;
