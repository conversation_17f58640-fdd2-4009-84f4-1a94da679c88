import { RouteConfig } from 'vue-router';

export const errorRoutes = (): RouteConfig[] => [
  /**
   * 嵌入页面的权限错误页面
   */
  {
    path: '/auth',
    name: 'auth-error-default',
    meta: {
      layout: 'default',
    },
    props: {
      errorCode: 403,
      isExternal: true, // 不显示返回按钮
    },
    component: () => import('../pages/generic-error'),
  },
  /**
   * 蔡司权限错误页面(没有用到)
   */
  {
    path: '/auth/zeiss',
    name: 'auth-error-zeiss',
    meta: {
      layout: 'default',
    },
    props: {
      errorCode: 403,
      lang: 'en',
    },
    component: () => import('../pages/generic-error'),
  },
  /**
   * 权限错误
   */
  {
    path: '/403',
    name: 'permission-error',
    meta: {
      layout: 'default',
    },
    props: {
      errorCode: 403,
    },
    component: () => import('../pages/generic-error'),
  },
  /**
   * 服务器错误
   */
  {
    path: '/500',
    name: 'server-error',
    meta: {
      layout: 'default',
    },
    props: {
      errorCode: 500,
    },
    component: () => import('../pages/generic-error'),
  },
  /**
   * 404
   */
  {
    path: '*',
    name: 'not-found',
    meta: {
      layout: 'default',
    },
    props: {
      errorCode: 404,
    },
    component: () => import('../pages/generic-error'),
  },
];
