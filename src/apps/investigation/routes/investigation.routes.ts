import type { RouteConfig } from 'vue-router';

// import InsightsLayout from '@/shared/layouts/insights';
import SidebarMenuLayout from '@/shared/layouts/sidebar-menu';
import { APP_MENU_CONFIG } from '@/config/menu.config';
import { Permission } from '@/config/permissions.config';
import { hasPermission } from '@/shared/composables/use-permission';

export const investigationRoutes = (): RouteConfig[] => [
  {
    path: '/investigation',
    component: SidebarMenuLayout,
    redirect: () => {
      if (hasPermission([Permission.INVESTIGATION_VIEW])) {
        return {
          name: `investigation-start`,
        };
      }
      if (hasPermission([Permission.INVESTIGATION_HISTORY_VIEW])) {
        return {
          name: `investigation-history`,
        };
      }
      return {
        name: 'investigation-model-list',
      };
    },
    props: {
      pageTitle: '企业尽调',
      menu: APP_MENU_CONFIG,
    },
    meta: {
      title: '企业尽调',
    },
    children: [
      {
        path: 'search',
        name: `investigation-start`,
        component: () => import('../pages/investigation-start'),
        meta: {
          title: '准入尽调',
          // 任务栏透明
          translucentHeader: false,
          permission: [Permission.INVESTIGATION_VIEW],
        },
      },
      {
        path: 'search/list',
        name: `investigation-search-list`,
        // component: () => import('../pages/company-search'),
        component: () => import('../../company/pages/company-search'),
        meta: {
          title: '企业搜索',
        },
      },
      {
        path: 'history',
        name: `investigation-history`,
        component: () => import('../pages/investigation-history'),
        meta: {
          title: '尽调记录',
          permission: [Permission.INVESTIGATION_HISTORY_VIEW],
        },
      },
      // search, history => detail
      {
        path: ':type(search|history)/detail/:id([a-z0-9,]+)', // 兼容排查记录入口与历史记录入口
        name: `investigation-detail`,
        props: true,
        component: () => import('../pages/investigation-detail'),
        meta: {
          title: '尽调详情',
          permission: [Permission.INVESTIGATION_VIEW, Permission.INVESTIGATION_HISTORY_VIEW],
        },
      },
      // models => model-detail
      {
        path: 'models',
        name: 'investigation-model-list',
        props: {
          modelType: '1',
        },
        component: () => import('../../risk-model/pages/risk-model-list'),
        meta: {
          title: '尽调模型',
          permission: [Permission.INVESTIGATION_MODEL_VIEW],
        },
      },
      {
        path: 'models/detail/:riskModelId([0-9]+)',
        name: 'investigation-model-detail',
        props: {
          default: true,
          modelType: '1',
        },
        component: () => import('../../risk-model/pages/risk-model-detail'),
        meta: {
          title: '尽调模型详情',
          permission: [Permission.INVESTIGATION_MODEL_VIEW],
        },
      },
    ],
  },
];
