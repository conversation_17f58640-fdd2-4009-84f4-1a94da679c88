import { defineComponent, onMounted, ref } from 'vue';

import CommonSearchFilter from '@/components/common/common-search-filter';
import QCard from '@/components/global/q-card';
import HeroicLayout from '@/shared/layouts/heroic';
import { user as userService } from '@/shared/services';

import { InvestigationHistoryColumns } from './investigation-history.page.config';
import SearchResult from './widgets/search-result';
import { useSearchResult } from './hooks/use-search-result';
import { useRoute } from 'vue-router/composables';

/**
 * 企业尽调历史记录
 */
const InvestigationHistoryPage = defineComponent({
  name: 'InvestigationHistoryPage',
  setup() {
    const init = ref(true);

    const {
      dataSource,
      pagination,
      isLoading,
      execute,

      filters,
      filterLoading,
      filterGroups,
      handleFilterChange,
      handleSortChange,
      getAggFilters,
    } = useSearchResult();

    // const handleExport = async () => {
    //   try {
    //     await diligenceService.export(getParams.value);
    //     message.success('正在导出，稍后可前往任务列表查看进度');
    //   } catch (error) {
    //     console.error(error);
    //   }
    // };

    // const handleExportByIds = async () => {
    //   try {
    //     if (!selectedIds.value.length) {
    //       message.warning('请选择需要导出的记录');
    //       return;
    //     }
    //     await diligenceService.export({ ids: selectedIds.value });
    //     message.success('正在导出，稍后可前往任务列表查看进度');
    //   } catch (error) {
    //     console.error(error);
    //   }
    // };

    const operatorList = ref([]);
    const getOperatorList = async () => {
      const data = await userService.getUserList();
      operatorList.value = data;
    };

    onMounted(async () => {
      await getOperatorList();
      init.value = false;
    });

    const route = useRoute();

    return {
      filterGroups,
      init,
      filters,
      pagination,
      dataSource,
      isLoading,
      handleFilterChange,
      handleSortChange,
      filterLoading,
      getAggFilters,
      execute,
      // handleExport,
      // handleExportByIds,
      operatorList,
      route,
    };
  },
  render() {
    return (
      <HeroicLayout loading={this.init && this.isLoading}>
        <QCard
          slot="hero"
          title={this.route.meta?.title}
          bodyStyle={{
            paddingTop: 0,
          }}
        >
          <CommonSearchFilter
            placeholder="请输入企业名称"
            filterConfig={this.filterGroups}
            onChange={this.handleFilterChange}
            loading={this.filterLoading}
            onGetOptions={this.getAggFilters}
            defaultValue={this.filters}
          />
        </QCard>
        <SearchResult
          rowKey={'id'}
          dataSource={this.dataSource}
          columns={InvestigationHistoryColumns}
          pagination={this.pagination}
          isLoading={this.isLoading}
          // scroll={{ x: 1200, y: 'calc(100vh - 146px - 215px)' }}
          searchKey={this.filters.keywords}
          operatorList={this.operatorList}
          on={{
            changePage: (pageIndex: number, pageSize: number) => {
              this.execute({ pageIndex, pageSize });
            },
            changeSort: (sorter) => {
              this.handleSortChange(sorter);
            },
          }}
        ></SearchResult>
      </HeroicLayout>
    );
  },
});

export default InvestigationHistoryPage;
