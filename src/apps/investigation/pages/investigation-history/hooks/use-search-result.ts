import { computed, reactive, ref, watch } from 'vue';

import { diligence as diligenceService } from '@/shared/services';
import { useFetchState } from '@/hooks/use-fetch-state';
import { useCachedQuery } from '@/apps/investigation/pages/investigation-history/hooks/use-cached-query';
import { getFilterGroup } from '@/apps/investigation/pages/investigation-history/investigation-history.page.config';
import { DataModelDistributeEnum, DistributeStatusEnumMap } from '@/shared/config/risk-model.config';

export const useSearchResult = () => {
  const { filterParams, cached, handleFilterChange, handleSortChange } = useCachedQuery();

  const total = ref(0);
  const fetchData = (params = {}) => {
    return diligenceService
      .search({
        pageIndex: cached.pagination.value.pageIndex,
        pageSize: cached.pagination.value.pageSize,
        ...filterParams.value,
        ...params,
      })
      .then((res) => {
        cached.pagination.value.pageIndex = res.pageIndex;
        cached.pagination.value.pageSize = res.pageSize;
        total.value = res.total;
        return res;
      });
  };

  const pagination = computed(() => ({
    current: cached.pagination.value.pageIndex,
    pageSize: cached.pagination.value.pageSize,
    total: total.value,
  }));

  const { execute, result, isLoading } = useFetchState(fetchData);

  const dataSource = computed<any[]>(() => result.value?.data || []);

  const riskLevelList = computed(() => {
    const resultSetting = dataSource.value?.[0]?.orgModel?.resultSetting || [];
    return resultSetting.map((v) => ({ label: v.name, value: v.level }));
  });

  const filterLoading = ref(false);
  const aggFilters = reactive<{ riskModelOptions: any[]; riskLevelOptions: any[] }>({
    riskModelOptions: [],
    riskLevelOptions: [],
  });
  const filterStash = ref<boolean>(false);
  const setFilterStash = (bool: boolean) => {
    filterStash.value = bool;
  };
  const getAggFilters = async (group) => {
    const isAggsFilter = ['orgModelIds', 'ekc'].includes(group.field);
    if (filterStash.value || !isAggsFilter) {
      return;
    }
    try {
      filterLoading.value = true;
      const { riskModelData = [], riskLevelData = [] } = await diligenceService.getAggSearch({
        pageIndex: cached.pagination.value.pageIndex,
        pageSize: cached.pagination.value.pageSize,
        ...filterParams.value,
      });
      aggFilters.riskModelOptions = riskModelData.map((v: any) => ({
        label: DataModelDistributeEnum.Enable === v.status ? v.modelName : `${v.modelName}(${DistributeStatusEnumMap[v.status]})`,
        value: v.moduleId,
        count: v.count,
      }));
      aggFilters.riskLevelOptions = riskLevelData;
      setFilterStash(true);
    } catch (e) {
      console.error(e);
    } finally {
      filterLoading.value = false;
    }
  };

  const filterGroups = computed(() => {
    return getFilterGroup(
      aggFilters.riskModelOptions,
      aggFilters.riskLevelOptions.map((v: any) => ({
        label: riskLevelList.value.find((r) => r.value === v.riskLevel)?.label || '未知',
        value: v.riskLevel,
        count: v.count,
      }))
    );
  });

  watch(
    () => filterParams.value,
    () => {
      execute();
      setFilterStash(false);
    },
    { immediate: true, deep: true }
  );

  return {
    execute,
    dataSource,
    result,
    isLoading,

    filterLoading,
    filterGroups,
    filterParams,
    pagination,
    filters: cached.query,
    handleFilterChange,
    handleSortChange,
    getAggFilters,
  };
};
