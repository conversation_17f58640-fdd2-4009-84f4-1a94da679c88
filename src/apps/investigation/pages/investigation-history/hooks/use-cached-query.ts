import { useRoute, useRouter } from 'vue-router/composables';
import { computed } from 'vue';

import { isJSONString } from '@/utils/data-type/is-json-string';
import { useCacheQuery } from '@/hooks/use-cache-query';
import { stateToQuery } from '@/utils/search-transform/company/state-to-query';

export const useCachedQuery = () => {
  const route = useRoute();
  const router = useRouter();

  /** 从URL中获取日期区间 */
  const dateRangeFromUrlQuery = isJSONString(route.query?.dateRange as string)
    ? JSON.parse(route.query?.dateRange as string)
    : { currently: true, flag: 1, number: 30, unit: 'day' };

  // 是否使用上次缓存的查询条件，不使用就清除上次缓存
  const DEFAULT_SEARCH_CONDITIONS = Object.freeze({
    query: {
      keywords: undefined,
      filters: { sd: dateRangeFromUrlQuery }, // 通过URL获取日期字段: `sd`
    },
    pagination: { pageSize: 10, pageIndex: 1 },
    sort: {},
  });

  const { cached } = useCacheQuery(
    // Namespace
    'investigation-history',
    // Defaults
    DEFAULT_SEARCH_CONDITIONS,
    // Deps
    { route, router }
  );

  const filterParams = computed(() => {
    const { keywords, filters } = cached.query.value;
    const filter = stateToQuery({
      keyword: keywords,
      filters,
    });
    return {
      searchKey: filter?.searchKey,
      createDate: filter.filter?.sd ? [filter.filter?.sd] : undefined,
      result: filter.filter?.ekc,
      operators: filters?.t,
      modelIds: filters?.orgModelIds,
      ...cached.sort.value,
    };
  });

  const handleFilterChange = (payload) => {
    cached.pagination.value.pageIndex = 1;
    cached.query.value = payload;
  };

  const handleSortChange = (payload) => {
    cached.pagination.value.pageIndex = 1;
    cached.sort.value = payload;
  };

  return {
    cached,
    filterParams,
    handleFilterChange,
    handleSortChange,
  };
};
