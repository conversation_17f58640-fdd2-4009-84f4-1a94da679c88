import { computed, defineComponent, PropType, ref } from 'vue';
import { Button } from 'ant-design-vue';
import { useRouter } from 'vue-router/composables';
import { escape } from 'lodash';

import EmptyWrapper from '@/shared/components/empty-wrapper';
import QRichTable from '@/components/global/q-rich-table';
import QCard from '@/components/global/q-card';
import SearchCount from '@/components/search-count';
import { convertSortStructure } from '@/utils/data-type/convert-sort-structure';
import DiligenceWarningPop from '@/components/diligence-warning-pop';
import QEntityLink from '@/components/global/q-entity-link';

const SearchResult = defineComponent({
  name: 'SearchResult',
  props: {
    dataSource: {
      type: Array,
      required: true,
    },
    columns: {
      type: Array,
      required: true,
    },
    rowKey: {
      type: String,
      default: 'id',
    },
    pagination: {
      type: Object,
      required: true,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    scroll: {
      type: Object as PropType<{ x?: string | boolean | number; y?: string | boolean | number }>,
      required: false,
    },
    selectedItems: {
      type: Array,
      default: () => [],
    },
    searchKey: {
      type: String,
      required: false,
    },
    operatorList: {
      type: Array as PropType<{ userId: number; name: string }[]>,
      default: () => [],
    },
  },
  emits: ['changePage', 'changeSort', 'action'],
  setup(props, { emit }) {
    const router = useRouter();

    const selectedIds = ref([]);

    const rowSelection = computed(() => ({
      checkStrictly: false,
      selectedRowKeys: selectedIds.value,
      onChange: (selectedRowKeys, selectedRows) => {
        selectedIds.value = selectedRowKeys;
      },
    }));

    const handlePageChange = (current, pageSize) => {
      emit('changePage', current, pageSize);
    };

    const updateSortInfo = ({ sorter }) => {
      const sortInfo = sorter;
      emit('changeSort', convertSortStructure(sortInfo));
    };

    const paginationProps = computed(() => ({
      ...props.pagination,
      onChange: handlePageChange,
      onShowSizeChange: handlePageChange,
    }));

    const handleGoToDetail = (record) => {
      router.push({
        path: `/investigation/history/detail/${record.companyId}`,
        query: {
          from: 'record',
          diligenceId: record.id,
        },
      });
    };

    return {
      selectedIds,
      rowSelection,
      paginationProps,
      updateSortInfo,
      handleGoToDetail,
    };
  },
  render() {
    return (
      <QCard bodyStyle={{ padding: '15px' }}>
        <div slot="title">
          <SearchCount
            slot="title"
            showSelects={true}
            total={this.pagination.total}
            loading={this.isLoading}
            selectedIds={this.selectedIds}
          />
        </div>
        {/* <div slot="extra"> */}
        {/*   <div class={styles.extra}> */}
        {/*     <DropdownButtonWrapper */}
        {/*       totalCount={this.pagination.total} */}
        {/*       v-permission={[2022]} */}
        {/*       btnText="导出列表" */}
        {/*       needPopConfirm={false} */}
        {/*       menuItems={EXPORTITEMS} */}
        {/*       onConfirm={(key) => { */}
        {/*         if (key === 'export') { */}
        {/*           this.handleExport(); */}
        {/*         } else { */}
        {/*           this.handleExportByIds(); */}
        {/*         } */}
        {/*       }} */}
        {/*     /> */}
        {/*   </div> */}
        {/* </div> */}
        <EmptyWrapper dataSource={this.dataSource} loading={this.isLoading}>
          <QRichTable
            loading={this.isLoading}
            showIndex={false}
            scroll={this.scroll}
            rowKey={this.rowKey}
            dataSource={this.dataSource}
            columns={this.columns}
            pagination={this.paginationProps}
            rowSelection={this.rowSelection}
            scopedSlots={{
              companyWithoutLogo: (item) => {
                let name = escape(item.name);
                if (this.searchKey) {
                  name = name.replace(this.searchKey, `<em>${this.searchKey}</em>`);
                }
                return <QEntityLink coyObj={{ KeyNo: item.companyId, Name: name }} ellipsis={false} class="emphasis"></QEntityLink>;
              },
              diligenceResult: (val, item) => {
                // 区分模型类型渲染风险等级
                let modelType: 'default' | 'tech' | 'monitor' = 'default';
                if (item?.orgModel?.extendJson?.showScoreCard === 1) {
                  modelType = 'tech';
                }
                // 风险等级
                return <DiligenceWarningPop modelType={modelType} score={val} rowData={item} diligenceId={item.id} />;
              },
              operatorName: (id) => {
                if (!this.operatorList?.length) {
                  return '-';
                }
                return this.operatorList.find((operator) => operator.userId === id)?.name || '-';
              },
              diligenceAction: (item) => {
                if (!item.id) {
                  return null;
                }
                return (
                  <Button type="link" onClick={() => this.handleGoToDetail(item)}>
                    排查详情
                  </Button>
                );
              },
            }}
            on={{
              change: this.updateSortInfo,
            }}
          />
        </EmptyWrapper>
      </QCard>
    );
  },
});

export default SearchResult;
