import { mount } from '@vue/test-utils';

import SearchResult from '..';

vi.mock('@/utils/data-type/convert-sort-structure');

vi.mock('vue-router/composables');

describe('SearchResult 组件测试', () => {
  it('正确渲染数据', () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: [
          { id: 1, companyId: 101, name: '公司A', result: 1, operatorId: null },
          { id: 2, companyId: 102, name: '公司B', result: 2, operatorId: 1 },
          { id: null, companyId: 103, name: '公司C', result: 0, operatorId: 1 },
        ],
        columns: [
          { title: '企业名称', scopedSlots: { customRender: 'companyWithoutLogo' } },
          { title: '操作人', dataIndex: 'operatorId', scopedSlots: { customRender: 'operatorName' } },
          { title: '操作', scopedSlots: { customRender: 'diligenceAction' } },
        ],
        searchKey: 'A',
        operatorList: [{ userId: 1, name: '操作员A' }],
        pagination: { total: 2, current: 1, pageSize: 10 },
      },
    });

    expect(wrapper.html()).toMatchSnapshot();
  });

  it('处理空数据源', () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: [],
        columns: [{ title: 'ID', dataIndex: 'id' }],
        pagination: { total: 0, current: 1, pageSize: 10 },
      },
    });

    expect(wrapper.html()).toMatchSnapshot();
  });

  it('显示加载状态', () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: [],
        columns: [{ title: 'ID', dataIndex: 'id' }],
        pagination: { total: 0, current: 1, pageSize: 10 },
        isLoading: true,
      },
    });

    expect(wrapper.findComponent({ name: 'QRichTable' }).props('loading')).toBe(true);
  });

  it('operatorList 为空时显示 "-"', () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: [{ id: 1, companyId: 101, name: '公司A', operatorId: 1 }],
        columns: [{ title: '操作人', dataIndex: 'id', scopedSlots: { customRender: 'operatorName' } }],
        pagination: { total: 1, current: 1, pageSize: 10 },
        operatorList: [],
      },
    });

    const table = wrapper.findComponent({ name: 'QRichTable' });
    expect(table.exists()).toBe(true);
    expect(table.text()).toContain('-');
  });
});
