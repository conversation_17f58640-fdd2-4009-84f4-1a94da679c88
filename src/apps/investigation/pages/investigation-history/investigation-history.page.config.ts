import { Group } from '@/components/global/q-filter/interface';
import { DEFAULT_DATE_RANGE } from '@/config/tender.config';

export const getFilterGroup = (modelList: any[] = [], riskLevelList: any[] = []) => {
  const DILIGENCE_SEARCH_FILTER_GROUPS: Group[] = [
    {
      field: 'filters',
      label: '筛选条件',
      type: 'groups',
      children: [
        {
          field: 'orgModelIds',
          type: 'multiple',
          label: '尽调模型',
          options: modelList,
          layout: 'inline',
        },
        {
          field: 'ekc',
          type: 'multiple',
          label: '风险等级',
          options: riskLevelList,
          layout: 'inline',
        },
        {
          field: 'sd',
          type: 'single',
          label: '排查时间',
          options: [{ value: undefined, label: '不限' }, ...DEFAULT_DATE_RANGE],
          custom: {
            type: 'date-range',
          },
        },
      ],
    },
  ];
  return DILIGENCE_SEARCH_FILTER_GROUPS;
};

export const InvestigationHistoryColumns = [
  {
    title: '企业名称',
    width: 358,
    scopedSlots: {
      customRender: 'companyWithoutLogo',
    },
  },
  {
    title: '尽调模型',
    width: 173,
    dataIndex: 'orgModel.modelName',
  },
  {
    title: '风险等级',
    width: 104,
    dataIndex: 'result',
    align: 'left',
    sorter: true,
    scopedSlots: {
      customRender: 'diligenceResult',
    },
  },
  {
    title: '操作人',
    width: 104,
    dataIndex: 'operator',
    scopedSlots: {
      customRender: 'operatorName',
    },
  },
  {
    title: '排查时间',
    width: 165,
    key: 'createDate',
    dataIndex: 'createDate',
    sorter: true,
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    title: '操作',
    width: 100,
    scopedSlots: {
      customRender: 'diligenceAction',
    },
  },
];
