import { mount } from '@vue/test-utils';

import { diligence as diligenceService, user as userService } from '@/shared/services';
import { flushPromises } from '@/test-utils/flush-promises';

import InvestigationHistoryPage from '..';

const mockList = [
  {
    paid: 0,
    id: 50996905,
    orgId: 208,
    companyId: '84c17a005a759a5e0d875c1ebb6c9846',
    name: '乐视网信息技术（北京）股份有限公司',
    score: 65,
    result: 1,
    operator: 1,
    orgModelId: 3487,
    modelBranchCode: 'f0a79aeb-c7a3-42fc-875b-d6406a7305be',

    details: {
      result: 1,
      modelType: 1,
      totalHits: 407,
      levelGroup: {
        '0': [],
        '1': [
          {
            level: 1,
            totalHits: 84,
            scoreDetails: [
              {
                name: '股权冻结',
                score: 8,
                isVeto: 0,
                status: 2,
                metricsId: 141544,
                riskLevel: 1,
                totalHits: 82,
                hitDetails: {
                  must: [
                    {
                      source: 'CreditES',
                      status: 2,
                      totalHits: 82,
                      strategyId: 225012,
                      description:
                        '匹配到目标主体 <em class=" - ">【股权冻结】 82条记录</em><span class="">，冻结股权数额：<em class=" - ">699,893.48万元</em></span>',
                      dimensionKey: 'FreezeEquity',
                      strategyName: '股权冻结',
                      strategyRole: 1,
                      dimensionName: '股权冻结',
                    },
                  ],
                  totalHits: 82,
                  hitStrategy: {
                    must: [225012],
                    order: 0,
                    status: 1,
                    scoreSettings: {
                      maxScore: 8,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                },
                metricType: 0,
                detailsJson: {},
                hitStrategy: [
                  {
                    must: [225012],
                    order: 0,
                    status: 1,
                    scoreSettings: {
                      maxScore: 8,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                ],
                otherHitDetails: [],
              },
              {
                name: '被列入经营异常名录（历史）',
                score: 2,
                isVeto: 0,
                status: 2,
                metricsId: 141542,
                riskLevel: 1,
                totalHits: 1,
                hitDetails: {
                  must: [
                    {
                      source: 'CreditES',
                      status: 2,
                      totalHits: 1,
                      strategyId: 225023,
                      description: '匹配到目标主体 <em class=" - ">【被列入经营异常名录（历史）】 1条记录</em>',
                      dimensionKey: 'OperationAbnormal',
                      strategyName: '被列入经营异常名录（历史）',
                      strategyRole: 1,
                      dimensionName: '被列入经营异常名录（历史）',
                    },
                  ],
                  totalHits: 1,
                  hitStrategy: {
                    must: [225023],
                    order: 0,
                    status: 1,
                    scoreSettings: {
                      maxScore: 2,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                },
                metricType: 0,
                detailsJson: {},
                hitStrategy: [
                  {
                    must: [225023],
                    order: 0,
                    status: 1,
                    scoreSettings: {
                      maxScore: 2,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                ],
                otherHitDetails: [],
              },
              {
                name: '企查分',
                score: 5,
                isVeto: 0,
                status: 2,
                metricsId: 141535,
                riskLevel: 1,
                totalHits: 1,
                hitDetails: {
                  must: [
                    {
                      source: 'EnterpriseLib',
                      status: 2,
                      totalHits: 1,
                      strategyId: 225022,
                      description: '匹配到目标主体 <em class=" - ">【企查分】 1分</em>',
                      dimensionKey: 'QCCCreditRate',
                      strategyName: '企查分',
                      strategyRole: 1,
                      dimensionName: '企查分',
                    },
                  ],
                  totalHits: 1,
                  hitStrategy: {
                    must: [225022],
                    order: 0,
                    status: 1,
                    scoreSettings: {
                      maxScore: 5,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                },
                metricType: 0,
                detailsJson: {
                  scoreStrategy: 0,
                },
                hitStrategy: [
                  {
                    must: [225022],
                    order: 0,
                    status: 1,
                    scoreSettings: {
                      maxScore: 5,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                ],
                otherHitDetails: [],
              },
            ],
            groupDefinition: {
              comment: '经营风险',
              groupId: 50013828,
              groupName: '经营风险',
              isVirtual: null,
              riskLevel: 2,
              detailsJson: null,
              parentGroupId: null,
            },
          },
        ],
        '2': [
          {
            level: 2,
            totalHits: 323,
            scoreDetails: [
              {
                name: '法定代表人限制高消费',
                score: 50,
                isVeto: 0,
                status: 2,
                metricsId: 141545,
                riskLevel: 2,
                totalHits: 323,
                hitDetails: {
                  should: [
                    {
                      source: 'CreditES',
                      status: 2,
                      totalHits: 323,
                      strategyId: 225021,
                      description: '匹配到目标主体 <em class=" - "></em><em class=" - ">【限制高消费】 323条记录</em>',
                      dimensionKey: 'RestrictedConsumptionCurrent',
                      strategyName: '限制高消费',
                      strategyRole: 1,
                      dimensionName: '被列入限制高消费名单',
                    },
                  ],
                  totalHits: 323,
                  hitStrategy: {
                    order: 0,
                    should: [225021],
                    status: 1,
                    scoreSettings: {
                      maxScore: 50,
                      riskLevel: 2,
                    },
                    minimum_should_match: 1,
                  },
                  minimum_should_match: 1,
                },
                metricType: 0,
                detailsJson: {},
                hitStrategy: [
                  {
                    order: 0,
                    should: [225021],
                    status: 1,
                    scoreSettings: {
                      maxScore: 50,
                      riskLevel: 2,
                    },
                    minimum_should_match: 1,
                  },
                  {
                    order: 3,
                    should: [225026],
                    status: 1,
                    scoreSettings: {
                      maxScore: 5,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                  {
                    order: 2,
                    should: [225027],
                    status: 1,
                    scoreSettings: {
                      maxScore: 15,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                  {
                    order: 1,
                    should: [225028],
                    status: 1,
                    scoreSettings: {
                      maxScore: 10,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                ],
                otherHitDetails: [],
              },
            ],
            groupDefinition: {
              comment: '经营风险',
              groupId: 50013828,
              groupName: '经营风险',
              isVirtual: null,
              riskLevel: 2,
              detailsJson: null,
              parentGroupId: null,
            },
          },
        ],
      },
      totalScore: 65,
    },
    shouldUpdate: 0,
    updateDate: '2025-03-25T02:46:22.000Z',
    snapshotDate: '2025-03-25T02:46:16.000Z',
    snapshotId: '527f38a0-0923-11f0-915b-d1c3fe829a10',
    createDate: '2025-03-25T02:46:16.000Z',
    creditRate: null,
    type: 0,
    orgModel: {
      modelType: 1,
      category: 1,
      modelId: 3487,
      modelName: '中行山东分行模型031801',
      resultSetting: [
        {
          name: '高风险',
          level: 2,
          maxScore: null,
          mimScore: 75,
        },
        {
          name: '中高风险',
          level: 1,
          maxScore: 74,
          mimScore: 60,
        },
        {
          name: '中风险',
          level: 0,
          maxScore: 59,
          mimScore: 40,
        },
        {
          name: '中低风险',
          level: -1,
          maxScore: 39,
          mimScore: 20,
        },
        {
          name: '良好',
          level: -2,
          maxScore: 19,
          mimScore: null,
        },
      ],
    },
    creditcode: '911100007693890511',
  },
  {
    paid: 1,
    id: 50996903,
    orgId: 208,
    companyId: '84c17a005a759a5e0d875c1ebb6c9846',
    name: '乐视网信息技术（北京）股份有限公司',
    score: 65,
    result: 1,
    operator: 101618,
    orgModelId: 3488,
    modelBranchCode: '35458e63-4121-4c99-8450-b23d63742450',

    details: {
      result: 1,
      modelType: 1,
      totalHits: 407,
      levelGroup: {
        '0': [],
        '1': [
          {
            level: 1,
            totalHits: 84,
            scoreDetails: [
              {
                name: '股权冻结',
                score: 8,
                isVeto: 0,
                status: 2,
                metricsId: 141557,
                riskLevel: 1,
                totalHits: 82,
                hitDetails: {
                  must: [
                    {
                      source: 'CreditES',
                      status: 2,
                      totalHits: 82,
                      strategyId: 225031,
                      description:
                        '匹配到目标主体 <em class=" - ">【股权冻结】 82条记录</em><span class="">，冻结股权数额：<em class=" - ">699,893.48万元</em></span>',
                      dimensionKey: 'FreezeEquity',
                      strategyName: '股权冻结',
                      strategyRole: 1,
                      dimensionName: '股权冻结',
                    },
                  ],
                  totalHits: 82,
                  hitStrategy: {
                    must: [225031],
                    order: 0,
                    status: 1,
                    scoreSettings: {
                      maxScore: 8,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                },
                metricType: 0,
                detailsJson: {},
                hitStrategy: [
                  {
                    must: [225031],
                    order: 0,
                    status: 1,
                    scoreSettings: {
                      maxScore: 8,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                ],
                otherHitDetails: [],
              },
              {
                name: '被列入经营异常名录（历史）',
                score: 2,
                isVeto: 0,
                status: 2,
                metricsId: 141556,
                riskLevel: 1,
                totalHits: 1,
                hitDetails: {
                  must: [
                    {
                      source: 'CreditES',
                      status: 2,
                      totalHits: 1,
                      strategyId: 225040,
                      description: '匹配到目标主体 <em class=" - ">【被列入经营异常名录（历史）】 1条记录</em>',
                      dimensionKey: 'OperationAbnormal',
                      strategyName: '被列入经营异常名录（历史）',
                      strategyRole: 1,
                      dimensionName: '被列入经营异常名录（历史）',
                    },
                  ],
                  totalHits: 1,
                  hitStrategy: {
                    must: [225040],
                    order: 0,
                    status: 1,
                    scoreSettings: {
                      maxScore: 2,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                },
                metricType: 0,
                detailsJson: {},
                hitStrategy: [
                  {
                    must: [225040],
                    order: 0,
                    status: 1,
                    scoreSettings: {
                      maxScore: 2,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                ],
                otherHitDetails: [],
              },
              {
                name: '企查分',
                score: 5,
                isVeto: 0,
                status: 2,
                metricsId: 141548,
                riskLevel: 1,
                totalHits: 1,
                hitDetails: {
                  must: [
                    {
                      source: 'EnterpriseLib',
                      status: 2,
                      totalHits: 1,
                      strategyId: 225041,
                      description: '匹配到目标主体 <em class=" - ">【企查分】 1分</em>',
                      dimensionKey: 'QCCCreditRate',
                      strategyName: '企查分',
                      strategyRole: 1,
                      dimensionName: '企查分',
                    },
                  ],
                  totalHits: 1,
                  hitStrategy: {
                    must: [225041],
                    order: 0,
                    status: 1,
                    scoreSettings: {
                      maxScore: 5,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                },
                metricType: 0,
                detailsJson: {},
                hitStrategy: [
                  {
                    must: [225041],
                    order: 0,
                    status: 1,
                    scoreSettings: {
                      maxScore: 5,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                ],
                otherHitDetails: [],
              },
            ],
            groupDefinition: {
              comment: '经营风险',
              groupId: 50013829,
              groupName: '经营风险',
              isVirtual: null,
              riskLevel: 2,
              detailsJson: null,
              parentGroupId: null,
            },
          },
        ],
        '2': [
          {
            level: 2,
            totalHits: 323,
            scoreDetails: [
              {
                name: '法定代表人限制高消费',
                score: 50,
                isVeto: 0,
                status: 2,
                metricsId: 141558,
                riskLevel: 2,
                totalHits: 323,
                hitDetails: {
                  should: [
                    {
                      source: 'CreditES',
                      status: 2,
                      totalHits: 323,
                      strategyId: 225038,
                      description: '匹配到目标主体 <em class=" - "></em><em class=" - ">【限制高消费】 323条记录</em>',
                      dimensionKey: 'RestrictedConsumptionCurrent',
                      strategyName: '限制高消费',
                      strategyRole: 1,
                      dimensionName: '被列入限制高消费名单',
                    },
                  ],
                  totalHits: 323,
                  hitStrategy: {
                    order: 0,
                    should: [225038],
                    status: 1,
                    scoreSettings: {
                      maxScore: 50,
                      riskLevel: 2,
                    },
                    minimum_should_match: 1,
                  },
                  minimum_should_match: 1,
                },
                metricType: 0,
                detailsJson: {},
                hitStrategy: [
                  {
                    order: 0,
                    should: [225038],
                    status: 1,
                    scoreSettings: {
                      maxScore: 50,
                      riskLevel: 2,
                    },
                    minimum_should_match: 1,
                  },
                  {
                    order: 3,
                    should: [225043],
                    status: 1,
                    scoreSettings: {
                      maxScore: 5,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                  {
                    order: 2,
                    should: [225044],
                    status: 1,
                    scoreSettings: {
                      maxScore: 15,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                  {
                    order: 1,
                    should: [225045],
                    status: 1,
                    scoreSettings: {
                      maxScore: 10,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                ],
                otherHitDetails: [],
              },
            ],
            groupDefinition: {
              comment: '经营风险',
              groupId: 50013829,
              groupName: '经营风险',
              isVirtual: null,
              riskLevel: 2,
              detailsJson: null,
              parentGroupId: null,
            },
          },
        ],
      },
      totalScore: 65,
    },
    shouldUpdate: 0,
    updateDate: '2025-03-25T02:42:37.000Z',
    snapshotDate: '2025-03-25T02:42:26.000Z',
    snapshotId: 'c9979d20-0922-11f0-ba95-7b5e0b0dd723',
    createDate: '2025-03-25T02:42:26.000Z',
    creditRate: null,
    type: 0,
    orgModel: {
      modelType: 1,
      category: 1,
      modelId: 3488,
      modelName: '中行山东分行模型031901',
      resultSetting: [
        {
          name: '高风险',
          level: 2,
          maxScore: null,
          mimScore: 75,
        },
        {
          name: '中高风险',
          level: 1,
          maxScore: 74,
          mimScore: 60,
        },
        {
          name: '中风险',
          level: 0,
          maxScore: 59,
          mimScore: 40,
        },
        {
          name: '中低风险',
          level: -1,
          maxScore: 39,
          mimScore: 20,
        },
        {
          name: '良好',
          level: -2,
          maxScore: 19,
          mimScore: null,
        },
      ],
    },
    creditcode: '911100007693890511',
  },
  {
    paid: 0,
    id: 50996904,
    orgId: 208,
    companyId: '84c17a005a759a5e0d875c1ebb6c9846',
    name: '乐视网信息技术（北京）股份有限公司',
    score: 110,
    result: 2,
    operator: 101618,
    orgModelId: 3489,
    modelBranchCode: '85dfa206-e431-42be-bc3b-d13c8686822b',

    details: {
      result: 2,
      modelType: 1,
      totalHits: 407,
      levelGroup: {
        '0': [],
        '1': [
          {
            level: 1,
            totalHits: 83,
            scoreDetails: [
              {
                name: '股权冻结',
                score: 8,
                isVeto: 0,
                status: 2,
                metricsId: 141569,
                riskLevel: 1,
                totalHits: 82,
                hitDetails: {
                  must: [
                    {
                      source: 'CreditES',
                      status: 2,
                      totalHits: 82,
                      strategyId: 225054,
                      description:
                        '匹配到目标主体 <em class=" - ">【股权冻结】 82条记录</em><span class="">，冻结股权数额：<em class=" - ">699,893.48万元</em></span>',
                      dimensionKey: 'FreezeEquity',
                      strategyName: '股权冻结',
                      strategyRole: 1,
                      dimensionName: '股权冻结',
                    },
                  ],
                  totalHits: 82,
                  hitStrategy: {
                    must: [225054],
                    order: 0,
                    status: 1,
                    scoreSettings: {
                      maxScore: 8,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                },
                metricType: 0,
                detailsJson: {},
                hitStrategy: [
                  {
                    must: [225054],
                    order: 0,
                    status: 1,
                    scoreSettings: {
                      maxScore: 8,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                ],
                otherHitDetails: [],
              },
              {
                name: '被列入经营异常名录（历史）',
                score: 2,
                isVeto: 0,
                status: 2,
                metricsId: 141568,
                riskLevel: 1,
                totalHits: 1,
                hitDetails: {
                  must: [
                    {
                      source: 'CreditES',
                      status: 2,
                      totalHits: 1,
                      strategyId: 225057,
                      description: '匹配到目标主体 <em class=" - ">【被列入经营异常名录（历史）】 1条记录</em>',
                      dimensionKey: 'OperationAbnormal',
                      strategyName: '被列入经营异常名录（历史）',
                      strategyRole: 1,
                      dimensionName: '被列入经营异常名录（历史）',
                    },
                  ],
                  totalHits: 1,
                  hitStrategy: {
                    must: [225057],
                    order: 0,
                    status: 1,
                    scoreSettings: {
                      maxScore: 2,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                },
                metricType: 0,
                detailsJson: {},
                hitStrategy: [
                  {
                    must: [225057],
                    order: 0,
                    status: 1,
                    scoreSettings: {
                      maxScore: 2,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                ],
                otherHitDetails: [],
              },
            ],
            groupDefinition: {
              comment: '经营风险',
              groupId: 50013830,
              groupName: '经营风险',
              isVirtual: null,
              riskLevel: 2,
              detailsJson: null,
              parentGroupId: null,
            },
          },
        ],
        '2': [
          {
            level: 2,
            totalHits: 324,
            scoreDetails: [
              {
                name: '法定代表人限制高消费',
                score: 50,
                isVeto: 0,
                status: 2,
                metricsId: 141571,
                riskLevel: 2,
                totalHits: 323,
                hitDetails: {
                  should: [
                    {
                      source: 'CreditES',
                      status: 2,
                      totalHits: 323,
                      strategyId: 225055,
                      description: '匹配到目标主体 <em class=" - "></em><em class=" - ">【限制高消费】 323条记录</em>',
                      dimensionKey: 'RestrictedConsumptionCurrent',
                      strategyName: '限制高消费',
                      strategyRole: 1,
                      dimensionName: '被列入限制高消费名单',
                    },
                  ],
                  totalHits: 323,
                  hitStrategy: {
                    order: 0,
                    should: [225055],
                    status: 1,
                    scoreSettings: {
                      maxScore: 50,
                      riskLevel: 2,
                    },
                    minimum_should_match: 1,
                  },
                  minimum_should_match: 1,
                },
                metricType: 0,
                detailsJson: {},
                hitStrategy: [
                  {
                    order: 0,
                    should: [225055],
                    status: 1,
                    scoreSettings: {
                      maxScore: 50,
                      riskLevel: 2,
                    },
                    minimum_should_match: 1,
                  },
                  {
                    order: 3,
                    should: [225061],
                    status: 1,
                    scoreSettings: {
                      maxScore: 5,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                  {
                    order: 2,
                    should: [225063],
                    status: 1,
                    scoreSettings: {
                      maxScore: 15,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                  {
                    order: 1,
                    should: [225064],
                    status: 1,
                    scoreSettings: {
                      maxScore: 10,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                ],
                otherHitDetails: [],
              },
              {
                name: '企查分',
                score: 50,
                isVeto: 0,
                status: 2,
                metricsId: 141570,
                riskLevel: 2,
                totalHits: 1,
                hitDetails: {
                  must: [
                    {
                      source: 'EnterpriseLib',
                      status: 2,
                      totalHits: 1,
                      strategyId: 225058,
                      description: '匹配到目标主体 <em class=" - ">【企查分】 1分</em>',
                      dimensionKey: 'QCCCreditRate',
                      strategyName: '企查分',
                      strategyRole: 1,
                      dimensionName: '企查分',
                    },
                  ],
                  totalHits: 1,
                  hitStrategy: {
                    must: [225058],
                    order: 0,
                    status: 1,
                    scoreSettings: {
                      maxScore: 50,
                      riskLevel: 2,
                    },
                    minimum_should_match: 1,
                  },
                },
                metricType: 0,
                detailsJson: {},
                hitStrategy: [
                  {
                    must: [225058],
                    order: 0,
                    status: 1,
                    scoreSettings: {
                      maxScore: 50,
                      riskLevel: 2,
                    },
                    minimum_should_match: 1,
                  },
                  {
                    must: [225060],
                    order: 2,
                    status: 1,
                    scoreSettings: {
                      maxScore: 1,
                      riskLevel: 0,
                    },
                    minimum_should_match: 1,
                  },
                  {
                    must: [225062],
                    order: 1,
                    status: 1,
                    scoreSettings: {
                      maxScore: 20,
                      riskLevel: 1,
                    },
                    minimum_should_match: 1,
                  },
                ],
                otherHitDetails: [],
              },
            ],
            groupDefinition: {
              comment: '经营风险',
              groupId: 50013830,
              groupName: '经营风险',
              isVirtual: null,
              riskLevel: 2,
              detailsJson: null,
              parentGroupId: null,
            },
          },
        ],
      },
      totalScore: 110,
    },
    shouldUpdate: 0,
    updateDate: '2025-03-25T02:42:37.000Z',
    snapshotDate: '2025-03-25T02:42:26.000Z',
    snapshotId: 'c999e710-0922-11f0-ba95-7b5e0b0dd723',
    createDate: '2025-03-25T02:42:26.000Z',
    creditRate: null,
    type: 0,
    orgModel: {
      modelType: 1,
      category: 1,
      modelId: 3489,
      modelName: '中行山东分行模型031902',
      resultSetting: [
        {
          name: '高风险',
          level: 2,
          maxScore: null,
          mimScore: 75,
        },
        {
          name: '中高风险',
          level: 1,
          maxScore: 74,
          mimScore: 60,
        },
        {
          name: '中风险',
          level: 0,
          maxScore: 59,
          mimScore: 40,
        },
        {
          name: '中低风险',
          level: -1,
          maxScore: 39,
          mimScore: 20,
        },
        {
          name: '良好',
          level: -2,
          maxScore: 19,
          mimScore: null,
        },
      ],
    },
    creditcode: '911100007693890511',
  },
];

vi.mock('@/shared/services');
vi.mock('vue-router/composables', () => ({
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
  })),
  useRoute: vi.fn(() => ({
    meta: {
      title: '调查历史',
    },
    path: '/investigation/history',
  })),
}));

describe('InvestigationHistoryPage', () => {
  beforeEach(() => {
    vi.mocked<any>(diligenceService.search).mockResolvedValue({
      data: mockList,
      pageIndex: 1,
      pageSize: 10,
      total: mockList.length,
    });
    vi.mocked<any>(userService.getUserList).mockResolvedValue([{ userId: 101618, name: '张三' }]);
  });
  afterEach(() => {
    vi.resetAllMocks();
  });
  it('正确渲染', async () => {
    const wrapper = mount(InvestigationHistoryPage);
    await wrapper.vm.$nextTick();
    await flushPromises(50);
    expect(wrapper.html()).toMatchSnapshot();
  });
});
