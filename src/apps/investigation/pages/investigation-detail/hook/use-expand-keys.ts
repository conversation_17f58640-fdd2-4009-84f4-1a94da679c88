import { difference } from 'lodash';
import { ref } from 'vue';

const expandKeys = ref<string[]>([]);
export const useExpandKeys = (defaultExpanded = false) => {
  const hasAllMetricExpanded = ref(defaultExpanded); // 是否已展开所有指标

  /**
   * 更新展开的 keys
   * @param keys
   * @param force 是否使用传入的 keys 全部覆盖
   */
  const setExpandKeys = (keys: string[] = [], force = true) => {
    if (force) {
      expandKeys.value = keys;
      return;
    }
    // 只增加原数据中不存在的key
    const newKeys = difference(keys, expandKeys.value);
    expandKeys.value.push(...newKeys);
  };

  const onToggleExpand = (flag: boolean, key: string) => {
    const strKey = key.toString();
    if (!flag) {
      expandKeys.value.push(strKey);
    } else {
      expandKeys.value = expandKeys.value.filter((v) => v !== strKey);
    }
  };

  return {
    expandKeys,
    hasAllMetricExpanded,
    setExpandKeys,
    onToggleExpand,
  };
};
