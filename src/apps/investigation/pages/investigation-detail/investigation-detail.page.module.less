@import '@/styles/token.less';

.limitation {
  margin-top: 15px;
  flex: 1;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.downloading {
  display: flex;
  align-items: center;

  .icon {
    display: flex;
    width: 14px;
    height: 14px;
    animation-name: spin;
    animation-duration: 1000ms;
    animation-iteration-count: infinite;
    animation-timing-function: linear; 
    margin-right: 5px;
  }
}

@keyframes spin {
  0% {
    transform:rotate(0deg);
  }

  100% {
    transform:rotate(360deg);
  }
}