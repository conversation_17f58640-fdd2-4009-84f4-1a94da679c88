import { defineComponent } from 'vue';

import { expandTableMap } from './config';

const ExpandTable = defineComponent({
  props: {
    scope: {
      type: Object,
      required: true,
    },
    dimensionKey: {
      type: String,
      required: true,
    },
  },
  render() {
    const component = expandTableMap[this.dimensionKey];
    return <component data={this.scope} dimensionKey={this.dimensionKey} />;
  },
});

export default ExpandTable;
