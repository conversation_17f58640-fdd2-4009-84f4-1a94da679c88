import { shallowMount } from '@vue/test-utils';

import RiskGroup from '..';

vi.mock('vue-router/composables', () => ({
  useRoute: () => ({
    query: { from: 'record' },
  }),
}));

vi.mock('@/apps/investigation/pages/investigation-detail/hook/use-expand-keys', () => ({
  useExpandKeys: () => ({
    expandKeys: ['testId'],
    onToggleExpand: vi.fn(),
  }),
}));

const baseProps = {
  diligenceData: {
    groupDefinition: { groupName: '测试分组' },
    totalHits: 5,
    scoreDetails: [
      {
        metricsId: 'testId',
        name: '测试项',
        totalHits: 3,
        hitDetails: {
          hitStrategy: {
            scoreSettings: {
              maxScore: 80,
              riskLevel: 2,
            },
          },
          should: [{ dimensionKey: 'testKey', totalHits: 2 }],
        },
      },
    ],
  },
  diligenceInfo: {
    companyName: '测试公司',
    keyNo: '123',
    snapshotId: '456',
  },
  riskInfo: {
    orgModelId: 'model1',
  },
};

describe('RiskGroup组件测试', () => {
  test('正常渲染分组信息及风险项', async () => {
    vi.useFakeTimers();
    vi.setSystemTime(new Date('2025-01-01T08:00:00Z'));

    const wrapper = shallowMount(RiskGroup, {
      propsData: baseProps,
      stubs: {
        DynamicCollapseBlock: true,
        RiskHitReason: true,
      },
      parentComponent: {
        provide: {
          riskModelDimensionStrategies: {
            value: { currentModelId: 1 },
          },
        },
      },
    });

    expect(wrapper.html()).toMatchSnapshot();
  });

  test('处理非数组scoreDetails数据', () => {
    const wrapper = shallowMount(RiskGroup, {
      propsData: {
        ...baseProps,
        diligenceData: {
          ...baseProps.diligenceData,
          scoreDetails: null,
        },
      },
    });
    expect(wrapper.findAllComponents({ name: 'DynamicCollapseBlock' }).length).toBe(0);
  });
});
