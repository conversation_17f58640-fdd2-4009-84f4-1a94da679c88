import { useToggle } from '@vueuse/core';
import { defineComponent, nextTick, watch, inject, Ref, ref, onMounted } from 'vue';

import QLoading from '@/components/global/q-loading';
import DimensionContent from '@/apps/investigation/pages/investigation-detail/widgets/dimension-content';
import { openDimensionContentModal } from '@/apps/investigation/pages/investigation-detail/widgets/dimension-content/dimension-content-modal';

import styles from './dynamic-collapse-block.module.less';

const DynamicCollapseBlock = defineComponent({
  name: 'DynamicCollapseBlock',
  props: {
    defaultCollapse: {
      type: Boolean,
      default: true,
    },
    arrow: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    metaList: {
      type: Array,
      default: () => [],
    },
    hitDetail: {
      type: Object,
      default: () => ({}),
    },
    dimensionStrategies: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['toggle'],
  setup(props) {
    const [isCollapse, setCollapse] = useToggle(props.defaultCollapse);

    const handleCollapse = (val?) => {
      setCollapse(val ?? !isCollapse.value);
    };

    // 是不是科创模型
    const isTechModel = inject('isTechModel') as Ref<Record<string, any>>;

    // 是否展示空白文案
    const isShowEmpty = ref(props.hitDetail.totalHits === 1);

    // 是否需要策略结果辅助判断,不需要的默认为true，需要的在Mounted的时候置为true
    const subStaticsVerified = ref(true);

    // 兼容非蔡司的时候点击维度预览打开对应的维度详情
    watch(
      () => props.defaultCollapse,
      (val) => {
        handleCollapse(val);
      }
    );

    const handleOpenRiskTypeModal = (params) => {
      openDimensionContentModal({
        meta: params,
        title: params.title,
      });
    };

    onMounted(() => {
      if (props.hitDetail.totalHits === 1) {
        subStaticsVerified.value = false;
      } else {
        isShowEmpty.value = props.hitDetail.totalHits === 0;
      }
    });

    const handleFetchDone = (isEmpty) => {
      if (props.hitDetail.totalHits === 1) {
        isShowEmpty.value = isEmpty;
        subStaticsVerified.value = true;
      }
    };
    return {
      isCollapse,
      isShowEmpty,
      subStaticsVerified,
      isTechModel,
      setCollapse,
      handleCollapse,
      handleOpenRiskTypeModal,
      handleFetchDone,
    };
  },
  render() {
    return (
      <div
        class={{
          [styles.container]: true,
          [styles.disabled]: this.disabled,
        }}
      >
        <header
          class={styles.header}
          onClick={async () => {
            if (this.disabled) {
              return;
            }
            this.handleCollapse();
            await nextTick();
            this.$emit('toggle', this.isCollapse);
          }}
        >
          {this.$scopedSlots.header?.(this.isCollapse)}
        </header>

        <div v-show={!this.subStaticsVerified && !this.isCollapse} style="height: 160px;">
          <QLoading size="fullsize" />
        </div>

        <div v-show={!this.isCollapse && this.subStaticsVerified}>
          {/* 科创模型下，hitParmCount为0，就展示空白文案 */}
          <div v-show={this.isTechModel && this.isShowEmpty} style="color: #666;margin-bottom: 5px;">
            {this.hitDetail?.detailsJson?.noDataDescription || '该维度未命中有效数据'}
          </div>
          <div v-show={!this.isShowEmpty}>
            {this.metaList.map((meta: any) => {
              return (
                <DimensionContent
                  key={`${meta.key}_${meta.strategyId}`}
                  hitDetail={this.hitDetail}
                  dimensionStrategies={this.dimensionStrategies}
                  meta={meta}
                  needRefresh={!this.isCollapse && this.hitDetail.totalHits > 0} // totalHits为0也展示后，这种情况不需要调用接口
                  onOpenRiskTypeModal={this.handleOpenRiskTypeModal}
                  onFetchDone={(isEmpty) => {
                    this.handleFetchDone(isEmpty);
                  }}
                />
              );
            })}
          </div>
        </div>
      </div>
    );
  },
});

export default DynamicCollapseBlock;
