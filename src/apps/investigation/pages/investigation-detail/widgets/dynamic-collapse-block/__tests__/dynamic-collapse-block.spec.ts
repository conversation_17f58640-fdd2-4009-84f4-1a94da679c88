import { mount } from '@vue/test-utils';

import DynamicCollapseBlock from '../index';

describe('DynamicCollapseBlock', () => {
  test('render', () => {
    const source = {
      meta: {
        source: 'Rover',
        key: 'BlacklistPartnerInvestigation',
        keyNo: 'f94fb9377568a75859f77dae68d5b2d4',
        companyName: '苏州互方得企业咨询管理中心（有限合伙）',
        snapshotId: 'ffde0a50-10ed-11ef-9262-5bfdcf0d80d8',
        from: 'record',
        tkey: '1717146689830_BlacklistPartnerInvestigation',
      },
      arrow: true,
      defaultCollapse: true,
      description: `目标主体 <em class='0'>【与内部黑名单企业存在投资任职关联】</em> 相关风险事项 <em class='0'>未匹配</em> 到风险信息`,
      disabled: true,
      totalHits: 0,
    };

    const wrapper = mount(DynamicCollapseBlock, {
      propsData: {
        meta: source.meta,
        arrow: source.arrow,
        defaultCollapse: source.defaultCollapse,
        description: source.description,
        disabled: source.disabled,
        totalHits: source.totalHits,
      },
    });

    expect(wrapper).toMatchSnapshot();
  });
});
