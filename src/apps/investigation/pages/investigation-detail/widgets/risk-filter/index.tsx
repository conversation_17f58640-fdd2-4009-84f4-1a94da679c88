import { computed, defineComponent, inject, PropType, Ref, ref, watch } from 'vue';
import { isNil } from 'lodash';

import styles from './risk-filter.module.less';

const RiskFilter = defineComponent({
  name: 'RiskFilter',
  props: {
    tabs: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    defaultActiveKey: {
      type: Number,
      default: -1,
    },
  },
  setup(props, { emit }) {
    const finalTabs = computed(() => {
      const prefix = [{ name: '全部', groupId: -1 }];
      const data =
        props.tabs?.map((v: any) => ({
          name: v.groupDefinition.groupName,
          groupId: v.groupDefinition.groupId,
          totalHits: v.totalHits,
          score: v.score,
        })) || [];
      return data.length > 0 ? prefix.concat(data) : [];
    });

    const selectIndex = ref(props.defaultActiveKey);
    const updateSelect = (key: number) => {
      if (key === selectIndex.value) {
        return;
      }
      selectIndex.value = key;
      emit('change', key);
    };

    const isTechModel = inject('isTechModel') as Ref<Record<string, any>>;

    watch(
      () => props.defaultActiveKey,
      (newVal) => {
        updateSelect(newVal);
      }
    );

    return {
      finalTabs,
      isTechModel,
      selectIndex,
      updateSelect,
    };
  },
  render() {
    const countKey = this.isTechModel ? 'score' : 'totalHits';
    return (
      <div class={styles.container}>
        <div class={styles.itemList}>
          {this.finalTabs.map((v: any) => {
            return (
              <div
                class={{ [styles.item]: true, [styles.active]: this.selectIndex === v.groupId }}
                onClick={() => {
                  this.updateSelect(v.groupId);
                }}
              >
                {v.name}
                {isNil(v[countKey]) ? null : <span class={styles.count}>{v[countKey]}</span>}
              </div>
            );
          })}
        </div>
        <div>{this.$slots.extra}</div>
      </div>
    );
  },
});

export default RiskFilter;
