.bg0 {
  background: linear-gradient(180deg, rgba(0, 173, 101, 0.1) 0%, rgba(0, 173, 101, 0.05) 32%, rgba(255, 255, 255, 0) 100%);
}

.bg1 {
  background: linear-gradient(180deg, rgba(18, 139, 237, 0.1) 0%, rgba(18, 139, 237, 0.05) 32%, rgba(255, 255, 255, 0) 100%);
}

.bg2 {
  background: linear-gradient(180deg, rgba(255, 192, 67, 0.1) 0%, rgba(255, 192, 67, 0.05) 32%, rgba(255, 255, 255, 0) 100%);
}

.bg3 {
  background: linear-gradient(180deg, rgba(255, 114, 45, 0.1) 0%, rgba(255, 114, 45, 0.05) 32%, rgba(255, 255, 255, 0) 100%);
}

.bg4 {
  background: linear-gradient(180deg, rgba(179, 0, 0, 0.1) 0%, rgba(179, 0, 0, 0.05) 32%, rgba(255, 255, 255, 0) 100%);
}

.container {
  border-radius: 4px;
  border: 1px solid #EEE;
  padding: 15px 15px 15px 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.switchButton {
  position: absolute;
  top: 12px;
  right: 12px;
}

.sortBy {
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
  color: #333;
  cursor: pointer;

  &.active {
    color: #128bed;

    .icon {
      color: #128bed;
    }
  }

  .icon {
    color: #999;
    font-size: 12px;
  }
}
