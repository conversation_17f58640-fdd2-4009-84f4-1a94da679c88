import { shallowMount } from '@vue/test-utils';

import RiskReview from '..';

describe('RiskReview 组件', () => {
  it('渲染默认视图模式', async () => {
    const wrapper = shallowMount(RiskReview, {
      propsData: {
        riskLevel: -2,
        riskInfo: {},
      },
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.vm.currentViewMode).toBe('riskLevelMode');
    expect(wrapper.vm.sortedRiskLevel).toEqual([2, 1, 0]);
  });

  it('切换视图模式', async () => {
    const wrapper = shallowMount(RiskReview, {
      propsData: {
        riskLevel: -2,
        riskInfo: {},
      },
    });

    await wrapper.vm.$nextTick();
    wrapper.vm.onToggleRiskSort();
    expect(wrapper.vm.currentViewMode).toBe('scoreMode');

    wrapper.vm.onToggleRiskSort();
    expect(wrapper.vm.currentViewMode).toBe('riskLevelMode');
  });

  it('默认 按风险等级 顺序', async () => {
    const wrapper = shallowMount(RiskReview, {
      propsData: {
        riskLevel: -2,
        riskInfo: {
          details: {
            originalHits: [{ riskLevel: 2 }, { riskLevel: 2 }, { riskLevel: 1 }, { riskLevel: 0 }, { riskLevel: 0 }, { riskLevel: 0 }],
          },
        },
      },
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.vm.sortedRiskLevel).toEqual([2, 1, 0]);
  });

  it('按 命中数量 排序', async () => {
    const wrapper = shallowMount(RiskReview, {
      propsData: {
        riskLevel: -2,
        riskInfo: {
          details: {
            originalHits: [{ riskLevel: 2 }, { riskLevel: 2 }, { riskLevel: 1 }, { riskLevel: 0 }, { riskLevel: 0 }, { riskLevel: 0 }],
          },
        },
      },
    });

    await wrapper.vm.$nextTick();
    wrapper.vm.onToggleRiskSort();
    expect(wrapper.vm.sortedRiskLevel).toEqual([0, 2, 1]);
  });

  it('scrollToView 方法应正确触发 scroll 事件', async () => {
    const wrapper = shallowMount(RiskReview, {
      propsData: {
        riskLevel: -2,
        riskInfo: {},
      },
    });

    await wrapper.vm.$nextTick();
    await wrapper.vm.scrollToView('testHash');
    expect(wrapper.emitted().scroll).toEqual([['testHash']]);
  });

  it('scrollToView 方法在没有 hash 值时不应触发 scroll 事件', async () => {
    const wrapper = shallowMount(RiskReview, {
      propsData: {
        riskLevel: -2,
        riskInfo: {},
      },
    });

    const mockEmit = vi.fn();
    wrapper.vm.$emit = mockEmit;

    window.location.hash = '';
    await wrapper.vm.scrollToView();
    expect(mockEmit).not.toHaveBeenCalled();
  });
});
