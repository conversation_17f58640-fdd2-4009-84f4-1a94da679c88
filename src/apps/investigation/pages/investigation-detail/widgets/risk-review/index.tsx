import { computed, defineComponent, nextTick, onMounted, ref } from 'vue';
import { sortBy } from 'lodash';

import DiligenceWarningBlockV2 from '@/components/diligence-waring-block-v2';
import { createTrackEvent } from '@/config/tracking-events';
import QIcon from '@/components/global/q-icon';

import styles from './risk-review.module.less';

type ViewMode = 'riskLevelMode' | 'scoreMode';
const RiskReview = defineComponent({
  name: 'RiskReview',
  props: {
    /**
     * 距离页面顶部的距离
     */
    scrollOffsetTop: {
      type: Number,
      default: 0,
    },
    riskLevel: { type: Number, default: -2 },
    riskInfo: { type: Object, default: () => ({}) },
  },
  setup(props, { emit }) {
    const scrollToView = async (id = window.location?.hash?.replace('#', ''), event?: Event) => {
      await nextTick();
      if (!id) {
        return;
      }
      // 取消默认行为（锚点）
      event?.stopPropagation?.();
      event?.preventDefault?.();
      emit('scroll', id);
    };

    const currentViewMode = ref<ViewMode>('riskLevelMode');
    const isRiskLevelMode = computed(() => currentViewMode.value === 'riskLevelMode');
    const sortedRiskLevel = computed(() => {
      const levelGroup = [2, 1, 0];
      if (!props.riskInfo.details) {
        return levelGroup;
      }
      const sortByScore = sortBy(levelGroup, [(o) => -props.riskInfo.details.originalHits.filter((v) => v.riskLevel === o).length]);
      return isRiskLevelMode.value ? levelGroup : sortByScore;
    });

    onMounted(() => {
      scrollToView();
    });

    const onToggleRiskSort = () => {
      currentViewMode.value = currentViewMode.value === 'riskLevelMode' ? 'scoreMode' : 'riskLevelMode';
    };

    return {
      isRiskLevelMode,
      currentViewMode,
      sortedRiskLevel,
      scrollToView,
      onToggleRiskSort,
    };
  },
  render() {
    const { riskLevel, riskInfo } = this;
    return (
      <div class={[styles.container, styles[`bg${+this.riskLevel + 2}`]]}>
        <DiligenceWarningBlockV2
          levelGroup={this.sortedRiskLevel}
          riskLevel={riskLevel}
          riskInfo={riskInfo}
          isCountSort={!this.isRiskLevelMode}
          scrollToView={(id, item, event) => {
            this.$track(createTrackEvent(6985, '准入排查详情页', item.name, '排查结果'));
            this.scrollToView(id.toString(), event);
          }}
        >
          <div slot="extra" class={styles.switchButton}>
            <div
              class={{
                [styles.sortBy]: true,
                [styles.active]: this.currentViewMode === 'scoreMode',
              }}
              onClick={this.onToggleRiskSort}
            >
              <QIcon class={styles.icon} type="icon-qiehuan" />
              <span>按数量</span>
            </div>
          </div>
        </DiligenceWarningBlockV2>
      </div>
    );
  },
});

export default RiskReview;
