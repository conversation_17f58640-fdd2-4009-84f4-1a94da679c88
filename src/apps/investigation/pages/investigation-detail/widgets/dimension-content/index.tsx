import { computed, defineComponent, inject, PropType, Ref, ref, watch } from 'vue';
import { isEmpty, isFunction } from 'lodash';
import { Select } from 'ant-design-vue';

import RiskTableNext from '@/apps/investigation/pages/investigation-detail/widgets/risk-table-next';
import { useFetchRiskDimension } from '@/hooks/use-fetch-risk-dimension';
import QLoading from '@/components/global/q-loading';
import CommonSearchFilter from '@/components/common/common-search-filter';
import { filterConfigMap } from '@/apps/investigation/pages/investigation-detail/widgets/dimension-content/config';
import RiskHitReasonWrapper from '@/shared/components/risk-hit-reason-wrapper';

import styles from './dimension-content.module.less';
import DimensionChart from '../dimension-chart';
import { HitSupportByChildren } from '../../utils/risk-type.config';

type MetaType = {
  snapshotId: string;
  key: string; // dimensionKey
  strategyId: string;
  [key: string]: any;
};

const DimensionContent = defineComponent({
  name: 'DimensionContent',
  props: {
    hitDetail: {
      type: Object,
      default: () => ({}),
    },
    needRefresh: {
      type: Boolean,
      default: false,
    },
    // 请求参数
    meta: {
      type: Object as PropType<MetaType>,
      default: () => ({}),
    },
    dimensionStrategies: {
      type: Array,
      default: () => [],
    },
  },
  setup(props, { emit }) {
    const isInit = ref(true);

    const hasFilter = computed(() => Object.keys(filterConfigMap).includes(props.meta.key));

    const { filterParams, initialAggs, pagination, setPagination, sort, setSort, result, isLoading, isLoaded, search, isSpecial, isChart } =
      useFetchRiskDimension(props.meta, hasFilter.value); // 风险维度 hook

    const refreshFilterConfig = ref<boolean>(false);
    const currentAggs = computed(() => {
      if (isEmpty(initialAggs.value)) {
        return {};
      }
      // 记录初始筛选项，防止选到空数据后筛选项消失只能刷新页面重新加载
      const newData = Object.keys(initialAggs.value).reduce((acc, cur) => {
        const oldAgg = initialAggs.value[cur] || [];
        const newAgg = result.value?.aggs?.[cur] || [];
        acc[cur] = oldAgg.map((v) => newAgg.find((agg) => agg.key === v.key) || { ...v, doc_count: 0 });
        return acc;
      }, {});
      return newData;
    });

    // 是不是科创模型
    const isTechModel = inject('isTechModel') as Ref<Record<string, any>>;
    const filterConfig = ref([]);
    const getFilterConfig = () => {
      if (!hasFilter.value) return;
      if (isLoading.value) {
        // 等接口加载完了再渲染，否则选项数据是上一次的
        refreshFilterConfig.value = true;
        return;
      }
      filterConfig.value = isFunction(filterConfigMap[props.meta.key])
        ? filterConfigMap[props.meta.key](currentAggs.value)
        : filterConfigMap[props.meta.key];
    };

    getFilterConfig();

    watch(
      () => result.value?.aggs,
      () => {
        if (refreshFilterConfig.value) {
          getFilterConfig();
          refreshFilterConfig.value = false;
        }
      }
    );

    const handleFilterChange = (payload) => {
      filterParams.value = payload;
      setPagination({
        current: 1,
        pageSize: pagination.pageSize,
      });
    };

    const handleSortChange = (payload) => {
      setSort(payload);
      setPagination({
        current: 1,
        pageSize: pagination.pageSize,
      });
    };

    const handleYearChange = (value) => {
      (filterParams.value as any).filters.year = value;
      setPagination({
        current: 1,
        pageSize: pagination.pageSize,
      });
    };

    const totalHits = computed(() => {
      if (isChart.value) {
        // 科创稳定性、连续性维度的数据要手动组合
        if (isSpecial.value || ['ACQUIRED_PATENT_RATIO'].includes(result.value?.displayKey)) {
          return result.value?.chartData?.reduce((sum, cur) => (sum += cur.N), 0);
        }
        return result.value?.chartData?.[0]?.ratio + '%';
      }
      return pagination?.total || props.meta.totalHits;
    });
    watch(
      () => props.needRefresh,
      async (val) => {
        if (val && isInit.value) {
          isInit.value = false;
          await search();
          // 图标维度得额外通过图的数据来判断，通过表格数据很可能出现最近一期命中为0的情况
          if (HitSupportByChildren.includes(result.value?.displayKey)) {
            const calcN = result.value?.chartData?.reduce((sum, cur) => (sum += cur.N), 0);
            emit('fetchDone', !calcN);
          } else {
            emit('fetchDone', !result.value?.data?.length);
          }
        }
      },
      { immediate: true }
    );

    /** 仅显示当前命中的条件 */
    const filteredDimensionStrategies = computed(() => {
      return props.dimensionStrategies.filter(({ dimensionStrategyId }: any) => dimensionStrategyId === props.meta.strategyId);
    });

    return {
      isInit,
      pagination,
      setPagination,
      sort,
      setSort,
      isTechModel,
      result,
      isLoading,
      isLoaded,
      isSpecial,
      search,
      hasFilter,
      filterConfig,
      getFilterConfig,
      handleFilterChange,
      filterParams,
      totalHits,
      initialAggs,
      handleSortChange,
      handleYearChange,
      filteredDimensionStrategies,
    };
  },
  render() {
    let customerScroll: any = null;
    if (this.result?.dataFetchType === 'api' && this.result.chartData) {
      customerScroll = {
        x: 'calc(100% - 30px - 16px)',
        y: this.result?.pagination.total > this.result?.pagination.pageSize ? 250 : 300,
      };
    }
    return (
      <div class={styles.container}>
        <div data-testid="el-loading" style="height: 160px" v-show={this.isLoading && this.result == null}>
          <QLoading size="fullsize" />
        </div>

        <RiskTableNext
          hitDetail={this.hitDetail}
          tkey={(this as any).meta.tkey}
          loading={this.isLoading}
          meta={this.meta}
          rowKey={this.meta.key === 'EmployeeStockPlatform' ? 'recordId' : ''}
          displayKey={this.result?.displayKey}
          totalHits={this.totalHits}
          dataSource={this.result?.data}
          customerScroll={customerScroll}
          sort={this.sort}
          onSortChange={this.handleSortChange}
          pagination={this.result?.pagination}
          onPageChange={this.setPagination}
          onUpdate={this.search}
          onOpenRiskTypeModal={(params) => this.$emit('openRiskTypeModal', params)}
        >
          {this.isTechModel ? null : (
            <span class={styles.hint} slot="extra">
              <RiskHitReasonWrapper
                placement="right"
                hitDetails={this.hitDetail.hitDetails}
                dimensionStrategies={this.filteredDimensionStrategies}
              />
            </span>
          )}

          <CommonSearchFilter
            v-show={this.hasFilter && !isEmpty(this.initialAggs)}
            showSearch={false}
            needReset={false}
            checkSelectBorder={true}
            defaultValue={this.filterParams as any}
            filterConfig={this.filterConfig}
            onChange={this.handleFilterChange}
            onGetOptions={this.getFilterConfig}
          />
          {(this.filterParams as any)?.filters?.year && this.isSpecial ? (
            <Select
              style={{ width: '78px' }}
              value={(this.filterParams as any)?.filters?.year}
              options={(this.initialAggs as any)?.yearOptions || []}
              onChange={this.handleYearChange}
            ></Select>
          ) : null}

          {this.result?.dataFetchType === 'api' && this.result.chartData && !this.isInit ? (
            <DimensionChart
              slot="chart"
              dimensionName={this.meta.strategyName}
              displayKey={this.result?.displayKey}
              data={this.result.chartData}
              onChartClick={(node) => {
                const name = node.name || node.publishyear;
                const matchOption = this.initialAggs.yearOptions.find((item) => item.label === +name);
                this.setPagination({
                  current: 1,
                  pageSize: 10,
                });
                (this.filterParams as any).filters.year = matchOption?.value;
              }}
            ></DimensionChart>
          ) : null}
        </RiskTableNext>
      </div>
    );
  },
});

export default DimensionContent;
