import { defineComponent, ref } from 'vue';

import QModal from '@/components/global/q-modal';
import { createPromiseDialog } from '@/components/promise-dialogs';
import DimensionContent from '@/apps/investigation/pages/investigation-detail/widgets/dimension-content/index';

const DimensionContentModal = defineComponent({
  name: 'DimensionContentModal',
  props: {
    title: {
      type: String,
      default: '',
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const visible = ref(true);

    const handleCancel = async () => {
      visible.value = false;
      emit('resolve', false);
    };

    return {
      visible,
      handleCancel,
    };
  },
  render() {
    const { meta, hitDetail, dimensionStrategies } = this.params;
    return (
      <QModal title={this.params.title} footer={false} size="extra-large" v-model={this.visible} onCancel={this.handleCancel}>
        <DimensionContent
          key={`${meta.key}_${meta.strategyId}`}
          hitDetail={hitDetail}
          dimensionStrategies={dimensionStrategies}
          meta={meta}
          needRefresh={true}
        />
      </QModal>
    );
  },
});

export default DimensionContentModal;

export const openDimensionContentModal = createPromiseDialog(DimensionContentModal);
