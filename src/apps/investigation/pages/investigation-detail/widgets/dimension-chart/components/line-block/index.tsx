import { defineComponent, PropType } from 'vue';

import styles from './line-block.module.less';

const LineBlock = defineComponent({
  name: 'LineBlock',
  props: {
    value: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  },
  render() {
    return (
      <div class={styles.container}>
        {this.value
          .map((item) => {
            let tip = '有';
            let curCss = styles.success;
            if (!item.N) {
              tip = '无';
              curCss = styles.fail;
            }
            return (
              <div
                class={[styles.single, curCss]}
                onClick={() => {
                  if (curCss === styles.fail) {
                    return;
                  }
                  this.$emit('chartClick', item);
                }}
              >
                <div class={styles.year}>{item.publishyear}年</div>
                <div class={styles.tip}>{tip}</div>
              </div>
            );
          })
          .reverse()}
      </div>
    );
  },
});

export default LineBlock;
