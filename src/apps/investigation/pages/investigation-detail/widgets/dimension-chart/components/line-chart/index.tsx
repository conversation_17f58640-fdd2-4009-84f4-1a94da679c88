import { computed, defineComponent, PropType } from 'vue';
import QChart from '@/components/global/q-chart';

const getLineConfig = (xData: any[] = [], yData: any[] = [], displayKey?: string) => {
  return {
    toolbox: {
      feature: {
        saveAsImage: {
          show: false,
        },
      },
    },
    tooltip: {
      show: true,
      padding: [8, 12],
      trigger: 'axis',
      borderWidth: 0,
      axisPointer: {
        type: 'line',
      },
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: {
        color: '#fff',
      },
      formatter: `{b}年<br />${displayKey === 'TALENT_RECRUIT_STABILITY' ? '招聘数量' : '专利申请量'}：{c}`,
    },
    grid: {
      top: 30,
      left: 5,
      right: 28,
      bottom: 5,
      containLabel: true,
    },
    // X轴配置
    xAxis: {
      data: xData,
      type: 'category',
      boundaryGap: false,
      axisLine: {
        onZero: true,
        lineStyle: {
          color: '#bbb',
          width: 1,
        },
      },
      axisTick: {
        show: true,
      },
      axisLabel: {
        color: '#999',
        fontSize: 12,
        lineHeight: 18,
        margin: 10, // 与图的上间距
      },
      splitLine: {
        show: false,
      },
    },
    // Y轴配置
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#999',
        fontSize: 12,
        formatter: '{value}',
        margin: 15,
      },
      splitLine: {
        lineStyle: {
          color: '#f3f3f3',
          type: 'solid',
        },
      },
    },
    series: [
      {
        type: 'line',
        symbol: 'emptyCircle',
        symbolSize: 6,
        label: {
          show: true,
          fontSize: 12,
          lineHeight: 18,
          distance: 4,
          color: '#7397ba',
          position: 'top',
          textBorderWidth: 2,
          textBorderType: 'solid',
        },
        emphasis: {
          scale: 2.5, // hover时的大小
          symbol: 'emptyCircle',
          itemStyle: {
            color: '#fff',
            borderWidth: 2,
          },
        },
        itemStyle: {
          color: '#5b8ff9',
          width: 2,
        },
        lineStyle: {
          color: '#5b8ff9',
          width: 2,
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(91, 143, 249, 0.3)',
              },
              {
                offset: 1,
                color: 'rgba(91, 143, 249, 0.05)',
              },
            ],
            global: false, // 缺省为 false
          },
        },
        data: yData,
      },
    ],
  };
};

const LineChart = defineComponent({
  name: 'LineChart',
  props: {
    value: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    displayKey: {
      type: String,
    },
  },
  setup(props) {
    const chartOptions = computed(() => {
      const dataSource = props.value;
      const xData = dataSource.map((item: any) => item.publishyear);
      const yData = dataSource.map((item) => item.N);
      return getLineConfig(xData, yData, props.displayKey);
    });
    return {
      chartOptions,
    };
  },
  render() {
    return (
      <QChart
        ref="pieChart"
        option={this.chartOptions}
        width="340px"
        height="273px"
        onClick={(_, data) => {
          if (!data.data) {
            return;
          }
          this.$emit('chartClick', data);
        }}
      />
    );
  },
});
export default LineChart;
