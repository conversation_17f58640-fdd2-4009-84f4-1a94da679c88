.container {
  width: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  gap: 14px;

  .single {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 48px;
    padding: 10px 20px;
    border-radius: 2px;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    flex: 1;

    .year {
      color: #333;
    }
  }

  .success {
    background: linear-gradient(90deg, rgba(224, 245, 236, 0.8) 4%, rgba(224, 245, 236, 0.3) 100%);
    cursor: pointer;

    .tip {
      color: #00AD65;
    }
  }

  .fail {
    background: linear-gradient(90deg, #FFECEC 0%, rgba(255, 236, 236, 0.3) 100%);
    cursor: not-allowed;

    .tip {
      color: #FF3A3A;
    }
  }
}
