import { shallowMount } from '@vue/test-utils';

import NationTag from '../index';

describe('NationTag', () => {
  test('render', () => {
    const wrapper = shallowMount(NationTag, {
      propsData: {
        company: {
          TagsInfoV2: [
            {
              Type: 905,
              Name: '发票抬头',
              ShortName: null,
              DataExtend: '',
              TradingPlaceCode: null,
              TradingPlaceName: null,
            },
            {
              Type: 903,
              Name: '存续',
              ShortName: null,
              DataExtend: '',
              TradingPlaceCode: null,
              TradingPlaceName: null,
            },
            {
              Type: 99,
              Name: '曾用名',
              ShortName: null,
              DataExtend: '北京小米科技有限责任公司',
              TradingPlaceCode: null,
              TradingPlaceName: null,
            },
            {
              Type: 505,
              Name: '小微企业',
              ShortName: '',
              DataExtend: '',
              TradingPlaceCode: null,
              TradingPlaceName: null,
            },
            {
              Type: 30,
              Name: '港股VIE',
              ShortName: '小米集团-W',
              DataExtend: '01810.HK',
              TradingPlaceCode: null,
              TradingPlaceName: null,
            },
          ],
          OriginalName: [
            {
              Name: '北京小米科技有限责任公司',
              ChangeDate: 1375113600,
              StartDate: 1267545600,
            },
          ],
        },
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('props: company Special type 622', () => {
    const wrapper = shallowMount(NationTag, {
      propsData: {
        company: {
          TagsInfoV2: [
            {
              Type: 622,
              Name: '央企',
              ShortName: null,
              DataExtend: '',
              TradingPlaceCode: null,
              TradingPlaceName: null,
            },
          ],
          OriginalName: [
            {
              Name: '北京小米科技有限责任公司',
              ChangeDate: 1375113600,
              StartDate: 1267545600,
            },
          ],
        },
      },
    });
    expect(wrapper.text()).toMatchInlineSnapshot(`"央企"`);
  });

  test('props: company Empty', () => {
    const wrapper = shallowMount(NationTag, {
      propsData: {
        company: {
          TagsInfoV2: [],
          OriginalName: [],
        },
      },
    });
    expect(wrapper.html()).toMatchInlineSnapshot(`""`);
  });

  test('props: company No date', () => {
    const wrapper = shallowMount(NationTag, {
      propsData: {
        company: {
          TagsInfoV2: [
            {
              Type: 905,
              Name: '发票抬头',
              ShortName: null,
              DataExtend: '',
              TradingPlaceCode: null,
              TradingPlaceName: null,
            },
            {
              Type: 903,
              Name: '存续',
              ShortName: null,
              DataExtend: '',
              TradingPlaceCode: null,
              TradingPlaceName: null,
            },
            {
              Type: 99,
              Name: '曾用名',
              ShortName: null,
              DataExtend: '北京小米科技有限责任公司',
              TradingPlaceCode: null,
              TradingPlaceName: null,
            },
            {
              Type: 505,
              Name: '小微企业',
              ShortName: '',
              DataExtend: '',
              TradingPlaceCode: null,
              TradingPlaceName: null,
            },
            {
              Type: 30,
              Name: '港股VIE',
              ShortName: '小米集团-W',
              DataExtend: '01810.HK',
              TradingPlaceCode: null,
              TradingPlaceName: null,
            },
          ],
          OriginalName: [
            {
              Name: '北京小米科技有限责任公司',
            },
          ],
        },
      },
    });
    expect(wrapper.text()).toMatchInlineSnapshot(`"曾用名小微企业港股VIE"`);
  });
});
