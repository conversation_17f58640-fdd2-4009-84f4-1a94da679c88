import { computed, defineComponent } from 'vue';
import { Tooltip } from 'ant-design-vue';
import moment from 'moment';

import { getTagsV2 } from '@/utils/company/company-tags';
import QTag from '@/components/global/q-tag';

import styles from './nation-tag.module.less';

// 国有企业名次解释
const renderNationEX = (label) => {
  const TAG_DESCRIPTION = {
    央企: ['指国务院授权国有资产监督管理委员会履行出资人职责的企业。', '依据国务院、中央及地方国资委等相关官方公开披露信息认定央企类型。'],
    央企子公司: [
      '指国有资产监督管理委员会披露的央企旗下的子公司或成员企业。',
      '依据国务院、中央及地方国资委等相关官方公开披露信息认定央企子公司类型。',
    ],
    省管国企: ['指各省国有资产监督管理委员会披露的省属监管企业。', '依据国务院、中央及地方国资委等相关官方公开披露信息认定省管国企类型。'],
    市管国企: [
      '指各直辖市和地级市国有资产监督管理委员会所披露的市属监管企业。',
      '依据国务院、中央及地方国资委等相关官方公开披露信息认定市管国企类型。',
    ],
    国有全资: [
      '指政府部门、机构、事业单位、国有独资企业单独或共同出资，直接或间接合计持股为100%的企业。',
      '依据《企业国有资产交易监督管理办法》规定的股权计算，结合官方公开信息披露，综合认定国有全资类型。',
    ],
    国有独资: [
      '指国家单独出资、由国务院或者地方人民政府授权本级人民政府国有资产监督管理机构履行出资人职责的企业。',
      '依据《企业国有资产交易监督管理办法》规定的股权计算，结合官方公开信息披露，综合认定国有独资类型。',
    ],
    国有控股: [
      '是指在企业的全部资本中，国家资本（股本）所占比例大于50%的企业。',
      '依据《企业国有资产交易监督管理办法》规定的股权计算，结合官方公开信息披露，综合认定国有控股类型。',
    ],
  };

  if (!TAG_DESCRIPTION[label]) {
    return null;
  }

  return (
    <div>
      {TAG_DESCRIPTION[label].map((description: string, index: number) => {
        return (
          <div class={styles.nationEx} key={index}>
            <div class={styles.labelDefine}>
              {' '}
              <span>{index ? '来源' : '定义'}</span>
            </div>
            <div>{description}</div>
          </div>
        );
      })}
    </div>
  );
};

const NationTag = defineComponent({
  name: 'NationTag',
  props: {
    company: {
      type: Object,
      required: true,
    },
  },

  setup(props) {
    const { TagsInfoV2: allTags } = props.company;
    /** 默认标签 */
    const defaultTags = computed(() => {
      const includedTags = [
        99, // 曾用名
      ];
      const tags = getTagsV2(allTags.filter((item) => includedTags.includes(item.Type)));
      return tags;
    });
    const extraTags = computed(() => {
      const excludedTags = [
        99, // 曾用名
        903, // 存续
        905, // 发票抬头
      ];
      const nationComTag = getTagsV2(allTags.filter((item) => !excludedTags.includes(item.Type)));
      return nationComTag;
    });

    return {
      defaultTags,
      extraTags,
    };
  },

  render() {
    if (this.defaultTags.length === 0 && this.extraTags.length === 0) {
      return null;
    }

    // const nationComTag = getTagsV2(company.TagsInfoV2.filter((item) => item.Type === 622));
    // if ((oriName && oriName?.length > 0) || (nationComTag && nationComTag.length > 0)) {

    const { company } = this as any;
    const oriName = company.OriginalName;
    return (
      <div>
        <ul
          style={{
            display: 'flex',
            gap: '5px',
            marginTop: '6px',
          }}
        >
          {/* 曾用名 */}
          {this.defaultTags.map((tag) => {
            return (
              <li key={tag.label}>
                {/* {tag.label} */}
                <Tooltip trigger="hover" placement="bottomLeft" overlayClassName={styles.oriName}>
                  <div slot="title">
                    {oriName.map((nameObj) => {
                      const s = nameObj.StartDate ? moment(nameObj.StartDate * 1000).format('YYYY-MM') : ' -';
                      const e = nameObj.ChangeDate ? moment(nameObj.ChangeDate * 1000).format('YYYY-MM') : ' - ';
                      return (
                        <div class={styles.timeRow}>
                          {nameObj.Name}{' '}
                          <span class={styles.time}>
                            ({s} 至 {e})
                          </span>
                        </div>
                      );
                    })}
                  </div>
                  <div
                    style={{
                      height: '22px',
                      lineHeight: '22px',
                      padding: '0 6px',
                      color: tag.styles.color,
                      background: tag.styles.backgroundColor,
                      fontSize: '12px',
                      borderRadius: '2px',
                      cursor: 'pointer',
                    }}
                  >
                    {tag.label}
                  </div>
                </Tooltip>
              </li>
            );
          })}

          {this.extraTags.map((tag) => {
            return (
              <li key={tag.label}>
                <div
                  style={{
                    height: '22px',
                    lineHeight: '22px',
                    padding: '0 6px',
                    color: tag.styles.color,
                    background: tag.styles.backgroundColor,
                    fontSize: '12px',
                    borderRadius: '2px',
                  }}
                  key={tag.label}
                >
                  <span domPropsInnerHTML={tag.label.replace(/\|/g, '<span>|</span>')}></span>
                </div>
              </li>
            );
          })}
        </ul>
      </div>
    );
  },
});
export default NationTag;
