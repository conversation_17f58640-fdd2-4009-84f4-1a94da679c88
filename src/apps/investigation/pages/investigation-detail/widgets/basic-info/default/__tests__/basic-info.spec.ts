import { mount } from '@vue/test-utils';

import BasicInfoDefault from '..';

describe('BasicInfo', () => {
  test('render', () => {
    const wrapper = mount(BasicInfoDefault, {
      propsData: {
        company: {
          TagsInfoV2: [],
          ContactInfo: {
            WebSite: [
              {
                Name: null,
                Url: 'www.mi.com',
                IsIcp: true,
                Year: '2022',
              },
            ],
            PhoneNumber: '010-60606666',
            Email: '<EMAIL>',
            TelSource: '3',
            TelSourceV2: '1',
            EmailSource: '2022',
            TelSourceDesc: '企业自主上传',
            TelTagsDesc: '',
          },

          VTList: [
            {
              k: '13501279672',
              v: '1',
              d: '正常',
            },
            {
              k: '18210579550',
              v: '0',
              d: '空号',
            },
          ],

          HisTelList: [
            {
              Tel: '010-69630728',
              SourceFrom: '2022',
              SourceFromV2: '2022',
              TelSourceDesc: '2022年报',
              TelTagsDesc: '',
              Status: '3',
            },
            {
              Tel: '13501279672',
              SourceFrom: '2013',
              SourceFromV2: '2013',
              TelArea: {
                Province: '北京',
                City: '',
              },
              TelSourceDesc: '2013年报',
              TelTagsDesc: '',
              Status: '1',
            },
            {
              Tel: '4006898688',
              SourceFrom: '6',
              SourceFromV2: '10',
              TelTags: 10,
              TelSourceDesc: '互联网',
              TelTagsDesc: '客服',
              Status: '3',
            },
            {
              Tel: '18210579550',
              SourceFrom: '6',
              SourceFromV2: '10',
              TelArea: {
                Province: '北京',
                City: '',
              },
              TelSourceDesc: '互联网',
              TelTagsDesc: '',
              Status: '0',
            },
          ],

          KeyNo: '9cce0780ab7644008b73bc2120479d31',
          Name: '小米科技有限责任公司',
          RecCap: '185000万元',
          RegistCapi: '185000万元',
          ShortStatus: '存续',
          Address: '北京市海淀区西二旗中路33号院6号楼6层006号',
          CreditCode: '91110108551385082Q',
          MultipleOper: {
            OperType: 1,
            OperList: [
              {
                Org: 2,
                KeyNo: 'p1910534b4ae98fea35ddbeb1d61cd44',
                Name: '雷军',
                HasImage: true,
                CompanyCount: 86,
                ImageUrl: 'https://image.qcc.com/person/p1910534b4ae98fea35ddbeb1d61cd44.jpg?x-oss-process=style/person_120',
                IsAC: 1,
              },
            ],
          },
          StartDate: 1267545600,

          LatestAnnualReportAddrInfo: [],
          SameTelAddressList: [
            {
              A: '010-60606666',
              K: '74785652526d5629',
              C: '100',
            },
            {
              A: '010-69630728',
              K: '3f529b8edce39959',
              C: '50',
            },
          ],
          Oper: {
            Org: 2,
            KeyNo: 'p1910534b4ae98fea35ddbeb1d61cd44',
            Name: '雷军',
            HasImage: true,
            CompanyCount: 86,
            OperType: 1,
            ImageUrl: 'https://image.qcc.com/person/p1910534b4ae98fea35ddbeb1d61cd44.jpg?x-oss-process=style/person_120',
            IsAC: 1,
          },
          Staffs: {
            c: 35314,
            s: '2023',
          },
          TaxNo: '91110108551385082Q',
          QccIndustry: {
            Ac: 'QCC08',
            An: '通讯服务',
            Bc: 'QCC0802',
            Bn: '通信设备及技术服务',
            Cc: 'QCC080201',
            Cn: '通信设备',
            Dc: '08020103',
            Dn: '通信终端及配件',
          },
          TermStart: 1267545600,
          TeamEnd: 0,
          Scope:
            '一般项目：技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广；货物进出口；技术进出口；进出口代理；通讯设备销售；厨具卫具及日用杂品批发；厨具卫具及日用杂品零售；个人卫生用品销售；卫生用品和一次性使用医疗用品销售；日用杂品销售；日用百货销售；日用品销售；化妆品批发；化妆品零售；第一类医疗器械销售；第二类医疗器械销售；玩具销售；体育用品及器材零售；体育用品及器材批发；文具用品零售；文具用品批发；鞋帽批发；鞋帽零售；服装服饰批发；服装服饰零售；钟表销售；眼镜销售（不含隐形眼镜）；针纺织品销售；家用电器销售；日用家电零售；家具销售；礼品花卉销售；农作物种子经营（仅限不再分装的包装种子）；照相机及器材销售；照相器材及望远镜批发；照相器材及望远镜零售；工艺美术品及收藏品零售（象牙及其制品除外）；工艺美术品及礼仪用品销售（象牙及其制品除外）；计算机软硬件及辅助设备零售；计算机软硬件及辅助设备批发；珠宝首饰零售；珠宝首饰批发；食用农产品批发；食用农产品零售；宠物食品及用品批发；宠物食品及用品零售；电子产品销售；摩托车及零配件零售；摩托车及零配件批发；电动自行车销售；助动自行车、代步车及零配件销售；自行车及零配件零售；自行车及零配件批发；单用途商业预付卡代理销售；商用密码产品销售；五金产品批发；五金产品零售；建筑材料销售；仪器仪表修理；计算机及办公设备维修；办公设备销售；会议及展览服务；组织文化艺术交流活动；广告设计、代理；广告制作；广告发布；摄影扩印服务；票务代理服务；通讯设备修理；移动终端设备制造；可穿戴智能设备制造。（除依法须经批准的项目外，凭营业执照依法自主开展经营活动）许可项目：第三类医疗器械经营；网络文化经营；出版物零售；出版物批发；食品销售；药品零售；广播电视节目制作经营；第一类增值电信业务；第二类增值电信业务；在线数据处理与交易处理业务（经营类电子商务）；基础电信业务；互联网信息服务。（依法须经批准的项目，经相关部门批准后方可开展经营活动，具体经营项目以相关部门批准文件或许可证件为准）（不得从事国家和本市产业政策禁止和限制类项目的经营活动。）',
          Scale: '大型企业',
          CompanyRevenue: {
            Revenue: '2709.7亿元',
            EndDate: '2023-12-31',
          },
          IndustryV3: {
            IndustryCode: 'M',
            Industry: '科学研究和技术服务业',
            SubIndustryCode: '75',
            SubIndustry: '科技推广和应用服务业',
            MiddleCategoryCode: '751',
            MiddleCategory: '技术推广服务',
            SmallCategoryCode: '7519',
            SmallCategory: '其他技术推广服务',
          },
          EconKind: '有限责任公司（自然人投资或控股）',
          Type: 0,
          Registcapiamount: {
            Key: 19,
            Value: '185000',
            KeyDesc: '注册资本金额',
          },
          CommonList: [
            {
              Key: 40,
              Value: '[{"k":"010-60606666","v":""}]',
            },
          ],
        },
      },
    });
    expect(wrapper).toMatchSnapshot();
  });
});
