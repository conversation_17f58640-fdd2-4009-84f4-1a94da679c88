@import '@/styles/token.less';

.container {
  .alert-danger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 22px;
    padding: 20px;
    border: 1px solid #e4eef6;
    background-color: #fff;

    em {
      color: @qcc-color-red-500;
    }

    .title {
      display: flex;
      align-items: center;
      font-weight: bold;

      .icon {
        color: @qcc-color-red-500;
        font-size: 18px;
        margin-right: 5px;
      }
    }

    .row {
      display: flex;
      align-items: center;

      &:not(:first-child) {
        margin-top: 15px;
      }
    }

    aside {
      display: flex;
      margin-left: 40px;
      align-items: center;
      justify-content: center;

      .detail-button {
        background-color: @qcc-color-red-500;
        color: #fff;
        padding: 5px 12px;
        border-radius: 2px;
        white-space: nowrap;

        &:hover {
          background-color: #e03333;
        }
      }
    }
  }

  .contract-breach {
    .cell {
      min-width: 210px;
      display: inline-block;
    }

    .row {
      .title {
        width: 172px;
      }
    }

    .value {
      color: @qcc-color-red-500;
      margin-left: 20px;
    }

    .content {
      > div:not(:first-child) {
        margin-top: 5px;
      }

      a {
        margin-left: 5px;
        color: @qcc-color-red-500;

        &:hover {
          color: #e03333;
        }
      }
    }
  }

  .company-shell {
    .row {
      .title {
        min-width: 82px;
      }
    }

    .content {
      > div:not(:first-child) {
        margin-top: 5px;
      }
    }

    .count {
      margin: 0 20px;
      font-size: 12px;
      line-height: 22px;
      width: 60px;
      height: 22px;
      background: #ffecec;
      border-radius: 2px;
      color: @qcc-color-red-500;
      text-align: center;
    }
  }

  .news-feed {
    position: relative;

    .loading {
      position: absolute;
      inset: 0;
      background-color: rgba(255, 255, 255, 0.5);
      z-index: 1;
    }

    .pagination {
      margin: 15px 0 0;
      display: flex;
      justify-content: flex-end;
    }
  }

  .highlight {
    em {
      color: @qcc-color-red-500;
    }
  }
}

.jobbox{
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  gap: 5px;
}

.jobPostion {
  padding: 0 4px;
  white-space: nowrap;
  height: 20px;
  line-height: 20px;
  font-size: 12px;
  color: #bb833d;
  background: #f6f0e7;
  border-radius: 2px;
}

.boxWapper{
  margin: -9px -10px;

  .pathBox{
    display: flex;
    border-bottom: 1px solid #E4EEF6;

    >div{
      padding: 9px 10px;
    }

    .relationType{
      display: flex;
      align-items: center;
      width: 150px;
      border-right: 1px solid #E4EEF6;
    }

    .path{
      flex: 1;
    }

    .tupu{
      display: flex;
      align-items: center;
      width: 60px;
      border-left: 1px solid #E4EEF6;
    }

    &:last-child{
      border-bottom: none;
    }
  }
}

.contentSpecial{
  padding: 0 15px;

  .dimenisionTip{
    color: #666;
  }

  .dimenision{
    .dimenisionTitle{
      margin-top: 10px;
      color: #666;
    }
  }
}

.filter {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.tableWrapper {
  padding-top: 4px;

  .heading {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    position: relative;
    padding-left: 16px;

    strong {
      font-weight: 500;
    }

    .left {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    &::before {
      content: '';
      position: absolute;
      width: 4px;
      height: 16px;
      border-radius: 2px;
      background: #128BED;
      top: 50%;
      left: 5px;
      transform: translateY(-50%);
    }
  }

  .techDesc{
    margin-bottom: 4px;
    display: flex;
    justify-content: space-between;
    color: #666;
    line-height: 22px;
  }

  // 纯文本描述
  .description {
    font-size: 14px;
    line-height: 22px;
    color: #666;
  }

  .description + .table {
    margin-top: 4px;
  }

  .table{
    display: flex;
    gap: 16px;
  }
}

.table-description {
  color: #999;
  line-height: 22px;
  margin-bottom: 5px;

  em {
    font-weight: 500;
    color: #333;
  }
}

.overflowText {
  max-width: 478px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.riskType + .riskType {
  margin-top: 4px;
}

.jobPostion {
  padding: 0 4px;
  white-space: nowrap;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  color: #bb833d;
  background: #f6f0e7;
  border-radius: 2px;

    &.tag-blue {
      background: #E2F1FD;
      color: #128BED;
    }

    &.tag-gold {
      background: #F6F0E7;
      color: #BB833D;
    }

    &.tag-default {
      background: #ECEEFF;
      color: #6171FF;
    }
}

.equityFinancing{
  display: flex;
  border-bottom: 1px dashed #EAEAEA ;

  &:first-child{
    .institutions,
    .investorEntity {
      padding-top: 9px;
    }
  }

  &:last-child{
    border-bottom: none;

    .institutions,
    .investorEntity {
      padding-bottom: 9px;
    }
  }

  .investorEntity{
    flex: 1;
    padding: 4px 9px 4px 0;
    border-right: 1px solid #EAEAEA;

    :global {
      a {
          white-space: pre-wrap;
          margin-right: 4px;
      }
    }
  }

  .institutions{
    padding: 4px 0 4px 9px;
    width: 339px,
  }
}
