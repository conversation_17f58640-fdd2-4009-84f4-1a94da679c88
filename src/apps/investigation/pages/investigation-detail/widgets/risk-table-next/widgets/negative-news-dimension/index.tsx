import { defineComponent, PropType } from 'vue';

import RiskTag from '@/shared/components/risk-tag';
import QTag from '@/components/global/q-tag';
import { DYNAMIC_TOPIC_NAME_MAP } from '@/shared/constants/dynamic-topic-name-map.constant';
import formatDate from '@/utils/format/date';
import QIcon from '@/components/global/q-icon';
import QRichTableEmpty from '@/components/global/q-rich-table/components/empty';

import styles from './negative-news-dimension.module.less';

const NegativeNewsDimension = defineComponent({
  name: 'NegativeNewsDimension',
  props: {
    dataSource: {
      type: Array as PropType<Record<string, any>[]>,
      required: true,
    },
    rowKey: {
      type: String,
      required: true,
    },
    from: {
      type: String,
      required: false,
    },
    meta: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}) as Record<string, any>,
    },
  },
  render() {
    if (!this.dataSource.length) {
      return <QRichTableEmpty size="100px">暂无数据</QRichTableEmpty>;
    }
    return (
      <div class={styles.list}>
        {this.dataSource.map((item) => {
          const codeDesc = item?.codedesc ?? [];
          const newsTags = item?.tagsnew ?? [];
          const tags = [...codeDesc, ...newsTags].filter(Boolean).map((tag) => `#${tag}`);

          return (
            <a
              class={styles.item}
              href={`/embed/post-news?newsId=${item.newsid}&keyNo=${this.meta.keyNo}&title=${item.title}`}
              target="_blank"
              key={item[this.rowKey]}
              onClick={() => this.$emit('clickNews')}
            >
              <header class={styles.header}>
                {item.dimensionLevel && (
                  <div class={styles.warningTag}>
                    <RiskTag level={item.dimensionLevel} />
                  </div>
                )}
                {item.needNewTag && (
                  <QTag type="warning" size="small" style={{ marginRight: '5px' }}>
                    新增
                  </QTag>
                )}
                <h3>{item.title}</h3>
                {item.dimensionKey?.split(',').map((k) => <div class={styles.newsTag}> {DYNAMIC_TOPIC_NAME_MAP[k]} </div>)}
                {this.from !== 'dashboard' ? (
                  <span class={styles.otherInfo}>
                    <span>{item.source || '-'}</span>
                    <span class={styles.divider}></span>
                    <span>{formatDate(item.publishtime)}</span>
                  </span>
                ) : null}
              </header>

              <div class={styles.tags}>
                {tags.map((tag, index) => (
                  <span key={index}>{tag}</span>
                ))}
              </div>

              {item.companyName && (
                <div class={styles.drawFooter}>
                  <span class={styles.relatedCompany}>
                    关联企业：
                    <q-entity-link coy-obj={{ KeyNo: item.companyId, Name: item.companyName }} />
                  </span>
                  <span class={styles.otherInfo}>
                    <span>{item.source || '-'}</span>
                    <span class={styles.divider}></span>
                    <span>{formatDate(item.publishtime)}</span>
                  </span>
                </div>
              )}
              {item.summary ? (
                <div class={styles.summaryBlock}>
                  <QIcon type="icon-summary" />
                  <span class={styles.divider}></span>
                  <div class={styles.description}>{item.summary}</div>
                </div>
              ) : null}
            </a>
          );
        })}
      </div>
    );
  },
});

export default NegativeNewsDimension;
