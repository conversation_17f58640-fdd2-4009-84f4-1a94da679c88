import { computed, defineComponent, PropType, reactive } from 'vue';

import QRichTable, { IQRichTableColumn, IQRichTablePagination } from '@/components/global/q-rich-table';

/**
 * 内存分页表格
 */
const VirtualPaginationTable = defineComponent({
  name: 'VirtualPaginationTable',
  props: {
    columns: {
      type: Array as PropType<IQRichTableColumn[]>,
      required: true,
      default: () => [],
    },
    dataSource: {
      type: Array as PropType<any[]>,
      required: false,
    },
    rowKey: {
      type: [String, Function],
    },
    pagination: {
      type: Object as PropType<Partial<IQRichTablePagination>>,
      default: () => ({}),
    },
  },
  setup(props) {
    // 内存分页
    const pageInfo = reactive({
      total: props.dataSource?.length ?? 0,
      current: 1,
      defaultCurrent: 1,
      defaultPageSize: 10,
      showQuickJumper: true,
      showSizeChanger: true,
      pageSize: 10,
      pageSizeOptions: ['10', '30', '50', '100'],
      ...props.pagination,
    });
    const tableData = computed(() =>
      (props.dataSource ?? []).slice((pageInfo.current - 1) * pageInfo.pageSize, pageInfo.current * pageInfo.pageSize)
    );
    const handlePageChange = (current: number, pageSize: number) => {
      pageInfo.current = current;
      pageInfo.pageSize = pageSize;
    };

    const paginationConfig = computed(() => {
      return {
        ...pageInfo,
        onChange: handlePageChange,
        onShowSizeChange: handlePageChange,
      };
    });

    return {
      tableData,
      paginationConfig,
      // pageInfo,
      // pagination,
    };
  },
  render() {
    return (
      <QRichTable
        pagination={this.paginationConfig}
        dataSource={this.tableData}
        columns={this.columns}
        rowKey={this.rowKey}
        scopedSlots={this.$scopedSlots}
      />
    );
  },
});

export default VirtualPaginationTable;
