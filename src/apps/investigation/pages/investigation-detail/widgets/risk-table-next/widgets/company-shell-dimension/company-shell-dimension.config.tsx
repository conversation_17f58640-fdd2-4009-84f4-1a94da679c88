import QEntityLink from '@/components/global/q-entity-link';

const SIMILAR_BASIC_REG_INFO = [
  {
    title: '企业名称',
    dataIndex: 'reCompanyName',
    customRender: (text, record) => {
      return <QEntityLink coyObj={{ KeyNo: record.reKeyNo, Name: record.reCompanyName }}></QEntityLink>;
    },
  },
  {
    title: '法定代表人',
    dataIndex: 'reOperName',
    customRender: (text, record) => {
      return <QEntityLink coyObj={{ KeyNo: record.reOperkeyNo, Name: record.reOperName }}></QEntityLink>;
    },
  },
  {
    title: '董监高',
    dataIndex: 'reNameCollect',
  },
  {
    title: '成立日期',
    dataIndex: 'reStartDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '注册地址',
    dataIndex: 'reAddress',
  },
];

const DECISION_OFFICE_INFO = [
  {
    title: '列入日期',
    dataIndex: 'addDate',
    width: 260,
    customRender: (text, row) => {
      // if (row.addDate) {
      //   return formatDate(row.addDate * 1000);
      // }
      // if (row.adddate) {
      //   return formatDate(row.adddate);
      // }
      // return '-';
      // TODO
      return text;
    },
  },
  {
    title: '作出决定机关',
    dataIndex: 'decisionOffice',
    customRender: (text, row) => {
      if (row.decisionOffice) {
        return row.decisionOffice;
      }
      if (row.decisionoffice) {
        return row.decisionoffice;
      }
      return '-';
    },
    // width: 100,
  },
  {
    title: '列入经营异常名录原因',
    dataIndex: 'addReason',
    width: 431,
    customRender: (text, row) => {
      if (row.addReason) {
        return row.addReason;
      }
      if (row.addreason) {
        return row.addreason;
      }
      return '-';
    },
  },
];

// 空壳扫描-频繁变更通用表格配置
const CommonFrequentChangesColumns = [
  {
    title: '变更日期',
    dataIndex: 'changeDate',
    width: 150,
  },
  {
    title: '变更前',
    dataIndex: 'beforeContent',
    customRender: (entities?: { keyNo: string | null; name: string }[]) => {
      if (Array.isArray(entities)) {
        const coyArr = entities.map(({ keyNo, name }) => ({
          KeyNo: keyNo || undefined,
          Name: name,
        }));
        return <QEntityLink coyArr={coyArr}></QEntityLink>;
      }
      return '-';
    },
  },
  {
    title: '变更后',
    dataIndex: 'afterContent',
    customRender: (entities?: { keyNo: string | null; name: string }[]) => {
      if (Array.isArray(entities)) {
        const coyArr = entities.map(({ keyNo, name }) => ({
          KeyNo: keyNo || undefined,
          Name: name,
        }));
        return <QEntityLink coyArr={coyArr}></QEntityLink>;
      }
      return '-';
    },
  },
];

export const DIMENSION_COLUMN_MAP = {
  // 6.2.2 注册基础信息重叠
  注册基础信息重叠: SIMILAR_BASIC_REG_INFO,
  // 6.2.3 注册信息相似度过高
  注册信息相似度过高: SIMILAR_BASIC_REG_INFO,
  // 6.2.4 一人多企
  一人多企: SIMILAR_BASIC_REG_INFO,

  // 6.2.5 一址多企
  一址多企: [
    {
      title: '公司名称',
      dataIndex: 'companyName',
      customRender: (text, record) => {
        return <QEntityLink coyObj={{ KeyNo: record.keyNo, Name: record.companyName }}></QEntityLink>;
      },
    },
    {
      title: '法定代表人',
      dataIndex: 'reOperName',
      customRender: (text, record) => {
        return <QEntityLink coyObj={{ KeyNo: record.operKeyNo, Name: record.operName }}></QEntityLink>;
      },
    },
    {
      title: '成立日期',
      dataIndex: 'startDate',
      scopedSlots: { customRender: 'date' },
    },
    {
      title: '注册地址',
      dataIndex: 'address',
    },
  ],

  // 6.2.6 无法联系该企业 √
  无法联系该企业: DECISION_OFFICE_INFO,
  // 6.2.7 未公示年报
  未公示年报: DECISION_OFFICE_INFO,

  // 6.2.8 企业自然人变更时间集中
  // TODO 未找到对应的表格配置
  企业自然人变更时间集中: [
    //   {
    //     title: '股东',
    //     dataIndex: 'partnerName',
    //     width: 240,
    //     customRender: (text) => {
    //       return text;
    //     },
    //     // format(row) {
    //     //   const partnerStr = new StringBuilder();
    //     //   partnerStr.append(
    //     //     '<span style="display: inline-block;width: 40px;height: 40px;line-height: 40px;overflow: hidden;border-radius: 6px !important;float: left;margin-right: 10px;">'
    //     //   );
    //     //   if (row.imageUrl) {
    //     //     partnerStr.append(
    //     //       `<img src="${row.imageUrl}" style="width: 100%;border-radius: 6px;height: 100%;object-fit: contain;vertical-align: middle;">`
    //     //     );
    //     //   } else {
    //     //     partnerStr.append(
    //     //       `<div style="display:inline-block;width: 100%;border-radius: 6px !important;height: 100%;object-fit: contain;vertical-align: middle;text-align:center;color:white;font-size:16px;background:${addCustomerImg()}">${row.partnerName.substring(
    //     //         0,
    //     //         1
    //     //       )}</div>`
    //     //     );
    //     //   }
    //     //   partnerStr.append('</span>');
    //     //   if (row.partnerName) {
    //     //     if (row.keyNo) {
    //     //       if (getCorpByOrg(row.org)) {
    //     //         partnerStr.append(
    //     //           `<a class="_clickContent" style="display:inline-block;margin-top:10px;" href="/beneficaryDetail?personId=${
    //     //             row.keyNo
    //     //           }&personName=${encodeURIComponent(row.partnerName)}" target="_blank">${row.partnerName}</a>`
    //     //         );
    //     //       } else {
    //     //         partnerStr.append(
    //     //           `<a class="_clickContent" style="display:inline-block;margin-top:10px;" href="/companyDetail?keyNo=${row.keyNo}" target="_blank">${row.partnerName}</a>`
    //     //         );
    //     //       }
    //     //     } else {
    //     //       partnerStr.append(
    //     //         `<span style="cursor:auto;display:inline-block;margin-top:10px;">${row.partnerName}</span>`
    //     //       );
    //     //     }
    //     //   }
    //     //   return partnerStr.toString();
    //     // },
    //   },
    //   {
    //     title: '持股比例',
    //     dataIndex: 'stockPercent',
    //     // width: 60,
    //   },
    //   {
    //     title: '股东类型',
    //     dataIndex: 'type',
    //     width: 70,
    //   },
    //   {
    //     title: '参股日期',
    //     dataIndex: 'inDate',
    //     scopedSlots: { customRender: 'date' },
    //   },
    //   {
    //     title: '退出日期',
    //     dataIndex: 'changeDate',
    //     // minWidth: 75,
    //     // emptyStr: '-',
    //     format(row) {
    //       // return toDash(long2DateStr(row.changeDate));
    //       return row;
    //     },
    //   },
  ],
  histShareHolderList: [
    {
      title: '股东',
      width: 240,
      customRender: (record) => {
        return <QEntityLink coyObj={{ KeyNo: record.keyNo, Name: record.partnerName }}></QEntityLink>;
      },
    },
    {
      title: '持股比例',
      dataIndex: 'stockPercent',
    },
    {
      title: '股东类型',
      dataIndex: 'type',
    },
    {
      title: '参股日期',
      width: 150,
      dataIndex: 'inDate',
      scopedSlots: { customRender: 'date' },
    },
    {
      title: '退出日期',
      width: 150,
      dataIndex: 'changeDate',
      scopedSlots: { customRender: 'date' },
    },
  ],
  historyEmployeeList: [
    {
      title: '姓名',
      width: 240,
      customRender: (record) => {
        return <QEntityLink coyObj={{ KeyNo: record.keyNo, Name: record.employeeName }}></QEntityLink>;
      },
    },
    {
      title: '职务',
      dataIndex: 'job',
    },
    {
      title: '任职日期',
      width: 150,
      dataIndex: 'inDate',
      scopedSlots: { customRender: 'date' },
    },
    {
      title: '卸职日期',
      width: 150,
      dataIndex: 'changeDate',
      scopedSlots: { customRender: 'date' },
    },
  ],
  historyOperList: [
    {
      title: '姓名',
      customRender: (record) => {
        return <QEntityLink coyObj={{ KeyNo: record.keyNo, Name: record.operName }}></QEntityLink>;
      },
    },
    {
      title: '任职日期',
      width: 150,
      dataIndex: 'inDate',
      scopedSlots: { customRender: 'date' },
    },
    {
      title: '卸任日期',
      width: 150,
      dataIndex: 'changeDate',
      scopedSlots: { customRender: 'date' },
    },
  ],

  '疑似异常变更 - 企业法定代表人频繁变更': CommonFrequentChangesColumns,
  '疑似异常变更 - 大股东频繁变更': CommonFrequentChangesColumns,
  '疑似异常变更 - 实际控制人频繁变更': CommonFrequentChangesColumns,
  '疑似异常变更 - 企业名称频繁变更': CommonFrequentChangesColumns,
};

export const SUB_DIMENSION_NAME = {
  histShareHolderList: '历史股东',
  historyEmployeeList: '历史高管',
  historyOperList: '历史法定代表人',
};
