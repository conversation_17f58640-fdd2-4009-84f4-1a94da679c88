import { mount } from '@vue/test-utils';

import VirtualPaginationTable from '..';

describe('VirtualPaginationTable', () => {
  it('应该正确显示表格数据', () => {
    const columns = [{ title: '姓名', dataIndex: 'name' }];
    const dataSource = [{ name: '张三' }, { name: '李四' }, { name: '王五' }, { name: '赵六' }, { name: '孙七' }];
    const wrapper = mount(VirtualPaginationTable, {
      propsData: {
        columns,
        dataSource,
        rowKey: 'name',
      },
    });
    expect(wrapper.vm.tableData).toEqual([{ name: '张三' }, { name: '李四' }, { name: '王五' }, { name: '赵六' }, { name: '孙七' }]);
  });

  it('自定义分页大小时，表格数据正确显示', () => {
    const columns = [{ title: '姓名', dataIndex: 'name' }];
    const dataSource = Array.from({ length: 15 }, (_, i) => ({ name: `用户${i + 1}` }));
    const wrapper = mount(VirtualPaginationTable, {
      propsData: {
        columns,
        dataSource,
        pagination: {
          pageSize: 5,
        },
        rowKey: 'name',
      },
    });
    expect(wrapper.vm.tableData).toEqual(Array.from({ length: 5 }, (_, i) => ({ name: `用户${i + 1}` })));
  });

  it('数据源为空时，表格数据为空', () => {
    const columns = [{ title: '姓名', dataIndex: 'name' }];
    const wrapper = mount(VirtualPaginationTable, {
      propsData: {
        columns,
        dataSource: [],
        rowKey: 'name',
      },
    });
    expect(wrapper.vm.tableData).toEqual([]);
  });

  it('数据源长度小于默认分页大小时，表格数据正确显示', () => {
    const columns = [{ title: '姓名', dataIndex: 'name' }];
    const dataSource = [{ name: '张三' }, { name: '李四' }];
    const wrapper = mount(VirtualPaginationTable, {
      propsData: {
        columns,
        dataSource,
        rowKey: 'name',
      },
    });
    expect(wrapper.vm.tableData).toEqual([{ name: '张三' }, { name: '李四' }]);
  });
});
