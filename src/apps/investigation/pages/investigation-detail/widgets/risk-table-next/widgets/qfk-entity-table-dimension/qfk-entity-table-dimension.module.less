.container {
  .title {
    font-size: 14px;
  }

  .list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .block {
    display: flex;
    flex-direction: column;

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 40px;
      position: relative;
      padding-left: 16px;

      &::before {
        content: '';
        position: absolute;
        width: 4px;
        height: 16px;
        border-radius: 2px;
        background: #128BED;
        top: 50%;
        left: 5px;
        transform: translateY(-50%);
      }
    }
  }

  .dimensionDescription {
    font-size: 14px;
    line-height: 22px;
    color: #666;
  }
}
