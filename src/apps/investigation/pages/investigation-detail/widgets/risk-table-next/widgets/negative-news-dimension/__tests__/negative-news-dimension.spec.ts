import { mount } from '@vue/test-utils';

import NegativeNewsDimension from '..';

describe('NegativeNewsDimension 组件', () => {
  it('应该正确渲染', () => {
    const wrapper = mount(NegativeNewsDimension, {
      propsData: {
        dataSource: [
          {
            newsid: '1',
            title: '测试标题',
            codedesc: ['code1', 'code2'],
            tagsnew: ['tag1'],
            dimensionLevel: 2,
            needNewTag: true,
            dimensionKey: 'key1,key2',
            source: '来源',
            publishtime: '2023-10-01T00:00:00Z',
            companyName: '关联企业',
            companyId: '123',
            summary: '摘要信息',
          },
        ],
        rowKey: 'newsid',
        from: 'other',
        meta: { keyNo: '456' },
      },
    });

    expect(wrapper.html()).toMatchSnapshot();
  });

  it('当数据源为空时，应该渲染 QRichTableEmpty 组件', () => {
    const wrapper = mount(NegativeNewsDimension, {
      propsData: {
        dataSource: [],
        rowKey: 'newsid',
        from: 'other',
        meta: { keyNo: '456' },
      },
    });

    expect(wrapper.text()).toContain('暂无数据');
  });
});
