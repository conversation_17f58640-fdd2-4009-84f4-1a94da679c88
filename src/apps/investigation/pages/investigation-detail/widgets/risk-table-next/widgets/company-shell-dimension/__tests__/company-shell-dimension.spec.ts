import { mount } from '@vue/test-utils';

import CompanyShellDimension from '..';

const mockDataSource1 = {
  title: '无法联系该企业',
  description: '企业信息无法联系',
  hasDetail: '1',
  data: {
    result: {
      addDate: '2021-01-01',
      decisionOffice: '北京市海淀区人民法院',
      addReason: '无法联系该企业',
    },
  },
};

const mockDataSource2 = {
  title: '企业自然人变更时间集中',
  description: '企业自然人变更时间集中',
  hasDetail: '1',
  data: {
    result: {
      histShareHolderList: [
        {
          partnerName: '李四',
          keyNo: 'abc',
          stockPercent: '99%',
          type: '大股东',
          inDate: '2021-02-01',
          changeDate: '2021-01-01',
        },
      ],
      historyEmployeeList: [{ employeeName: '李四', keyNo: 'abc', job: '董事', inDate: '2021-02-01', changeDate: '2021-01-01' }],
      historyOperList: [{ operName: '李四', keyNo: 'abc', inDate: '2021-02-01', changeDate: '2021-01-01' }],
    },
  },
};

const mockDataSource3 = {
  title: '其他维度',
  description: '其他维度信息',
  hasDetail: '1',
  data: {
    resultList: [
      {
        reCompanyName: '公司A',
        reKeyNo: 'a',
        reOperkeyNo: 'zhangsan',
        reOpername: '张三',
        reNameCollect: '董事',
        reStartDate: '2024-01-10',
        reAddress: '江苏省苏州市吴中区',
      },
      {
        reCompanyName: '公司B',
        reKeyNo: 'b',
        reOperkeyNo: 'zhangsan',
        reOpername: '张三',
        reNameCollect: '董事',
        reStartDate: '2024-01-10',
        reAddress: '江苏省苏州市吴中区',
      },
    ],
  },
};

const mockDataSource4 = {
  title: '一址多企',
  description: '一址多企信息',
  hasDetail: '1',
  data: {
    result: {
      companyName: '公司A',
      keyNo: 'a',
      operKeyNo: 'zhangsan',
      operName: '张三',
      startDate: '2024-01-10',
      address: '北京市海淀区',
    },
  },
};

const list = [
  ['注册基础信息重叠', mockDataSource3],
  ['注册信息相似度过高', mockDataSource3],
  ['一人多企', mockDataSource3],
  ['一址多企', mockDataSource4],
  ['无法联系该企业', mockDataSource1],
  ['未公示年报', mockDataSource1],
  ['企业自然人变更时间集中', mockDataSource2],
];

describe('CompanyShellDimension', () => {
  it.each(list)('展示: %s', (title: string, args: any) => {
    const wrapper = mount(CompanyShellDimension, {
      propsData: {
        dataSource: args,
      },
    });
    expect(wrapper.html()).toMatchSnapshot();
  });

  it('dataSource为空对象时不展示任何内容', () => {
    const wrapper = mount(CompanyShellDimension, {
      propsData: {
        dataSource: {},
      },
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});
