// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`NegativeNewsDimension 组件 > 应该正确渲染 1`] = `
<div class="list"><a href="/embed/post-news?newsId=1&amp;keyNo=456&amp;title=测试标题" target="_blank" class="item">
    <header class="header">
      <div class="warningTag">
        <div class="container high">警示风险</div>
      </div>
      <div class="container small warning" style="margin-right: 5px;">新增</div>
      <h3>测试标题</h3>
      <div class="newsTag"> </div>
      <div class="newsTag"> </div><span class="otherInfo"><span>来源</span><span class="divider"></span><span>2023-10-01</span></span>
    </header>
    <div class="tags"><span>#code1</span><span>#code2</span><span>#tag1</span></div>
    <div class="drawFooter"><span class="relatedCompany">关联企业：<q-entity-link coy-obj="[object Object]"></q-entity-link></span><span class="otherInfo"><span>来源</span><span class="divider"></span><span>2023-10-01</span></span></div>
    <div class="summaryBlock">
      <q-icon-stub type="icon-summary"></q-icon-stub><span class="divider"></span>
      <div class="description">摘要信息</div>
    </div>
  </a></div>
`;
