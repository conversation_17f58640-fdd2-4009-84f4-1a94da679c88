@import '@/styles/token.less';

.list {
  border: 1px solid @qcc-color-gray-500;

  > .item:not(:last-child) {
    border-bottom: 1px solid @qcc-color-gray-500;
  }
}

.item {
  display: block;
  padding: 15px;
  line-height: 22px;

  &:hover {
    background-color: @qcc-color-blue-200;
    cursor: pointer;

    .header h3 {
      color: @qcc-color-blue-500;
    }
  }

  > *:not(:last-child) {
    margin-bottom: 5px;
  }

  .header {
    display: flex;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 14px;
      line-height: 26px;
      color: @qcc-color-black-600;
      font-weight: @qcc-font-bold;
    }

    .init-active h3{
      color: #128BED;
    }

    .warning-tag {
      display: flex;
      align-items: center;
      margin-right: 8px;
    }
  }

  .tags {
    color: @qcc-color-black-300;
    font-size: 13px;
    line-height: 20px;

    > span:not(:last-child) {
      margin-right: 10px;
    }
  }

  .info {
    > span {
      color: @qcc-color-black-500;

      i {
        color: @qcc-color-black-300;

        &::after {
          content: '：';
        }
      }

      &:not(:last-child) {
        margin-right: 30px;
      }
    }
  }
}


  .other-info {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    line-height: 22px;
    color: #999;
    gap: 8px;
  }

  .divider {
    width: 1px;
    height: 12px;
    background: #eee;
  }

  .summary-block {
    display: flex;
    align-items: center;

    svg {
      width: 28px;
      height: 14px;
      flex-shrink: 0;
    }

    .divider {
      width: 2px;
      height: 12px;
      background: #BBB;
      margin: 0 4px;
      flex-shrink: 0;
    }

    .description {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: @qcc-color-black-500;
    }
  }


.news-tag {
  flex-shrink: 0;
  height: 20px;
  border-radius: 2px;
  padding: 0 4px;
  background: #eee;
  font-size: 12px;
  color: #808080;
  margin-left: 8px;
  display: flex;
  align-items: center;
}