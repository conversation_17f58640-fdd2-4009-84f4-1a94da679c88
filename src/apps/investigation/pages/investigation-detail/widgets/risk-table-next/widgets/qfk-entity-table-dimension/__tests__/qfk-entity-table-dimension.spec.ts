import { mount } from '@vue/test-utils';

import QfkEntityTableDimension from '..';

const mockDataSource = [
  {
    recordId: '1',
    dimensionDesc: '维度描述1',
    partnerList: [
      { stockName: '股东1', keyNo: '1001', stockPercent: '10%', shouldCapi: '100', shoudDate: '2021-01-01' },
      { stockName: '股东2', keyNo: '1002', stockPercent: '20%', shouldCapi: '200', shoudDate: '2021-03-01' },
    ],
    employeeList: [
      { name: '员工1', keyNo: '2001', job: '职务1' },
      { name: '员工2', keyNo: '2002', job: '职务2' },
    ],
  },
  {
    recordId: '2',
    dimensionDesc: '',
    partnerList: [],
    employeeList: [],
  },
];

describe('QfkEntityTableDimension', () => {
  it('渲染正常路径时应该正确显示维度描述、股东信息和主要人员', () => {
    const wrapper = mount(QfkEntityTableDimension, {
      propsData: {
        dataSource: mockDataSource,
      },
    });

    expect(wrapper.html()).toMatchSnapshot();
  });

  it('dataSource 为空数组时，不应该显示任何内容', () => {
    const wrapper = mount(QfkEntityTableDimension, {
      propsData: {
        dataSource: [],
      },
    });

    expect(wrapper.isEmpty()).toBe(true);
  });
});
