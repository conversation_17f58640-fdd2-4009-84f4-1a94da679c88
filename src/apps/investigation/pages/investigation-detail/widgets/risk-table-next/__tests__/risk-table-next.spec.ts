import { shallowMount } from '@vue/test-utils';

import RiskTableNext from '../index';

describe('RiskTableNext', () => {
  beforeEach(() => {
    vi.useFakeTimers({
      now: new Date('2024-01-01T00:00:00.000Z'),
    });
  });
  afterEach(() => {
    vi.useRealTimers();
  });

  // meta.key
  test.each([
    [
      'BusinessAbnormal7',
      [
        {
          description: 'DESCRIPTION',
          keyNoAndNames: [
            {
              keyNo: 'keyNo',
              name: 'name',
            },
          ],
        },
      ],
    ],
    ['NoCapital', [[{ label: 'LABEL', value: 'VALUE' }]]],
    ['BusinessAbnormal1', []],
    ['CompanyShell', [{ detailId: 'detailId', title: 'title', description: 'description' }]],
    ['ContractBreach', []],
    [
      'NegativeNewsRecent',
      [
        {
          codedesc: ['codedesc'],
          tagsnew: ['tagsnew'],
        },
      ],
    ],
    ['Liquidation', []],
  ])('props: meta.key - %s', (metaKey, dataSource) => {
    const wrapper = shallowMount<InstanceType<typeof RiskTableNext>>(RiskTableNext, {
      propsData: {
        meta: {
          key: metaKey,
        },
        dataSource,
        pagination: {
          total: 1,
          pageSize: 10,
        },
      },
    });
    expect(wrapper).toMatchSnapshot();
  });
});
