.list {
  display: flex;
  flex-direction: column;
  max-height: 400px;
  overflow-y: auto;
  padding: 2px;

  .item {
    border-radius: 4px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: 4px;
      border: 1px solid #eee;
    }

    &.item-checked,
    &:hover {
      background: #f8fbfe;
      cursor: pointer;

      &::after {
        border: 2px solid #128bed;
      }
    }

    &:hover {
      .radio {
        display: block;
      }
    }

    & + .item {
      margin-top: 15px;
    }

    .title {
      display: flex;
      align-items: center;
      gap: 10px;
      font-weight: bold;
      color: #333;

      .tag {
        font-weight: normal;
        padding: 2px 6px;
        color: #128bed;
        background: #e2f1fd;
        border-radius: 2px;
        font-size: 12px;
      }
    }

    .desc {
      color: #999;
    }

    .checked {
      position: absolute;
      color: #128bed;
      right: 0;
      bottom: 0;
      font-size: 36px;
    }

    .radio {
      display: none;
    }
  }
}
