import { mount } from '@vue/test-utils';
import { message } from 'ant-design-vue';

import { setting as settingService } from '@/shared/services';

import RiskModelModal from '..';

vi.mock('@/shared/services', () => ({
  setting: {
    getModelLists: vi.fn(),
  },
}));

describe('RiskModelModal', () => {
  let wrapper;

  beforeEach(() => {
    (settingService.getModelLists as any).mockClear();
  });

  it('正常加载模型列表并选择模型', async () => {
    (settingService.getModelLists as any).mockResolvedValue({ data: [{ modelId: 1, modelName: '模型1' }] });

    wrapper = mount(RiskModelModal);

    await wrapper.vm.$nextTick();
    expect(wrapper.vm.isLoading).toBe(false);
    expect(wrapper.vm.modelList).toEqual([{ modelId: 1, modelName: '模型1' }]);
    wrapper.vm.handleCheck(1);
    expect(wrapper.vm.selectedValues).toEqual([1]);
    await wrapper.vm.onSubmit();
    expect(wrapper.emitted().resolve).toBeTruthy();
    expect(wrapper.emitted().resolve).toEqual([[[1]]]);
  });

  it('点击取消按钮时关闭模态框', () => {
    wrapper = mount(RiskModelModal);
    wrapper.vm.onCancel();
    expect(wrapper.vm.visible).toBe(false);
  });

  it('模型列表加载失败时关闭加载状态', async () => {
    (settingService.getModelLists as any).mockRejectedValue(new Error('加载失败'));
    wrapper = mount(RiskModelModal);
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.isLoading).toBe(false);
  });

  it('未选择模型时提交操作显示警告信息', async () => {
    (settingService.getModelLists as any).mockResolvedValue({ data: [{ modelId: 1, modelName: '模型1' }] });
    message.warning = vi.fn();
    wrapper = mount(RiskModelModal);
    await wrapper.vm.$nextTick();
    await wrapper.vm.$nextTick();
    await wrapper.vm.onSubmit();
    expect(message.warning).toHaveBeenCalledWith('至少选择一个模型');
  });

  it('选择和取消选择模型', async () => {
    (settingService.getModelLists as any).mockResolvedValue({ data: [{ modelId: 1, modelName: '模型1' }] });
    wrapper = mount(RiskModelModal);
    await wrapper.vm.$nextTick();
    const item = wrapper.findAll('.setting-model-item');
    item.at(0).trigger('click');
    expect(wrapper.vm.selectedValues).toEqual([1]);
    item.at(0).trigger('click');
    expect(wrapper.vm.selectedValues).toEqual([]);
  });
});
