import { defineComponent, onMounted, ref } from 'vue';
import { message as Message, Spin } from 'ant-design-vue';

import QModal from '@/components/global/q-modal/q-modal';
import { createPromiseDialog } from '@/components/promise-dialogs';
import { setting as settingService } from '@/shared/services';

import styles from './styles.module.less';

const RiskModelModal = defineComponent({
  name: 'RiskModelModal',
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const visible = ref(true);
    const onCancel = () => {
      visible.value = false;
      emit('resolve');
    };

    const isLoading = ref(false);
    const modelList = ref<any[]>([]);
    const selectedValues = ref<number[]>([]);
    const getEnabledModelList = async () => {
      try {
        isLoading.value = true;
        const params = {
          product: 'SAAS_PRO',
          status: 1,
          pageSize: 20,
          pageIndex: 1,
        };
        const res = await settingService.getModelLists(params);
        modelList.value = res.data;
        isLoading.value = false;
      } catch (error) {
        isLoading.value = false;
      }
    };

    onMounted(() => {
      getEnabledModelList();
    });

    const onSubmit = async () => {
      if (!selectedValues.value.length) {
        Message.warning('至少选择一个模型');
        return;
      }
      emit('resolve', selectedValues.value);
    };

    const handleCheck = (id: number) => {
      if (selectedValues.value.includes(id)) {
        selectedValues.value = selectedValues.value.filter((item) => item !== id);
      } else {
        selectedValues.value.push(id);
      }
    };

    return {
      isLoading,
      visible,
      modelList,
      onCancel,
      onSubmit,
      selectedValues,
      handleCheck,
    };
  },
  render() {
    return (
      <QModal
        {...{
          props: {
            onOk: this.onSubmit,
            visible: this.visible,
            destroyOnClose: true,
            title: '切换模型',
          },
          on: {
            cancel: () => this.onCancel(),
          },
        }}
      >
        <Spin spinning={this.isLoading}>
          <div class={styles.list}>
            {this.modelList.map((item) => {
              const isSelected = this.selectedValues.includes(item.modelId);
              return (
                <div
                  class={[styles.item, isSelected && styles.itemChecked, 'setting-model-item']}
                  onClick={(e) => {
                    e.preventDefault();
                    this.handleCheck(item.modelId);
                  }}
                >
                  <div class="flex justify-between">
                    <span class={styles.title}>
                      <span>{item.modelName}</span>
                    </span>
                  </div>
                  {isSelected ? <q-icon class={styles.checked} type="icon-checked" /> : null}
                </div>
              );
            })}
          </div>
        </Spin>
      </QModal>
    );
  },
});

export default RiskModelModal;

export const openRiskModelModal = createPromiseDialog(RiskModelModal);
