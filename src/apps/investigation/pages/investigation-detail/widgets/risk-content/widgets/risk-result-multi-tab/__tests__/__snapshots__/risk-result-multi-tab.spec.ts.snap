// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`RiskResultMultiTab 组件测试 > 正常渲染列表 1`] = `
<ul class="container">
  <li class="item overview defaultColor active">
    <div class="title"><span class="icon"><img src="/src/apps/investigation/pages/investigation-detail/widgets/risk-content/widgets/risk-result-multi-tab/img/icon-dd-all.svg" width="22"></span><span class="text">尽调综述</span></div>
    <div class="risk" style="display: none;"></div>
  </li>
  <li class="item textColor1">
    <div class="title"><span class="icon"><q-icon-stub type="icon-icon_fengxianmoxing1"></q-icon-stub></span><span class="text">模型A</span></div>
    <div class="risk">风险A</div>
  </li>
  <li class="item textColor2">
    <div class="title"><span class="icon"><q-icon-stub type="icon-icon_fengxianmoxing1"></q-icon-stub></span><span class="text">模型B</span></div>
    <div class="risk">风险B</div>
  </li>
</ul>
`;
