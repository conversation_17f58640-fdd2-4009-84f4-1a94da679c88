import { Tooltip } from 'ant-design-vue';
import { computed, defineComponent, PropType } from 'vue';

import { getRiskLevelTextColorName } from '@/config/risk.config';
import QIcon from '@/components/global/q-icon';

import imgSrc from './img/icon-dd-all.svg';
import styles from './risk-result-multi-tab.module.less';

const RiskResultMultiTab = defineComponent({
  name: 'RiskResultMultiTab',
  props: {
    list: {
      type: Array as PropType<Array<Record<string, any>>>,
      default: () => [],
    },
    highlightId: {
      type: Number,
      required: false,
    },
    currentSelectModelID: {
      type: Number,
      default: -10,
    },
  },
  emits: ['change', 'hover'],
  setup(props, { emit }) {
    const showList = computed(() => {
      const list = props.list.map((v) => ({
        module: v.riskModelName,
        id: v.id,
        riskType: v.result,
        riskName: v.riskName,
        type: 1,
      }));

      if (props.list.length > 0) {
        // Overview 综述
        list.unshift({ module: '尽调综述', id: -10, riskType: -10, type: 2, riskName: null });
      }
      return list;
    });

    const changeSelectTab = (row, index) => {
      emit('change', row, index);
    };

    const handleMouseEnter = (item) => {
      emit('hover', item);
    };

    const handleMouseLeave = () => {
      emit('hover', null);
    };

    return {
      showList,
      changeSelectTab,
      handleMouseEnter,
      handleMouseLeave,
    };
  },
  render() {
    return (
      <ul class={styles.container}>
        {this.showList.map((item, index) => {
          return (
            <li
              key={item.id}
              class={{
                [styles.item]: true,
                [styles.overview]: item.type === 2,
                [styles[getRiskLevelTextColorName(item.riskType)]]: true,
                [styles.hover]: this.highlightId === item.id,
                [styles.active]: this.currentSelectModelID === item.id,
              }}
              onClick={() => this.changeSelectTab(item, index)}
              onMouseenter={() => this.handleMouseEnter(item)}
              onMouseleave={() => this.handleMouseLeave()}
            >
              <div class={styles.title}>
                <span class={styles.icon}>
                  {item.type === 2 ? <img src={imgSrc} width="22" /> : null}
                  {item.type === 1 ? <QIcon type="icon-icon_fengxianmoxing1" /> : null}
                </span>
                <Tooltip title={item.module}>
                  <span class={styles.text}>{item.module}</span>
                </Tooltip>
              </div>
              <div class={styles.risk} v-show={item.riskName}>
                {item.riskName}
              </div>
            </li>
          );
        })}
      </ul>
    );
  },
});

export default RiskResultMultiTab;
