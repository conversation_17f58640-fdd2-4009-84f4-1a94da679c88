import { mount } from '@vue/test-utils';

import RiskResultMultiTab from '..';

const mockList = [
  { riskModelName: '模型A', id: 1, result: 1, riskName: '风险A' },
  { riskModelName: '模型B', id: 2, result: 2, riskName: '风险B' },
];

vi.mock('@/config/risk.config', () => ({
  getRiskLevelTextColorName: vi.fn().mockImplementation((riskType) => {
    if (riskType === 1) return 'textColor1';
    if (riskType === 2) return 'textColor2';
    return 'defaultColor';
  }),
}));

describe('RiskResultMultiTab 组件测试', () => {
  it('正常渲染列表', () => {
    const wrapper = mount(RiskResultMultiTab, {
      propsData: { list: mockList },
    });
    expect(wrapper.html()).toMatchSnapshot();
  });

  it('点击事件触发', async () => {
    const wrapper = mount(RiskResultMultiTab, {
      propsData: { list: mockList },
    });
    await wrapper.find('li:nth-child(2)').trigger('click');
    expect(wrapper.emitted().change).toBeTruthy();
    expect(wrapper.emitted().change[0]).toEqual([
      {
        id: 1,
        module: '模型A',
        riskName: '风险A',
        riskType: 1,
        type: 1,
      },
      1,
    ]);
  });

  it('鼠标进入事件触发', async () => {
    const wrapper = mount(RiskResultMultiTab, {
      propsData: { list: mockList },
    });
    await wrapper.find('li:nth-child(2)').trigger('mouseenter');
    expect(wrapper.emitted().hover).toBeTruthy();
    expect(wrapper.emitted().hover[0]).toEqual([
      {
        id: 1,
        module: '模型A',
        riskName: '风险A',
        riskType: 1,
        type: 1,
      },
    ]);
  });

  it('鼠标离开事件触发', async () => {
    const wrapper = mount(RiskResultMultiTab, {
      propsData: { list: mockList },
    });
    await wrapper.find('li:nth-child(2)').trigger('mouseenter');
    await wrapper.find('li:nth-child(2)').trigger('mouseleave');
    expect(wrapper.emitted().hover[1]).toEqual([null]);
  });
});
