import { mount } from '@vue/test-utils';
import { ref } from 'vue';

import { uesInvestStore } from '@/hooks/use-invest-store';
import { useExpandKeys } from '@/apps/investigation/pages/investigation-detail/hook/use-expand-keys';

import RiskContentDefault from '..';

vi.mock('@/hooks/use-invest-store');
vi.mock('@/apps/investigation/pages/investigation-detail/hook/use-expand-keys');
vi.mock('@/components/full-watermark/use-watermark');

describe('RiskContentDefault', () => {
  beforeEach(() => {
    vi.mocked<any>(uesInvestStore).mockReturnValue({
      currentSelectModelID: ref(1),
      isCurrentDDResultsDesc: ref(false),
    });
    vi.mocked<any>(useExpandKeys).mockReturnValue({
      setExpandKeys: vi.fn(),
      hasAllMetricExpanded: ref(false),
    });
  });
  afterEach(() => {
    vi.clearAllMocks();
  });
  it('加载中状态', () => {
    const wrapper = mount(RiskContentDefault, {
      propsData: {
        loading: true,
        company: { Name: 'Test Company', KeyNo: '12345' },
        riskLevel: 1,
        riskInfo: {},
        allModelRiskInfoList: [],
        riskModels: {},
        errorInfo: {},
      },
    });
    expect(wrapper.html()).toBe('<div style="height: 0px;"></div>');
  });

  it('空状态', () => {
    const wrapper = mount(RiskContentDefault, {
      propsData: {
        loading: false,
        company: { Name: 'Test Company', KeyNo: '12345' },
        riskLevel: 1,
        riskInfo: { details: { groupMetricScores: [], originalHits: [] } },
        allModelRiskInfoList: [],
        riskModels: {},
        errorInfo: {},
      },
      stubs: {
        RiskReview: true,
        FullWatermark: true,
      },
    });
    const watermark = wrapper.findComponent({ name: 'FullWatermark' });
    expect(watermark.exists()).toBe(true);
    expect(wrapper.findComponent({ name: 'EmptyResult' }).exists()).toBe(true);
    expect(wrapper.html()).toContain('当前企业未检测到相关风险项');
  });

  it('错误信息渲染', async () => {
    const wrapper = mount(RiskContentDefault, {
      propsData: {
        loading: false,
        company: { Name: 'Test Company', KeyNo: '12345' },
        riskLevel: 1,
        riskInfo: {},
        allModelRiskInfoList: [],
        riskModels: {},
        errorInfo: { error: true, code: 400203, message: 'Custom error message' },
      },
    });
    expect(wrapper.findComponent({ name: 'ErrorResult' }).exists()).toBe(true);
    expect(wrapper.text()).toContain('Custom error message');
    await wrapper.setProps({
      errorInfo: { error: true, code: 400204, message: 'Custom error message' },
    });
    expect(wrapper.text()).toContain('重新排查');
    const retryBtn = wrapper.findComponent({ name: 'AButton' });
    retryBtn.trigger('click');
    expect(wrapper.emitted().retry).toBeTruthy();
  });

  it('多排查模型时', () => {
    const wrapper = mount(RiskContentDefault, {
      propsData: {
        loading: false,
        company: { Name: 'Test Company', KeyNo: '12345' },
        riskLevel: 1,
        riskInfo: {
          orgModelId: '1',
          result: 1,
          details: {
            groupMetricScores: [
              { totalHits: 1, groupDefinition: { groupId: 1 } },
              { totalHits: 2, groupDefinition: { groupId: 2 } },
            ],
            originalHits: [],
          },
        },
        allModelRiskInfoList: [
          { orgModelId: '1', result: 1, name: 'Test Model 1' },
          { orgModelId: '2', result: 2, name: 'Test Model 2' },
        ],
        riskModels: {
          '1': { resultSetting: [{ level: 1, name: 'Low Risk' }] },
          '2': { resultSetting: [{ level: 2, name: 'Medium Risk' }] },
        },
        errorInfo: {},
      },
      stubs: {
        RiskReview: true,
        FullWatermark: true,
      },
    });
    expect(wrapper.html()).toMatchSnapshot();
  });

  it('单排查模型', () => {
    const wrapper = mount(RiskContentDefault, {
      propsData: {
        loading: false,
        company: { Name: 'Test Company', KeyNo: '12345' },
        riskLevel: 1,
        riskInfo: {
          orgModelId: '1',
          result: 1,
          details: { groupMetricScores: [{ totalHits: 1, groupDefinition: { groupId: 1 } }], originalHits: [] },
        },
        allModelRiskInfoList: [{ orgModelId: '1', result: 1, name: 'Test Model 1' }],
        riskModels: { '1': { resultSetting: [{ level: 1, name: 'Low Risk' }] } },
        errorInfo: {},
      },
      parentComponent: {
        provide: {
          isCurrentDDResultsDesc: { value: false },
        },
      },
      stubs: {
        RiskReview: true,
        FullWatermark: true,
      },
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});
