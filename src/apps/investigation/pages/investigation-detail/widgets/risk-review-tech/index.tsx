import { defineComponent, nextTick, onMounted } from 'vue';
// import { createTrackEvent } from '@/config/tracking-events';
import DiligenceWarningBlockTech from '@/components/diligence-waring-block-tech';

/**
 * 科创模型评分卡
 */
const RiskReviewTech = defineComponent({
  name: 'RiskReviewTech',
  props: {
    /**
     * 距离页面顶部的距离
     */
    scrollOffsetTop: {
      type: Number,
      default: 0,
    },
    riskInfo: {
      type: Object,
      default: () => ({}),
    },
    levelGroup: {
      type: Array,
      default: () => [3, 2, 1, 0, -1, -2],
    },
    mapping: {
      type: Object,
      default: () => ({
        '2': {
          step: 5,
          scheme: ['linear-gradient(180deg, rgba(0, 173, 101, 0.1) 0%, rgba(0, 173, 101, 0.05) 32%, rgba(255, 255, 255, 0) 100%)'],
          text: '优秀',
        },
        '1': {
          step: 4,
          scheme: ['linear-gradient(180deg, rgba(18, 139, 237, 0.1) 0%, rgba(18, 139, 237, 0.05) 32%, rgba(255, 255, 255, 0) 100%)'],
          text: '良好',
        },
        '0': {
          step: 3,
          scheme: ['linear-gradient(180deg, rgba(255, 192, 67, 0.1) 0%, rgba(255, 192, 67, 0.05) 32%, rgba(255, 255, 255, 0) 100%)'],
          text: '中等',
        },
        '-1': {
          step: 2,
          scheme: ['linear-gradient(180deg, rgba(255, 114, 45, 0.1) 0%, rgba(255, 114, 45, 0.05) 32%, rgba(255, 255, 255, 0) 100%)'],
          text: '一般',
        },
        '-2': {
          step: 1,
          scheme: ['linear-gradient(180deg, rgba(179, 0, 0, 0.1) 0%, rgba(179, 0, 0, 0.05) 32%, rgba(255, 255, 255, 0) 100%)'],
          text: '较差',
        },
        '-3': {
          step: 0,
          scheme: ['linear-gradient(180deg, rgba(179, 0, 0, 0.1) 0%, rgba(179, 0, 0, 0.05) 32%, rgba(255, 255, 255, 0) 100%)'],
          text: '重大风险',
        },
      }),
    },
  },
  setup(props, { emit }) {
    const scrollToView = async (id = window.location?.hash?.replace('#', ''), event?: Event) => {
      await nextTick();
      if (!id) {
        return;
      }
      // 取消默认行为（锚点）
      event?.stopPropagation?.();
      event?.preventDefault?.();
      emit('scroll', id);
    };

    onMounted(() => {
      scrollToView();
    });

    return {
      scrollToView,
    };
  },
  render() {
    const { riskInfo } = this;
    const riskLevelInfo = this.mapping[`${riskInfo.riskLevel}`];
    if (!riskLevelInfo) {
      return null;
    }

    const { scheme } = riskLevelInfo;
    return (
      <div
        class="flex items-center justify-center px-30px py-15px border-1px border-solid border-#eee rounded-4px"
        style={{
          background: scheme[0],
        }}
      >
        <DiligenceWarningBlockTech
          levelGroup={this.levelGroup}
          riskInfo={riskInfo}
          scrollToView={(id, item, event) => {
            // this.$track(createTrackEvent(6985, '准入排查详情页', item.name, '排查结果'));
            this.scrollToView(id.toString(), event);
          }}
        />
      </div>
    );
  },
});

export default RiskReviewTech;
