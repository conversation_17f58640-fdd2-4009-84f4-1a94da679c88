import { getDetailByType } from '@/config/risk-detail.config';

describe('getDetailByType', () => {
  test.each([
    'MainMembersRestrictedConsumptionCurrent',
    'SubsidiaryRestrictedConsumptionCurrent',
    'RestrictedConsumptionCurrent',
    'RestrictedConsumptionHistory',
  ])('should return the correct url for %s', (key: string) => {
    const item = { RiskId: 123, Court: 'ABC Court', CaseNo: '123456' };
    const expectedResult = '/embed/sumptuary?id=123&title=ABC Court123456';
    expect(getDetailByType(key, item)).toEqual(expectedResult);
  });

  test.each(['BusinessAbnormal5', 'ProductQualityProblem3', 'ProductQualityProblem4', 'ProductQualityProblem8'])(
    'should return the correct url for %s',
    (key: string) => {
      const item = { id: 456, CaseNo: '789012' };
      const expectedResult = '/embed/adminpenaltydetail?id=456&title=789012';
      expect(getDetailByType(key, item)).toEqual(expectedResult);
      const item2 = { Id: 456, CaseNo: '789012' };
      expect(getDetailByType(key, item2)).toEqual(expectedResult);
    }
  );

  test.each([
    'EnvironmentalPenalties',
    'AdministrativePenalties',
    'AdministrativePenalties2',
    'AdministrativePenalties3',
    'BidAdministrativePenalties',
    'EnvironmentalPenalties',
  ])('should return the correct url for %s', (key: string) => {
    const item = { riskid: 456, caseno: '789012' };
    const expectedResult = '/embed/adminpenaltydetail?id=456&title=789012';
    expect(getDetailByType(key, item)).toEqual(expectedResult);
    const item2 = { RiskId: 456, caseno: '789012' };
    expect(getDetailByType(key, item2)).toEqual(expectedResult);
  });

  test.each(['JudicialAuction', 'JudicialAuction1'])('should return the correct url for %s', (key: string) => {
    const item = { id: 456, name: '789012' };
    const expectedResult = '/embed/judicialSale?id=456&title=789012';
    expect(getDetailByType(key, item)).toEqual(expectedResult);
    const item2 = { Id: 456, name: '789012' };
    expect(getDetailByType(key, item2)).toEqual(expectedResult);
  });

  test.each(['ProductQualityProblem1'])('should return the correct url for %s', (key: string) => {
    const item = { RiskId: 456, Title: '789012' };
    const expectedResult = '/embed/recall-product?url=456&title=789012';
    expect(getDetailByType(key, item)).toEqual(expectedResult);
  });

  test.each(['GuaranteeRisk'])('should return the correct url for %s', (key: string) => {
    const item = { id: 456 };
    const expectedResult = '/embed/judgementInfo?id=456';
    expect(getDetailByType(key, item)).toEqual(expectedResult);
    const item2 = { Id: 456 };
    expect(getDetailByType(key, item2)).toEqual(expectedResult);
  });

  test.each([
    'SalesContractDispute',
    'MajorDispute',
    'CompanyOrMainMembersCriminalInvolve',
    'CompanyOrMainMembersCriminalInvolveHistory',
    'BidAdministrativeJudgement',
  ])('should return the correct url for %s', (key: string) => {
    const item = { id: 456, casename: 789 };
    const expectedResult = '/embed/judgementInfo?id=45&title=789';
    expect(getDetailByType(key, item)).toEqual(expectedResult);
  });

  test.each(['CompanyOrMainMembersCriminalOffence', 'CompanyOrMainMembersCriminalOffenceHistory', 'LaborContractDispute'])(
    'should return the correct url for %s',
    (key: string) => {
      const item = { id: 456, CaseName: 789 };
      const expectedResult = '/embed/courtCaseDetail?caseId=456&title=789';
      expect(getDetailByType(key, item)).toEqual(expectedResult);
      const item2 = { Id: 456, CaseName: 789 };
      expect(getDetailByType(key, item2)).toEqual(expectedResult);
    }
  );

  test.each(['SecurityNotice'])('should return the correct url for %s', (key: string) => {
    const item = { id: 456, keyNo: 789, reason: 0 };
    const expectedResult = '/embed/news-detail-page?newsId=456&keyNo=789&title=0';
    expect(getDetailByType(key, item)).toEqual(expectedResult);
  });

  test.each(['PersonCreditHistory'])('should return the correct url for %s', (key: string) => {
    const expectedResult = '/embed/courtCaseDetail?caseId=456&title=7890';
    [
      { CaseSearchId: 456, Court: 789, CaseNo: 0 },
      { CaseSearchId: 456, ExecuteGov: 789, CaseNo: 0 },
    ].forEach((item) => {
      expect(getDetailByType(key, item)).toEqual(expectedResult);
    });

    [
      { CaseSearchId: 456, CaseNo: 0, expect: '/embed/courtCaseDetail?caseId=456&title=0' },
      { Court: 789, CaseNo: 0, expect: '' },
    ].forEach((item) => {
      expect(getDetailByType(key, item)).toEqual(item.expect);
    });
  });

  test.each(['Unknown key'])('should return the correct url for %s', (key: string) => {
    const expectedResult = '/embed/courtCaseDetail?caseId=456&title=7890';
    [
      { CaseSearchId: 456, Court: 789, CaseNo: 0 },
      { Id: 456, Court: 789, CaseNo: 0 },
      { id: 456, ExecuteGov: 789, CaseNo: 0 },
      { id: 456, CaseNo: 0, expect: '/embed/courtCaseDetail?caseId=456&title=0' },
    ].forEach((item) => {
      expect(getDetailByType(key, item)).toEqual(item.expect || expectedResult);
    });
  });
});
