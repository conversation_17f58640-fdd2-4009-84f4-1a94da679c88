import { getHighLightDifferent } from '../string-buffer';

describe('getHighLightDifferent', () => {
  test('should return default values when both inputs are empty', () => {
    const result = getHighLightDifferent();
    expect(result).toEqual({
      beforeContent: '-',
      afterContent: '-',
    });
  });

  test('should return default values when one of the inputs is empty', () => {
    const result1 = getHighLightDifferent('', 'abc');
    expect(result1).toEqual({
      beforeContent: '-',
      afterContent: 'abc',
    });

    const result2 = getHighLightDifferent('abc', '');
    expect(result2).toEqual({
      beforeContent: 'abc',
      afterContent: '-',
    });
  });

  test('should highlight the differences between two non-empty strings', () => {
    const a = 'hello';
    const b = 'holla';
    const result = getHighLightDifferent(a, b);
    expect(result).toEqual({
      beforeContent: 'h<em>e</em>ll<em>o</em>',
      afterContent: 'h<em>o</em>ll<em>a</em>',
    });
  });
});
