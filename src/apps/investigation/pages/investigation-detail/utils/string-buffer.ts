import * as diffFun from 'diff';

class StringBuffer {
  private content: string[] = [];

  /**
   * 向缓冲区追加一个字符串。
   * @param str 要追加的字符串。
   */
  append(str: string): void {
    this.content.push(str);
  }

  /**
   * 返回当前缓冲区中的字符串。
   * @returns 当前缓冲区的字符串表示。
   */
  toString(): string {
    return this.content.join('');
  }
}

export const getHighLightDifferent = (a = '', b = '') => {
  const result = {
    beforeContent: a || '-',
    afterContent: b || '-',
  };
  if (a && b) {
    const diffList = diffFun.diffChars(a, b);
    const afterContent = new StringBuffer();
    const beforeContent = new StringBuffer();
    diffList.forEach((part) => {
      if (part.removed) {
        // 文字删减，灰色删除线
        beforeContent.append('<em>');
        beforeContent.append(part.value);
        beforeContent.append('</em>');
      } else if (part.added) {
        // 文字新增，红色
        afterContent.append('<em>');
        afterContent.append(part.value);
        afterContent.append('</em>');
      } else {
        afterContent.append(part.value);
        beforeContent.append(part.value);
      }
    });
    result.afterContent = afterContent.toString();
    result.beforeContent = beforeContent.toString();
  }
  return result;
};
