import moment from 'moment';
import { get, isObject } from 'lodash';
import { Tooltip } from 'ant-design-vue';

import { BLACKLIST_DURATION_MAP } from '@/shared/constants/black-list.constants';
import { dateFormat } from '@/utils/format';
import { numberToHuman } from '@/utils/number-formatter';
import { getCurrentLocale } from '@/utils/locale';
import QIcon from '@/components/global/q-icon';
import { getRiskLevelStyle } from '@/config/risk.config';
import QGlossaryInfo from '@/components/global/q-glossary-info';
import QEntityLink from '@/components/global/q-entity-link';
import { getArea } from '@/utils';
import RelationPath, { Path } from '@/components/relation-path';
import { PlainRelationPathConverter } from '@/utils/relation-path-converter';
import CompanyStatus from '@/components/global/q-company-status';
import MoreContent from '@/components/more-content';
import ClampContent from '@/components/clamp-content';

import { getHighLightDifferent } from './string-buffer';
import styles from '../widgets/risk-table-next/risk-table-next.module.less';

// 持股比例处理
const renderStockPercent = (item) => {
  const number = parseFloat(item.stockPercent);
  if (!number) {
    return '';
  }
  return `${number}%`;
};

const blacklistDurationFormatter = (item) => {
  const value = isObject(item) ? get(item, 'duration') : item;
  if (value === undefined || value === null) {
    return '-';
  }
  // duration后端返回了两种，一种是时间戳，一种是type，type只有0-8，大于100默认它是时间戳，做处理
  if (value > 100) {
    return moment
      .duration(value * 1000)
      .locale(getCurrentLocale())
      .humanize();
  }
  return BLACKLIST_DURATION_MAP[value] || '-';
};

// ------------- 经营合规风险 -------------

// 简易注销
const BusinessAbnormal2Columns = [
  {
    title: '公告名称',
    scopedSlots: {
      customRender: 'annoAction',
    },
  },
  {
    title: '公告开始日期-结束日期',
    dataIndex: 'publishDate',
  },
  {
    title: '简易注销结果',
    dataIndex: 'resultContent',
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: {
      customRender: 'action',
    },
  },
];

// 被列入经营异常(未移出)
const BusinessAbnormal3Columns = [
  {
    title: '企业名称',
    scopedSlots: {
      customRender: 'NameAndKeyNo',
    },
  },
  {
    title: '列入日期',
    width: 103,
    dataIndex: 'PublishDate',
    scopedSlots: {
      customRender: 'date',
    },
    // sorter: true,
    // key: 'currencedate',
  },
  { title: '作出决定机关(列入)', dataIndex: 'Court', width: 200 },
  { title: '列入经营异常名录原因', dataIndex: 'CaseReason' },
];

// 疑似停业歇业停产或被吊销证照
const BusinessAbnormal5Columns = [
  {
    title: '决定书文号',
    width: 200,
    scopedSlots: { customRender: 'docNo' },
  },
  {
    title: '违法事实',
    width: 200,
    dataIndex: 'punishReason',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '处罚结果',
    dataIndex: 'Title',
    placeholder: '未公示',
    scopedSlots: { customRender: 'clampcontent' },
  },
  // {
  //   title: '处罚金额(元)',
  //   dataIndex: 'Amount',
  //   scopedSlots: { customRender: 'money' },
  // },
  {
    title: '处罚单位',
    width: 150,
    dataIndex: 'Court',
  },
  {
    title: '处罚日期',
    width: 120,
    datepattern: true,
    dataIndex: 'punishDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  // {
  //   title: '原文',
  //   width: 68,
  //   scopedSlots: { customRender: 'originalSource' },
  // },
];

// ------------- 经营合规风险 -------------

// ------------- 法律风险 -------------

// 税收违法
const TaxationOffencesColumns = [
  {
    title: '发布日期',
    dataIndex: 'PublishDate',
    width: 103,
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '所属税务机关',
    dataIndex: 'Court',
  },
  {
    title: '案件性质',
    dataIndex: 'ActionRemark',
  },
  {
    title: '主要违法事实',
    dataIndex: 'CaseReason',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '法律依据及处理处罚情况',
    dataIndex: 'Title',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 股权冻结
const FreezeEquityColumns = [
  {
    title: '执行通知书文号',
    // RA-1787: 内蒙古新大地建设集团股份有限公司 bd35e32c0f98d0c99c0b7096e6f5cf42
    scopedSlots: { customRender: 'CaseSearchId' },
  },
  {
    title: '被执行人',
    dataIndex: 'Name',
    scopedSlots: { customRender: 'pledgName' },
  },
  {
    title: '冻结股权标的企业',
    dataIndex: 'Pledgor',
    scopedSlots: { customRender: 'pledgPledgor' },
  },
  {
    title: '股权数额',
    width: 180,
    dataIndex: 'AmountDesc',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  {
    title: '执行法院',
    dataIndex: 'Court',
  },
  {
    title: '类型|状态',
    customRender: (item) => {
      // 兼容老数据，老数据没有statusdesc
      const status = item.statusdesc || item.ExecuteStatus;
      return status ? `${item.TypeDesc} | ${status}` : '-';
    },
  },
  // {
  //   title: '冻结起止日期',
  //   customRender: (record) => {
  //     const start = dateFormat(record.LianDate * 1000);
  //     if (start === '-' || !start) {
  //       return '-';
  //     }
  //     const end = dateFormat(record.ActionRemark * 1000);
  //     return `${start}至${end}`;
  //   },
  // },
  // {
  //   title: '公示日期',
  //   dataIndex: 'PublishDate',
  //   customRender: (date) => {
  //     return dateFormat(date * 1000);
  //   },
  // },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 被列入失信被执行人（不支持金额排序
// 法定代表人失信风险（支持金额排序
const PersonCreditCurrentColumns = [
  {
    title: '案号',
    scopedSlots: { customRender: 'CaseNo' },
  },
  {
    title: '失信被执行人',
    dataIndex: 'NameAndKeyNos',
    scopedSlots: { customRender: 'EntityLinksAndJob' },
  },
  {
    title: '执行法院',
    width: 170,
    dataIndex: 'ExecuteGov',
  },
  // {
  //   title: '执行依据文号',
  //   width: 200,
  //   customRender: (item) => item.ExecuteNo || '-',
  // },
  {
    title: '涉案金额(元)',
    width: 120,
    // sorter: true,
    dataIndex: 'Amount',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '履行情况',
    width: 90,
    customCell: (item) => {
      return {
        domProps: {
          innerHTML: item.executeStatus || item.ExecuteStatus || '-',
        },
      };
    },
  },
  { title: '失信行为', dataIndex: 'ActionRemark' },
  {
    title: '立案日期',
    key: 'Liandate',
    sorter: true,
    dataIndex: 'LiAnDate',
    width: 103,
    customRender: (item) => {
      return dateFormat(item);
    },
  },
  {
    title: '发布日期',
    width: 103,
    key: 'Publicdate',
    dataIndex: 'PublicDate',
    sorter: true,
    customRender: (item) => {
      return dateFormat(item);
    },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 历史失信被执行人
const PersonCreditColumns = [
  {
    title: '案号',
    scopedSlots: { customRender: 'CaseNo' },
  },
  {
    title: '失信被执行人',
    dataIndex: 'NameAndKeyNos',
    scopedSlots: { customRender: 'EntityLinksAndJob' },
  },
  // {
  //   title: '疑似申请执行人',
  //   scopedSlots: { customRender: 'SqrInfo' },
  // },
  {
    title: '执行法院',
    customCell: (item) => {
      return {
        domProps: {
          innerHTML: item.Executegov || item.ExecuteGov || (Array.isArray(item.Court) ? item.Court[0] : item.Court || '-'),
        },
      };
    },
  },
  // {
  //   title: '执行依据文号',
  //   customRender: (item) => item.ExecuteNo || '-',
  // },
  {
    title: '涉案金额(元)',
    width: 120,
    sorter: true,
    key: 'amount',
    dataIndex: 'Amount',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '履行情况',
    customCell: (item) => {
      return {
        domProps: {
          innerHTML: item.executeStatus || item.ExecuteStatus || '-',
        },
      };
    },
  },
  { title: '失信行为', dataIndex: 'ActionRemark' },
  {
    title: '立案日期',
    // key: 'liandate',
    // sorter: true,
    width: 103,
    customRender: (item) => {
      return dateFormat(item.lianDate || item.LianDate || item.LiAnDate);
    },
  },
  {
    title: '发布日期',
    width: 103,
    key: 'pubdate',
    sorter: true,
    dataIndex: 'PublicDate',
    customRender: (item) => {
      return dateFormat(item);
    },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 主要负责人被列入失信被执行人
const MainMembersPersonCreditCurrent = [
  {
    title: '案号',
    scopedSlots: { customRender: 'CaseNo' },
  },
  {
    title: '疑似申请执行人',
    scopedSlots: { customRender: 'SqrInfo' },
  },
  {
    title: '执行法院',
    customCell: (item) => {
      return {
        domProps: {
          innerHTML: item.Executegov || item.ExecuteGov || (Array.isArray(item.Court) ? item.Court[0] : item.Court || '-'),
        },
      };
    },
  },
  {
    title: '执行依据文号',
    scopedSlots: { customRender: 'OrgNo' },
  },
  {
    title: '失信被执行人',
    width: 140,
    dataIndex: 'SubjectInfo',
    scopedSlots: { customRender: 'EntityLinksAndJob' },
  },
  {
    title: '涉案金额(元)',
    width: 120,
    // sorter: true,
    key: 'amount',
    dataIndex: 'Amount',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '履行情况',
    customCell: (item) => {
      return {
        domProps: {
          innerHTML: item.executeStatus || item.ExecuteStatus || '-',
        },
      };
    },
  },
  { title: '失信行为', dataIndex: 'ActionRemark' },
  {
    title: '立案日期',
    // key: 'liandate',
    // sorter: true,
    width: 103,
    customRender: (item) => {
      return dateFormat(item.lianDate || item.LianDate || item.LiAnDate);
    },
  },
  {
    title: '发布日期',
    width: 103,
    key: 'publishdate',
    // sorter: true,
    customRender: (item) => {
      return dateFormat(item.publishDate || item.PublishDate || item.PublicDate);
    },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 子公司被列入失信被执行人
const SubsidiaryPersonCreditCurrentColumns = [
  {
    title: '案号',
    scopedSlots: { customRender: 'CaseNo' },
  },
  {
    title: '被执行子公司',
    scopedSlots: { customRender: 'NameAndKeyNo' },
  },
  {
    title: '执行法院',
    customCell: (item) => {
      return {
        domProps: {
          innerHTML: Array.isArray(item.Court) ? item.Court[0] : item.Court,
        },
      };
    },
  },
  {
    title: '执行依据文号',
    customRender: (item) => item.ExecuteNo || '-',
  },
  {
    title: '涉案金额(元)',
    width: 120,
    key: 'amount',
    dataIndex: 'Amount',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '履行情况',
    customCell: (item) => {
      return {
        domProps: {
          innerHTML: item.executeStatus || item.ExecuteStatus || '-',
        },
      };
    },
  },
  { title: '失信行为', dataIndex: 'ActionRemark' },
  {
    key: 'liandate',
    title: '立案日期',
    width: 103,
    customRender: (item) => {
      return dateFormat(item.lianDate || item.LianDate);
    },
  },
  {
    title: '发布日期',
    width: 103,
    key: 'publishdate',
    customRender: (item) => {
      return dateFormat(item.publishDate || item.PublishDate);
    },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 被列入限制高消费名单 主要人员被列入限制高消费 历史限制高消费
const RestrictedConsumptionColumns = [
  {
    title: '案号',
    width: 200,
    scopedSlots: { customRender: 'CaseNo' },
  },
  {
    title: '限消令对象',
    dataIndex: 'SubjectInfo',
    scopedSlots: { customRender: 'EntityLinksAndJob' },
  },
  {
    title: '关联对象',
    scopedSlots: { customRender: 'pledgorInfo' },
  },
  {
    title: '申请人',
    scopedSlots: { customRender: 'applicant' },
  },
  {
    title: '立案日期',
    width: 100,
    dataIndex: 'LianDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'liandate',
  },
  {
    title: '发布日期',
    width: 100,
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'publishdate',
  },
  {
    title: '原文',
    width: 90,
    scopedSlots: { customRender: 'originalSource' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 主要人员被列入限制出境
const RestrictedOutboundColumns = [
  {
    title: '案号',
    scopedSlots: { customRender: 'CaseNo' },
  },
  {
    title: '限制出境对象',
    dataIndex: 'SubjectInfo',
    width: 140,
    scopedSlots: { customRender: 'EntityLinksAndJob' },
  },
  {
    title: '被执行人',
    dataIndex: 'PledgorInfo',
    scopedSlots: { customRender: 'entityLinks' },
  },
  {
    title: '被执行人地址',
    dataIndex: 'Address',
  },
  {
    title: '申请执行人',
    dataIndex: 'ApplicantInfo',
    scopedSlots: { customRender: 'entityLinks' },
  },
  {
    title: '执行标的金额(元)',
    dataIndex: 'Amount',
    scopedSlots: { customRender: 'money' },
    sorter: true,
    key: 'amount',
  },
  {
    title: '承办法院',
    dataIndex: 'Court',
  },
  {
    title: '发布日期',
    width: 103,
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'publishdate',
  },
];

// 破产重整
const BankruptcyColumns = [
  {
    title: '案号',
    width: 240,
    scopedSlots: { customRender: 'CaseNo' },
  },
  {
    title: '破产类型',
    width: 120,
    dataIndex: 'CaseReasonType',
  },
  {
    width: 224,
    title: '被申请人',
    dataIndex: 'RespondentNameAndKeyNo',
    scopedSlots: { customRender: 'respondentName' },
  },
  {
    title: '申请人',
    dataIndex: 'ApplicantNameAndKeyNo',
    scopedSlots: { customRender: 'respondentName' },
  },
  {
    title: '经办法院',
    width: 149,
    dataIndex: 'Court',
  },
  {
    title: '公开日期',
    width: 100,
    dataIndex: 'RiskDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 被执行人信息
const PersonExecutionColumns = [
  {
    title: '案号',
    customRender: (text, record) => {
      if (record.CaseNo && record.CaseSearchId) {
        // NOTE: 传递 anno 不能滚动到对应的位置（专业版未与C端同步）
        return (
          <a href={`/embed/courtCaseDetail?caseId=${record.CaseSearchId}&anno=${record.CaseNo}`} target="_blank">
            {record.CaseNo}
          </a>
        );
      }
      return <span>{record.CaseNo || '-'}</span>;
    },
  },
  // 被执行人、
  {
    title: '被执行人',
    dataIndex: 'Name',
    customRender: (text, record) => {
      if (Array.isArray(record.NameAndKeyNos) && record.NameAndKeyNos.length >= 1) {
        return record.NameAndKeyNos.map((item) => {
          return (
            <div key={item.KeyNo}>
              <a href={`/embed/companyDetail?keyNo=${item.KeyNo}&title=${item.Name}`} target="_blank">
                {item.Name}
              </a>
            </div>
          );
        });
      }
      return <div>{text}</div>;
    },
  },
  // 执行标的（元）、
  {
    title: '执行标的(元)',
    dataIndex: 'BiaoDi',
    scopedSlots: { customRender: 'money' },
    sorter: true,
    key: 'biaodiamount',
  },
  // 执行法院、
  { title: '执行法院', dataIndex: 'ExecuteGov' },
  // 立案日期、内容（详情）
  {
    title: '立案日期',
    width: 103,
    dataIndex: 'LiAnDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'liandate',
  },
  // 详情
  {
    title: '详情',
    scopedSlots: { customRender: 'CaseNo' },
  },
];

// ------------- 法律风险 -------------

// ------------- 行政监管风险 -------------

// 行政处罚 环保处罚 税务处罚
const AdministrativePenaltiesColumns = (amountSorter = true) => {
  return [
    {
      title: '决定书文号',
      width: 200,
      scopedSlots: { customRender: 'casenoWithTag' },
    },
    {
      title: '违法事实',
      width: 200,
      dataIndex: 'CaseReason',
      scopedSlots: { customRender: 'clampContent' },
    },
    {
      title: '处罚结果',
      width: 200,
      dataIndex: 'Title',
      placeholder: '未公示',
      scopedSlots: { customRender: 'clampContent' },
    },
    {
      title: '处罚金额(元)',
      width: 100,
      dataIndex: 'Amount',
      scopedSlots: { customRender: 'money' },
      sorter: amountSorter,
      key: 'amount',
    },
    {
      title: '处罚单位',
      width: 150,
      dataIndex: 'Court',
      customRender: (text) => text?.trim() || '-',
    },
    {
      title: '处罚日期',
      width: 100,
      datepattern: true,
      dataIndex: 'PunishDate',
      scopedSlots: {
        customRender: 'date',
      },
      sorter: true,
      key: 'punishdate',
    },
    {
      title: '原文',
      width: 90,
      scopedSlots: { customRender: 'originalSource' },
    },
    {
      title: '内容',
      width: 70,
      scopedSlots: { customRender: 'urlAction' },
    },
  ];
};

// 涉及商业贿赂、垄断行为或政府采购活动违法行为行政处罚
// 3年前涉及商业贿赂、垄断行为或政府采购活动违法行为行政处罚
export const AdministrativePenalties2Columns = [
  {
    title: '决定文书号',
    width: 200,
    dataIndex: 'caseno',
  },
  {
    title: '违法事实',
    width: 200,
    dataIndex: 'casereason',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '处罚结果',
    width: 200,
    dataIndex: 'title',
    placeholder: '未公示',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '处罚金额(元)',
    width: 100,
    dataIndex: 'amount',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '处罚单位',
    width: 150,
    dataIndex: 'court',
  },
  {
    title: '处罚日期',
    width: 100,
    datepattern: true,
    dataIndex: 'punishdate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '原文',
    width: 68,
    scopedSlots: { customRender: 'originalSource' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 欠税公告
const TaxArrearsNoticeColumns = [
  {
    title: '欠税税种',
    dataIndex: 'Title',
  },
  {
    title: '欠税余额(元)',
    dataIndex: 'Amount',
    scopedSlots: { customRender: 'money' },
    sorter: true,
    key: 'amount',
  },
  {
    title: '当前新发生的欠税金额(元)',
    dataIndex: 'NewAmount',
    scopedSlots: { customRender: 'money' },
    sorter: true,
    key: 'amount2',
  },
  { title: '发布单位', dataIndex: 'IssuedBy' },
  {
    title: '发布日期',
    width: 103,
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'liandate',
  },
  { title: '内容', width: 70, scopedSlots: { customRender: 'action' } },
];

// 产品召回
const ProductQualityProblem1Columns = [
  {
    title: '召回产品',
    dataIndex: 'Title',
    scopedSlots: { customRender: 'html' },
  },
  {
    title: '召回企业',
    key: 'NameAndKeyNo',
    scopedSlots: { customRender: 'NameAndKeyNo' },
  },
  {
    title: '发布日期',
    width: 103,
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 产品抽查不合格
const ProductQualityProblem2Columns = [
  {
    title: '产品名称',
    dataIndex: 'CaseReason',
  },
  {
    title: '产品类别',
    dataIndex: 'CaseReasonType',
  },
  {
    title: '规格型号',
    dataIndex: 'OrgNo',
  },
  {
    title: '生产单位',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '抽查/公告时间',
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '主要不合格项目',
    dataIndex: 'ActionRemark',
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: {
      customRender: 'productCheckedDetail',
    },
  },
];

// 假冒伪劣产品
const ProductQualityProblem3Columns = [
  {
    title: '决定书文号',
    scopedSlots: {
      customRender: 'PenaltiesNo',
    },
  },
  {
    title: '处罚事由/违法行为类型',
    dataIndex: 'CaseReason',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '处罚结果/内容',
    dataIndex: 'Title',
    placeholder: '未公示',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '处罚金额(元)',
    dataIndex: 'Amount',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '处罚单位',
    dataIndex: 'Court',
  },
  {
    title: '处罚日期',
    datepattern: true,
    width: 103,
    dataIndex: 'PunishDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '原文',
    width: 68,
    scopedSlots: { customRender: 'originalSource' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 虚假宣传
const ProductQualityProblem4Columns = [
  {
    title: '决定书文号',
    scopedSlots: {
      customRender: 'PenaltiesNo',
    },
  },
  {
    title: '处罚事由',
    dataIndex: 'CaseReason',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '处罚结果',
    dataIndex: 'Title',
    placeholder: '未公示',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '处罚金额(元)',
    dataIndex: 'Amount',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '处罚单位',
    dataIndex: 'Court',
  },
  {
    title: '处罚日期',
    datepattern: true,
    width: 103,
    dataIndex: 'PunishDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '原文',
    width: 68,
    scopedSlots: { customRender: 'originalSource' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 其他质量问题
const ProductQualityProblem5Columns = [
  {
    title: '任务编号',
    scopedSlots: { customRender: 'taskNo' },
  },
  {
    title: '任务名称',
    dataIndex: 'Title',
  },
  {
    title: '抽查机关',
    dataIndex: 'Court',
  },
  {
    title: '完成日期',
    width: 103,
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'drcDetail' },
  },
];

// 未准入境
const ProductQualityProblem6Columns = [
  { title: '产品名称', dataIndex: 'Title' },
  { title: '产品类型', dataIndex: 'CaseReasonType' },
  // { title: '生产企业信息/品牌', dataIndex: 'Applicant' },
  {
    title: '数/重量',
    dataIndex: 'AmountDesc',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  { title: '原因', dataIndex: 'ActionRemark' },
  {
    title: '报送时间',
    dataIndex: 'LianDate',
    customRender: (text) => {
      if (!text || text < 0) return '-';

      return moment(text * 1000).format('YYYY年MM月');
    },
  },
  {
    title: '发布日期',
    width: 103,
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '原文',
    width: 68,
    scopedSlots: { customRender: 'originalSource' },
  },
  { title: '内容', scopedSlots: { customRender: 'action' }, width: 70 },
];

// 药品抽查
const ProductQualityProblem7Columns = [
  { title: '药品品名', dataIndex: 'Specs' },
  {
    title: '检查实施机关',
    dataIndex: 'Court',
  },
  {
    title: '类型',
    dataIndex: 'CaseReasonType',
  },
  {
    title: '检测结果',
    dataIndex: 'ActionRemark',
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: {
      customRender: 'medicineDetail',
    },
  },
];

// 抽查检查-不合格
const SpotCheck = [
  {
    title: '检查实施机关',
    dataIndex: 'court',
  },
  {
    title: '类型',
    width: 110,
    dataIndex: 'casereasontype',
  },
  {
    title: '日期',
    width: 110,
    dataIndex: 'publishdate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '结果',
    scopedSlots: { customRender: 'SpotCheck' },
  },
];

// 假冒化妆品
const ProductQualityProblem8Columns = [
  {
    title: '产品名称',
    dataIndex: 'CaseNo',
  },
  {
    title: '规格',
    dataIndex: 'StockInfo',
  },
  {
    title: '生产商',
    scopedSlots: { customRender: 'applicant' },
  },
  {
    title: '授权商',
    scopedSlots: { customRender: 'pledgorInfo' },
  },
  {
    title: '运营单位',
    dataIndex: 'CaseReason',
  },
  {
    title: '公告时间',
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
  },
];

// 食品安全不合格
const ProductQualityProblem9Columns = [
  {
    title: '食品名称',
    dataIndex: 'Title',
  },
  {
    title: '抽检次数',
    dataIndex: 'Amount2',
    customRender: (text) => {
      return text > 0 ? `第${text}次抽检` : '-';
    },
  },
  {
    title: '被抽检企业',
    scopedSlots: { customRender: 'NameAndKeyNo' },
  },
  {
    title: '标称生产企业',
    scopedSlots: { customRender: 'applicant' },
  },
  {
    title: '标称生产企业地址',
    dataIndex: 'Address',
  },
  {
    title: '商标',
    dataIndex: 'ActionRemark',
  },
  {
    title: '规格型号',
    dataIndex: 'Specs',
  },
  {
    title: '生产日期/批号',
    width: 103,
    customRender: (item) => {
      return dateFormat(item.lianDate || item.LianDate);
    },
  },
  {
    title: '抽检结果',
    scopedSlots: { customRender: 'CheckResult' },
  },
];

// 被列入严重违法失信企业名录
const CompanyCreditColumns = [
  {
    title: '列入日期',
    width: 103,
    datepattern: true,
    dataIndex: 'AddDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '作出决定机关(列入)',
    width: 200,
    dataIndex: 'AddOffice',
  },
  {
    title: '列入严重违法失信企业名单原因',
    dataIndex: 'AddReason',
  },
];

// 被列入严重违法失信企业名录(历史)
const CompanyCreditColumnsHistory = [
  {
    title: '列入日期',
    width: 103,
    datepattern: true,
    dataIndex: 'AddDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '作出决定机关(列入)',
    width: 200,
    dataIndex: 'AddOffice',
  },
  {
    title: '列入严重违法失信企业名单原因',
    dataIndex: 'AddReason',
  },
  {
    title: '移出日期',
    width: 103,
    datepattern: true,
    dataIndex: 'RemoveDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '作出决定机关(移出)',
    dataIndex: 'RemoveOffice',
  },
  {
    title: '移出严重违法失信企业名单原因',
    dataIndex: 'RemoveReason',
  },
];

// 被列入经营异常名录（历史）
const OperationAbnormalColumns = [
  {
    title: '列入日期',
    width: 103,
    dataIndex: 'PublishDate',
    scopedSlots: {
      customRender: 'date',
    },
    // sorter: true,
    // key: 'currencedate',
  },
  {
    title: '作出决定机关(列入)',
    width: 200,
    dataIndex: 'Court',
  },
  {
    title: '列入经营异常名录原因',
    dataIndex: 'CaseReason',
  },
  {
    title: '移出日期',
    width: 103,
    dataIndex: 'LianDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '作出决定机关(移出)',
    dataIndex: 'ActionRemark',
  },
  {
    title: '移出经营异常名录原因',
    dataIndex: 'RemoveReason',
  },
];

// ------------- 行政监管风险 -------------

// ------------- 经营稳定性风险 -------------

// 动产抵押
const ChattelMortgageColumns = [
  {
    title: '登记编号',
    scopedSlots: { customRender: 'RegisterNo' },
  },
  {
    title: '抵押人',
    scopedSlots: {
      customRender: 'ChattelMortgagedyr',
    },
  },
  {
    title: '抵押权人',
    scopedSlots: {
      customRender: 'ChattelMortgagebdyr',
    },
  },
  {
    title: '所有权或使用权归属',
    scopedSlots: { customRender: 'MPledgeDetail' },
  },
  {
    title: '债务人履行债务的期限',
    customRender: (item) => {
      return item?.MPledgeDetail?.GuaranteedCredRight?.FulfillObligation || '-';
    },
  },
  {
    title: '被担保主债权数额',
    dataIndex: 'DebtSecuredAmount',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  {
    title: '状态',
    dataIndex: 'Status',
    scopedSlots: {
      customRender: 'status',
    },
  },
  {
    title: '登记日期',
    datepattern: true,
    width: 103,
    scopedSlots: {
      customRender: 'date',
    },
    dataIndex: 'RegisterDate',
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 股权出质
const EquityPledgeColumns = [
  {
    title: '登记编号',
    width: 180,
    scopedSlots: {
      customRender: 'RegistNo',
    },
  },
  {
    title: '出质人',
    scopedSlots: {
      customRender: 'pledgorInfo',
    },
  },
  {
    title: '质权人',
    scopedSlots: {
      customRender: 'pledgeeInfo',
    },
  },
  {
    title: '出质股权标的企业',
    scopedSlots: {
      customRender: 'relatedCompanyInfo',
    },
  },
  {
    title: '出质股权数额',
    dataIndex: 'PledgedAmount',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  {
    title: '登记日期',
    width: 103,
    dataIndex: 'RegDate',
    scopedSlots: {
      customRender: 'date',
    },
    sorter: true,
    key: 'liandate',
  },
  {
    title: '状态',
    statusTd: true,
    dataIndex: 'Status',
    scopedSlots: {
      customRender: 'status',
    },
  },
  // {
  //   title: '内容',
  //   width: 70,
  //   scopedSlots: {
  //     customRender: 'action',
  //   },
  // },
];

// 土地抵押
const LandMortgageColumns = [
  {
    title: '土地坐落',
    dataIndex: 'Address',
  },
  {
    title: '抵押人',
    scopedSlots: {
      customRender: 'LandMortgagedyr',
    },
  },
  {
    title: '抵押权人',
    scopedSlots: {
      customRender: 'LandMortgagebdyr',
    },
  },
  {
    title: '抵押起止日期',
    scopedSlots: { customRender: 'startEndDate' },
  },
  {
    title: '抵押面积(公顷)',
    dataIndex: 'MortgageAcreage',
  },
  {
    title: '抵押金额(万元)',
    dataIndex: 'MortgagePrice',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 司法拍卖信息
const JudicialAuction1Columns = [
  {
    title: '标题',
    scopedSlots: { customRender: 'biaoti' },
  },
  {
    title: '案号',
    dataIndex: 'Caseno',
  },
  {
    title: '起拍价(元)',
    dataIndex: 'yiwu',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '评估价(元)',
    dataIndex: 'EvaluationPrice',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '拍卖时间',
    width: 300,
    dataIndex: 'actionremark',
  },
  {
    title: '处置单位',
    dataIndex: 'executegov',
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 担保明细
const GuaranteeInfoColumns = [
  {
    title: '被担保方',
    scopedSlots: {
      customRender: 'VoucheeInfo',
    },
  },
  {
    title: '担保方',
    scopedSlots: {
      customRender: 'GuaranteeInfo',
    },
  },
  {
    title: '担保方式',
    dataIndex: 'GuaranteeType',
  },
  {
    title: '担保金额(万元)',
    dataIndex: 'GuaranteeMoney',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '公告日期',
    datepattern: true,
    width: 103,
    dataIndex: 'PublicDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'allAction' },
  },
];

// 担保风险
const GuaranteeRiskColumns = [
  {
    title: '保证类型',
    width: 100,
    dataIndex: 'GuaranteeType',
  },
  {
    title: '被担保方',
    scopedSlots: {
      customRender: 'VoucheeInfo',
    },
  },
  {
    title: '担保方',
    scopedSlots: {
      customRender: 'GuaranteeInfo',
    },
  },
  {
    title: '债权人',
    scopedSlots: {
      customRender: 'Creditor',
    },
  },
  {
    title: '被保证债权本金(万元)',
    width: 150,
    customRender: (item) => {
      return item.GuaranteeMoney ? numberToHuman(item.GuaranteeMoney / 10000, { precision: 2 }) : '-';
    },
  },
  {
    title: '裁判日期',
    width: 103,
    datepattern: true,
    dataIndex: 'Judgedate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '发布日期',
    width: 103,
    datepattern: true,
    dataIndex: 'PublicDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 经营范围变更
const MainInfoUpdateScopeColumns = [
  {
    title: '变更日期',
    width: 103,
    dataIndex: 'ChangeDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '变更前',
    width: 400,
    scopedSlots: { customRender: 'beforeScope' },
  },
  {
    title: '变更后',
    width: 400,
    scopedSlots: { customRender: 'afterScope' },
  },
];

// 注册地址变更
const MainInfoUpdateAddressColumns = [
  {
    title: '变更日期',
    width: 103,
    dataIndex: 'ChangeDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '变更前',
    width: 400,
    scopedSlots: { customRender: 'beforeAddress' },
  },
  {
    title: '变更后',
    width: 400,
    scopedSlots: { customRender: 'afterAddress' },
  },
];

// 企业名称变更
const MainInfoUpdateNameColumns = [
  {
    title: '变更日期',
    width: 103,
    dataIndex: 'ChangeDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '变更前',
    width: 400,
    customCell: (item) => {
      const { beforeContent } = getHighLightDifferent(item.CompanyName, item?.after?.CompanyName);
      return {
        domProps: {
          innerHTML: beforeContent || '-',
        },
      };
    },
  },
  {
    title: '变更后',
    width: 400,
    customCell: (item) => {
      const { afterContent } = getHighLightDifferent(item.CompanyName, item?.after?.CompanyName);
      return {
        domProps: {
          innerHTML: afterContent || '-',
        },
      };
    },
  },
];

// 法定代表人变更
const MainInfoUpdateLegalPersonColumns = [
  {
    title: '变更日期',
    dataIndex: 'ChangeDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '变更前',
    width: 400,
    scopedSlots: { customRender: 'LegalPerson' },
  },
  {
    title: '变更后',
    width: 400,
    scopedSlots: { customRender: 'afterLegalPerson' },
  },
];

// 大股东变更
const MainInfoUpdateHolderColumns = [
  {
    title: '变更日期',
    width: 103,
    dataIndex: 'ChangeDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '变更前',
    width: 400,
    scopedSlots: { customRender: 'BeforeContent' },
  },
  {
    title: '变更后',
    width: 400,
    scopedSlots: { customRender: 'AfterContent' },
  },
];

// 董监高变更
const MainInfoUpdateManagerColumns = [
  {
    title: '变更日期',
    width: 103,
    dataIndex: 'ChangeDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '变更前',
    width: 400,
    scopedSlots: { customRender: 'beforeEmployees' },
  },
  {
    title: '变更后',
    width: 400,
    scopedSlots: { customRender: 'Employees' },
  },
];

// 实际控制人变更
const MainInfoUpdatePersonColumns = [
  {
    title: '变更日期',
    width: 103,
    dataIndex: 'ChangeDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '变更前',
    width: 400,
    scopedSlots: { customRender: 'BeforeContent' },
  },
  {
    title: '变更后',
    width: 400,
    scopedSlots: { customRender: 'AfterContent' },
  },
];

// 债券违约
const BondDefaultsColumns = [
  { title: '债券简称', dataIndex: 'BondShortName' },
  { title: '债券类型', dataIndex: 'BondTypeName' },
  { title: '违约状态', dataIndex: 'DefaultStatusDesc' },
  {
    title: '首次违约日期',
    width: 120,
    dataIndex: 'FirstDefaultDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'firstdefaultdate',
  },
  {
    title: '累计违约本金(亿元)',
    dataIndex: 'AccuOverdueCapital',
    scopedSlots: { customRender: 'money' },
    sorter: true,
    key: 'overduecapitalval',
  },
  {
    title: '累计违约利息(亿元)',
    dataIndex: 'AccuOverdueInterest',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '到期日期',
    dataIndex: 'MaturityDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'maturitydate',
  },
  { title: '违约历程', width: 80, scopedSlots: { customRender: 'action' } },
];

// ------------- 经营稳定性风险 -------------

// ------------- 负面舆情 -------------
// ------------- 负面舆情 -------------

// ------------- 黑名单排查 -------------
// 外部黑名单
const HitOuterBlackListColumns = [
  {
    title: '黑名单企业名称',
    width: 220,
    scopedSlots: { customRender: 'blacklistCompanyName' },
  },
  {
    title: '命中黑名单类型',
    width: 180,
    dataIndex: 'CaseReasonType',
  },
  // {
  //   title: '风险等级',
  //   dataIndex: 'level',
  //   width: 80,
  //   scopedSlots: { customRender: 'riskLevel' },
  // },
  {
    title: '列入原因',
    dataIndex: 'CaseReason',
    width: 280,
  },
  {
    title: '列入机关',
    dataIndex: 'Court',
  },
  {
    title: '列入日期',
    width: 103,
    dataIndex: 'Publishdate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 内部黑名单
export const HitInnerBlackListColumns = [
  {
    title: '黑名单企业名称',
    scopedSlots: { customRender: 'companyName' },
  },
  {
    title: '列入原因',
    dataIndex: 'reason',
    width: 400,
  },
  {
    title: '列入日期',
    width: 103,
    dataIndex: 'joinDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '黑名单有效期',
    customRender: blacklistDurationFormatter,
  },
];

export const EmploymentRelationshipColumns = [
  {
    title: '关联人员',
    scopedSlots: { customRender: 'personName' },
  },
  {
    title: '关联黑名单企业名称',
    width: 260,
    scopedSlots: { customRender: 'relatedCompanyNameWithTag' },
  },
  {
    title: '关联链',
    scopedSlots: { customRender: 'path' },
  },
  {
    title: '图谱',
    width: 70,
    scopedSlots: { customRender: 'atlasAction' },
  },
];

export const ShareholderColumns = [
  {
    title: '关联黑名单企业名称',
    width: 260,
    scopedSlots: { customRender: 'relatedCompanyNameWithTag' },
  },
  {
    title: '持股比例',
    width: 100,
    customRender: renderStockPercent,
  },
  {
    title: '列入原因',
    dataIndex: 'reason',
    width: '25%',
  },
  {
    title: '列入日期',
    width: 103,
    dataIndex: 'joinDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '黑名单有效期',
    width: 100,
    customRender: blacklistDurationFormatter,
  },
  {
    title: '图谱',
    width: 70,
    scopedSlots: { customRender: 'atlasAction' },
  },
];

export const ForeignInvestmentColumns = [
  {
    title: '关联黑名单企业名称',
    width: 260,
    scopedSlots: { customRender: 'relatedCompanyNameWithTag' },
  },
  {
    title: '持股比例',
    width: 100,
    customRender: renderStockPercent,
  },
  {
    title: '列入原因',
    dataIndex: 'reason',
    width: '25%',
  },
  {
    title: '列入日期',
    width: 103,
    dataIndex: 'joinDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '黑名单有效期',
    width: 100,
    customRender: blacklistDurationFormatter,
  },
  {
    title: '图谱',
    width: 70,
    scopedSlots: { customRender: 'atlasAction' },
  },
];

// ------------- 黑名单排查 -------------

// 潜在利益冲突
export const ConflictInterestColumns = [
  {
    title: '姓名',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
  },
  {
    title: '“潜在利冲”人员',
    scopedSlots: { customRender: 'PersonNo' },
  },
  {
    title: '“潜在利冲”人员分组',
    dataIndex: 'group',
  },
  {
    title: '操作',
    scopedSlots: { customRender: 'ActionCheck' },
  },
];

// 潜在利益冲突 相同联系方式
const ConflictInterestSamePhoneColumns = [
  {
    title: '联系人',
    customRender: (scope) => {
      const str = String(scope?.contacts || '')
        .split(',')
        .filter((item) => item)
        .join('，');
      return str;
    },
  },
  {
    title: '联系方式',
    customRender: (scope) => {
      const str = `${scope.phones || ''},${scope.emails || ''}`
        .split(',')
        .filter((item) => item)
        .join('，');
      return str;
    },
  },
  {
    title: '“潜在利冲”人员',
    scopedSlots: { customRender: 'PersonNo' },
  },
  {
    title: '“潜在利冲”人员姓名',
    dataIndex: 'name',
  },
  {
    title: '“潜在利冲”人员分组',
    dataIndex: 'group',
  },
];

// 潜在利益冲突 对外投资
export const StaffForeignInvestmentColumns = [
  {
    title: '姓名',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
  },
  {
    title: '持股比例',
    customRender: renderStockPercent,
  },
  {
    title: '“潜在利冲”人员',
    scopedSlots: { customRender: 'PersonNo' },
  },
  {
    title: '“潜在利冲”人员分组',
    dataIndex: 'group',
  },
  {
    title: '操作',
    scopedSlots: { customRender: 'ActionCheck' },
  },
];

// 潜在利益冲突
export const StaffWorkingOutsideForeignInvestmentColumns = [
  {
    title: '姓名',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
  },
  {
    title: '“潜在利冲”人员',
    scopedSlots: { customRender: 'PersonNo' },
  },
  {
    title: '“潜在利冲”人员分组',
    dataIndex: 'group',
  },
];

// 疑似潜在利益冲突
export const SuspectedInterestConflictColumns = [
  {
    title: '疑似关系',
    scopedSlots: { customRender: 'interestConflictPerson' },
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
  },
  {
    title: '“疑似潜在利冲”人员',
    scopedSlots: { customRender: 'PersonNo' },
  },
  {
    title: '“潜在利冲”人员分组',
    dataIndex: 'group',
  },
  {
    title: '操作',
    scopedSlots: { customRender: 'ActionCheck' },
  },
];

// 注销备案
const CancellationOfFilingColumns = [
  {
    title: '清算组备案日期',
    width: 200,
    dataIndex: 'LiqBADate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '债权人公告日期',
    valueKeyList: ['PublicStartDate', 'PublicEndDate'],
    formatter: 'YYYY年MM月DD日',
    scopedSlots: { customRender: 'timeDuration' },
  },
  {
    title: '公告状态',
    width: 200,
    dataIndex: 'NoticeStatus',
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 合作方交叉重叠
export const ShareholdingRelationshipColumns = [
  {
    title: '关联企业名称',
    width: 260,
    scopedSlots: { customRender: 'relatedCompanyNameWithTag' },
  },
  {
    title: '持股比例',
    customRender: renderStockPercent,
  },
  {
    title: '关联链',
    scopedSlots: { customRender: 'path' },
  },
  {
    title: '图谱',
    width: 70,

    scopedSlots: { customRender: 'atlasAction' },
  },
];

export const ServeRelationshipColumns = [
  {
    title: '关联人员',
    scopedSlots: { customRender: 'personName' },
  },
  {
    title: '关联企业名称',
    width: 260,
    scopedSlots: { customRender: 'relatedCompanyName' },
  },
  {
    title: '关联链',
    scopedSlots: { customRender: 'path' },
  },
  {
    title: '图谱',
    width: 70,

    scopedSlots: { customRender: 'atlasAction' },
  },
];

export const PartnershipColumns = [
  {
    title: '关联企业名称',
    width: 260,
    scopedSlots: { customRender: 'relatedCompanyNameWithTag' },
  },
  {
    title: '持股比例',
    customRender: renderStockPercent,
  },
  {
    title: '关联链',
    scopedSlots: { customRender: 'path' },
  },
  {
    title: '图谱',
    width: 70,

    scopedSlots: {
      customRender: 'atlasAction',
    },
  },
];

export const BlacklistPartnerInvestigationColumns = [
  {
    title: '关联企业名称',
    width: 280,
    scopedSlots: { customRender: 'relatedCompanyNameWithTag' },
  },
  {
    title: '列入原因',
    width: 150,
    _showReason: true,
    customCell: () => {
      return {
        attrs: {
          colSpan: 4,
        },
      };
    },
    scopedSlots: { customRender: 'relateType' },
  },
  {
    title: '关联类型',
    width: 150,
    customRender: () => {
      return {
        attrs: {
          colSpan: 0,
        },
      };
    },
  },
  {
    title: '关联路径详情',
    customRender: () => {
      return {
        attrs: {
          colSpan: 0,
        },
      };
    },
  },
  {
    title: '内容',
    width: 60,
    customRender: () => {
      return {
        attrs: {
          colSpan: 0,
        },
      };
    },
  },
];

/// 与第三方列表企业存在投资任职关联
export const CustomerPartnerInvestigationColumns = [
  {
    title: '关联企业名称',
    width: 280,
    scopedSlots: { customRender: 'relatedCompanyNameWithTag' },
  },
  {
    title: '关联类型',
    width: 150,
    _showReason: false,
    customCell: () => {
      return {
        attrs: {
          colSpan: 3,
        },
      };
    },
    scopedSlots: { customRender: 'relateType' },
  },
  {
    title: '关联路径详情',
    customRender: () => {
      return {
        attrs: {
          colSpan: 0,
        },
      };
    },
  },
  {
    title: '内容',
    width: 60,
    customRender: () => {
      return {
        attrs: {
          colSpan: 0,
        },
      };
    },
  },
];

// 在外任职
const PunishedEmployeesWorkingOutsideColumns = [
  {
    title: '姓名',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
  },
  {
    title: '人员编号',
    scopedSlots: { customRender: 'PersonNo' },
  },
  {
    title: '人员分组',
    dataIndex: 'group',
  },
  {
    title: '操作',
    scopedSlots: { customRender: 'ActionCheck' },
  },
];

// 对外投资
const PunishedEmployeesForeignInvestmentColumns = [
  {
    title: '姓名',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
  },
  {
    title: '持股比例',
    customRender: renderStockPercent,
  },
  {
    title: '人员编号',
    scopedSlots: { customRender: 'PersonNo' },
  },
  {
    title: '人员分组',
    dataIndex: 'group',
  },
  {
    title: '操作',
    scopedSlots: { customRender: 'ActionCheck' },
  },
];

export const CompanyOrMainMembersCriminalOffence = [
  {
    title: '案件名称',
    dataIndex: 'CaseName',
    width: 260,
  },
  // {
  //   title: '案件身份',
  //   width: 120,
  //   scopedSlots: {
  //     customRender: 'CaseIdentity',
  //   },
  // },

  {
    title: '当事人',
    width: 360,
    scopedSlots: { customRender: 'RoleAmt' },
  },

  {
    title: '案号',
    scopedSlots: { customRender: 'AnNoList' },
  },

  // {
  //   title: '案件金额(元)',
  //   customRender: (item) => {
  //     try {
  //       const route = useRoute();
  //       if (!route?.params) return null;
  //       const AmtInfo = JSON.parse(item.AmtInfo);
  //       return get(AmtInfo, `${route.params.id}.Amt`);
  //     } catch (error) {
  //       console.error(error);
  //       return '-';
  //     }
  //   },
  // },

  {
    title: '最新案件进程',
    scopedSlots: { customRender: 'LastCaseProgress' },
  },
  {
    title: '法院',
    scopedSlots: { customRender: 'CourtList' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 买卖合同纠纷/重大纠纷
// 近3年涉贪污受贿裁判相关提及方/涉贪污受贿裁判相关提及方（3年以上及其他）
const Dispute = ({ sortable = true } = {}) => {
  return [
    {
      title: '文书标题',
      width: '15%',
      scopedSlots: { customRender: 'caseName' },
    },
    {
      title: '案号',
      // width: '15%',
      scopedSlots: { customRender: 'CaseNo' },
    },
    {
      title: '案由',
      width: 100,
      dataIndex: 'casereason',
    },
    {
      title: '当事人',
      width: '15%',
      scopedSlots: { customRender: 'Parties' },
    },
    {
      title: '案件金额(元) ',
      width: 120,
      dataIndex: 'amountinvolved',
      scopedSlots: { customRender: 'money' },
    },
    {
      title: '裁判结果',
      // width: '15%',
      dataIndex: 'judgeresult',
      scopedSlots: { customRender: 'clampcontent' },
    },
    {
      title: '裁判日期',
      dataIndex: 'judgedate',
      width: 100,
      scopedSlots: { customRender: 'date' },
      sorter: sortable,
    },
    {
      title: '发布日期',
      width: 100,
      dataIndex: 'submitdate',
      scopedSlots: { customRender: 'date' },
      sorter: sortable,
      key: 'courtdate',
    },
    {
      title: '内容',
      width: 70,
      scopedSlots: { customRender: 'urlAction' },
    },
  ];
};

const SameControllRelation = [
  {
    title: '关联企业名称',
    width: 260,
    scopedSlots: { customRender: 'companyName' },
  },
  {
    title: '实际控制人',
    width: 260,
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '控制链',
    scopedSlots: { customRender: 'detailPath' },
  },
  {
    title: '图谱',
    width: 70,

    scopedSlots: { customRender: 'controllerAtlasAction' },
  },
];

const BlacklistSameSuspectedActualController = [
  {
    title: '关联黑名单企业名称',
    width: 260,
    scopedSlots: { customRender: 'companyName' },
  },
  {
    title: '实际控制人',
    width: 260,
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '控制链',
    scopedSlots: { customRender: 'detailPath' },
  },
  {
    title: '图谱',
    width: 70,

    scopedSlots: { customRender: 'controllerAtlasAction' },
  },
];

// 近期变更受益所有人
const MainInfoUpdateBeneficiaryColumns = [
  {
    title: '变更日期',
    width: 103,
    dataIndex: 'ChangeDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '变更前',
    width: 400,
    scopedSlots: { customRender: 'BeforeChange' },
  },
  {
    title: '变更后',
    width: 400,
    scopedSlots: { customRender: 'AfterChange' },
  },
];

// 票据违约
const BillDefaultsColumns = [
  {
    title: '承兑人',
    width: 320,
    scopedSlots: { customRender: 'companyNamePascal' },
  },
  {
    title: '承兑人开户行',
    width: 320,
    dataIndex: 'BankName',
  },
  {
    title: '截止日期',
    width: 100,
    dataIndex: 'EndDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '披露日期',
    width: 100,
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'publishdate',
  },
  {
    title: '逾期金额(元)',
    dataIndex: 'OverdueBalance',
    scopedSlots: { customRender: 'money' },
    sorter: true,
    key: 'overduebalance',
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: {
      customRender: 'action',
    },
  },
];

// 税务催缴公告
const TaxCallNoticeColumns = [
  {
    title: '标题',
    width: 520,
    dataIndex: 'title',
  },
  {
    title: '发布机构',
    width: 300,
    dataIndex: 'courtname',
  },
  {
    title: '发布日期',
    width: 100,
    dataIndex: 'publicdate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '公告类型',
    width: 100,
    dataIndex: 'noticetype',
  },
  {
    title: '内容',
    width: 70,
    dataIndex: 'id',
    scopedSlots: { customRender: 'govNoticeDetail' },
  },
];

// 税务催缴
const TaxCallNoticeV2Columns = [
  {
    title: '税种',
    width: 120,
    dataIndex: 'TaxCategory',
  },
  {
    title: '欠缴金额（元）',
    width: 120,
    dataIndex: 'AmountOwed',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '所属期起',
    width: 100,
    dataIndex: 'PeriodStartDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '所属期止',
    width: 100,
    dataIndex: 'PeriodEndDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '缴款期限',
    width: 100,
    dataIndex: 'PaymentDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '主管税务机关',
    width: 300,
    dataIndex: 'TaxKeyNoArray',
    scopedSlots: { customRender: 'taxOffice' },
  },
  {
    title: '发布日期',
    width: 100,
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 税务催报
const TaxReminderColumns = [
  {
    title: '税种',
    width: 120,
    dataIndex: 'TaxCategory',
  },
  {
    title: '所属期起',
    width: 100,
    dataIndex: 'PeriodStartDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '所属期止',
    width: 100,
    dataIndex: 'PeriodEndDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '申报期限',
    width: 100,
    dataIndex: 'PaymentDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '主管税务机关',
    width: 300,
    dataIndex: 'TaxKeyNoArray',
    scopedSlots: { customRender: 'taxOffice' },
  },
  {
    title: '发布日期',
    width: 100,
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 终本案件
const EndExecutionCaseColumns = [
  {
    title: '案号',
    width: 300,
    scopedSlots: { customRender: 'endExecutionCaseDetail' },
  },
  {
    title: '被执行人',
    scopedSlots: { customRender: 'NameAndKeyNo' },
  },
  {
    title: '疑似申请执行人',
    scopedSlots: { customRender: 'SqrInfo' },
  },
  {
    title: '未履行金额(元)',
    width: 135,
    dataIndex: 'FailureAct',
    scopedSlots: { customRender: 'money' },
    sorter: true,
  },
  {
    title: '执行标的(元)',
    width: 135,
    dataIndex: 'ExecuteObject',
    scopedSlots: { customRender: 'money' },
    sorter: true,
  },
  {
    title: '执行法院',
    dataIndex: 'Court',
  },
  {
    title: '立案日期',
    dataIndex: 'LiAnDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'liandate',
  },
  {
    title: '终本日期',
    dataIndex: 'EndDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
  },
];

// 公安通告
const SecurityNoticeColumns = [
  {
    title: '涉案企业',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '涉案案由',
    dataIndex: 'reason',
  },
  {
    title: '发布单位',
    dataIndex: 'publishUnit',
  },
  {
    title: '发布日期',
    width: 140,
    dataIndex: 'publishDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '内容',
    with: 100,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 监管处罚
const RegulateFinanceColoums = [
  {
    title: '决定文书号',
    dataIndex: 'caseno',
    width: 200,
  },
  {
    title: '违规事实',
    dataIndex: 'punishReason',
    scopedSlots: { customRender: 'clampContent' },
  },
  {
    title: '处理结果',
    dataIndex: 'Title',
    scopedSlots: { customRender: 'clampContent' },
  },
  {
    title: '处理单位',
    width: 160,
    dataIndex: 'Court',
  },
  {
    title: '处理日期',
    width: 100,
    dataIndex: 'PunishDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '内容',
    width: 70,
    customRender: (item) => {
      return (
        <a target="_blank" href={`/embed/adminpenaltydetail?id=${item.RiskId}`}>
          详情
        </a>
      );
    },
  },
];

const CapitalReductionColumns = [
  {
    title: '公告企业',
    width: 200,
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '公告内容',
    dataIndex: 'Content',
  },
  {
    title: '公告期限',
    width: 170,
    dataIndex: 'NoticePeriod',
  },
  {
    title: '公告日期',
    width: 100,
    dataIndex: 'DecideDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: {
      customRender: 'action',
    },
  },
];

// 劳动纠纷
const LaborContractDisputeColumns = [
  {
    title: '案件名称',
    scopedSlots: { customRender: 'caseName' },
  },
  {
    title: '案件身份',
    width: '11%',
    scopedSlots: {
      customRender: 'CaseIdentity',
    },
  },
  // {
  //   title: '案由',
  //   width: '9%',
  //   align: 'left',
  //   customCell: 'CaseReasonInfo'
  // },
  {
    title: '案号',
    width: '18.5%',
    scopedSlots: {
      customRender: 'AnNoList',
    },
  },
  {
    title: () => {
      return (
        <div>
          <span>案件金额(元)</span>
          <Tooltip
            overlayClassName="self-customed"
            title="该数据是基于裁判文书、终本案件、被执行人数据分析得出，仅供参考，并不代表企查查任何明示、暗示之观点或保证"
          >
            <QIcon type="icon-icon_zhushi" style="color: #d8d8d8;margin-left: 4px;"></QIcon>
          </Tooltip>
        </div>
      );
    },
    width: '11%',
    dataIndex: 'Amt',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: () => {
      return (
        <div>
          <span>最新案件进程</span>
          <Tooltip
            overlayClassName="self-customed"
            title="最新案件进程是基于司法案件已公开的基础数据维度综合分析得出，仅供参考，并不代表企查查任何明示、暗示之观点或保证。"
          >
            <QIcon type="icon-icon_zhushi" style="color: #d8d8d8;margin-left: 4px;"></QIcon>
          </Tooltip>
        </div>
      );
    },
    width: '12%',
    customRender: (record) => {
      return <div style="white-space: pre;" domPropsInnerHTML={record.LatestDateTrialRound}></div>;
    },
  },
  // {
  //   title: '最新进程日期',
  //   width: '115',
  //   dataIndex: 'LastestDateNew',
  //   datepattern: true
  // },
  {
    title: '法院',
    width: '12%',
    scopedSlots: { customRender: 'CourtList' },
  },
  {
    title: '内容',
    width: 60,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 资质筛查
const CertificationColumns = [
  {
    title: '资质证书',
    dataIndex: 'name',
  },
  {
    title: '状态',
    dataIndex: 'expirationDesc',
    customRender: (text) => {
      const map = {
        0: ['正常', '有效'],
        1: ['近7日到期', '近3个月到期', '近1个月到期'],
        2: ['缺失', '已到期'],
      };
      const key = Object.keys(map).find((k) => map[k].includes(text));
      return <span style={getRiskLevelStyle(key)}>{text}</span>;
    },
  },
  {
    title: '有效期',
    customRender: (record) => {
      const std = record.startDate ? moment(record.startDate * 1000).format('YYYY-MM-DD') : '-';
      const edd = record.endDate ? moment(record.endDate * 1000).format('YYYY-MM-DD') : '-';
      if (std === '-' && edd === '-') {
        return '-';
      }
      return `${std} 至 ${edd}`;
    },
  },
];

const StockPledgeColumns = [
  {
    title: '质押人',
    width: 200,
    dataIndex: 'Holders',
    scopedSlots: { customRender: 'companyList' },
  },
  {
    title: () => {
      return (
        <div>
          质押人参股企业
          <QGlossaryInfo tooltip="即股权被质押企业" />
        </div>
      );
    },
    width: 200,
    dataIndex: 'Companys',
    scopedSlots: { customRender: 'companyList' },
  },
  {
    title: '质押权人',
    dataIndex: 'Pledgees',
    width: 200,
    scopedSlots: { customRender: 'companyList' },
  },
  {
    title: '质押股份总数(股)',
    width: 134,
    dataIndex: 'ShareFrozenNum',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  {
    title: '质押股份市值(元)',
    width: 132,
    dataIndex: 'SZ',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  {
    title: '状态',
    width: 105,
    dataIndex: 'Type',
  },
  {
    title: '公告日期',
    width: 108,
    key: 'publicdate',
    dataIndex: 'NoticeDate',
    scopedSlots: {
      customRender: 'date',
    },
    sorter: true,
  },
  {
    title: '内容',
    width: 60,
    scopedSlots: {
      customRender: 'action',
    },
  },
];

const BusinessAbnormal4Columns = [
  {
    title: '纳税人识别号',
    dataIndex: 'taxUnormalsCaseNo',
  },
  {
    title: '列入机关',
    dataIndex: 'taxUnormalsExecuteGov',
  },
  {
    title: '列入日期',
    dataIndex: 'joinTime',
    scopedSlots: {
      customRender: 'date',
    },
  },
];

// 被列入税务非正常户
const TaxUnnormalsColumns = [
  {
    title: '纳税人识别号',
    dataIndex: 'CaseNo',
  },
  {
    title: '列入机关',
    dataIndex: 'ExecuteGov',
  },
  {
    title: '列入日期',
    width: 180,
    dataIndex: 'JoinDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
];

const JudicialCaseColumns = [
  {
    title: '案件名称',
    customRender: (record) => {
      if (!record.Id) return record.CaseName || '-';
      return (
        <a href={`/embed/courtCaseDetail?caseId=${record.Id}&title=${record.CaseName}`} target="_blank">
          {record.CaseName || '-'}
        </a>
      );
    },
  },
  {
    title: '进程日期',
    width: 110,
    scopedSlots: { customRender: 'judicialCase' },
    customCell: () => {
      return {
        attrs: {
          colSpan: 5,
          style: 'padding: 0;',
        },
      };
    },
  },
  {
    title: '案件进程',
    width: 90,
    dataIndex: 'TrailRoundDetail.TrialRound',
    customRender: () => {
      return {
        attrs: {
          colSpan: 0,
        },
      };
    },
  },
  {
    title: '案件身份',
    width: 150,
    dataIndex: 'TrailRoundDetail.CaseRole',
    scopedSlots: { customRender: 'caseRole' },
    customRender: () => {
      return {
        attrs: {
          colSpan: 0,
        },
      };
    },
  },
  {
    title: '案号',
    width: 220,
    dataIndex: 'TrailRoundDetail.CaseNo',
    customRender: () => {
      return {
        attrs: {
          colSpan: 0,
        },
      };
    },
  },
  {
    title: '法院',
    width: 160,
    dataIndex: 'TrailRoundDetail.Court',
    customRender: () => {
      return {
        attrs: {
          colSpan: 0,
        },
      };
    },
  },
  {
    title: '案件金额(元)',
    width: 130,
    dataIndex: 'AmtInfo.Amt',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  {
    title: '未履行金额(元)',
    width: 130,
    dataIndex: 'UnfulfilledAmt',
    scopedSlots: { customRender: 'UnfulfilledAmt' },
  },
];

const CourtNoticeColumns = [
  {
    title: '案号',
    scopedSlots: { customRender: 'CaseSearchId' },
  },
  {
    title: '案由',
    dataIndex: 'casereason',
  },
  {
    title: '当事人',
    dataIndex: 'RoleList',
    scopedSlots: { customRender: 'partyContent' },
  },
  {
    title: '法院',
    dataIndex: 'executegov',
  },
  {
    title: '开庭时间',
    sorter: true,
    dataIndex: 'liandate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '内容',
    width: 60,
    scopedSlots: {
      customRender: 'action',
    },
  },
];

const CaseRegistrationColumns = [
  {
    title: '案号',
    customRender: (record) => {
      if (record.CaseSearchId) {
        return (
          <a href={`/embed/courtCaseDetail?caseId=${record.CaseSearchId}&anno=${record.CaseNo}`} target="_blank">
            {record.CaseNo}
          </a>
        );
      }
      return record.CaseNo || '-';
    },
  },
  { title: '案由', dataIndex: 'CaseReason', scopedSlots: { customRender: 'CaseReason' } },
  { title: '当事人', dataIndex: 'RoleList', scopedSlots: { customRender: 'RoleList' } },
  { title: '法院', dataIndex: 'ExecuteGov' },
  {
    title: '立案日期',
    dataIndex: 'PunishDate',
    sorter: true,
    scopedSlots: { customRender: 'date' },
    dateProps: {
      pattern: 'YYYY-MM-DD',
      defaultVal: '-',
      x1000: true,
    },
  },
  { title: '内容', scopedSlots: { customRender: 'action' } },
];

// 案件风险
const JudgementColumns = [
  {
    title: '文书标题',
    width: '15%',
    scopedSlots: { customRender: 'CaseNo' },
  },
  {
    title: '案件金额(元) ',
    width: 120,
    dataIndex: 'amountinvolved',
    scopedSlots: { customRender: 'money' },
    sorter: true,
  },
  {
    title: '案号',
    width: '15%',
    dataIndex: 'caseno', // FIXME: 字段存在大小写问题
  },
  {
    title: '案由',
    width: 100,
    dataIndex: 'casereason', // FIXME: 字段存在大小写问题
  },
  {
    title: '当事人',
    width: '15%',
    scopedSlots: { customRender: 'Parties' },
  },
  {
    title: '执行法院',
    dataIndex: 'court',
  },
  {
    title: '裁判结果',
    dataIndex: 'judgeresult', // FIXME: 字段存在大小写问题
    scopedSlots: { customRender: 'clampContent' },
  },
  {
    title: '发布日期',
    width: 100,
    dataIndex: 'submitdate', // FIXME: 字段存在大小写问题
    scopedSlots: { customRender: 'date' },
    key: 'courtdate',
    sorter: true,
  },
  {
    title: '裁判日期',
    dataIndex: 'judgedate', // FIXME: 字段存在大小写问题
    width: 100,
    scopedSlots: { customRender: 'date' },
    sorter: true,
  },
];

const ReviewAndInvestigationColumns = [
  {
    title: '公告类型',
    width: 150,
    dataIndex: 'type',
  },
  {
    title: '姓名',
    width: 150,
    scopedSlots: { customRender: 'reviewAndInvestigationName' },
  },
  {
    title: '职务',
    width: 150,
    dataIndex: 'job',
  },
  {
    title: '纪检监察机关',
    dataIndex: 'disciplinaryOrg',
  },
  {
    title: '党纪处分',
    width: 150,
    dataIndex: 'partyPunishDesc',
  },
  {
    title: '政务处分',
    width: 150,
    dataIndex: 'politicalPunishDesc',
  },
  {
    title: '发布日期',
    width: 100,
    dataIndex: 'publishDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '处分详情',
    dataIndex: 'punishResult',
    scopedSlots: { customRender: 'clampContent' },
  },
];

const BeneficialOwnersControlNumerousEnterprisesColumns = [
  {
    title: '企业名称',
    scopedSlots: { customRender: 'companyNameLowerCamel' },
  },
  {
    title: '受益所有人',
    scopedSlots: { customRender: 'benefitPerson' },
  },
  {
    title: '成立日期',
    width: 100,
    dataIndex: 'startDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '法定代表人',
    scopedSlots: { customRender: 'operPerson' },
  },
  {
    title: '注册地址',
    dataIndex: 'address',
  },
  {
    title: '登记状态',
    dataIndex: 'shortStatus',
    scopedSlots: { customRender: 'companyStatus' },
  },
];

// 【同法定代表人企业众多且地区分散】
const QfkRisk2210Columns = [
  {
    title: '企业名称',
    scopedSlots: { customRender: 'companyLowerCamel' },
  },
  {
    title: '持股比例',
    dataIndex: 'stockPercent',
  },
  {
    title: '担任法定代表人起止时间',
    scopedSlots: { customRender: 'dateRange' },
  },
  {
    title: '注册资本',
    dataIndex: 'regCap',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  {
    title: '地区',
    dataIndex: 'province',
  },
  {
    title: '行业',
    dataIndex: 'industry',
  },
  {
    title: '状态',
    dataIndex: 'status',
    scopedSlots: { customRender: 'companyStatus' },
  },
];

// 控制权分散
const QfkRisk6803Columns = [
  {
    title: '疑似实控人名称',
    scopedSlots: { customRender: 'companyLowerCamel' },
  },
  {
    title: '持股比例',
    dataIndex: 'stockPercent',
  },
];

// 【实际控制人控制企业集中注册且均无实缴资本】
const QfkRisk6609Columns = [
  {
    title: '企业名称',
    width: 300,
    scopedSlots: { customRender: 'companyNameLowerCamel' },
  },
  {
    title: '成立日期',
    width: 100,
    dataIndex: 'startDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
  },
  {
    title: '法定代表人',
    width: 150,
    scopedSlots: { customRender: 'operPerson' },
  },
  {
    title: '注册地址',
    dataIndex: 'address',
  },
];

// 【实际控制人控制企业涉及高风险行业】
// 法定代表人控制企业涉及高风险行业
const QfkRisk6610Columns = [
  {
    title: '企业名称',
    scopedSlots: { customRender: 'companyNameLowerCamel' },
  },
  {
    title: '成立日期',
    width: 100,
    dataIndex: 'startDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '法定代表人',
    width: 150,
    scopedSlots: { customRender: 'operPerson' },
  },
  {
    title: '经营范围',
    width: 600,
    dataIndex: 'scope',
    scopedSlots: { customRender: 'clampContent' },
  },
];

// 联系方式或注册地址重复
const QfkRisk2010Columns = [
  {
    title: '企业名称',
    scopedSlots: { customRender: 'companyLowerCamel' },
  },
  {
    title: '法定代表人',
    scopedSlots: { customRender: 'operPerson' },
  },
  {
    title: '注册资本',
    dataIndex: 'registCapi',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  {
    title: '成立日期',
    width: 100,
    dataIndex: 'startDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '登记状态',
    dataIndex: 'shortStatus',
    scopedSlots: { customRender: 'companyStatus' },
  },
];

// 同实际控制人企业众多增加不确定风险
const QfkRisk2310Columns = [
  {
    title: '企业名称',
    scopedSlots: { customRender: 'companyLowerCamel' },
  },
  {
    title: '投资比例',
    dataIndex: 'percentTotal',
  },
  {
    title: '投资链',
    scopedSlots: { customRender: 'investmentPath' },
  },
];

// 法定代表人控制企业集中注册且均无实缴资本
const QfkRisk6709Columns = [
  {
    title: '企业名称',
    width: 300,
    scopedSlots: { customRender: 'companyNameLowerCamel' },
  },
  {
    title: '成立日期',
    width: 100,
    dataIndex: 'startDate',
    sorter: true,
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '法定代表人',
    scopedSlots: { customRender: 'operPerson' },
  },
  {
    title: '异常信息类型',
    customRender: () => {
      return '法定代表人控制企业集中注册且均无实缴资本';
    },
  },
];

// 关联方成员集中注册且均无实缴资本
const QfkRisk7099Columns = [
  {
    title: '企业名称',
    scopedSlots: { customRender: 'companyNameLowerCamel' },
  },
  {
    title: '成立日期',
    width: 100,
    dataIndex: 'startDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '法定代表人',
    scopedSlots: { customRender: 'operPerson' },
  },
  {
    title: '注册地址',
    dataIndex: 'address',
  },
  {
    title: '关联方类型',
    dataIndex: 'relatedType',
    customRender: (text) => {
      return text.split(';').map((v) => <div>{v.trim()}</div>);
    },
  },
];

// 实际控制人控制企业位于边境贸易区
const QfkRisk6611Columns = [
  {
    title: '企业名称',
    scopedSlots: { customRender: 'companyNameLowerCamel' },
  },
  {
    title: '成立日期',
    width: 100,
    dataIndex: 'startDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '法定代表人',
    scopedSlots: { customRender: 'operPerson' },
  },
  {
    title: '注册地址',
    dataIndex: 'address',
  },
];

// 【来自高风险I类国家或地区】
const QfkRisk1410Columns = [
  {
    title: '发起人及出资人',
    scopedSlots: { customRender: 'investor' },
  },
  {
    title: '来自国家或地区',
    dataIndex: 'area',
  },
];

// 行政许可被中止或者注销
const QfkRisk1312Columns = [
  {
    title: '许可证编号',
    dataIndex: 'licenseNo',
  },
  {
    title: '业务类型',
    dataIndex: 'type',
  },
  {
    title: '业务覆盖范围',
    dataIndex: 'coverageArea',
  },
  {
    title: '换证日期',
    dataIndex: 'renewalDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '首次许可日期',
    dataIndex: 'issueDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '有效期至',
    dataIndex: 'endDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
];

// 注册资本降幅过大
const QfkRisk6907Columns = [
  {
    title: '变更日期',
    width: 103,
    dataIndex: 'changeDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '变更项目',
    dataIndex: 'projectName',
  },
  {
    title: '变更前',
    width: 400,
    dataIndex: 'beforeInfo',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  {
    title: '变更后',
    width: 400,
    dataIndex: 'afterInfo',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
];

const SeriousViolationColumns = [
  {
    title: '企业名称',
    scopedSlots: { customRender: 'companyNameRelated' },
  },
  // TODO 成立日期 & 法定代表人还没有
  // {
  //   title: '成立日期',
  //   width: 100,
  //   dataIndex: 'startDate',
  //   scopedSlots: { customRender: 'date' },
  // },
  // {
  //   title: '法定代表人',
  //   scopedSlots: { customRender: 'operPerson' },
  // },
  {
    title: '关联方类型',
    width: 500,
    dataIndex: 'relatedTypeDescList',
    scopedSlots: { customRender: 'relatedTypeDesc' },
  },
  {
    title: '信息类型',
    width: 100,
    scopedSlots: { customRender: 'riskType' },
  },
];

// 违规处理
const ViolationProcessingsColumns = [
  {
    title: '处罚对象',
    scopedSlots: { customRender: 'violationPerson' },
  },
  {
    title: '职务',
    dataIndex: 'job',
  },
  {
    title: '违规类型',
    dataIndex: 'type',
  },
  {
    title: '公告日期',
    dataIndex: 'publicdate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
  },
  {
    title: '内容',
    scopedSlots: { customRender: 'action' },
  },
];

const EmployeeListColumns = [
  {
    title: '姓名',
    customRender: (_, record) => {
      return (
        <QEntityLink
          coy-obj={{
            KeyNo: record.keyNo,
            Name: record.name,
          }}
        ></QEntityLink>
      );
    },
  },
  {
    title: '职务',
    dataIndex: 'job',
  },
  {
    title: '持股比例',
  },
  {
    title: '最终收益股份',
  },
  {
    title: '个人简介',
  },
];

const PartnerListColumns = [
  {
    title: '股东',
    customRender: (_, record) => {
      return (
        <QEntityLink
          coy-obj={{
            KeyNo: record.keyNo,
            Name: record.stockName,
          }}
        ></QEntityLink>
      );
    },
  },
  {
    title: '持股比例',
    dataIndex: 'stockPercent',
  },
  {
    title: '认缴出资额(万元)',
    dataIndex: 'shouldCapi',
  },
  {
    title: '认缴出资日期',
    dataIndex: 'shoudDate',
    customRender: (text) => {
      if (!text || text < 0) return '-';

      return moment(text * 1000).format('YYYY年MM月');
    },
  },
];

const RiskChangeColumns = [
  {
    title: '风险内容',
    scopedSlots: { customRender: 'MonitorContent' },
  },
  {
    title: '更新日期',
    width: 180,
    dataIndex: 'PublishTime',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '操作',
    width: 80,
    scopedSlots: { customRender: 'MonitorDetail' },
  },
];

const PledgeMergerColumns = [
  { title: '登记编号', dataIndex: 'registerno' },
  { title: '抵押人', dataIndex: 'DebtorJson', scopedSlots: { customRender: 'companies' } },
  { title: '抵押权人', dataIndex: 'PledgeeJson', scopedSlots: { customRender: 'companies' } },
  {
    title: '债务人履行债务的期限',
    entity: { start: 'debttermstart', end: 'debttermend' },
    scopedSlots: { customRender: 'startEndDate' },
  },
  {
    title: '被担保主债权数额',
    dataIndex: 'pledgedamountdesc',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  { title: '状态', scopedSlots: { customRender: 'effectiveTag' } },
  { title: '登记日期', dataIndex: 'registerstartdate', scopedSlots: { customRender: 'date' } },
  { title: '内容', width: 70, scopedSlots: { customRender: 'action' } },
];

const AssetInvestigationAndFreezingColumns = [
  {
    title: '企业名称',
    dataIndex: 'NameAndKeyNo',
    scopedSlots: { customRender: 'companyList' },
  },
  {
    title: '资产查冻',
    dataIndex: 'Title',
  },
  {
    title: '案号',
    dataIndex: 'caseno',
  },
  {
    title: '查封公告日期',
    dataIndex: 'publishdate',
    scopedSlots: { customRender: 'date' },
    dateProps: {
      pattern: 'YYYY-MM-DD',
      defaultVal: '-',
      x1000: true,
    },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

const ControllerCompanyColumns = [
  {
    title: '企业名称',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '状态',
    dataIndex: 'ShortStatus',
    scopedSlots: { customRender: 'companyStatus' },
  },
  {
    title: '成立日期',
    dataIndex: 'StartDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '所属地区',
    customRender: (record) => {
      return getArea(record.Area) || '-';
    },
  },
  {
    title: '注册资本',
    dataIndex: 'RegistCapi',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  {
    title: '投资比例',
    dataIndex: 'PercentTotal',
  },
];
const ShareholderInformationColumns = [
  {
    title: '股东名称',
    scopedSlots: {
      customRender: 'investor',
    },
  },
  {
    title: '持股比例',
    width: '20%',
    dataIndex: 'StockPercent',
  },
];
const ActuralControllerInformationColumns = [
  {
    title: '实控人名称',
    width: 500,
    scopedSlots: {
      customRender: 'name',
    },
  },
  {
    title: '总持股比例',
    dataIndex: 'PercentTotal',
  },
  {
    title: '操作',
    width: 100,
    scopedSlots: {
      customRender: 'actualControllerOperation',
    },
  },
];
const PatentInfoBase = [
  {
    title: '专利名称',
    dataIndex: 'Title',
  },
  {
    title: '专利状态',
    width: 200,
    scopedSlots: {
      customRender: 'patentInfoStatus',
    },
  },
];
const PatentInfoColumns = [
  ...PatentInfoBase,
  {
    title: '公开（公示）日期',
    width: 180,
    dataIndex: 'PublicationDate',
    scopedSlots: { customRender: 'date' },
    dateProps: {
      pattern: 'YYYY-MM-DD',
      defaultVal: '-',
      x1000: true,
    },
  },
];

const PatentInfoExInColumns = [
  ...PatentInfoBase,
  {
    title: '转入日期',
    width: 180,
    dataIndex: 'TransferInTime',
    key: 'transferintime',
    sorter: true,
    scopedSlots: { customRender: 'date' },
    dateProps: {
      pattern: 'YYYY-MM-DD',
      defaultVal: '-',
      x1000: true,
    },
  },
];

const OutwardInvestmentColumns = [
  {
    title: '企业名称',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '成立日期',
    width: 180,
    dataIndex: 'StartDate',
    scopedSlots: { customRender: 'date' },
    dateProps: {
      pattern: 'YYYY-MM-DD',
      defaultVal: '-',
      x1000: true,
    },
  },
  {
    title: '持股比例',
    width: 250,
    dataIndex: 'TotalPercent',
  },
];

const OutwardInvestmentAnalysisColumns = [
  {
    title: '企业名称',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '成立日期',
    dataIndex: 'StartDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '存续状态',
    dataIndex: 'Status',
    scopedSlots: { customRender: 'companyStatus' },
  },
  {
    title: '股权比例',
    dataIndex: 'TotalPercent',
  },
];

const ActualControllerNameColumns = [
  {
    title: '实控人名称',
    scopedSlots: { customRender: 'name' },
  },
];

const ActualControllerTimeColumns = [
  {
    title: '实控人名称',
    width: 500,
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '实控时间',
    dataIndex: 'monthsDiff',
    customRender: (text) => {
      return `${text}个月`;
    },
  },
];

const IPOProcessColumns = [
  {
    title: '时间',
    customRender: (record) => {
      const { RegisterProcedureList } = record;
      const data = RegisterProcedureList[RegisterProcedureList.length - 1] || {};
      if (!data.Date) {
        return '-';
      }
      return moment(data.Date * 1000).format('YYYY-MM-DD');
    },
  },
  {
    title: '最新上市进度',
    customRender: (record) => {
      const { RegisterProcedureList } = record;
      const data = RegisterProcedureList[RegisterProcedureList.length - 1] || {};
      const { Status, StageRemark } = data;
      return StageRemark || Status || '-';
    },
  },
];

const IPOProcessRelatedColumns = [
  {
    title: '关联方名称',
    scopedSlots: { customRender: 'name' },
  },
  ...IPOProcessColumns,
];

const RelatedCompanyDetailColumns = [
  {
    title: '关联方名称',
    customRender: (record) => {
      return (
        <QEntityLink
          coy-obj={{
            KeyNo: record.id,
            Name: record.name,
          }}
        ></QEntityLink>
      );
    },
  },
  {
    title: '股票代码',
    dataIndex: 'stockinfo',
    customRender: (text) => {
      return text[0] || '-';
    },
  },
  {
    title: '上市地点',
    dataIndex: 'listedIndustryDesc',
  },
];

const ProvincialHonorColumns = [
  {
    title: '名称',
    scopedSlots: {
      customRender: 'directoryName',
    },
  },
  {
    title: '荣誉类型',
    width: 100,
    dataIndex: 'KindDesc',
  },
  {
    title: '级别',
    width: 80,
    dataIndex: 'ApproveClassDesc',
    scopedSlots: {
      customRender: 'approveClassDesc',
    },
  },
  {
    title: '认证年份',
    width: 76,
    dataIndex: 'PublishYear',
  },
  {
    title: '发布日期',
    width: 110,
    dataIndex: 'PublishDate',
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD',
    },
  },
  {
    title: '有效期',
    width: 110,
    valueKeyList: ['LastestBegingDate', 'LatestDeadLine'],
    scopedSlots: {
      customRender: 'timeDuration',
    },
  },
  {
    title: '发布单位',
    width: 180,
    dataIndex: 'PublishOfficeJson',
    scopedSlots: {
      customRender: 'companies',
    },
  },
  {
    title: '来源',
    scopedSlots: {
      customRender: 'honorSource',
    },
  },
];

// 员工持股平台
const EmployeeStockPlatformColumns = [
  {
    title: '股东名称',
    scopedSlots: { customRender: 'expanded' },
    // expandIcons: {
    //   expand: () => <QIcon type="icon-xuelizhengshu"></QIcon>,
    //   collapse: () => <span>456</span>,
    // },
    inject: 'companyWithLogo',
  },
  {
    title: '持股比例',
    width: 150,
    dataIndex: 'stockPercent',
  },
  {
    title: '认缴出资额(万元）',
    width: 150,
    dataIndex: 'TotalShouldAmount',
    valueKeyMap: {
      compareKey: 'shouldCapi',
      detailKey: 'PartnerShouldDetailList',
      valueKey: 'ShouldCapi',
      dateKey: 'ShoudDate',
      typeKey: 'InvestType',
      title: '认缴出资信息',
    },
    scopedSlots: { customRender: 'shouldCapi' },
  },
  {
    title: '认缴出资日期',
    dataIndex: 'shoudDate',
    width: 150,
    scopedSlots: {
      customRender: 'scDate',
    },
  },
  {
    title: '实缴出资额(万元）',
    width: 150,
    dataIndex: 'TotalRealAmount',
    valueKeyMap: {
      compareKey: 'RealCapi',
      valueKey: 'RealCapi',
      dateKey: 'RealDate',
      typeKey: 'InvestName',
      detailKey: 'PartnerRealDetailList',
      title: '实缴出资信息',
    },
    scopedSlots: { customRender: 'shouldCapi' },
  },
  {
    title: '实缴出资日期',
    width: 150,
    dataIndex: 'CapiDate',
    scopedSlots: {
      customRender: 'scDate',
    },
  },
];

// 知识产权出质押
const IntellectualPropertyPledgeColumns = [
  {
    title: '出质知产类型',
    dataIndex: 'TypeDesc',
    width: 110,
  },
  {
    title: '名称',
    scopedSlots: { customRender: 'intellectualPropertyPledgeName' },
  },
  {
    title: '出质登记号',
    dataIndex: 'RegNo',
    width: 130,
  },
  {
    title: '出质公告日',
    key: 'PublishDate',
    width: 110,
    sorter: true,
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '出质人名称',
    dataIndex: 'PledgorInfo',
    scopedSlots: { customRender: 'companyList' },
  },
  {
    title: '质权人名称',
    dataIndex: 'PledgeeInfo',
    scopedSlots: { customRender: 'companyList' },
  },
  {
    title: '出质期限',
    width: 100,
    scopedSlots: { customRender: 'IPRPledgePeriod' },
  },
];

const ScientificAchievementColumns = [
  {
    title: '名称',

    dataIndex: 'AchievementName',
  },
  {
    title: '登记号',
    width: 110,
    dataIndex: 'RegistNumber',
  },
  {
    title: '完成单位',
    dataIndex: 'CompletionUnitKeyNo',
    scopedSlots: {
      customRender: 'companyList',
    },
  },
  {
    title: '登记单位',
    dataIndex: 'RegistDepartmentKeyNo',
    scopedSlots: {
      customRender: 'companyList',
    },
  },
  {
    title: '登记日期',
    dataIndex: 'RegistDate',
    width: 110,
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD',
    },
  },
  {
    title: '完成人',
    dataIndex: 'CompletionPerson',
    customRender: (textList, record) => {
      if (!textList?.length) {
        return '-';
      }
      return <ClampContent clampKey={record.Id}>{textList.join('、')}</ClampContent>;
    },
  },
  {
    title: '合作完成单位',
    dataIndex: 'CooperateUnitKeyNo',
    scopedSlots: {
      customRender: 'companyList',
    },
  },
];

const MainInfoUpdateCapitalChangeColumns = [
  {
    title: '变更时间',
    width: 110,
    dataIndex: 'ChangeDate',
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD',
    },
  },
  {
    title: '变更项目',
    width: 200,
    dataIndex: 'Subtitle',
  },
  {
    title: '变更动态',
    scopedSlots: {
      customRender: 'MonitorContent',
    },
  },
];

const PatentInfoOutColumns = [
  {
    title: '公开（公告）号/发明名称',
    dataIndex: 'Title',
    customRender: (text, record) => {
      return (
        <div>
          <div>{record.ApplicationNumber}</div>
          <a
            href={`/embed/${record.PatentType === 1 ? 'patentDetail' : 'intPatentDetail'}?id=${record.Id}&title=${record.Title}`}
            target="_blank"
          >
            {record.Title}
          </a>
        </div>
      );
    },
  },
  {
    title: '专利类型',
    dataIndex: 'KindCodeDesc',
    width: 110,
  },
  {
    title: '法律状态',
    width: 260,
    dataIndex: 'LegalStatusDesc',
    scopedSlots: {
      customRender: 'legalStatus',
    },
  },
  {
    title: '申请日期',
    width: 98,
    dataIndex: 'ApplicationDate',
    key: 'applicationdate',
    sorter: true,
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD',
    },
  },
  {
    title: '流入方权利人',
    width: 200,
    dataIndex: 'HistoryChange',
    scopedSlots: {
      customRender: 'historyChangeFrom',
    },
  },
  {
    title: '流出日期',
    width: 110,
    dataIndex: 'TransferOutTime',
    sorter: true,
    key: 'transferouttime',
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD',
    },
  },
  {
    title: '发明人',
    width: 154,
    scopedSlots: {
      customRender: 'inventors',
    },
  },
];
const PatentOutPurchaseColumns = [
  {
    title: '公开（公告）号/发明名称',
    dataIndex: 'Title',
    customRender: (text, record) => {
      return (
        <div>
          <div>{record.ApplicationNumber}</div>
          <a
            href={`/embed/${record.PatentType === 1 ? 'patentDetail' : 'intPatentDetail'}?id=${record.Id}&title=${record.Title}`}
            target="_blank"
          >
            {record.Title}
          </a>
        </div>
      );
    },
  },
  {
    title: '专利类型',
    dataIndex: 'KindCodeDesc',
    width: 110,
  },
  {
    title: '法律状态',
    width: 110,
    dataIndex: 'LegalStatusDesc',
    scopedSlots: {
      customRender: 'legalStatus',
    },
  },
  {
    title: '申请日期',
    width: 98,
    dataIndex: 'ApplicationDate',
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD',
    },
  },
  {
    title: '转出方权利人',
    dataIndex: 'HistoryChange',
    valueKey: 'beforeKey',
    scopedSlots: {
      customRender: 'historyChangeFrom',
    },
  },
  {
    title: '转入日期',
    width: 98,
    dataIndex: 'TransferInTime',
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD',
    },
  },
  {
    title: '发明人',
    scopedSlots: {
      customRender: 'inventors',
    },
  },
];

const PatentAnalysisColumns = [
  {
    title: '公开（公告）号/发明名称',
    dataIndex: 'Title',
    customRender: (text, record) => {
      return (
        <div>
          <div>{record.ApplicationNumber}</div>
          <a
            href={`/embed/${record.PatentType === 1 ? 'patentDetail' : 'intPatentDetail'}?id=${record.Id}&title=${record.Title}`}
            target="_blank"
          >
            {record.Title}
          </a>
        </div>
      );
    },
  },
  {
    title: '专利类型',
    dataIndex: 'KindCodeDesc',
    width: 110,
  },
  {
    title: '法律状态',
    width: 130,
    dataIndex: 'LegalStatusDesc',
    scopedSlots: {
      customRender: 'legalStatus',
    },
  },
  {
    title: '申请日期',
    width: 110,
    dataIndex: 'ApplicationDate',
    scopedSlots: {
      customRender: 'date',
    },
    sorter: true,
    key: 'publishtime',
    dateProps: {
      pattern: 'YYYY-MM-DD',
    },
  },
  {
    title: '发明人',
    scopedSlots: {
      customRender: 'inventors',
    },
  },
];

const BatchCompanyDetailColumns = [
  {
    title: '企业名称',
    scopedSlots: {
      customRender: 'name',
    },
  },
  {
    title: '成立日期',
    width: 110,
    dataIndex: 'StartDate',
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD',
    },
  },
  {
    title: '被吊销日期',
    width: 110,
    dataIndex: 'RevokeEntity',
    scopedSlots: {
      customRender: 'revokeDate',
    },
  },
  {
    title: '法定代表人',
    width: 300,
    scopedSlots: {
      customRender: 'operPerson',
    },
  },
  {
    title: '登记状态',
    width: 300,
    dataIndex: 'ShortStatus',
    scopedSlots: {
      customRender: 'companyStatus',
    },
  },
];

const InternationPatentColumns = [
  {
    title: '公开（公告）号/发明名称',
    dataIndex: 'Title',
    customRender: (text, record) => {
      return (
        <div>
          <div>{record.ApplicationNumber}</div>
          <a
            href={`/embed/${record.PatentType === 1 ? 'patentDetail' : 'intPatentDetail'}?id=${record.Id}&title=${record.Title}`}
            target="_blank"
          >
            {record.Title}
          </a>
        </div>
      );
    },
  },
  {
    title: '专利类型',
    width: 110,
    customRender: () => '发明',
  },
  {
    title: '法律状态',
    width: 260,
    dataIndex: 'StatusDesc',
    scopedSlots: {
      customRender: 'legalStatus',
    },
  },
  {
    title: '申请日期',
    width: 110,
    sorter: true,
    dataIndex: 'ApplicationDate',
    key: 'ApplicationDate',
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD',
    },
  },
  {
    title: '发明人',
    scopedSlots: {
      customRender: 'inventors',
    },
  },
];

const RelatedCompanyMassRegistrationCancellationColumns = [
  {
    title: '企业名称',
    scopedSlots: {
      customRender: 'name',
    },
  },
  {
    title: '成立日期',
    width: 110,
    dataIndex: 'startdatecode',
    customRender: (text) => {
      if (!text) {
        return '-';
      }
      return moment(text + '').format('YYYY-MM-DD');
    },
  },
  {
    title: '被吊销日期',
    width: 110,
    dataIndex: 'RevokeEntity.RevocationDate',
    customRender: (text) => {
      if (!text) {
        return '-';
      }
      return moment(text + '').format('YYYY-MM-DD');
    },
  },
  {
    title: '法定代表人',
    width: 300,
    scopedSlots: {
      customRender: 'operPerson',
    },
  },
  {
    title: '登记状态',
    width: 300,
    dataIndex: 'status',
    scopedSlots: {
      customRender: 'companyStatus',
    },
  },
];

const TALENT_RECRUIT_STABILITYColumns = [
  {
    title: '发布日期',
    width: 110,
    dataIndex: 'PublishTime',
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD',
    },
  },
  {
    title: '招聘职位',
    dataIndex: 'PositionName',
  },
  {
    title: '月薪',
    width: 100,
    dataIndex: 'Salary',
  },
  {
    title: '学历',
    width: 80,
    dataIndex: 'Education',
  },
  {
    title: '经验',
    width: 100,
    dataIndex: 'Experience',
  },
  {
    title: '办公地点',
    width: 100,
    dataIndex: 'City',
  },
  {
    title: '内容',
    width: 56,
    scopedSlots: {
      customRender: 'action',
    },
  },
];

const pathConverter = (dataList: any[]) => {
  const converter = new PlainRelationPathConverter();
  const path = new Path();
  return converter.covert(path, dataList);
};

const EquityStructureAbnormalColumns = [
  {
    title: '持股关系',
    dataIndex: 'relationPaths',
    customRender: (relationPaths?: any[]) => {
      if (!Array.isArray(relationPaths)) {
        return '-';
      }
      // 多条路径处理
      const paths = relationPaths.map((path, index, oroginData) => {
        const key = `relation-path-${index}`;
        return (
          <div key={key}>
            {oroginData.length > 1 ? <div style={{ marginBottom: '5px', fontWeight: '700' }}>路径 {index + 1}</div> : null}
            <RelationPath path={pathConverter(path)} />
          </div>
        );
      });

      return <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>{paths}</div>;
    },
  },
];

const EquityFinancingColumns = [
  {
    title: '融资日期',
    dataIndex: 'FinanceDate',
    width: 110,
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '融资轮次',
    width: 110,
    dataIndex: 'Round',
  },
  {
    title: '融资金额',
    width: 160,
    dataIndex: 'Amount',
  },
  {
    title: '投资方',
    customRender: (record, row) => {
      const content = (
        <MoreContent clampKey={record.KeyNo} style={{ padding: '0 10px' }} line={6} marginTop={8}>
          {record?.ParticipantDetails ? (
            record?.ParticipantDetails?.map((item) => {
              const relative = item.RelationInfo;
              const isLead = item.Type === 1;
              return (
                <div class={styles.equityFinancing}>
                  <div class={styles.investorEntity}>
                    <QEntityLink coyObj={item}>{isLead ? <CompanyStatus type="primary" status="领投" /> : null}</QEntityLink>
                  </div>
                  <div class={styles.institutions}>{relative ? <QEntityLink coyArr={relative} /> : '-'}</div>
                </div>
              );
            })
          ) : (
            <div class={styles.equityFinancing}>
              <div class={styles.investorEntity}>-</div>
              <div class={styles.institutions}>-</div>
            </div>
          )}
        </MoreContent>
      );
      return {
        children: content,
        attrs: { colSpan: 2, style: 'padding: 0;' },
      };
    },
  },
  {
    title: '关联机构',
    width: 350,
    dataIndex: 'Institutions',
    customRender: (value) => {
      return {
        children: value,
        attrs: { colSpan: 0 },
      };
    },
  },
];

const Category_B_InvestorsColumns = [
  {
    title: '股东名称',
    scopedSlots: {
      customRender: 'companyLowerCamel',
    },
  },
  {
    title: '持股比例',
    width: 110,
    dataIndex: 'stockPercent',
  },
  {
    title: '认缴出资额(万元）',
    width: 150,
    dataIndex: 'TotalShouldAmount',
    valueKeyMap: {
      compareKey: 'shouldCapi',
      detailKey: 'PartnerShouldDetailList',
      valueKey: 'ShouldCapi',
      dateKey: 'ShoudDate',
      typeKey: 'InvestType',
      title: '认缴出资信息',
    },
    scopedSlots: {
      customRender: 'shouldCapi',
    },
  },
  {
    title: '认缴出资日期',
    width: 150,
    dataIndex: 'shoudDate',
    scopedSlots: {
      customRender: 'scDate',
    },
  },
  {
    title: '实缴出资额(万元）',
    width: 150,
    dataIndex: 'TotalRealAmount',
    valueKeyMap: {
      compareKey: 'RealCapi',
      detailKey: 'PartnerRealDetailList',
      valueKey: 'RealCapi',
      dateKey: 'RealDate',
      typeKey: 'InvestName',
      title: '实缴出资信息',
    },
    scopedSlots: {
      customRender: 'thousandsNumber',
    },
  },
  {
    title: '实缴出资日期',
    width: 150,
    dataIndex: 'CapiDate',
    scopedSlots: {
      customRender: 'scDate',
    },
  },
];

export const riskColumns = {
  BusinessAbnormal2: BusinessAbnormal2Columns,
  BusinessAbnormal3: BusinessAbnormal3Columns,
  BusinessAbnormal5: BusinessAbnormal5Columns,
  FreezeEquity: FreezeEquityColumns,
  ChattelMortgage: ChattelMortgageColumns,
  LandMortgage: LandMortgageColumns,
  PersonCreditCurrent: PersonCreditCurrentColumns,
  PersonCreditHistory: PersonCreditColumns,
  EquityPledge: EquityPledgeColumns,
  JudicialAuction: JudicialAuction1Columns,
  JudicialAuction1: JudicialAuction1Columns,
  GuaranteeInfo: GuaranteeInfoColumns,
  GuaranteeRisk: GuaranteeRiskColumns,
  RestrictedConsumptionCurrent: RestrictedConsumptionColumns,
  RestrictedConsumptionHistory: RestrictedConsumptionColumns,
  RestrictedOutbound: RestrictedOutboundColumns,
  TaxationOffences: TaxationOffencesColumns,
  Bankruptcy: BankruptcyColumns,
  PersonExecution: PersonExecutionColumns,
  ProductQualityProblem1: ProductQualityProblem1Columns,
  ProductQualityProblem2: ProductQualityProblem2Columns,
  SpotCheck,
  ProductQualityProblem3: ProductQualityProblem3Columns,
  ProductQualityProblem4: ProductQualityProblem4Columns,
  ProductQualityProblem5: ProductQualityProblem5Columns,
  ProductQualityProblem6: ProductQualityProblem6Columns,
  ProductQualityProblem7: ProductQualityProblem7Columns,
  ProductQualityProblem8: ProductQualityProblem8Columns,
  ProductQualityProblem9: ProductQualityProblem9Columns,
  MainInfoUpdateScope: MainInfoUpdateScopeColumns,
  MainInfoUpdateAddress: MainInfoUpdateAddressColumns,
  MainInfoUpdateBeneficiary: MainInfoUpdateBeneficiaryColumns,
  MainInfoUpdateName: MainInfoUpdateNameColumns,
  MainInfoUpdateLegalPerson: MainInfoUpdateLegalPersonColumns,
  MainInfoUpdateHolder: MainInfoUpdateHolderColumns,
  MainInfoUpdateManager: MainInfoUpdateManagerColumns,
  MainInfoUpdatePerson: MainInfoUpdatePersonColumns,
  BondDefaults: BondDefaultsColumns,
  AdministrativePenalties: AdministrativePenaltiesColumns(false),
  TaxPenalties: AdministrativePenaltiesColumns(),
  AdministrativePenalties2: AdministrativePenalties2Columns,
  AdministrativePenalties3: AdministrativePenalties2Columns,
  EnvironmentalPenalties: AdministrativePenaltiesColumns(),
  CompanyCredit: CompanyCreditColumns,
  CompanyCreditHistory: CompanyCreditColumnsHistory,
  OperationAbnormal: OperationAbnormalColumns,
  TaxArrearsNotice: TaxArrearsNoticeColumns,
  MainMembersPersonCreditCurrent,
  MainMembersRestrictedConsumptionCurrent: RestrictedConsumptionColumns,
  MainMembersRestrictedOutbound: RestrictedOutboundColumns,
  SubsidiaryPersonCreditCurrent: SubsidiaryPersonCreditCurrentColumns,
  SubsidiaryRestrictedConsumptionCurrent: RestrictedConsumptionColumns,
  // StaffWorkingOutside: ConflictInterestColumns,
  SamePhone: ConflictInterestSamePhoneColumns,
  // PunishedEmployeesWorkingOutside: PunishedEmployeesWorkingOutsideColumns,
  // PunishedEmployeesForeignInvestment: PunishedEmployeesForeignInvestmentColumns,
  // StaffForeignInvestment: StaffForeignInvestmentColumns,
  HitInnerBlackList: HitInnerBlackListColumns,
  // EmploymentRelationship: EmploymentRelationshipColumns,
  // Shareholder: ShareholderColumns,
  // ForeignInvestment: ForeignInvestmentColumns,
  CancellationOfFiling: CancellationOfFilingColumns,
  HitOuterBlackList: HitOuterBlackListColumns,
  OvsSanction: HitOuterBlackListColumns, // 出口管制合规
  // InvestorsRelationship: PartnershipColumns,
  // ShareholdingRelationship: ShareholdingRelationshipColumns,
  // ServeRelationship: ServeRelationshipColumns,
  CompanyOrMainMembersCriminalOffence,
  CompanyOrMainMembersCriminalOffenceHistory: CompanyOrMainMembersCriminalOffence,
  SalesContractDispute: Dispute(),
  MajorDispute: Dispute(),
  CompanyOrMainMembersCriminalInvolve: Dispute({ sortable: false }),
  CompanyOrMainMembersCriminalInvolveHistory: Dispute({ sortable: false }),
  SameSuspectedActualController: SameControllRelation,
  BlacklistSameSuspectedActualController,
  // SuspectedInterestConflict: SuspectedInterestConflictColumns,
  StaffWorkingOutsideForeignInvestment: StaffWorkingOutsideForeignInvestmentColumns,
  // CustomerPartnerInvestigation: CustomerPartnerInvestigationColumns, // 与第三方列表企业存在投资任职关联
  // CustomerSuspectedRelation: CustomerPartnerInvestigationColumns, // 与第三方列表企业存在交叉重叠疑似关联
  // BlacklistPartnerInvestigation: BlacklistPartnerInvestigationColumns, // 与内部黑名单企业存在投资任职关联
  // BlacklistSuspectedRelation: BlacklistPartnerInvestigationColumns, // 与内部黑名单企业存在疑似关联关系
  BillDefaults: BillDefaultsColumns, // 票据违约
  TaxCallNotice: TaxCallNoticeColumns, // 税务催缴公告
  TaxCallNoticeV2: TaxCallNoticeV2Columns, // 税务催缴
  TaxReminder: TaxReminderColumns, // 税务催报
  EndExecutionCase: EndExecutionCaseColumns, // 终本案件
  SecurityNotice: SecurityNoticeColumns, // 公安通告
  RegulateFinance: RegulateFinanceColoums, // 监管处罚
  CapitalReduction: CapitalReductionColumns, // 减资公告
  LaborContractDispute: LaborContractDisputeColumns, // 劳动纠纷
  Certification: CertificationColumns, // 资质筛查
  StockPledge: StockPledgeColumns, // 股权质押
  BusinessAbnormal4: BusinessAbnormal4Columns, // 被列入非正常户
  CourtSessionAnnouncement: CourtNoticeColumns, // 开庭公告
  Judgement: JudgementColumns, // 案件风险
  ReviewAndInvestigation: ReviewAndInvestigationColumns, // 审查调查
  BeneficialOwnersControlNumerousEnterprises: BeneficialOwnersControlNumerousEnterprisesColumns, // 受益所有人控制企业众多
  QfkRisk2210: QfkRisk2210Columns, // 同法定代表人企业众多且地区分散
  QfkRisk6610: QfkRisk6610Columns, // 实际控制人控制企业涉及高风险行业
  QfkRisk2010: QfkRisk2010Columns, // 联系方式或注册地址重复
  QfkRisk2310: QfkRisk2310Columns, // 同实际控制人企业众多增加不确定风险
  QfkRisk6709: QfkRisk6709Columns, // 法定代表人控制企业集中注册且均无实缴资本
  QfkRisk7099: QfkRisk7099Columns, // 关联方企业集中注册且均无实缴资本
  QfkRisk6611: QfkRisk6611Columns, // 实际控制人控制企业位于边境贸易区
  QfkRisk6612: QfkRisk6611Columns, // 实际控制人控制企业边境贸易区占比较高
  QfkRisk6907: QfkRisk6907Columns, // 注册资本降幅过大
  QfkRisk1312: QfkRisk1312Columns, // 行政许可被中止或者注销
  QfkRisk1410: QfkRisk1410Columns, // 来自高风险I类国家或地区
  QfkRisk1411: QfkRisk1410Columns, // 来自高风险II类国家或地区
  QfkRisk6710: QfkRisk6610Columns, // 法定代表人控制企业涉及高风险行业
  QfkRisk6609: QfkRisk6609Columns, // 实际控制人控制企业集中注册且均无实缴资本
  QfkRisk6803: QfkRisk6803Columns, // 控制权分散
  BusinessAnomalies: SeriousViolationColumns, // 关联方成员企业存在异常-企业多个关联方成员存在异常信息
  SeriousViolation: SeriousViolationColumns, // 关联方成员企业存在异常-企业关联方成员存在严重违法事项
  BusinessAnomaliesWithSamePhoneAndAddress: SeriousViolationColumns, // 关联方成员企业存在异常-同联系方式或者同地址企业存在异常
  MoneyLaundering: SeriousViolationColumns, // 关联方成员企业存在异常-企业关联方成员曾发生过洗钱类刑事案件
  RelatedAnnouncement: SeriousViolationColumns, // 关联方成员企业存在异常-关联方成员企业有开庭公告信息
  RelatedCompanies: SeriousViolationColumns, // 关联方成员企业存在异常-关联方企业注销或吊销
  ViolationProcessings: ViolationProcessingsColumns, // 主要成员存在外部关联风险-违规处理
  PartnerListColumns, // 所有权与经营权分离-股东信息
  EmployeeListColumns, // 所有权与经营权分离-主要人员信息
  TaxUnnormals: TaxUnnormalsColumns, // 被列入税务非正常户
  RiskChange: RiskChangeColumns, // 风险监控相关纬度
  SSERelatedRiskChange: RiskChangeColumns, // 风险监控相关纬度
  RecentInvestCancellations: RiskChangeColumns, // 投资变动
  ActualControllerRiskChange: RiskChangeColumns, // 实控人/大股东新增股权质押
  ListedEntityRiskChange: RiskChangeColumns, // 财务变动
  PledgeMerger: PledgeMergerColumns, // 动产抵押
  AssetInvestigationAndFreezing: AssetInvestigationAndFreezingColumns, // 资产查冻
  ControllerCompany: ControllerCompanyColumns, // 投资异常
  ShareholderInformation: ShareholderInformationColumns, // 第一股东持股比例
  ActuralControllerInformation: ActuralControllerInformationColumns, // 实控人控股比例比例
  PatentInfo: PatentInfoOutColumns, // 授权发明专利数量
  PatentInfoExIn: PatentInfoExInColumns, // 被转入发明专利数量
  OutwardInvestment: OutwardInvestmentColumns, // 对外投资企业涉房/涉金融
  OutwardInvestmentAnalysis: OutwardInvestmentAnalysisColumns, // 近一年企业对外投资企业注销占比
  ActualControllerTime: ActualControllerTimeColumns, // 实控人实控时间
  ActualControllerName: ActualControllerNameColumns, // 实控人是否国资委
  IPOProcess: IPOProcessColumns, // 上市进程
  IPOProcessRelated: IPOProcessRelatedColumns, // 关联方上市进程
  RelatedCompanyDetail: RelatedCompanyDetailColumns, // 关联方境内上市
  JudicialCase: JudicialCaseColumns, // 近5年主体企业所涉的执行案件（申请执行人）中未全部执行完毕的案件总金额
  CaseRegistration: CaseRegistrationColumns, // 主体企业存在刑事立案信息
  ProvincialHonor: ProvincialHonorColumns, // 省部级荣誉、荣誉取消
  EmployeeStockPlatform: EmployeeStockPlatformColumns, // 员工持股平台
  IntellectualPropertyPledge: IntellectualPropertyPledgeColumns, // 知识产权质押
  ScientificAchievement: ScientificAchievementColumns, // 科技成果
  PatentInfoOut: PatentInfoOutColumns, //专利流出
  MainInfoUpdateCapitalChange: MainInfoUpdateCapitalChangeColumns, // 减资
  PatentAnalysis: PatentAnalysisColumns, //专利分析
  PATENT_APP_RATIO: PatentAnalysisColumns, // 发明专利申请占比
  PATENT_REJECTION_RATE: PatentAnalysisColumns, // 发明专利申请驳回率
  PATENT_GRANT_RATE: PatentAnalysisColumns, // 发明专利授权率
  PATENT_APP_STABILITY: PatentAnalysisColumns, // 发明专利申请稳定性
  PATENT_GRANT_STABILITY: PatentAnalysisColumns, // 发明专利授权稳定性
  PATENT_APP_CONTINUITY: PatentAnalysisColumns, // 发明专利申请连续性
  PATENT_APP_CONCENTRATION: PatentAnalysisColumns, // 发明专利申请集中度
  ACQUIRED_PATENT_RATIO: PatentOutPurchaseColumns, // 外购专利占比
  PATENT_MAINTENANCE_RATE: PatentAnalysisColumns, // 发明专利授权维持率
  BatchCompanyDetail: BatchCompanyDetailColumns, // 关联方异动
  InternationPatent: InternationPatentColumns, // 有效PCT国际专利
  RelatedCompanyMassRegistrationCancellation: RelatedCompanyMassRegistrationCancellationColumns, // 关联方吊注销
  TALENT_RECRUIT_STABILITY: TALENT_RECRUIT_STABILITYColumns, // 招聘稳定性
  EquityStructureAbnormal: EquityStructureAbnormalColumns, // 关联方存在循环持股
  EquityFinancing: EquityFinancingColumns, // A类知名机构入股
  Category_B_Investors: Category_B_InvestorsColumns, // B类知名机构入股
};
