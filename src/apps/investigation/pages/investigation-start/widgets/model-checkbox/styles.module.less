@import '@/styles/token.less';

.container {
  width: 900px;
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
  gap: 5px 31px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;

  .model-item {
    width: 196px;
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    cursor: default;
    padding: 0 10px;
  }

  .model-item:not(:nth-child(4n), :last-child)::after {
    content: "";
    position: absolute;
    top: 50%;
    right: -17px;
    transform: translateY(-50%);
    width: 1px;
    height: 24px;
    background: #EEE;
  }

  .model-name {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #fff;

    .name {
      flex: 1;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      display: block;
      width: 1px; // HACK: 设置1px让省略符号生效
    }

    .icon {
      font-size: 24px;
      color: inherit;
      opacity: 0.5;
    }
  }

  :global {
    .ant-checkbox:not(.ant-checkbox-checked) {
      &:hover .ant-checkbox-inner,
      & .ant-checkbox-input:focus + .ant-checkbox-inner
      {
        border-color: #FFF;
      }
    }

    .ant-checkbox-inner {
      width: 20px;
      height: 20px;
      background-color: transparent;
      border-radius: 2.86px;
      border-color: #FFF;
    }

    .ant-checkbox-inner::after {
      width: 7px;
      height: 11px;
    }

    .ant-checkbox-checked .ant-checkbox-inner {
      background-color: @qcc-color-blue-500;
      border-color: @qcc-color-blue-500;
    }
  }
}
