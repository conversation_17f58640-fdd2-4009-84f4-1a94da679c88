import { mount } from '@vue/test-utils';
import { message } from 'ant-design-vue';

import ModelCheckbox from '..';

describe('ModelCheckbox 组件', () => {
  it('选择模型时应该触发input事件', async () => {
    const modelList = [
      { modelName: '模型1', modelId: 1 },
      { modelName: '模型2', modelId: 2 },
    ];
    const wrapper = mount(ModelCheckbox, {
      propsData: { modelList },
    });

    await wrapper.find('[value="1"]').setChecked();
    expect(wrapper.emitted().input).toBeTruthy();
    expect(wrapper.emitted().input[0][0]).toEqual([1]);
  });

  it('选择多个模型时应该触发input事件', async () => {
    const modelList = [
      { modelName: '模型1', modelId: 1 },
      { modelName: '模型2', modelId: 2 },
    ];
    const wrapper = mount(ModelCheckbox, {
      propsData: { modelList },
    });

    wrapper.setProps({
      value: [1, 2],
    });
    const checkbox = wrapper.findComponent({ name: 'ACheckboxGroup' });
    checkbox.vm.$emit('change', [1, 2]);
    expect(wrapper.emitted().input).toBeTruthy();
    expect(wrapper.emitted().input[0][0]).toEqual([1, 2]);
  });

  it('取消选择模型时应该触发input事件', async () => {
    const modelList = [
      { modelName: '模型1', modelId: 1 },
      { modelName: '模型2', modelId: 2 },
    ];
    const wrapper = mount(ModelCheckbox, {
      propsData: { modelList, value: [1, 2] },
    });

    await wrapper.find('[value="1"]').setChecked(false);
    expect(wrapper.emitted().input).toBeTruthy();
    expect(wrapper.emitted().input[0][0]).toEqual([2]);
  });

  it('未选择任何模型时应该显示警告信息', async () => {
    message.warn = vi.fn();
    const modelList = [
      { modelName: '模型1', modelId: 1 },
      { modelName: '模型2', modelId: 2 },
    ];
    const wrapper = mount(ModelCheckbox, {
      propsData: { modelList, value: [1, 2] },
    });

    wrapper.findComponent({ name: 'ACheckboxGroup' }).vm.$emit('change', []);
    expect(message.warn).toHaveBeenCalledWith('至少选择一个模型');
  });

  it('modelList为空时不应该渲染任何内容', () => {
    const wrapper = mount(ModelCheckbox, {
      propsData: { modelList: [] },
    });

    expect(wrapper.html()).toBe('');
  });

  it('modelList只有一个模型时应该渲染一个Checkbox', () => {
    const modelList = [{ modelName: '模型1', modelId: 1 }];
    const wrapper = mount(ModelCheckbox, {
      propsData: { modelList },
    });

    expect(wrapper.findAllComponents({ name: 'ACheckbox' })).toHaveLength(1);
  });
});
