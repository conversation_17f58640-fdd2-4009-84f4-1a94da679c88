import { defineComponent, PropType } from 'vue';
import { Checkbox, message as Message, Tooltip } from 'ant-design-vue';

import QIcon from '@/components/global/q-icon';

import styles from './styles.module.less';

const ModelCheckbox = defineComponent({
  name: 'ModelCheckbox',
  emits: ['input'],
  model: {
    prop: 'value',
    event: 'input',
  },
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    modelList: {
      type: Array as PropType<Array<{ modelName: string; modelId: number }>>,
      default: () => [],
    },
  },
  setup(props, { emit }) {
    const handleModelChange = (checkedValues: number[]) => {
      if (checkedValues.length === 0) {
        Message.warn('至少选择一个模型');
        return;
      }
      emit('input', checkedValues);
    };
    return {
      handleModelChange,
    };
  },
  render() {
    if (!this.modelList?.length) {
      return null;
    }
    return (
      <Checkbox.Group value={this.value} class={styles.container} onChange={this.handleModelChange}>
        {this.modelList.map((model) => (
          <div key={model.modelId} class={styles.modelItem}>
            <Tooltip title={model.modelName}>
              <div class={styles.modelName}>
                <QIcon class={styles.icon} type="icon-icon_fengxianmoxing1" />
                <span class={styles.name}>{model.modelName}</span>
              </div>
            </Tooltip>
            <Checkbox value={model.modelId} />
          </div>
        ))}
      </Checkbox.Group>
    );
  },
});

export default ModelCheckbox;
