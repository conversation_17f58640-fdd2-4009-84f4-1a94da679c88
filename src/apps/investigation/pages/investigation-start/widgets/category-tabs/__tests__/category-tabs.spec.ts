import { mount } from '@vue/test-utils';

import { appRouter as router } from '@/router/app.router';

import CategoryTabs from '..';

vi.mock('@/router/app.router', () => ({
  appRouter: {
    push: vi.fn(),
  },
}));

describe('CategoryTabs', () => {
  it('点击一个未禁用且无链接的标签时，应更新 currentTab 并触发 tabClick 事件', async () => {
    const wrapper = mount(CategoryTabs, {
      propsData: {
        tabs: [
          { name: '标签1', key: 'tab1' },
          { name: '标签2', key: 'tab2' },
        ],
      },
    });

    await wrapper.find('li').trigger('click');
    expect(wrapper.vm.currentTab).toBe('tab1');
    expect(wrapper.emitted('tabClick')?.[0][0]).toEqual({ name: '标签1', key: 'tab1' });
  });

  it('当 value 属性设置为第二个标签时，currentTab 应初始化为第二个标签', () => {
    const wrapper = mount(CategoryTabs, {
      propsData: {
        value: 'tab2',
        tabs: [
          { name: '标签1', key: 'tab1' },
          { name: '标签2', key: 'tab2' },
        ],
      },
    });

    expect(wrapper.vm.currentTab).toBe('tab2');
  });

  it('点击一个禁用的标签时，currentTab 不应更新，也不应触发 tabClick 事件', async () => {
    const wrapper = mount(CategoryTabs, {
      propsData: {
        value: 'tab2',
        tabs: [
          { name: '标签1', key: 'tab1', disabled: true },
          { name: '标签2', key: 'tab2' },
        ],
      },
    });

    await wrapper.find('li').trigger('click');
    expect(wrapper.vm.currentTab).toBe('tab2');
    expect(wrapper.emitted('tabClick')).toBeUndefined();
  });

  it('点击一个带有链接的标签时，应调用 router.push', async () => {
    const wrapper = mount(CategoryTabs, {
      propsData: {
        tabs: [
          { name: '标签1', key: 'tab1', link: '/path1' },
          { name: '标签2', key: 'tab2' },
        ],
      },
    });

    await wrapper.find('li').trigger('click');
    expect(router.push).toHaveBeenCalledWith({ path: '/path1' });
    expect(wrapper.emitted('tabClick')).toBeUndefined();
  });

  it('只有一个标签时，currentTab 应初始化为该标签', () => {
    const wrapper = mount(CategoryTabs, {
      propsData: {
        tabs: [{ name: '标签1', key: 'tab1' }],
      },
    });

    expect(wrapper.vm.currentTab).toBe('tab1');
  });

  it('value 属性为 undefined 时，currentTab 应初始化为第一个标签', () => {
    const wrapper = mount(CategoryTabs, {
      propsData: {
        value: undefined,
        tabs: [
          { name: '标签1', key: 'tab1' },
          { name: '标签2', key: 'tab2' },
        ],
      },
    });

    expect(wrapper.vm.currentTab).toBe('tab1');
  });
});
