.tab {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px;
  padding-bottom: 8px;
}

.tab-item {
  width: 110px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 2px;
  background: rgba(255, 255, 255, 0.2);
  font-size: 16px;
  line-height: 24px;
  color: #FFF;
  cursor: pointer;

  &:not(.disabled):hover,
  &.active {
    background: #FFF;
    font-size: 16px;
    font-weight: 500;
    color: #128BED;
    position: relative;
  }

  &:not(.disabled):hover::after,
  &.active::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 100%);
    width: 14px;
    height: 0;
    background: none;
    border-top: 8px solid #FFF;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-bottom: 8px solid transparent;
  }

  &.disabled {
    cursor: not-allowed;
  }
}

