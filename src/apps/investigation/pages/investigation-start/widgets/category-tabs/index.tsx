import { computed, defineComponent, PropType } from 'vue';

import { appRouter as router } from '@/router/app.router';

import styles from './styles.module.less';

interface TabItem {
  name: string;
  key: string;
  disabled?: boolean;
  link?: string;
}

const CategoryTabs = defineComponent({
  name: 'CategoryTabs',
  props: {
    value: {
      type: String as PropType<string>,
      required: false,
    },
    tabs: {
      type: Array as PropType<TabItem[]>,
      default: () => [],
    },
  },
  emits: ['tabClick', 'input'],
  setup(props, { emit }) {
    const currentTab = computed({
      get: () => props.value || props.tabs[0].key,
      set: (value: string) => {
        emit('input', value);
      },
    });

    const handleTabClick = (item: TabItem) => {
      if (item.disabled) {
        return;
      }
      if (item.link) {
        router.push({
          path: item.link,
        });
        return;
      }
      currentTab.value = item.key;
      emit('tabClick', item);
    };

    return {
      currentTab,
      handleTabClick,
    };
  },
  render() {
    return (
      <ul class={styles.tab}>
        {this.tabs.map((item) => (
          <li
            key={item.key}
            data-disabled={item.disabled ? 'true' : 'false'}
            class={{
              [styles.tabItem]: true,
              [styles.disabled]: item.disabled,
              [styles.active]: this.currentTab === item.key,
            }}
            onClick={() => this.handleTabClick(item)}
          >
            {item.name}
          </li>
        ))}
      </ul>
    );
  },
});

export default CategoryTabs;
