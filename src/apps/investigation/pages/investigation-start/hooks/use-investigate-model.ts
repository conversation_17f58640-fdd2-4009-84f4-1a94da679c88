import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

import { useStore } from '@/store';

export const useInvestigateModel = () => {
  const store = useStore();
  const route = useRoute();
  const router = useRouter();

  const profileId = computed(() => {
    const profile = store.getters['user/profile'];
    return profile?.id || profile?.userId;
  });
  const key = `DD_MODEL_${profileId.value}`;

  const setModelList = (ids) => {
    sessionStorage.setItem(key, JSON.stringify(ids));
    if (route.query?.orgModelIds !== ids.join(',')) {
      router.replace({
        path: route.path,
        query: {
          ...route.query,
          orgModelIds: ids.join(','),
        },
      });
    }
  };
  const getModelList = () => {
    if (route.query?.orgModelIds) {
      return route.query.orgModelIds
        .toString()
        .split(',')
        .map((id) => Number(id));
    }
    return JSON.parse(sessionStorage.getItem(key) || '[]');
  };
  return {
    setModelList,
    getModelList,
  };
};
