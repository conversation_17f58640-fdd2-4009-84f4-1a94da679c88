import { mount } from '@vue/test-utils';
import { useRoute, useRouter } from 'vue-router/composables';

import { setting as settingService } from '@/shared/services';
import { flushPromises } from '@/test-utils/flush-promises';

import InvestigationStartPage from '..';

const mockData = [
  {
    modelType: 1,
    category: 1,
    modelId: 3489,
    modelName: '中行山东分行模型031902',
    product: 'SAAS_PRO',
    comment: '中行山东分行模型031902',
    updateDate: '2025-03-19T03:16:34.000Z',
    branchCode: '85dfa206-e431-42be-bc3b-d13c8686822b',
    orgId: 1,
    status: 2,
    distributedResource: [
      {
        isOrgDefault: 0,
        id: 29207,
        resourceType: 1,
        product: 'SAAS_PRO',
        branchCode: '85dfa206-e431-42be-bc3b-d13c8686822b',
        resourceId: 3489,
        distributeStatus: 1,
        distributedBy: 1,
        orgId: 208,
        createDate: '2025-03-19T03:16:35.000Z',
        expireDate: null,
        updateDate: '2025-03-19T03:16:35.000Z',
      },
    ],
  },
  {
    modelType: 1,
    category: 1,
    modelId: 3488,
    modelName: '中行山东分行模型031901',
    product: 'SAAS_PRO',
    comment: '中行山东分行模型031901',
    updateDate: '2025-03-19T03:08:40.000Z',
    branchCode: '35458e63-4121-4c99-8450-b23d63742450',
    orgId: 1,
    status: 2,
    distributedResource: [
      {
        isOrgDefault: 0,
        id: 29206,
        resourceType: 1,
        product: 'SAAS_PRO',
        branchCode: '35458e63-4121-4c99-8450-b23d63742450',
        resourceId: 3488,
        distributeStatus: 1,
        distributedBy: 1,
        orgId: 208,
        createDate: '2025-03-19T03:08:42.000Z',
        expireDate: null,
        updateDate: '2025-03-19T03:08:42.000Z',
      },
    ],
  },
  {
    modelType: 1,
    category: 1,
    modelId: 3487,
    modelName: '中行山东分行模型031801',
    product: 'SAAS_PRO',
    comment: '中行山东分行模型031801',
    updateDate: '2025-03-18T09:24:35.000Z',
    branchCode: 'f0a79aeb-c7a3-42fc-875b-d6406a7305be',
    orgId: 1,
    status: 2,
    distributedResource: [
      {
        isOrgDefault: 0,
        id: 29205,
        resourceType: 1,
        product: 'SAAS_PRO',
        branchCode: 'f0a79aeb-c7a3-42fc-875b-d6406a7305be',
        resourceId: 3487,
        distributeStatus: 1,
        distributedBy: 1,
        orgId: 208,
        createDate: '2025-03-18T09:24:59.000Z',
        expireDate: null,
        updateDate: '2025-03-18T09:24:59.000Z',
      },
    ],
  },
];

vi.mock('@/shared/services');
vi.mock('vue-router/composables', () => ({
  useRouter: vi.fn(),
  useRoute: vi.fn(),
}));

describe('InvestigationStartPage', () => {
  const mockPush = vi.fn();
  const mockReplace = vi.fn();
  beforeEach(() => {
    vi.mocked<any>(useRouter).mockReturnValue({
      push: mockPush,
      replace: mockReplace,
    });
    vi.mocked<any>(useRoute).mockReturnValue({
      query: {},
    });
    vi.mocked<any>(settingService.getModelLists).mockResolvedValue({
      data: mockData,
    });
  });
  afterEach(() => {
    vi.resetAllMocks();
  });
  it('render', async () => {
    const wrapper = mount(InvestigationStartPage);
    await flushPromises(50);
    expect(wrapper.html()).toMatchSnapshot();
  });
});
