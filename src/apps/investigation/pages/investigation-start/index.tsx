import { computed, defineComponent, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { message as Message } from 'ant-design-vue';
import { intersection } from 'lodash';

import CompanySearch from '@/shared/components/company-search';
import { isValidCompanyType } from '@/utils/company/company-type';
import { useI18n } from '@/shared/composables/use-i18n';
import { useStore } from '@/store';
import { setting as settingService } from '@/shared/services';
import { useInvestigateModel } from '@/apps/investigation/pages/investigation-start/hooks/use-investigate-model';

import ModelCheckbox from './widgets/model-checkbox';
import PicTitle from './images/title.png';
import styles from './investigation-start.module.less';

const DEFAULT_TAB_LIST = [
  { name: '企业尽调', key: 'investigation' },
  { name: '查企业', key: 'company' },
];

/**
 * 企业尽调搜索入口
 */
const InvestigationStartPage = defineComponent({
  name: 'InvestigationStartPage',
  setup() {
    const router = useRouter();
    const route = useRoute();
    const store = useStore();
    const { tc } = useI18n();

    const defaultKeywords = ref(route.query.name);
    const defaultKeyNo = ref(route.query.keyNo);
    const currentTab = ref(DEFAULT_TAB_LIST[0].key);
    const modelList = ref<any[]>([]);
    const orgModelIds = ref<any[]>([]);
    const storageKey = computed(() => `${currentTab.value.toUpperCase()}_SEARCH_HISTORY`);

    const profileId = computed(() => {
      const profile = store.getters['user/profile'];
      return profile?.id || profile?.userId;
    });

    const { setModelList, getModelList } = useInvestigateModel();

    const getSelectedModel = () => {
      const storageIds = getModelList();
      const allIds = modelList.value?.map((v) => v?.modelId).filter(Boolean);
      if (!allIds?.length) {
        return;
      }
      const defaultIds = allIds?.[0] ? [allIds[0]] : [];
      orgModelIds.value = storageIds.length ? intersection(storageIds, allIds) : defaultIds;
    };

    const getEnabledModelList = async () => {
      const params = {
        product: 'SAAS_PRO',
        statuses: [1, 2],
        modelType: 1,
        pageSize: 20,
        pageIndex: 1,
      };
      const res = await settingService.getModelLists(params);
      modelList.value = res.data;
      getSelectedModel();
    };

    /**
     * 工商搜索
     * @param keywords
     */
    const handleSearch = (keywords: string) => {
      setModelList(orgModelIds.value);
      router.push({
        path: '/investigation/search/list',
        query: {
          keyword: keywords,
          type: currentTab.value,
          ...(currentTab.value === 'investigation' ? route.query : {}),
        },
      });
    };

    const handleInvestigationSelect = async (option) => {
      if (option?.IsHide) {
        Message.warning('涉嫌非法社会组织不支持排查');
        return;
      }
      if (!isValidCompanyType(option?.type)) {
        Message.warning('注册地在中国香港、中国澳门、中国台湾以及境外的企业、机关单位等暂不支持风险排查');
        return;
      }
      // 当前选的风险模型
      if (orgModelIds.value.length === 0) {
        Message.warning('至少选择一个模型');
        return;
      }
      router.push({
        path: `/investigation/search/detail/${option.id}`,
        query: {
          from: 'investigation',
          ...route.query,
        },
      });
    };

    const handleCompanySelect = (option) => {
      router.push({
        path: `/company/detail/${option.id}`,
        query: {
          from: 'home',
          info: JSON.stringify({
            status: option.ShortStatus,
            name: option.label,
            code: option.CreditCode,
          }),
        },
      });
    };

    /**
     * 选择关联搜索结果
     * @param option
     */
    const handleSelect = async (option) => {
      setModelList(orgModelIds.value);
      switch (currentTab.value) {
        case 'investigation':
          await handleInvestigationSelect(option);
          break;
        case 'company':
          handleCompanySelect(option);
          break;
        default:
          break;
      }
    };

    const getPlaceholder = () => {
      const data = JSON.parse(localStorage.getItem(`${storageKey.value}_${profileId.value}`) || '[]');
      if (data.length > 0) {
        return data[0].label;
      }
      return tc('Please enter a company name/USCI');
    };

    onMounted(async () => {
      await getEnabledModelList();
    });

    const isModalSelectorVisible = computed(() => {
      return currentTab.value === 'investigation';
    });

    return {
      orgModelIds,
      storageKey,
      defaultKeywords,
      defaultKeyNo,
      currentTab,
      modelList,
      handleSearch,
      handleSelect,
      getPlaceholder,
      isModalSelectorVisible,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.wrapper}>
          <img class={styles.title} src={PicTitle} alt="企查查 洞察先机 守护未来" />
          <CompanySearch
            placeholder={this.getPlaceholder()}
            enterText="查一下"
            onSearch={this.handleSearch}
            onSelect={this.handleSelect}
            defaultKeywords={this.defaultKeywords as string}
            defaultKeyNo={this.defaultKeyNo as string}
            hasBulkSearch={false}
            storagePrefix={this.storageKey}
            class={styles.searchBox}
            bordered={false}
          />
          <ModelCheckbox v-show={this.isModalSelectorVisible} v-model={this.orgModelIds} modelList={this.modelList} />
        </div>
      </div>
    );
  },
});

export default InvestigationStartPage;
