import { computed, defineComponent, ref, shallowReactive, unref, watch } from 'vue';
import { useRoute } from 'vue-router/composables';
import moment from 'moment';
import axios from 'axios';

import { company as companyService } from '@/shared/services';
import { uesInvestStore } from '@/hooks/use-invest-store';
import Empty from '@/shared/components/empty';
import { ERROR_MESSAGES } from '@/config/message.config';
import { useWebTitle } from '@/shared/composables/use-web-title';
import RiskReview from '@/apps/investigation/pages/investigation-detail/widgets/risk-review';
import { useRiskModels } from '@/hooks/use-risk-models';

import styles from './index.module.less';

const InterestInvestigationCard = defineComponent({
  name: 'InterestInvestigationCard',
  props: {
    isExternal: {
      type: Boolean,
      default: false,
    },
  },
  setup() {
    const {
      riskInfo,
      companyInfo: company,
      riskLevel,
      loading,
      getDiligence,
      dimensionDetails,
      allModelRiskInfoList,
      setAllModelRiskInfoList,
    } = uesInvestStore();

    const { setWebTitle } = useWebTitle();

    const route = useRoute();
    const snapshotDate = computed(() =>
      unref(riskInfo)?.createDate ? moment(unref(riskInfo)?.createDate).format('YYYY-MM-DD HH:mm:ss') : ''
    );

    // 最新的diligenceId
    const cacheDiligenceId = ref(unref(riskInfo).id || route?.query?.diligenceId);
    // 错误处理
    const errorOutOfLimitation = shallowReactive<{ error: boolean; message: string | undefined }>({
      error: false,
      message: undefined,
    });

    const { riskModels, getRiskModels } = useRiskModels();
    const fetchRiskModels = async () => {
      const modelIds = allModelRiskInfoList.value.map(({ orgModelId }) => orgModelId);
      await getRiskModels(modelIds);
    };

    const orgModelIds = computed(() => {
      const orgModelIdsQuery = (route.query?.orgModelIds as string) ?? '';
      const orgModelIdsList = orgModelIdsQuery.split(',').filter((id) => !!id);
      return orgModelIdsList.length ? orgModelIdsList.map((id) => Number(id)) : undefined;
    });

    /**
     * 获取风险排查详情
     * @param keyNo company keyNo
     * @param needRefreshSnapshot 是否刷新快照：刷新快照时不传 diligenceId 和 snapshotId
     * @param isDynamicDetails 是否是排查详情
     */
    const fetchData = async (keyNo: string, needRefreshSnapshot = false, isDynamicDetails = false) => {
      try {
        loading.value = true;
        errorOutOfLimitation.error = false;
        errorOutOfLimitation.message = undefined;
        company.value = await companyService.getDetail({ keyNo });
        setWebTitle(company.value?.Name);
        if (!company.value?.Name) return;

        const riskInfoList = await getDiligence({
          keyNo,
          orgModelIds: orgModelIds.value,
          diligenceId: unref(cacheDiligenceId),
          isDynamicDetails: needRefreshSnapshot ? false : !!cacheDiligenceId.value,
          cacheHours: needRefreshSnapshot ? 0 : 24,
        });
        setAllModelRiskInfoList(riskInfoList.details);
        cacheDiligenceId.value = unref(riskInfo).id;

        // setAllModelRiskInfoList(
        //   (
        //     await getDiligence({
        //       keyNo,
        //       orgModelIds: orgModelIds.value,
        //       diligenceId: needRefreshSnapshot ? undefined : unref(cacheDiligenceId),
        //       isDynamicDetails,
        //       // 刷新快照时需要传 `orgSettingsId`
        //       // ambiguousSettingId: needRefreshSnapshot ? riskInfo.value.orgSettingsId : undefined,
        //     })
        //   ).details
        // );
        // cacheDiligenceId.value = unref(riskInfo).id;
        // // 仅在重新生成快照时更新 URL
        // if (needRefreshSnapshot || unref(riskInfo).notMatch) {
        //   await router
        //     .replace({
        //       query: {
        //         ...router.currentRoute.query, // 获取实时 query
        //         diligenceId: unref(riskInfo).id,
        //       },
        //     })
        //     .catch(_.noop);
        // }
      } catch (error) {
        if (axios.isAxiosError(error)) {
          if (error.response?.data.code === 400203 && error.response?.data.statusCode === 403) {
            errorOutOfLimitation.error = true;
            errorOutOfLimitation.message = ERROR_MESSAGES.OUT_OF_DILIGENCE_LIMITATION;
          }
        } else {
          console.error(error);
        }
      } finally {
        loading.value = false;
      }
    };

    const handleRouteChange = async (params, oldParams: any = {}) => {
      if (params.id !== oldParams.id) {
        await fetchData(params.id, false);
      }
      await fetchRiskModels();
    };

    watch(() => route.params, handleRouteChange, {
      immediate: true,
      deep: true,
    });

    const currentRiskInfo = computed(() => {
      const riskModel = riskModels.value[riskInfo.value.orgModelId];
      const resultSetting = riskModel?.resultSetting ?? [];
      const riskLevelInfo = resultSetting.find((rs) => rs.level === riskInfo.value.result);
      return {
        ...riskInfo.value,
        riskName: riskLevelInfo?.name,
        riskLevel: riskLevelInfo?.level,
      };
    });

    return {
      dimensionDetails,
      riskInfo,
      company,
      loading,
      riskLevel,
      handleRouteChange,
      snapshotDate,
      fetchData,
      errorOutOfLimitation,
      riskModels,
      currentRiskInfo,
    };
  },
  render() {
    return (
      <div>
        {!this.loading && this.company?.KeyNo ? (
          <RiskReview scrollOffsetTop={0} riskLevel={this.riskInfo.result} riskInfo={this.currentRiskInfo} />
        ) : null}

        {/* 用量超限 */}
        <div v-show={this.errorOutOfLimitation.error}>
          <Empty type="search" description={this.errorOutOfLimitation.message} />
        </div>
      </div>
    );
  },
});

export default InterestInvestigationCard;
