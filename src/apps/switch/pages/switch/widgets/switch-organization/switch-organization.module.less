@import '@/styles/token.less';

.container {
  width: 100%;
  padding: 32px 47px;
  background-color: #fff;
  border-radius: 8px;

  .header {
    position: relative;
    text-align: center;

    .logo {
      margin-bottom: 32px;
    }

    .description {
      margin-bottom: 32px;
      color: #333;
      font-size: 20px;
      text-align: left;
    }

    .disabledInfo {
      margin-bottom: 10px;
      color: @danger-color;
      text-align: center;
      font-weight: bold;

      a {
        color: @primary-color;
      }
    }
  }

  .organizations {
    overflow: auto;

    .item {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 15px;
      line-height: 32px;
      border: 1px solid #d6d6d6;
      border-radius: 4px;
      transition: all 0.15s linear;
      position: relative;
      margin-bottom: 15px;

      &:not(:last-child) {
        margin-bottom: 15px;
      }

      span {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .entLogo {
        width: 24px;
        height: 24px;
        border-radius: 4px;
        line-height: 24px;
        text-align: center;
        color: #fff;
        margin-right: 9px;
        font-size: 14px;
      }

      .entUser {
        display: flex;
        flex-direction: column;

        span {
          line-height: 20px;
        }
      }

      .addCurrent {
        position: absolute;
        top: 0;
        right: 0;
        padding: 0 5px;
        font-size: 12px;
        line-height: 18px;
        letter-spacing: 0;
        border-radius: 0 4px;
        background: #cae6fc;
        color: #128bed;
      }

      &.selected:not(.disabled) {
        color: #333;
        background: #f6fbfe;
        border-color: @primary-color;
      }

      &.disabled {
        border: 1px solid #d6d6d6;
        background-color: #f5f5f5;
        color: #333;
        opacity: 0.6;

        &:hover {
          cursor: not-allowed;
        }
      }
    }
  }

  .pagination {
    display: flex;
    justify-content: center;
    margin-top: 32px;
    padding: 10px 0;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
  }

  .footer {
    margin-top: 32px;

    .action {
      margin-bottom: 10px;

      button {
        height: 32px;
      }

      button[disabled] {
        color: #fff;
        background-color: #a0d1f8;
        border-color: #a0d1f8;
      }
    }
  }
}
