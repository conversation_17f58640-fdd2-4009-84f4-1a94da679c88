import { Button, Icon, Pagination } from 'ant-design-vue';
import { keyBy } from 'lodash';
import { defineComponent } from 'vue';

import styles from './switch-organization.module.less';

const DEFAULT_PAGE_SIZE = 5;

const SwitchOrganization = defineComponent({
  name: 'SwitchOrganization',

  props: {
    dataSource: {
      type: Array,
      required: true,
    },
    currentOrgId: {
      type: Number,
      required: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      currentPage: 1,
      selectedOrgId: undefined,
      disabledSelectedOrgId: undefined,
    };
  },

  watch: {
    currentOrgId: {
      handler(v) {
        (this as any).selectedOrgId = v;
      },
      immediate: true,
    },
    dataSource: {
      handler(v) {
        const currOrgInfo = v.find((s) => s.organizationId === this.currentOrgId);
        if (!currOrgInfo) {
          return;
        }
        const hasProBundle = (currOrgInfo.organizationBundles || []).find((bundle) => bundle?.serviceCode === 'SAAS_PRO');
        if (!hasProBundle || hasProBundle.isExpired) {
          (this as any).disabledSelectedOrgId = this.currentOrgId;
        }
      },
      immediate: true,
    },
  },

  computed: {
    total(): number {
      return this.dataSource.length;
    },
    currentList(): any[] {
      const { dataSource, currentPage } = this;
      const start = DEFAULT_PAGE_SIZE * (currentPage - 1);
      const end = DEFAULT_PAGE_SIZE + start;
      return dataSource.slice(start, end);
    },
    currentListMapper(): object {
      return keyBy(this.currentList, 'organizationId');
    },
  },

  methods: {
    enter() {
      if (this.currentOrgId === this.selectedOrgId) {
        this.$emit('close');
        return;
      }

      this.$emit('enter', this.selectedOrgId);
    },
    handleSelect(organizationId: number) {
      (this as any).selectedOrgId = organizationId;
    },
    getRoverBundle(bundles) {
      return bundles.find((v) => v.serviceCode === 'SAAS_PRO');
    },
    onContact(orgId) {
      window.location.href = `/?org=${encodeURIComponent(orgId)}`;
    },
    renderItem(item) {
      const { selectedOrgId, getRoverBundle, handleSelect } = this as any;
      const { organizationId, organizationBundles, name, test, roles } = item;
      const selected = organizationId === selectedOrgId;
      const roverBundle = getRoverBundle(organizationBundles);
      const disabled = !roverBundle || !!roverBundle.isExpired; // 没有rover套餐或者过期了
      const roleNames = roles.filter((roleItem) => !!roleItem?.roleName).map((roleItem) => roleItem?.roleName);
      return (
        <div
          key={organizationId}
          class={{
            [styles.item]: true,
            [styles.selected]: selected,
            [styles.disabled]: disabled,
          }}
          onClick={() => {
            if (disabled) {
              this.disabledSelectedOrgId = organizationId;
              return;
            }
            this.disabledSelectedOrgId = undefined;
            handleSelect(organizationId);
          }}
        >
          <div class={styles.entLogo} style={test}>
            {name.slice(0, 1)}
          </div>
          <span class={styles.entUser}>
            <span>{name}</span>
            <span>{roleNames.join(', ')}</span>
          </span>
          <Icon type="right" />
          {organizationId === this.currentOrgId ? <div class={styles.addCurrent}>当前组织</div> : null}
        </div>
      );
    },
    renderDisabledMsg() {
      const { currentListMapper, getRoverBundle } = this as any;
      const selectedOrgId: any = this.disabledSelectedOrgId;
      if (!selectedOrgId) {
        return null;
      }
      const orgInfo = currentListMapper[selectedOrgId];
      if (!orgInfo) {
        return null;
      }
      const { isOwner, organizationBundles } = orgInfo; // 是否管理员
      const roverBundle = getRoverBundle(organizationBundles); // rover套餐
      const orgName = `“${orgInfo.name}”`;

      const contactLabelSale = (
        <span>
          请<a onClick={() => (this as any).onContact(selectedOrgId)}>联系客户经理</a>
        </span>
      );
      const contactLabelAdmin = '请联系管理员';

      // 套餐已过期
      if (roverBundle && roverBundle.isExpired) {
        if (isOwner) {
          return (
            <div class={styles.disabledInfo}>
              {orgName}套餐已过期，{contactLabelSale}
            </div>
          );
        }
        return (
          <div class={styles.disabledInfo}>
            {orgName}套餐已过期，{contactLabelAdmin}
          </div>
        );
      }

      if (isOwner) {
        return (
          <div class={styles.disabledInfo}>
            {orgName}暂无套餐，{contactLabelSale}
          </div>
        );
      }
      return (
        <div class={styles.disabledInfo}>
          {orgName}暂无套餐，{contactLabelAdmin}
        </div>
      );
    },
  },

  render() {
    const { total, enter, renderItem, renderDisabledMsg } = this as any;

    return (
      <div class={styles.container}>
        <div class={styles.header}>
          <div class={styles.description}>切换组织</div>
          {renderDisabledMsg()}
        </div>

        <div class={styles.organizations} data-testid="organizationList">
          {this.currentList.map(renderItem)}
        </div>

        <div class={styles.pagination} v-show={total > DEFAULT_PAGE_SIZE}>
          <Pagination vModel={this.currentPage} total={total} defaultPageSize={DEFAULT_PAGE_SIZE} size="small" />
        </div>

        <div class={styles.footer}>
          <div class={styles.action}>
            <Button
              type="primary"
              block
              onClick={enter}
              disabled={this.selectedOrgId === this.disabledSelectedOrgId}
              loading={this.loading}
            >
              点击进入
            </Button>
          </div>
        </div>
      </div>
    );
  },
});

export default SwitchOrganization;
