import { shallowMount } from '@vue/test-utils';
import { Button } from 'ant-design-vue';

import SwitchOrganization from '..';

describe('SwitchOrganization', () => {
  describe('render', () => {
    test('default', () => {
      const wrapper = shallowMount<InstanceType<typeof SwitchOrganization>>(SwitchOrganization, {
        propsData: {
          currentOrgId: 2,
          dataSource: [
            {
              organizationId: 1,
              name: 'ORG_NAME_1',
              createDate: '2020-06-18T01:48:39.000Z',
              roles: [
                {
                  roleName: '超级管理员',
                },
                {
                  roleName: '管理员',
                },
              ],
              organizationBundles: [],
            },
            {
              organizationId: 2,
              name: 'ORG_NAME_2',
              createDate: '2021-02-18T08:37:07.000Z',
              roles: [
                {
                  roleName: '超级管理员',
                },
              ],
              organizationBundles: [],
            },
          ],
        },
      });
      expect(wrapper).toMatchSnapshot();
    });

    test('render: rover bundle', () => {
      const wrapper = shallowMount<InstanceType<typeof SwitchOrganization>>(SwitchOrganization, {
        propsData: {
          currentOrgId: 1,
          dataSource: [
            {
              organizationId: 1,
              name: 'ORG_NAME_1',
              createDate: '2021-02-18T08:37:07.000Z',
              serviceCode: 'SAAS_ROVER',
              roles: [
                {
                  roleName: '管理员',
                },
              ],
              organizationBundles: [],
            },
          ],
        },
      });
      wrapper.setData({
        selectedOrgId: 1,
      });
      expect(wrapper).toMatchSnapshot();
    });
  });

  describe('events', () => {
    let wrapper: ReturnType<typeof shallowMount>;
    beforeEach(() => {
      wrapper = shallowMount<InstanceType<typeof SwitchOrganization>>(SwitchOrganization, {
        propsData: {
          currentOrgId: 1,
          dataSource: [
            {
              organizationId: 1,
              name: 'ORG_NAME_1',
              createDate: '2021-02-18T08:37:07.000Z',
              serviceCode: 'SAAS_ROVER',
              roles: [
                {
                  roleName: '管理员',
                },
              ],
              organizationBundles: [],
            },
          ],
        },
      });
    });

    test('close', () => {
      // Act
      wrapper.setData({
        selectedOrgId: 1,
      });
      wrapper.findComponent(Button).vm.$emit('click');
      // Assert
      expect(wrapper.emitted('close')).toHaveLength(1);
    });

    test('enter', () => {
      // Act
      wrapper.setData({
        selectedOrgId: 2,
      });
      wrapper.findComponent(Button).vm.$emit('click');
      // Assert
      expect(wrapper.emitted('enter')).toHaveLength(1);
    });
  });
});
