import { defineComponent } from 'vue';

import { useUserStore } from '@/shared/composables/use-user-store';
import LogoZeiss from '@/shared/layouts/workbench/images/logo-zeiss.svg';
import LogoDefault from '@/shared/layouts/workbench/images/logo-default.svg';

const Logo = defineComponent({
  render() {
    const { isZeiss } = useUserStore();
    if (isZeiss.value) {
      return <img src={LogoZeiss} alt="logo" />;
    }
    return <img src={LogoDefault} alt="logo" />;
  },
});

export default Logo;
