import { shallowMount } from '@vue/test-utils';
import { computed } from 'vue';

import { useUserStore } from '@/shared/composables/use-user-store';

import Logo from '..';

vi.mock('@/shared/composables/use-user-store');

describe('Logo', () => {
  let wrapper: ReturnType<typeof shallowMount>;

  afterEach(() => {
    vi.clearAllMocks();
  });

  test('render', () => {
    vi.mocked<() => { isZeiss: { value: boolean } }>(useUserStore).mockReturnValue({
      isZeiss: computed(() => false),
    });
    wrapper = shallowMount<InstanceType<typeof Logo>>(Logo);
    expect(wrapper).toMatchSnapshot();
  });

  test('render: isZeiss', () => {
    vi.mocked<() => { isZeiss: { value: boolean } }>(useUserStore).mockReturnValue({
      isZeiss: { value: true },
    });
    wrapper = shallowMount<InstanceType<typeof Logo>>(Logo);
    expect(wrapper).toMatchSnapshot();
  });
});
