import { shallowMount } from '@vue/test-utils';
import { Modal } from 'ant-design-vue';

import SwitchPage from '../switch.page';
import SwitchOrganization from '../widgets/switch-organization';

vi.mock('@/store', () => ({
  useStore: () => ({
    getters: {
      'user/profile': {
        name: 'NAME',
        phone: 'PHONE',
        email: 'EMAIL',
      },
      'user/organizations': [
        {
          organizationId: 1,
          name: 'ORG_NAME_1',
          createDate: '2020-06-18T01:48:39.000Z',
          roles: [
            {
              roleName: '超级管理员',
            },
            {
              roleName: '管理员',
            },
          ],
          organizationBundles: [],
        },
        {
          organizationId: 2,
          name: 'ORG_NAME_2',
          createDate: '2021-02-18T08:37:07.000Z',
          roles: [
            {
              roleName: '超级管理员',
            },
          ],
          organizationBundles: [],
        },
      ],
    },
    dispatch: vi.fn().mockResolvedValue([]),
  }),
}));

vi.mock('@/shared/config/env', () => ({
  default: {
    ENTERPRISE_HOME: 'ENTERPRISE_HOME',
  },
}));

describe('SwitchPage', () => {
  let wrapper: ReturnType<typeof shallowMount>;

  beforeEach(() => {
    wrapper = shallowMount<InstanceType<typeof SwitchPage>>(SwitchPage, {
      propsData: {
        dataSource: [],
      },
    });
  });

  test('render', () => {
    expect(wrapper).toMatchSnapshot();
  });

  test('events: create', async () => {
    // Arrange
    const mockedModal = vi.spyOn(Modal, 'info');
    // Act
    wrapper.findComponent(SwitchOrganization).vm.$emit('create');
    // Assert
    expect(mockedModal).toHaveBeenCalledWith({
      title: '温馨提示',
      content: '如果您需要创建新组织请联系咨询顾问400-088-8275。',
      okText: '知道了',
    });
  });

  test('events: enter', async () => {
    vi.useFakeTimers();
    // Arrange
    Object.defineProperty(window, 'location', {
      value: { href: 'http://localhost/' },
      writable: true,
    });
    // Act
    wrapper.findComponent(SwitchOrganization).vm.$emit('enter', 'ORGANIZATION_ID');
    await vi.advanceTimersByTimeAsync(300);
    // Assert
    expect(window.location.href).toBe('ENTERPRISE_HOME/qcc/e/changeOrg/ORGANIZATION_ID?redirect=/e');
    // Reset
    vi.useRealTimers();
  });
});
