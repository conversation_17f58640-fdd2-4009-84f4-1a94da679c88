// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`SwitchPage > render 1`] = `
<div class="container" datasource=""><img src="/src/shared/layouts/workbench/images/logo-default.svg" alt="logo" class="logo">
  <div class="container organization">
    <div class="header">
      <div class="description">切换组织</div>
    </div>
    <div data-testid="organizationList" class="organizations">
      <div class="item disabled">
        <div class="entLogo" style="background: #E79177;">O</div><span class="entUser"><span>ORG_NAME_1</span><span>超级管理员, 管理员</span></span>
        <localereceiver-stub componentname="Icon"><i aria-label="undefined: right" class="anticon anticon-right">
            <antdicon-stub type="right-o" focusable="false" class=""></antdicon-stub>
          </i></localereceiver-stub>
      </div>
      <div class="item disabled">
        <div class="entLogo" style="background: #97a2e2;">O</div><span class="entUser"><span>ORG_NAME_2</span><span>超级管理员</span></span>
        <localereceiver-stub componentname="Icon"><i aria-label="undefined: right" class="anticon anticon-right">
            <antdicon-stub type="right-o" focusable="false" class=""></antdicon-stub>
          </i></localereceiver-stub>
      </div>
    </div>
    <div class="pagination" style="display: none;">
      <localereceiver-stub componentname="Pagination" defaultlocale="[object Object]">
        <pagination-stub prefixcls="ant-pagination" selectprefixcls="ant-select" current="1" defaultcurrent="1" total="2" defaultpagesize="5" selectcomponentclass="[object Object]" showprevnextjumpers="true" showtitle="true" locale="[object Object]" itemrender="[Function]" previcon="[object Object]" nexticon="[object Object]" jumpprevicon="[object Object]" jumpnexticon="[object Object]" class="mini"></pagination-stub>
      </localereceiver-stub>
    </div>
    <div class="footer">
      <div class="action">
        <wave-stub><button disabled="disabled" type="button" class="ant-btn ant-btn-primary ant-btn-block"><span>点击进入</span></button></wave-stub>
      </div>
    </div>
  </div>
</div>
`;
