import type { RouteConfig } from 'vue-router';

import SidebarMenuLayout from '@/shared/layouts/sidebar-menu';
import { APP_MENU_CONFIG } from '@/config/menu.config';
import InsightsLayout from '@/shared/layouts/insights';

export const companySearchRoutes = (): RouteConfig[] => [
  {
    path: '/company/search/list',
    component: SidebarMenuLayout,
    props: {
      pageTitle: '企业搜索',
      menu: APP_MENU_CONFIG,
    },
    meta: {
      title: '企业搜索',
    },
    children: [
      {
        path: '',
        name: `company-search`,
        component: () => import('../pages/company-search'),
        meta: {
          title: '企业搜索',
        },
      },
    ],
  },
];

export const companyDetailRoutes = (): RouteConfig[] => [
  {
    path: '/company/search/detail',
    component: InsightsLayout,
    props: {
      pageTitle: '企业搜索',
    },
    meta: {
      title: '企业搜索',
    },
    children: [
      {
        path: ':companyId([a-z0-9]+)',
        name: `company-detail`,
        props: true,
        component: () => import('../pages/company-detail'),
        meta: {
          title: '企业详情',
        },
      },
    ],
  },
];
