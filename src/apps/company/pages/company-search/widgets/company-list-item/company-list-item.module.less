.root {
  padding: 15px 15px 0;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  cursor: pointer;

  &:hover {
    background: #f2f8fe;
  }

  em {
    color: #F04040;
  }

  &.isHide {
    cursor: auto;

    &:hover {
      background: #fff;
    }
  }

  &::after {
    content: ' ';
    display: block;
    height: 1px;
    background-color: #eee;
    position: absolute;
    bottom: 0;
    left: 15px;
    right: 15px;
  }

  :global {
    .text-999 {
      color: #999;
    }

    .text-666 {
      color: #666;
    }
  }
}

.img {
  border: 1px solid #eee;
  margin-left: 10px;
  margin-right: 25px;
}

.main {
  display: flex;
  flex-direction: column;
  color: #999;
  flex: 1;

  .bolded {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    color: #333;
    font-size: 18px;

    .copyValue {
      display: flex;
      align-items: center;
    }

    .name {
      margin-right: 8px;
      font-weight: bold;
    }

    &:hover {
      color: #128bed;
    }

    &:hover em {
      color: #128bed;
    }

    :global {
      .copy-btn-item {
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }

  ul {
    line-height: 26px;
  }
}

.line {
  display: flex;
  flex-wrap: wrap;
  // min-width: 600px;

  li {
    margin-right: 30px;
    color: #666;
    white-space: nowrap;

    span {
      color: #999;
    }

    a {
      &:hover {
        color: #007add;
      }
    }
  }
}

.labels {
  margin-bottom: 8px;
}

.hits {
  // display: flex;
  i {
    font-size: 12px;
    color: #bbb;
    margin-right: 4px;
    padding-top: 4px;
  }
}

.sep {
  color: #eee;
}

.hit {
  position: relative;
  display: inline;

  & + & {
    margin-left: 10px;

    &::before {
      content: ' ';
      display: inline-block;
      width: 1px;
      height: 12px;
      background: #eee;
      vertical-align: middle;
      position: relative;
      margin-right: 10px;
    }
  }

  .field {
    color: #999;
  }

  .value {
    color: #666;
  }
}

.fullMatch {
  :hover {
    color: #128bed;
  }
}

.hideBg {
  width: 504px;
}

.tas {
  margin-right: 8px;
  line-height: 18px;
  display: inline-block;
  padding: 2px 6px;
  height: auto;
  box-sizing: border-box;
  border-width: 0 !important;
}

.alert {
  font-size: 14px;
  line-height: 1.5;
  color: #808080;
  padding: 6px 25px;
  gap: 10px;
  background-color: #fff4e0;
  width: 110%;
  margin-left: -15px;
  margin-right: -15px;
}

.illegalText {
  background-color: #f7f7f7;
  color: #666;
  font-size: 14px;
  line-height: 22px;
  padding: 10px;
  display: inline-block;
  border-radius: 2px;
}
