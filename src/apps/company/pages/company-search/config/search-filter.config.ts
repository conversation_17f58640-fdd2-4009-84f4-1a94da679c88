import moment from 'moment';

import AREA_DATA from '@/shared/constants/area.constant';
import NATIONAL_INDUSTRY_OPTIONS from '@/shared/constants/national-industry.constant';
import { INDUSTRY_CODE_OPTIONS } from '@/shared/constants/industry.constant';
import {
  COMPANY_SCALE,
  COMPANY_TYPES_C,
  COMPANY_STATUS_C,
  REGISTERED_CAPITAL_RANGE,
  REGISTERED_UNIT,
  QUALIFICATION_CERTIFICATE,
} from '@/config/tender.config';

export enum ViewTypeEnum {
  card = 'card',
  table = 'table',
}

export const SEARCH_TYPES = {
  company: 'company',
};

export const SEARCH_INDEX_OPTIONS = [
  { label: '企业名', value: 'name' },
  { label: '法定代表人', value: 'opername' },
  { label: '股东', value: 'promoterlist' },
  { label: '高管', value: 'employeelist' },
  { label: '品牌/产品', value: 'product' },
  { label: '联系地址', value: 'address' },
  { label: '经营范围', value: 'scope' },
  { label: '商标', value: 'featurelist' },
];

/**
 * 数据来源
 * @see http://gitlab.greatld.com:18888/qcc/pc-web/-/blob/master/src/store/filterData.js
 */
export function getSearchFilterConfig(today = new Date()) {
  return Object.freeze([
    {
      field: 'filters-common',
      label: '筛选条件',
      type: 'groups',
      children: [
        {
          field: 'r',
          type: 'cascader-multiple',
          label: '省份地区',
          options: AREA_DATA,
          layout: 'inline',
        },
        {
          field: 'd',
          type: 'multiple',
          label: '成立年限',
          options: [
            { value: undefined, label: '不限' },
            // ...DEFAULT_DATE_RANGE_LIFE,
            {
              label: '3个月内',
              value: {
                start: moment(today).subtract(3, 'months').format('YYYYMMDD'),
                end: '',
                value: '0-0.25',
              },
            },
            {
              label: '半年内',
              value: {
                start: moment(today).subtract(6, 'months').format('YYYYMMDD'),
                end: '',
                value: '0-0.5',
              },
            },
            {
              label: '1年内',
              value: {
                start: moment(today).subtract(1, 'years').format('YYYYMMDD'),
                end: '',
                value: '0-1',
              },
            },
            {
              label: '1-3年',
              value: {
                start: moment(today).subtract(3, 'years').format('YYYYMMDD'),
                end: moment(today).subtract(1, 'years').format('YYYYMMDD'),
                value: '1-3',
              },
            },
            {
              label: '3-5年',
              value: {
                start: moment(today).subtract(5, 'years').format('YYYYMMDD'),
                end: moment(today).subtract(3, 'years').format('YYYYMMDD'),
                value: '3-5',
              },
            },
            {
              label: '5-10年',
              value: {
                start: moment(today).subtract(10, 'years').format('YYYYMMDD'),
                end: moment(today).subtract(5, 'years').format('YYYYMMDD'),
                value: '5-10',
              },
            },
            {
              label: '10年以上',
              value: {
                start: '',
                end: moment(today).subtract(10, 'years').format('YYYYMMDD'),
                value: '10-0',
              },
            },
          ],
          // TODO: 日期范围需要转换为: `{\"start\":\"20230705\",\"end\":\"20230706\",\"value\":\"20230706-20230705\",\"x\":true}`
          // custom: {
          //   type: 'date-range',
          //   // type: 'number-range',
          //   // props: {
          //   //   unit: '年',
          //   //   max: [9999, 9999],
          //   //   min: [1970, 1970],
          //   // },
          // },
        },
        {
          field: 'i',
          type: 'cascader-multiple',
          label: '国标行业',
          options: NATIONAL_INDUSTRY_OPTIONS,
        },
      ],
    },
    // 更多筛选条件
    {
      field: 'filters-more',
      label: '更多筛选',
      type: 'groups',
      children: [
        {
          field: 'rc',
          type: 'multiple',
          label: '注册资本',
          options: REGISTERED_CAPITAL_RANGE,
          custom: {
            type: 'number-range',
            props: {
              unit: '万元',
            },
          },
        },
        {
          field: 'cp',
          type: 'multiple',
          label: '实缴资本',
          options: REGISTERED_CAPITAL_RANGE,
          custom: {
            type: 'number-range',
            props: {
              unit: '万元',
            },
          },
        },
        {
          field: 'scale',
          type: 'multiple',
          label: '企业规模',
          options: COMPANY_SCALE,
        },
        {
          field: 's',
          type: 'multiple',
          label: '登记状态',
          options: COMPANY_STATUS_C,
          transform: 'split',
        },
        { field: 'ot', type: 'multiple', label: '组织机构', options: COMPANY_TYPES_C },
        // { field: 'x', type: 'single', label: '投资商地区', options: [] },
        { field: 'c', type: 'multiple', label: '资本类型', options: REGISTERED_UNIT },
        { field: 'nil', type: 'multiple', label: '新兴行业', options: INDUSTRY_CODE_OPTIONS },
        {
          field: '__f__ISBR',
          type: 'single',
          label: '分支机构',
          options: [
            { value: undefined, label: '不限' },
            { value: 'ISBR', label: '是分支机构' },
            { value: 'N_ISBR', label: '非分支机构' },
          ],
        },
        {
          field: '__f__GT',
          type: 'single',
          label: '一般纳税人',
          options: [
            {
              label: '不限',
              value: undefined,
            },
            {
              label: '是一般纳税人',
              value: 'GT',
            },
            {
              label: '非一般纳税人',
              value: 'N_GT',
            },
          ],
        },
        {
          field: 'cl',
          type: 'multiple',
          label: '资质证书',
          options: QUALIFICATION_CERTIFICATE,
        },
        {
          field: '__f__SME',
          type: 'single',
          label: '小微企业',
          options: [
            {
              label: '不限',
              value: undefined,
            },
            {
              label: '是小微企业',
              value: 'SME',
            },
            {
              label: '非小微企业',
              value: 'N_SME',
            },
          ],
        },
        {
          field: 'tec',
          type: 'multiple',
          label: '科技型企业',
          options: [
            {
              label: '高新技术企业',
              value: 'HT001',
            },
            {
              label: '科技型中小企业',
              value: 'T_TSMES',
            },
            {
              label: '专精特新小巨人企业',
              value: 'T_SSTE',
            },
            {
              label: '专精特新中小企业',
              value: 'SSE',
            },
            {
              label: '创新型中小企业',
              value: 'T_INNMS',
            },
            {
              label: '制造业单项冠军企业',
              value: 'M_CP',
            },
            {
              label: '制造业单项冠军产品企业',
              value: 'M_SCPE',
            },
            {
              label: '独角兽企业',
              value: 'UE',
            },
            {
              label: '瞪羚企业',
              value: 'GE',
            },
            {
              label: '企业技术中心',
              value: 'T_TC',
            },
            {
              label: '重点实验室',
              value: 'M_KL',
            },
            {
              label: '技术创新示范企业',
              value: 'T_TD',
            },
            {
              label: '技术先进型服务企业',
              value: 'T_ATS',
            },
            {
              label: '众创空间',
              value: 'T_MS',
            },
            {
              label: '隐形冠军企业',
              value: 'T_IC',
            },
          ],
        },
        {
          field: '__f__HT001',
          type: 'single',
          label: '高新企业',
          options: [
            {
              label: '不限',
              value: undefined,
            },
            {
              label: '高新技术企业',
              value: 'HT001',
            },
            {
              label: '非高新技术企业',
              value: 'N_HT001',
            },
          ],
        },
        {
          field: '__f__TA',
          type: 'single',
          label: '纳税信用',
          options: [
            {
              label: '不限',
              value: undefined,
            },
            {
              label: 'A级',
              value: 'TA',
            },
            {
              label: '非A级',
              value: 'N_TA',
            },
          ],
        },
        {
          field: '__f__CI',
          type: 'single',
          label: '进出口信用',
          options: [
            {
              label: '不限',
              value: undefined,
            },
            {
              label: '有进出口信用',
              value: 'CI',
            },
            {
              label: '无有进出口信用',
              value: 'N_CI',
            },
          ],
        },
      ],
    },
  ]);
}
