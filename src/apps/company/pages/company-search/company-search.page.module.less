@import '@/styles/token.less';

.value {
  :global {
    .ant-checkbox-group-item {
      margin-right: 22px;

      &:hover,
      &.ant-checkbox-wrapper-checked {
        color: #128bed;
      }
    }

    .ant-checkbox-wrapper-checked {
      color: #128bed;
    }

    .ant-checkbox-wrapper {
      white-space: nowrap;
      margin-bottom: 10px;
    }
  }

  .checkboxGroup {
    display: inline;
  }
}

.tips {
  height: 40px;
  width: 100%;
  line-height: 40px;
  margin: 10px 0;
  padding: 0 15px;
  background-color: #e4f1fd;
  border-radius: 4px;

  em {
    color: #F04040;
  }
}

.search-input {
  padding-bottom: 15px;
  display: flex;
  align-items: center;
}

.search-range {
  display: flex;
  position: relative;
  z-index: 10;

  .label {
    width: 76px;
    color: #999;
    margin-right: 10px;
    white-space: pre;
    font-size: 14px;
  }
}

.name {
  display: flex;
  align-items: center;

  img {
    width: 40px;
    height: 40px;
    margin-right: 9px;
    border: 1px solid #eee;
    border-radius: 4px;
  }

  em {
    color: #F04040;
  }

  .info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .company-name {
    .tags {
      margin-bottom: 4px;
    }
  }
}

.hits {
  margin-top: 2px;
  overflow: hidden;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.hit {
  position: relative;
  color: #999;
  font-size: 12px;

  & + & {
    padding-left: 20px;

    &::before {
      position: absolute;
      content: ' ';
      display: inline-block;
      width: 1px;
      background-color: #999;
      left: 10px;
      top: 3px;
      height: 10px;
    }
  }
}

.person-wrap {
  display: flex;
  flex-wrap: wrap;
  // justify-content: space-between;
}

.pagination {
  text-align: right;
  padding: 20px 0;

  :global {
    .ant-pagination-item {
      border-radius: 2px;
      min-width: 20px;
      height: 20px;

      &-active {
        background-color: @primary-color;

        a {
          color: #fff;
        }
      }
    }

    .ant-pagination-options {
      height: 20px;
    }

    .ant-select-selection {
      border: none;
    }

    .ant-select-sm {
      .ant-select-selection {
        &--single {
          height: 20px !important;
        }
      }
    }

    .ant-pagination.mini .ant-pagination-options-quick-jumper input {
      height: 20px;
    }
  }
}

.toggle {
  border-radius: 2px;
  border: 1px solid #d8d8d8;
  padding: 2px;
  color: #333;
  height: 32px;
  display: flex;
  align-items: center;

  span {
    height: 100%;
    line-height: 24px;
    padding: 0 7px;
    display: inline-block;
    border-radius: 2px;
    cursor: pointer;
    vertical-align: middle;

    &.active {
      color: #fff;
      background: #128bed;
    }
  }
}

.extra {
  display: flex;

  :global {
    .anticon {
      color: #666;
    }
  }
}

.extra > *:not(:first-child) {
  margin-left: 10px;
}

.table {
  padding-bottom: 20px;

  .gwIcon {
    font-size: 12px;
    color: #094;
    margin-right: 5px;
  }

  a:hover {
    em {
      color: #0069bf !important;
    }
  }
}

.loading {
  height: calc(100vh - 338px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.tel-icon {
  color: #00ad65;
  margin-right: 2px;
}

.phone {
  margin-left: 2px;
}

.is-hide {
  color: #999;
}

.actionBtn{
  i{
    color: #666;
    font-size: 16px;
    margin-left: 0 !important;
  }

  &:hover{
    i{
      color: #128bed;
      transform: translateY(-3px) rotate(180deg);
    }
  }
}

.searchPart{
  :global{
    .q-filter-group:last-child{
      .q-filter-label{
        align-self: flex-start;
      }
    }

  }
}
