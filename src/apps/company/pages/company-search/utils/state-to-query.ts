import _ from 'lodash';

import { Path } from '@/utils/tree';
import toQuery, { parseQCCArea, parseQCCNational } from '@/utils/search-transform/to-query';
import { filterGroups, SimpleFilterState } from '@/utils/search-transform/company/simple-filter';

export const stateToQuery = ({ keyword, filters }: SimpleFilterState) => {
  const query = {} as any;
  const filter = toQuery(filterGroups, filters) as any;

  if (keyword) {
    query.searchKey = keyword;
  }
  if (_.isPlainObject(filters)) {
    _.forEach(filters, (v, k) => {
      switch (k) {
        case 'r':
          {
            const value = parseQCCArea(v as Path[]);
            if (value?.length) {
              filter[k] = value;
            }
          }
          break;
        case 'i':
          {
            const value = parseQCCNational(v as Path[]);
            if (value?.length) {
              filter[k] = value;
            }
          }
          break;
        default:
          {
            // 处理 `f` 开头的字段
            const matched = k.match(/^__f__(\w+)$/i);
            if (Array.isArray(matched) && matched[1]) {
              filter.f = [...(filter.f || []), v];
            } else {
              filter[k] = v;
            }
          }
          break;
      }
    });
  }
  if (!_.isEmpty(filter)) {
    query.filter = filter;
  }

  return query;
};
