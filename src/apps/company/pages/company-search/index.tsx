/**
 * FIXME: 老旧组件
 */
import { Breadcrumb, Checkbox, Icon, Pagination, message } from 'ant-design-vue';
import _ from 'lodash';
import mixins from 'vue-typed-mixins';
import { mapActions } from 'vuex';
import { Location } from 'vue-router';
import { useRoute } from 'vue-router/composables';

import QCard from '@/components/global/q-card';
import QInputSearch from '@/components/global/q-input-search';
import QRichTableEmpty from '@/components/global/q-rich-table/components/empty';
import QLoading from '@/components/global/q-loading';
import QFilter from '@/components/global/q-filter';
import QSelect from '@/components/global/q-select';
import { COMPANY_SORT_OPTIONS } from '@/config/search.config';
import type { ICompanyQcc } from '@/interfaces/company.interface';
import { createMixin } from '@/shared/mixins/list.mixin';
import { removeHTMLTags } from '@/utils/format/remove-html-tags';
import { validateCompanyTypeWithWarning } from '@/utils/company/company-type';
import { useWebTitle } from '@/shared/composables/use-web-title';
import { createTrackEvent } from '@/config/tracking-events';
import { useTrackEvent } from '@/hooks/use-track-event';
import { getHTMLText } from '@/utils';
import HeroicLayout from '@/shared/layouts/heroic';

import { SEARCH_INDEX_OPTIONS, SEARCH_TYPES, ViewTypeEnum, getSearchFilterConfig } from './config/search-filter.config';
import { stateToQuery } from './utils/state-to-query';
import CompanyListItem from './widgets/company-list-item';
import email from './widgets/company-list-item/images/email.svg';
import styles from './company-search.page.module.less';

const defaultState = ({ pr, reordFilter, tab, searchIndex = [] }: any = {}) => {
  const company: any = {};
  if (tab === SEARCH_TYPES.company) {
    company.recordPlace = {
      pr,
      reordFilter,
    };
  }
  const filters = {
    [SEARCH_TYPES.company]: { ...company },
  };
  return {
    query: {},
    filters,
    searchIndex: Array.isArray(searchIndex) ? searchIndex : [searchIndex],
    // searchIndex: SearchIndexOption.map((item) => item.value),
    confirmedFilters: _.cloneDeep(filters),
    sorters: {
      [SEARCH_TYPES.company]: null,
    },
  };
};

/**
 * 工商搜索结果页
 */
const CompanySearchPage = mixins(
  createMixin(
    [{ key: SEARCH_TYPES.company, autoScroll: true }],
    {
      rowKey: 'id',
    },
    {
      autoInit: false,
    }
  )
).extend({
  name: 'CompanySearchPage',
  props: {
    type: {
      type: String,
      default: 'investigation', // 'investigation' 排查入口 | 'company' 工商搜索入口
    },
  },
  data() {
    const keyword = _.trim(this.$route.query.keyword as string).slice(0, 100) || '';
    const tab = this.$route.query.tab as keyof typeof SEARCH_TYPES;
    const tabKeys = Object.keys(SEARCH_TYPES);
    const searchFilter = {};

    return {
      searchFilter,
      viewType: ViewTypeEnum.card,
      searchKey: keyword,
      keyword,
      mListActiveTabKey: tabKeys.includes(tab) ? tab : SEARCH_TYPES.company,
      searchType: 'fuzzy',
      confirmedKeyword: keyword,
      isQueryChanged: false,
      ...defaultState(this.$route.query),
    };
  },
  computed: {
    // 真全选，所有筛选项都确认才是
    isReallyAllChecked() {
      return (this as any).searchIndex?.length === SEARCH_INDEX_OPTIONS.length;
    },
    // 不限是否勾选
    isShowAllChecked() {
      return !(this as any).searchIndex?.length;
    },
    isFiltersEmpty() {
      return _.isEmpty(this.searchFilter);
    },
  },
  watch: {
    // filters: {
    //   deep: true,
    //   handler() {
    //     this.updateFilterChanged();
    //   },
    // },
    searchIndex: {
      handler(val) {
        (this as any).$router.replace({
          ...((this as any).$route as Location),
          query: {
            ...(this as any).$route.query,
            searchIndex: val,
          },
        });
        (this as any).mListResetModule((this as any).mListActiveTabKey);
        (this as any).mListInit((this as any).mListActiveTabKey);
      },
    },
    keyword: {
      handler() {
        if (!(this as any).keyword) {
          (this as any).$message.warning('请输入企业名称');
        }
      },
    },
    '$route.query.keyword': {
      handler(val) {
        if (!val) {
          return;
        }
        (this as any).keyword = val; // url 中 keyword 变化需要实时反应到搜索内容
        (this as any).mListResetModule((this as any).mListActiveTabKey);
        (this as any).mListInit((this as any).mListActiveTabKey);
      },
    },
  },
  setup() {
    const route = useRoute();
    const documentTitle = route.query.keyword ? `${route.query.keyword}的搜索结果` : '搜索结果';
    const { setWebTitle } = useWebTitle(documentTitle);
    const { handleSearchTrack } = useTrackEvent('搜索结果');
    return {
      setWebTitle,
      handleSearchTrack,
    };
  },
  methods: {
    ...mapActions('app', ['syncKeyword']),

    /**
     * 搜索请求
     */
    fetchDataSource({ key, pagination }) {
      const { keyword, confirmedFilters, searchIndex, sorters } = this as any;
      const share = { isHighlight: true, ...pagination, ...sorters[key] };
      (this as any).syncKeyword(keyword);
      if (!(this as any).updateConfirmedKeyword()) {
        return false;
      }
      return (
        this.$service.company
          .getCompanyListQCC({
            ...stateToQuery({
              keyword,
              // 排除掉没有值的filter
              filters: Object.keys(confirmedFilters[key]).reduce((data, cur) => {
                if (confirmedFilters[key][cur].length > 0) {
                  data[cur] = confirmedFilters[key][cur];
                }
                return data;
              }, {}),
            }),
            searchIndex: searchIndex.length ? searchIndex : undefined,
            ...share,
          })
          // 预防接口报错兜底
          .catch((error) => {
            console.error(error);
            return {
              Result: [],
            };
          })
      );
    },

    handleKeywordSearch(keyword: string) {
      if (!keyword?.trim()) {
        message.warning('请输入企业名称');
        return;
      }
      if (keyword?.trim()?.length < 2) {
        message.warning('关键词须至少包含2个字符');
        return;
      }
      // fix(RA-1351): 切换分页数量后，重新搜索，分页数要求重置
      this.mListHandlePageSizeChange((this as any).mListActiveTabKey, 1, 10);
      const input = this.$refs.InputSearch as any;
      this.$nextTick(input.focus);

      // if (_.trim(keyword) === this.confirmedKeyword) {
      //   if (this.isQueryChanged) {
      //     this.submitFilter();
      //   }
      //   return;
      // }

      if (!(this as any).updateConfirmedKeyword()) {
        return;
      }

      const route = this.$route;
      this.$router
        .replace({
          path: route.path,
          query: {
            ...route.query,
            keyword: (this as any).confirmedKeyword,
          },
        })
        .catch(_.noop);
    },
    handleFilterChange(filter) {
      const key = (this as any).mListActiveTabKey;

      // NOTE: confirmedFilters 保存转换后的接口数据
      this.$set((this as any).confirmedFilters, key, _.cloneDeep(filter || {}));
      // this.$set(this.filters, key, filter);

      // this.confirmedFilters = {
      //   ...this.confirmedFilters,
      //   [key]: _.cloneDeep(this.filters[key] || {}),
      // };

      // this.$set(this.filters, this.mListActiveTabKey, filter?.filters);
      // this.$nextTick(this.submitFilter);
      // 重置分页
      this.mListResetModule(key);
      this.mListInit(key);
    },
    handleFilterReset() {
      (this as any).searchFilter = {};
      // 重置分页
      this.mListResetModule((this as any).mListActiveTabKey);
      this.mListInit((this as any).mListActiveTabKey);
    },
    updateConfirmedKeyword() {
      const keyword = _.trim((this as any).keyword).slice(0, 100);

      if (keyword.length === 1) {
        this.$message.warning('关键词须至少包含2个字符');
        return false;
      }

      (this as any).confirmedKeyword = keyword;

      return true;
    },
    renderHitReasons(row) {
      const excludeHitReasons = ['公司名称', '法定代表人'];
      const { hitReasons, commonlist, highlight, opername } = row;
      const node: JSX.Element[] = [];

      hitReasons.forEach(({ Field, Value }) => {
        if (!excludeHitReasons.includes(Field)) {
          const name = removeHTMLTags(Value);
          const noLink = ['投资机构', 'App', '商标', '品牌/产品', '股票简称'];

          if (commonlist) {
            // 10:法人
            const legalList = commonlist?.filter((item) => ['10'].includes(item.k));
            const legalData = JSON.parse(_.get(legalList, '[0].v', '{}'));
            const legalHit = legalData.OperList?.filter((item) => item.n === name);
            // 9，11 Employe
            const employeList = commonlist?.filter((item) => ['9', '11'].includes(item.k)) || [];
            const employeData = _.flatten(employeList.map((item) => JSON.parse(item.v)));
            const employHit = employeData.filter((item: any) => item.n === name);

            // 4: 集团信息
            const groupList = commonlist?.filter((item) => ['4'].includes(item.k));
            const grouplData = JSON.parse(_.get(groupList, '[0].v', '{}'));

            if (_.get(legalHit, '[0].n') === name) {
              node.push(
                <span class={styles.hit}>
                  <span class={styles.field}>{Field} </span>:{(this as any).renderOper(legalData.OperList, highlight, opername)}
                </span>
              );
            } else if (_.get(employHit, '[0].n') === name) {
              node.push(
                <span class={styles.hit}>
                  <span class={styles.field}>{Field} </span>:{(this as any).renderOper([_.head(employHit)], highlight, opername)}
                </span>
              );
            } else if (grouplData.V === name && !noLink.includes(Field)) {
              node.push(
                <span class={styles.hit}>
                  <span class={styles.field}>{Field} </span>:
                  <q-link to={`group/${grouplData.K}/base`}>
                    <span domPropsInnerHTML={Value} />
                  </q-link>
                </span>
              );
            } else {
              node.push(
                <span class={styles.hit}>
                  <span class={styles.field}>{Field} </span>: <span domPropsInnerHTML={Value.split('。')} />
                </span>
              );
            }
          } else {
            node.push(
              <span class={styles.hit}>
                <span class={styles.field}>{Field} </span>: <span domPropsInnerHTML={Value.split('。')} />
              </span>
            );
          }
        }
      });
      node.length = 3;
      return node;
    },
    renderCompany(dataSource: ICompanyQcc[]) {
      const key = (this as any).mListActiveTabKey;
      if (key !== SEARCH_TYPES.company) {
        return null;
      }
      if (this.mListGetState(key).loading) {
        return (
          <div style={{ height: 'calc(100vh - 120px)' }}>
            <QLoading size="fullsize" />
          </div>
        );
      }
      if (!dataSource.length) {
        return (
          <div class={styles.loading}>
            <QRichTableEmpty size={'100px'}>暂时没有找到相关数据</QRichTableEmpty>
          </div>
        );
      }
      return (
        <div>
          {dataSource.map((item) => {
            return <CompanyListItem key={item.KeyNo} dataSource={item} onClick={(this as any).handleCompanyItemClick} />;
          })}
          {this.mListGetTableData(key).props.pagination.total > this.mListGetTableData(key).props.pagination.pageSize && (
            <Pagination
              style={{ padding: '15px 0', justifyContent: 'flex-end' }}
              onChange={this.mListGetTableData(key).props.pagination.onChange}
              onShowSizeChange={this.mListGetTableData(key).props.pagination.onShowSizeChange}
              {...{ props: this.mListGetTableData(key).props.pagination }}
            />
          )}
        </div>
      );
    },
    onShowContactMore(type, list, id?) {
      const that = this as any;
      if (type === 'email') {
        that.$modal.showDimension('companyContact', { title: '更多邮箱', size: 'medium' }, { type, list });
      } else if (type === 'tel') {
        this.$service.company.getDetail({ keyNo: id }).then((res) => {
          that.$modal.showDimension(
            'companyContact',
            {
              size: 'medium',
            },
            {
              type,
              list,
              info: res,
            }
          );
        });
      }
    },
    genEmailField(list): JSX.Element {
      if (!list.length) {
        return <span>-</span>;
      }
      if (list && list.length > 1) {
        return (
          <a>
            <a href={`mailto:${list[0].e}`}>
              {/* <q-icon type="icon-a-youxiangxian"></q-icon> */}
              <img src={email} style={'margin-top: -2px; margin-right: 4px;'}></img>
              <span>{list[0].e}</span>
            </a>
            <div onClick={() => (this as any).onShowContactMore('email', list)}>更多 {list.length}</div>
          </a>
        );
      }
      return (
        <a href={`mailto:${list[0].e}`}>
          {/* <q-icon type="icon-a-youxiangxian"></q-icon> */}
          <img src={email} style={'margin-top: -2px;margin-right: 4px;'}></img>
          <span>{list[0].e}</span>
        </a>
      );
    },
    genTelField({ tellist, id, commonlist }) {
      if (!tellist.length) {
        return <span>-</span>;
      }
      const telInfo = commonlist.filter((item) => item.k === '5')[0];
      if (tellist && tellist.length > 1) {
        return (
          <div>
            <q-phone-status phone={tellist[0].t} vtList={JSON.parse(telInfo?.v || '[]')} style={'margin-right: 4px'} />
            <span class={styles.phone}>{tellist[0].t}</span>
            <div>
              <a onClick={() => (this as any).onShowContactMore('tel', tellist, id)}>更多 {tellist.length}</a>
            </div>
          </div>
        );
      }
      return (
        <div>
          <q-phone-status phone={tellist[0].t} vtList={JSON.parse(telInfo?.v || '[]')} style={'margin-right: 4px'} />
          <span class={styles.phone}>{tellist[0].t}</span>
        </div>
      );
    },
    renderToggleView() {
      const key = (this as any).mListActiveTabKey;
      const toggle = (type) => {
        (this as any).viewType = type;
      };
      if (key !== SEARCH_TYPES.company) {
        return null;
      }
      return (
        <div class={styles.toggle}>
          <span onClick={() => toggle(ViewTypeEnum.card)} class={{ [styles.active]: (this as any).viewType === ViewTypeEnum.card }}>
            卡片
          </span>
          <span onClick={() => toggle(ViewTypeEnum.table)} class={{ [styles.active]: (this as any).viewType === ViewTypeEnum.table }}>
            表格
          </span>
        </div>
      );
    },
    updateSorter(key: string, value) {
      this.$set((this as any).sorters, key, value);
      // 重置分页
      this.mListResetModule(key);
      this.mListInit(key);
      this.$track(createTrackEvent(7717, '搜索结果', '默认排序'));
    },
    // eslint-disable-next-line consistent-return
    tableChange(params) {
      const key = (this as any).mListActiveTabKey;
      if (params.sorter.columnKey === 'registcapi' && params.sorter.order) {
        const value = {
          sortField: 'registcapiamount',
          sortOrder: params.sorter.order === 'ascend' ? 'ASC' : 'DESC',
        };
        this.$nextTick(() => {
          this.$set((this as any).sorters, key, value);
          // 重置分页
          this.mListResetModule(key);
          this.mListInit(key);
        });
        return false;
      }
      if (params.sorter.columnKey === 'startdatecode' && params.sorter.order) {
        const value = {
          sortField: 'startdatecode',
          sortOrder: params.sorter.order === 'ascend' ? 'ASC' : 'DESC',
        };
        this.$nextTick(() => {
          this.$set((this as any).sorters, key, value);
          // 重置分页
          this.mListResetModule(key);
          this.mListInit(key);
        });
        return false;
      }
      if (!params.sorter.order) {
        const value = null;
        this.$nextTick(() => {
          this.$set((this as any).sorters, key, value);
          // 重置分页
          this.mListResetModule(key);
          this.mListInit(key);
        });
        return false;
      }
    },
    renderOper(operList, highlight, name): any {
      const opername = _.get(highlight, 'opername[0]');
      const oname = opername || _.get(highlight, 'promoterlist2[0]');

      const noTagsStr = oname?.replace(/<[^>]+>/g, '');

      return operList.map((item, index) => {
        const node: any = [];
        if (index > 0) {
          node.push('、');
        }

        // 公司0 香港公司3 台湾公司5
        if ([0, 3, 5].includes(item?.o)) {
          node.push(
            <a
              href={`/embed/companyDetail?keyNo=${item.k}&title=${item.n}`}
              target="_blank"
              domPropsInnerHTML={item.n === noTagsStr ? oname || name : item.n || name}
            ></a>
          );
          return node;
        }
        if (item.k) {
          node.push(
            <a
              href={`/embed/beneficaryDetail?personId=${item.k}&title=${item.n}`}
              target="_blank"
              domPropsInnerHTML={item.n === noTagsStr ? oname || name : item.n || name}
            ></a>
          );
          return node;
        }
        node.push(
          item.n?.length ? (
            <a
              href={`/embed/beneficaryDetail?personId=${item.k}&title=${item.n}`}
              target="_blank"
              domPropsInnerHTML={item.n === noTagsStr ? oname || name : item.n || name}
            ></a>
          ) : (
            '-'
          )
        );
        return node;
      });
    },
    /**
     * 风险排查跳转
     */
    async handleInvestigationItemClick(item) {
      try {
        if (item?.IsHide || !validateCompanyTypeWithWarning(item.Type)) {
          return;
        }
        this.$router.push({
          path: `/investigation/search/detail/${item.KeyNo}`,
          query: {
            ...this.$route.query,
            from: 'list',
          },
        });
      } catch (error) {
        console.error(error);
      }
    },
    /**
     * 公司详情跳转
     */
    handleCompanyItemClick(item) {
      const type = this.$route.query.type ?? this.$route.query.from;
      switch (type) {
        case 'searchbar':
        case 'company':
          window.open(`/embed/companyDetail?keyNo=${item.KeyNo}&title=${getHTMLText(item.Name)}`, '_blank');
          break;
        case 'investigation':
          (this as any).handleInvestigationItemClick(item);
          break;
        default:
          break;
      }
    },

    /**
     * 操作选择
     */
    handleOperateActions(actionType: string, item: Record<string, any>) {
      switch (actionType) {
        case 'investigation': // 风险排查
          (this as any).handleInvestigationItemClick(item);
          break;
        default:
          break;
      }
    },
  },
  mounted() {
    (this as any).mListInit((this as any).mListActiveTabKey);
    // this.$track('page_view', {
    //   page_name: '搜索',
    // });
  },
  render() {
    const key = (this as any).mListActiveTabKey;
    const total = (this as any).mListGetTotal(key);
    const { dataSource, loading } = (this as any).mListGetState(key);

    return (
      <HeroicLayout class={styles.container}>
        <QCard
          slot="hero"
          bodyStyle={{
            padding: (this as any).type === 'company' ? '15px 15px 0 15px' : '15px',
          }}
        >
          <div class={styles.searchInput}>
            <QInputSearch
              width={650}
              placeholder="请输入企业名称或统一社会信用代码"
              v-model={(this as any).keyword}
              minLength={1}
              enterButtonText="搜索"
              onSearch={(keyword) => {
                (this as any).handleKeywordSearch(keyword);
                (this as any).setWebTitle(`${keyword}的搜索结果`);
              }}
              ref="InputSearch"
              cache={false}
            />
          </div>
          <div class={styles.searchRange}>
            <div class={styles.label}>搜索范围</div>
            <div class={styles.value} style={{ marginBottom: '-10px' }}>
              <Checkbox
                disabled={!(this as any).keyword}
                indeterminate={!(this as any).isShowAllChecked}
                checked={(this as any).isShowAllChecked}
                onChange={() => {
                  if ((this as any).searchIndex?.length) {
                    (this as any).searchIndex = [];
                  }
                }}
                style={{ marginRight: '22px' }}
              >
                不限
              </Checkbox>
              <Checkbox.Group
                disabled={!(this as any).keyword}
                v-show={key === SEARCH_TYPES.company}
                v-model={(this as any).searchIndex}
                options={SEARCH_INDEX_OPTIONS}
                class={styles.checkboxGroup}
              />
            </div>
          </div>
          {(this as any).type === 'company' ? (
            <QFilter
              class={styles.searchPart}
              disabled={!(this as any).keyword}
              groups={getSearchFilterConfig() as any}
              v-model={this.filters[(this as any).mListActiveTabKey]}
              onChange={(payload, group) => {
                (this as any).handleFilterChange(payload);
                (this as any).handleSearchTrack(7719, { keyword: null, filter: group.label });
              }}
            >
              {/* <Button type="link" onClick={this.handleFilterReset}>
              <Icon type="icon-chexiaozhongzhi" /> 重置筛选
            </Button> */}
            </QFilter>
          ) : null}
        </QCard>
        <QCard bodyStyle={{ padding: '0' }}>
          <div slot="title">
            共找到
            <Icon v-show={loading} type="sync" spin />
            <em v-show={!loading}>{Number(total) > 100000 ? '10万+' : total}</em>
            条相关结果
          </div>
          <div slot="extra">
            <div class={styles.extra}>
              {/* NOTE: 隐藏视图切换 */}
              <QSelect
                allowClear={false}
                value={(this as any).sorters[key]}
                options={COMPANY_SORT_OPTIONS}
                v-show={key === SEARCH_TYPES.company}
                onChange={(value) => (this as any).updateSorter(key, value)}
              />
            </div>
          </div>
          {(this as any).renderCompany(dataSource as ICompanyQcc[])}
        </QCard>
      </HeroicLayout>
    );
  },
});

export default CompanySearchPage;
