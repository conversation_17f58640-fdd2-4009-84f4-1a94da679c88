import { computed, defineComponent, ref, Ref } from 'vue';
import { Breadcrumb } from 'ant-design-vue';
import { omit } from 'lodash';
import { useRoute } from 'vue-router/composables';

import { useFetchState } from '@/hooks/use-fetch-state';
import { auth as authService } from '@/shared/services';
import CompanyStatus from '@/components/global/q-company-status';

import { getEmbedUrl } from './utils/embed-url';
import styles from './company-detail.page.module.less';

const CompanyDetailPage = defineComponent({
  name: 'CompanyDetailPage',
  props: {
    companyId: {
      type: [String, Number],
      required: true,
    },
  },
  setup(props) {
    const iframeRef: Ref<HTMLIFrameElement | null> = ref(null);

    const route = useRoute();
    const isHome = computed(() => route.query.from?.toString() === 'home');

    const { execute, result, isLoading } = useFetchState<typeof undefined, { token: string; key: string }>(authService.getProToken);
    execute();

    const iframeURL = computed(() => {
      if (!result.value?.token) {
        return '';
      }
      return getEmbedUrl({
        token: result.value.token,
        accessKey: result.value.key,
        type: 'companyDetail',
        // NOTE: 通过类型判断 `type` 从 `route.params` 中取值, 如有异常则返回 null
        query: {
          keyNo: props.companyId,
          // cWidth: '1400', // 需要动态计算宽度
        },
      });
    });

    iframeRef.value?.addEventListener('click', (ev) => {
      ev.stopPropagation();
      ev.preventDefault();
    });

    return {
      iframeRef,
      iframeURL,
      isHome,
    };
  },
  render() {
    const renderCompanyInfo = () => {
      const { name, status, code } = JSON.parse(this.$route.query.info?.toString() || '{}');
      return (
        <span class={styles.companyInfo}>
          <span domPropsInnerHTML={`${name}（${code}）`}></span>
          <CompanyStatus status={status} />
        </span>
      );
    };
    return (
      <div class={styles.container}>
        <div class={styles.breadcrumb}>
          <Breadcrumb class="sticky-breadcrumb">
            <Breadcrumb.Item>
              <router-link to="/">
                <q-icon type="icon-mianbaoxiefanhui" />
                首页
              </router-link>
            </Breadcrumb.Item>
            {this.isHome ? null : (
              <Breadcrumb.Item>
                <a onClick={() => this.$router.push({ path: '/company/search/list', query: omit(this.$route.query, ['info']) })}>
                  搜索列表
                </a>
              </Breadcrumb.Item>
            )}
            <Breadcrumb.Item>{renderCompanyInfo()}</Breadcrumb.Item>
            <Breadcrumb.Item class="lastItem">企业主页</Breadcrumb.Item>
          </Breadcrumb>
        </div>
        <div class={styles.iframe}>
          <iframe ref="iframeRef" src={this.iframeURL} allowfullscreen="true"></iframe>
        </div>
      </div>
    );
  },
});

export default CompanyDetailPage;
