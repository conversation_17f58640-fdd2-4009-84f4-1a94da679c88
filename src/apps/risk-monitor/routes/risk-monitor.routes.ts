import type { RouteConfig } from 'vue-router';

import SidebarMenuLayout from '@/shared/layouts/sidebar-menu';
import { APP_MENU_CONFIG } from '@/config/menu.config';
import { Permission } from '@/config/permissions.config';
import { hasPermission } from '@/shared/composables/use-permission';

export const riskMonitorRoutes = (): RouteConfig[] => [
  {
    path: '/risk-monitor',
    component: SidebarMenuLayout,
    props: {
      pageTitle: '风险监控',
      menu: APP_MENU_CONFIG,
    },
    redirect: () => {
      if (hasPermission([Permission.RISK_TRENDS_DASHBOARD])) {
        return {
          name: `risk-monitor-dashboard`,
        };
      }
      if (hasPermission([Permission.RISK_TRENDS_VIEW])) {
        return {
          name: `risk-monitor-trends`,
        };
      }
      if (hasPermission([Permission.MONITOR_ENTERPRISE_VIEW])) {
        return {
          name: `risk-monitor-targets`,
        };
      }
      return {
        name: 'risk-monitor-model-list',
      };
    },
    meta: {
      title: '风险监控',
    },
    children: [
      {
        path: 'dashboard',
        name: `risk-monitor-dashboard`,
        component: () => import('../pages/dashboard'),
        meta: {
          title: '监控面板',
          permission: [Permission.RISK_TRENDS_DASHBOARD],
        },
      },
      // {
      //   path: 'overview',
      //   name: `risk-monitor-overview`,
      //   component: () => import('../pages/targets2'),
      //   meta: {
      //     title: '监控模板',
      //     permission: [Permission.RISK_TRENDS_DASHBOARD],
      //   },
      // },
      {
        path: 'trends',
        name: `risk-monitor-trends`,
        component: () => import('../pages/trends'),
        meta: {
          title: '监控动态',
          permission: [Permission.RISK_TRENDS_VIEW],
        },
      },
      {
        path: 'targets',
        name: `risk-monitor-targets`,
        component: () => import('../pages/targets'),
        meta: {
          title: '监控企业',
          permission: [Permission.MONITOR_ENTERPRISE_VIEW],
        },
      },
      {
        path: 'targets/upload-confirm',
        name: 'risk-monitor-upload-confirm',
        meta: {
          title: '企业核实',
        },
        props: (route) => {
          return {
            pageType: 'monitor',
            batchId: parseInt(route.query.batchId.toString(), 10),
            groupId: parseInt(route.query.groupId.toString(), 10),
          };
        },
        component: () => import('../pages/targets-upload-confirm'),
      },
      {
        path: '(trends|targets)/detail/:id',
        name: `risk-monitor-targets-detail`,
        component: () => import('../pages/targets-detail'),
        meta: {
          title: '监控详情', // 监控企业相关动态
          permission: [Permission.RISK_TRENDS_VIEW],
        },
        props: true,
      },
      {
        path: 'targets/detail-trends/:id',
        name: `risk-monitor-targets-detail-trends`,
        component: () => import('../../../shared/components/dimenison-detail-drawer'),
        meta: {
          title: '监控详情', // 监控企业某一类型的具体动态列表
          permission: [Permission.RISK_TRENDS_VIEW],
        },
        props: true,
      },
      {
        path: 'targets/group-management',
        name: `risk-monitor-group-management`,
        component: () => import('../pages/group-management'),
        meta: {
          title: '分组管理',
          permission: [Permission.MONITOR_ENTERPRISE_GROUP_MANAGE],
        },
      },
      // models => model-detail
      {
        path: 'models',
        name: 'risk-monitor-model-list',
        props: {
          modelType: '2',
        },
        component: () => import('../../risk-model/pages/risk-model-list'),
        meta: {
          title: '监控模型',
          permission: [Permission.MONITOR_MODEL_VIEW],
        },
      },
      {
        path: 'models/detail/:riskModelId([0-9]+)',
        name: 'risk-monitor-model-detail',
        props: {
          default: true,
          modelType: '2',
        },
        component: () => import('../../risk-model/pages/risk-model-detail'),
        meta: {
          title: '监控模型详情',
          permission: [Permission.MONITOR_MODEL_VIEW],
        },
      },
    ],
  },
];
