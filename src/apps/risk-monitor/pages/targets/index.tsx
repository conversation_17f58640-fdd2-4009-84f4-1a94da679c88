import { message } from 'ant-design-vue';
import { computed, defineComponent, ref, watch } from 'vue';
import { debounce, isEmpty, isEqual } from 'lodash';
import { useRoute, useRouter } from 'vue-router/composables';

import HeroicLayout from '@/shared/layouts/heroic';
import QCard from '@/components/global/q-card';
import CommonSearchFilter from '@/components/common/common-search-filter';
import { monitor } from '@/shared/services';
import { convertSortStructure } from '@/utils/data-type/convert-sort-structure';

import { useSearchFilter } from './hooks/use-search-filter';
import { useSearchCompanies } from './hooks/use-search-companies';
import SearchResult from './widgets/search-result';
import { SEARCH_RESULT_TABLE_COLUMNS } from './config/search-config';
import { OpenRelatedCompanyModal } from './widgets/open-related-company-modal';
import { useRoomSocket } from '@/hooks/use-room-socket/use-room-socket';
import { BatchBusinessTypeEnums } from '@/config/batch.config';
import env from '@/shared/config/env';

const RiskMonitorTargetsPage = defineComponent({
  name: 'RiskMonitorTargetsPage',
  setup() {
    const route = useRoute();
    const router = useRouter();

    const {
      handleFilterChange,
      handleFilterReset,
      filterGroups,
      filterValues,
      operators: operatorList,
      monitorAggsResMap,
      getFilterOptions,
      isFilterLoading,
      getGroups,
      isInit,
    } = useSearchFilter();

    const searchCompanies = useSearchCompanies(filterValues, monitor.searchCompanies);

    const refreshCompanies = async () => {
      await searchCompanies.search({
        pageIndex: 1,
      });
      await getGroups();
    };

    watch(
      () => searchCompanies.data.value?.aggsRes,
      (newV, old) => {
        if (!isEqual(newV, old) && newV) {
          monitorAggsResMap.value = newV;
        }
      }
    );
    watch([isFilterLoading, searchCompanies.isLoading], () => {
      if (isFilterLoading.value && !searchCompanies.isLoading.value) {
        getFilterOptions();
        isFilterLoading.value = false;
      }
    });
    /** 表格配置 */
    const tableColumns = computed(() => {
      return SEARCH_RESULT_TABLE_COLUMNS;
    });

    const resRef = ref<any | null>(null);
    /** 操作公司 */
    const processCompanies = async (requestFn: (...args: any[]) => Promise<any>, data: Record<string, any>) => {
      try {
        const { failList } = await requestFn(data);
        if (!isEmpty(failList)) {
          return Promise.resolve(failList);
        }
        message.success('操作成功');
        // 刷新列表
        await refreshCompanies();
        return null;
      } catch (error) {
        console.error(error);
        return null;
      }
    };

    /** 移除公司 */
    const handleRemoveCompanies = async (removedCompanies: { companyId: string; monitorGroupId: string }[]) => {
      const params = removedCompanies.length
        ? {
            deleteMonitors: removedCompanies.map((item) => ({
              companyId: item.companyId,
              monitorGroupId: item.monitorGroupId,
            })),
          }
        : {
            needAggs: 0,
            keywords: filterValues.value?.keywords,
            ...filterValues.value?.filters,
            groupIds: (filterValues.value?.filters as any)?.groupIds ? [(filterValues.value?.filters as any)?.groupIds] : undefined,
          };
      const fn = removedCompanies.length ? monitor.removeCompanyFromGroup : monitor.removeAllCompany;
      await processCompanies(fn, params);
      if (removedCompanies.length) {
        resRef?.value?.resetSelect();
      } else {
        handleFilterReset();
      }
    };

    /** 移动公司 */
    const handleMoveCompanies = async (item: { oldGroupId: string; newGroupId: string; companyId: string }) => {
      await processCompanies(monitor.moveCompanyFromGroup, item);
    };

    /** 打开分组管理页面 */
    const openGroupManage = async () => {
      router.push({
        path: 'targets/group-management',
      });
    };

    const defaultGroupId = computed(() => {
      const { groupIds } = filterValues.value?.filters || ({} as any);
      const groupId = groupIds?.length > 0 ? groupIds[groupIds.length - 1] : route.query?.monitorGroupId;
      return groupId?.toString();
    });

    const socketUpdater = debounce(() => {
      searchCompanies.search();
      getGroups();
    }, 500);

    useRoomSocket(env.WEBSOCKET_BASE_URL, {
      eventType: 'SystemMessage',
      filter: (messageData: any) => {
        return [BatchBusinessTypeEnums.MonitorImport].includes(messageData.businessType);
      },
      refresh: socketUpdater,
    });

    return {
      isInit,
      resRef,
      filterValues,
      filterGroups,
      operatorList,
      openGroupManage,
      handleFilterChange,
      handleFilterReset,
      getFilterOptions,
      searchCompanies,
      tableColumns,

      handleMoveCompanies,
      handleRemoveCompanies,
      getGroups,
      refreshCompanies,

      defaultGroupId,
    };
  },
  render() {
    return (
      <div>
        <HeroicLayout loading={this.isInit}>
          {/* Filter */}
          <QCard
            slot="hero"
            title={this.$route.meta?.title}
            bodyStyle={{
              paddingTop: 0,
            }}
          >
            <CommonSearchFilter
              placeholder="请输入企业名称"
              filterConfig={this.filterGroups}
              onChange={this.handleFilterChange}
              defaultValue={this.filterValues}
              onReset={this.handleFilterReset}
              onGetOptions={this.getFilterOptions}
            />
          </QCard>

          {/* 搜索结果 */}
          <SearchResult
            ref="resRef"
            isLoading={this.searchCompanies.isLoading.value}
            rowKey={'monitorCompanyId'}
            searchKey={this.filterValues.keywords}
            columns={this.tableColumns}
            scroll={{ x: 1045, y: 'calc(100vh - 288px)' }}
            monitorGroupId={this.defaultGroupId}
            operatorList={this.operatorList}
            dataSource={this.searchCompanies.data.value?.data ?? []}
            pagination={this.searchCompanies.pagination.value}
            on={{
              groupManage: this.openGroupManage,
              refresh: () => {
                this.searchCompanies.search();
                this.getGroups();
              },
              moveItems: (item) => {
                this.handleMoveCompanies(item);
              },
              removeItems: (items) => {
                this.handleRemoveCompanies(items);
              },
              changePage: (pageIndex: number, pageSize: number) => {
                this.searchCompanies.search({ pageIndex, pageSize });
              },
              sorterChange: (sorterData) => {
                this.searchCompanies.sortInfo.value = convertSortStructure(sorterData);
                this.searchCompanies.search({ pageIndex: 1 });
              },
              update: () => {
                this.searchCompanies.search();
              },
              expandCompany: async (record, options) => {
                const res = await OpenRelatedCompanyModal({
                  operatorList: this.operatorList,
                  record,
                  options,
                });
                if (res) {
                  this.refreshCompanies();
                }
              },
            }}
          />
        </HeroicLayout>
      </div>
    );
  },
});

export default RiskMonitorTargetsPage;
