import { mount } from '@vue/test-utils';
import { RelatedCompanyModal } from '..';
import { monitor } from '@/shared/services';
import { flushPromises } from '@/test-utils/flush-promises';
import SearchResult from '../../search-result';

vi.mock('@/config/permissions.config', () => ({
  Permission: {
    MONITOR_ENTERPRISE_DELETE: 'MONITOR_ENTERPRISE_DELETE',
  },
}));
vi.mock('@/shared/services');

describe('RelatedCompanyModal', () => {
  const params = {
    record: {
      companyId: '123',
      monitorGroupId: '456',
      companyName: 'Test Company',
    },
    operatorList: [{}],
  };

  it('renders the modal with the correct title', async () => {
    vi.mocked(monitor.searchRelatedCompanies).mockResolvedValue({
      data: [{}],
    });
    const wrapper = mount(RelatedCompanyModal, {
      propsData: { params },
    });

    await flushPromises();
    expect(wrapper.text()).toContain('Test Company-监控关联方企业');
  });

  it('sets visible to true on mount', async () => {
    const wrapper = mount(RelatedCompanyModal, {
      propsData: { params },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.visible).toBe(true);
  });

  it('calls searchCompanies.search on mount', () => {
    mount(RelatedCompanyModal, {
      propsData: { params },
    });
    expect(monitor.searchRelatedCompanies).toHaveBeenCalled();
  });

  it('calls handleRemoveCompanies with an empty array when no companies are selected', async () => {
    const wrapper = mount(RelatedCompanyModal, {
      propsData: { params },
    });
    wrapper.vm.handleRemoveCompanies([]);
    await wrapper.vm.$nextTick();
    expect(monitor.removeAllRelated).toHaveBeenCalledWith({
      companyId: '123',
      monitorGroupId: '456',
    });
  });

  it('calls handleRemoveCompanies with selected companies', async () => {
    vi.mocked(monitor.searchRelatedCompanies).mockResolvedValue({
      data: [{}],
    });
    const wrapper = mount(RelatedCompanyModal, {
      propsData: { params },
    });
    await wrapper.vm.$nextTick();
    const table = wrapper.findComponent(SearchResult);
    table.vm.$emit('selectItems', [{ companyIdRelated: '789', monitorGroupId: '456' }]);
    await wrapper.vm.$nextTick();
    wrapper.vm.handleRemoveCompanies(wrapper.vm.selectRows);
    await wrapper.vm.$nextTick();
    expect(monitor.removeCompanyFromGroup).toHaveBeenCalledWith({
      deleteMonitors: [{ companyId: '789', monitorGroupId: 456 }],
    });
  });

  it('deleted success', async () => {
    const wrapper = mount(RelatedCompanyModal, {
      propsData: { params },
    });
    wrapper.vm.handleRemoveCompanies([]);
    await wrapper.vm.$nextTick();
    expect(monitor.searchRelatedCompanies).toHaveBeenCalled();
  });

  it('disables the "删除全部" button when total is 0', async () => {
    vi.mocked(monitor.searchRelatedCompanies).mockResolvedValue({
      data: [{}],
    });
    const wrapper = mount(RelatedCompanyModal, {
      propsData: { params },
    });
    await wrapper.vm.$nextTick();
    const removeButton = wrapper.find('button[disabled]');
    expect(removeButton.exists()).toBe(true);
  });

  it('disables the "删除选中" button when no companies are selected', async () => {
    vi.mocked(monitor.searchRelatedCompanies).mockResolvedValue({
      data: [{}],
      total: 1,
    });
    const wrapper = mount(RelatedCompanyModal, {
      propsData: { params },
    });
    await wrapper.vm.$nextTick();
    const deleteSelectedButton = wrapper.find('[data-testid="delete-selected-btn"]');
    expect(deleteSelectedButton.exists()).toBe(true);
  });

  it('enables the "删除选中" button when companies are selected', async () => {
    vi.mocked(monitor.searchRelatedCompanies).mockResolvedValue({
      data: [{}],
      total: 1,
    });
    const wrapper = mount(RelatedCompanyModal, {
      propsData: { params },
    });
    await wrapper.vm.$nextTick();
    const table = wrapper.findComponent(SearchResult);
    table.vm.$emit('selectItems', [{ companyIdRelated: '789', monitorGroupId: '456' }]);

    await wrapper.vm.$nextTick();
    const deleteSelectedButton = wrapper.find('[data-testid="delete-selected-btn"]') as any;
    expect(deleteSelectedButton.vm.disabled).toBe(false);
    await deleteSelectedButton.trigger('click');
    await wrapper.vm.$nextTick();
    expect(monitor.removeCompanyFromGroup).toHaveBeenCalledWith({
      deleteMonitors: [{ companyId: '789', monitorGroupId: 456 }],
    });
  });

  it('calls handleRemoveCompanies with an empty array when "删除全部" button is clicked', async () => {
    vi.mocked(monitor.searchRelatedCompanies).mockResolvedValue({
      data: [{}],
      total: 1,
    });
    const wrapper = mount(RelatedCompanyModal, {
      propsData: { params },
    });
    await wrapper.vm.$nextTick();
    const removeButton = wrapper.find('[data-testid="delete-all-btn"]') as any;
    expect(removeButton.vm.disabled).toBe(false);
    await removeButton.trigger('click');
    await wrapper.vm.$nextTick();
    expect(monitor.removeAllRelated).toHaveBeenCalledWith({
      companyId: '123',
      monitorGroupId: '456',
    });
  });

  it('calls searchCompanies.search when changePage is triggered', async () => {
    const wrapper = mount(RelatedCompanyModal, {
      propsData: { params },
    });
    await wrapper.vm.searchCompanies.search({ pageIndex: 1, pageSize: 10 });
    expect(monitor.searchRelatedCompanies).toHaveBeenCalled();
  });

  it('calls searchCompanies.search when sorterChange is triggered', async () => {
    vi.mocked(monitor.searchRelatedCompanies).mockResolvedValue({
      data: [{}],
      total: 1,
    });
    const wrapper = mount(RelatedCompanyModal, {
      propsData: { params },
    });
    await wrapper.vm.$nextTick();
    const table = wrapper.findComponent(SearchResult);
    table.vm.$emit('sorterChange', { field: 'name', order: 'ascend' });
    await wrapper.vm.$nextTick();
    expect(monitor.searchRelatedCompanies).toHaveBeenCalled();
  });
});
