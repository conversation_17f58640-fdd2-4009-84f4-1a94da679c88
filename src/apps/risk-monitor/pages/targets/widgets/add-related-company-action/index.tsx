import { Button } from 'ant-design-vue';
import { defineComponent, PropType, ref } from 'vue';

import { createTrackEvent } from '@/config/tracking-events';

import Modal from './modal';

const AddRelatedCompanyAction = defineComponent({
  name: 'AddRelatedCompanyAction',
  props: {
    /**
     * 文本
     */
    text: {
      type: String,
      default: '添加关联方',
    },
    /**
     * 传参
     */
    item: {
      type: Object,
      required: true,
    },
    /**
     * 当前选择分组
     */
    currentGroupId: {
      type: Number,
      required: true,
    },
    /**
     * 埋点
     */
    trackInfo: {
      type: Object as PropType<{ trackCode: number; actionPage: string }>,
      required: false,
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    const modalVisible = ref(false);
    const setModalVisible = (isVisible: boolean) => {
      modalVisible.value = isVisible;
    };
    const handleSubmit = () => {
      emit('update');
    };
    return {
      modalVisible,
      setModalVisible,
      handleSubmit,
    };
  },
  render() {
    return (
      <div>
        <Button
          type="link"
          style={{ width: '100%', textAlign: 'left' }}
          onClick={() => {
            this.setModalVisible(true);
            if (this.trackInfo) {
              this.$track(createTrackEvent(this.trackInfo.trackCode, this.trackInfo.actionPage, '添加关联方'));
            }
            // openAddRelatedCompanyModal({
            //   keyNo: this.item?.KeyNo,
            // });
          }}
        >
          {this.text}
        </Button>
        <Modal v-model={this.modalVisible} item={this.item} currentGroupId={this.currentGroupId} onSubmit={this.handleSubmit} />
      </div>
    );
  },
});

export default AddRelatedCompanyAction;
