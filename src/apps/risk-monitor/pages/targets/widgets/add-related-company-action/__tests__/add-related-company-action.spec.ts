import { shallowMount } from '@vue/test-utils';

import AddRelatedCompanyAction from '..';
import { monitor } from '@/shared/services';

vi.mock('@/shared/services');

describe('AddRelatedCompanyAction', () => {
  it('默认文本应该是“添加关联方”', () => {
    const wrapper = shallowMount(AddRelatedCompanyAction, {
      propsData: {
        item: { KeyNo: '123' },
        currentGroupId: 1,
      },
    });
    expect(wrapper.text()).toMatchInlineSnapshot(
      `"添加关联方序号企业名称状态暂时没有找到相关数据暂不支持对注册地在中国香港、中国澳门、中国台湾及境外的企业、机关单位、个体工商户发起监控"`
    );
  });

  it('应该正确显示传入的文本', () => {
    const wrapper = shallowMount(AddRelatedCompanyAction, {
      propsData: {
        text: '添加公司',
        item: { KeyNo: '123' },
        currentGroupId: 1,
      },
    });
    expect(wrapper.text()).toMatchInlineSnapshot(
      `"添加公司序号企业名称状态暂时没有找到相关数据暂不支持对注册地在中国香港、中国澳门、中国台湾及境外的企业、机关单位、个体工商户发起监控"`
    );
  });

  it('点击按钮应该显示模态框', async () => {
    vi.mocked(monitor.getRelatedCompany).mockResolvedValue({
      Result: [],
      Paging: {},
    });
    vi.mocked(monitor.getUnMonitorCompanyRelatedCount).mockResolvedValue({
      unMonitorCompanyCount: 0,
    });

    const wrapper = shallowMount(AddRelatedCompanyAction, {
      propsData: {
        item: { KeyNo: '123' },
        currentGroupId: 1,
      },
    });
    await wrapper.findComponent({ name: 'AButton' }).vm.$emit('click');
    expect(wrapper.vm.modalVisible).toBe(true);
  });

  it('点击按钮应该触发埋点', async () => {
    const mockTrack = vi.fn();
    const wrapper = shallowMount(AddRelatedCompanyAction, {
      propsData: {
        item: { KeyNo: '123' },
        currentGroupId: 1,
        trackInfo: { trackCode: 6975, actionPage: 'page' },
      },
      mocks: {
        $track: mockTrack,
      },
    });
    try {
      await wrapper.findComponent({ name: 'AButton' }).vm.$emit('click');
    } catch (e) {
      expect(mockTrack).toHaveBeenCalled();
      expect(e).toThrow('Error: 埋点事件: 123不存在');
    }
  });

  it('模态框提交应该触发update事件', async () => {
    const wrapper = shallowMount(AddRelatedCompanyAction, {
      propsData: {
        item: { KeyNo: '123' },
        currentGroupId: 1,
      },
    });
    await wrapper.findComponent({ name: 'AddRelatedCompanyModal' }).vm.$emit('submit');
    expect(wrapper.emitted('update')).toBeTruthy();
  });
});
