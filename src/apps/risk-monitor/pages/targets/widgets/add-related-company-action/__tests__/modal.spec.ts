import { mount } from '@vue/test-utils';
import { message } from 'ant-design-vue';

import { monitor } from '@/shared/services';

import AddRelatedCompanyModal from '../modal';
import { Mocked } from 'vitest';

vi.mock('@/shared/services');
vi.mock('@/config/tracking-events', () => ({
  useTrack: vi.fn(() => vi.fn()),
  createTrackEvent: vi.fn(() => 'trackEvent'),
}));

const mockItem = {
  companyId: '123',
  companyName: '测试公司',
};
const mockGroupId = 1;

const mockPaging = {
  PageIndex: 1,
  PageSize: 5,
  TotalRecords: 10,
};
const mockDataSource = Array(5)
  .fill(null)
  .map((_, i) => ({
    companyKeynoRelated: `key${i}`,
    companyNameRelated: `公司${i}`,
    relatedTypes: ['供应商'],
    isMonitor: i % 2 === 0,
  }));

describe('AddRelatedCompanyModal 组件测试', () => {
  let wrapper: any;

  beforeEach(() => {
    vi.mocked(monitor.getRelatedCompany).mockResolvedValue({
      Paging: mockPaging,
      Data: mockDataSource,
    });
    vi.mocked(monitor.getUnMonitorCompanyRelatedCount).mockResolvedValue({
      unMonitorCompanyCount: 5,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  test('应该正确打开和关闭模态框', async () => {
    wrapper = mount(AddRelatedCompanyModal, {
      propsData: {
        item: mockItem,
        visible: true,
        currentGroupId: mockGroupId,
      },
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent({ name: 'QModal' }).exists()).toBe(true);
    await wrapper.vm.close();
    expect(wrapper.emitted('visibleChange')).toBeTruthy();
  });

  test('应该正确处理搜索操作', async () => {
    wrapper = mount(AddRelatedCompanyModal, {
      propsData: {
        item: mockItem,
        visible: true,
        currentGroupId: mockGroupId,
      },
    });

    await wrapper.vm.$nextTick();
    const searchText = '重要客户';
    await wrapper.vm.handleSearch(searchText);
    expect(wrapper.vm.queryParams.keyword).toBe(searchText);
  });

  test('应该正确处理表格行选择', async () => {
    wrapper = mount(AddRelatedCompanyModal, {
      propsData: {
        item: mockItem,
        visible: true,
        currentGroupId: mockGroupId,
      },
    });

    const selectableItems = mockDataSource.filter((item) => !item.isMonitor);
    wrapper.vm.selection = selectableItems.slice(0, 3);
    expect(wrapper.vm.canAddMultiple).toBe(true);
  });

  test('当选择数量超过6000时应显示警告', async () => {
    vi.mocked<any>(monitor.addRelatedCompany).mockResolvedValue(true);
    wrapper = mount(AddRelatedCompanyModal, {
      propsData: {
        item: mockItem,
        visible: true,
        currentGroupId: mockGroupId,
      },
    });

    wrapper.vm.selection = Array(6001).fill({});
    await wrapper.vm.handleAddMultiple();
    expect(monitor.addRelatedCompany).not.toHaveBeenCalled();
  });

  test('当总数量超过6000时监控全部应失败', async () => {
    vi.mocked<any>(monitor.getRelatedCompany).mockResolvedValue({
      Paging: { TotalRecords: 6001 },
    });

    wrapper = mount(AddRelatedCompanyModal, {
      propsData: {
        item: mockItem,
        visible: false,
        currentGroupId: mockGroupId,
      },
    });
    await wrapper.setProps({ visible: true });

    await wrapper.vm.$nextTick();
    await wrapper.vm.handleAddAll();
    expect(monitor.monitorAllRelated).not.toHaveBeenCalled();
  });

  test('应该处理添加监控失败的情况', async () => {
    vi.mocked<any>(monitor.addRelatedCompany).mockRejectedValue(new Error('API Error'));
    message.error = vi.fn();

    wrapper = mount(AddRelatedCompanyModal, {
      propsData: {
        item: mockItem,
        visible: true,
        currentGroupId: mockGroupId,
      },
    });

    wrapper.vm.selection = [mockDataSource[1]];
    await wrapper.vm.handleAddMultiple();
    expect(message.error).not.toHaveBeenCalled();
    expect(wrapper.vm.isSubmitting).toBe(false);
  });
});
