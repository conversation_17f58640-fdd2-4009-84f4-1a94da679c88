import { nextTick, ref } from 'vue';

export const useInfinityList = (searchFn, className = '.infinity-list .ant-table-scroll .ant-table-body') => {
  const dataSource = ref<any[]>([]);
  const isEnd = ref(false);
  const loading = ref(false);

  const pagination = ref({
    pageIndex: 1,
    pageSize: 100,
    total: 0,
  });

  const loadMore = async () => {
    try {
      loading.value = true;
      const res = await searchFn(pagination.value);
      const Result = res.Result || res.data || [];
      const Paging = res.Paging || { TotalRecords: res.total, PageIndex: res.pageIndex, PageSize: res.pageSize };
      dataSource.value.push(...Result);
      loading.value = false;
      pagination.value.pageIndex++;
      pagination.value.total = Paging.TotalRecords;
      if (Result.length < pagination.value.pageSize) {
        isEnd.value = true;
      }
    } catch (error) {
      isEnd.value = true;
      loading.value = false;
    }
  };

  const getTableRef = () => {
    return document.querySelector(className);
  };

  const listenScroll = () => {
    const container = getTableRef();

    if (!container || loading.value || isEnd.value) return;

    const { scrollTop, clientHeight, scrollHeight } = container;

    const offset = 20;
    if (scrollTop + clientHeight >= scrollHeight - offset) {
      loadMore();
    }
  };

  const resetList = (load = true) => {
    pagination.value.pageIndex = 1;
    isEnd.value = false;
    dataSource.value = [];
    if (load) {
      loadMore();
    }
  };

  const initScrollListener = async () => {
    await loadMore();
    await nextTick();
    const tableRef = getTableRef();
    if (tableRef) {
      tableRef.addEventListener('scroll', listenScroll);
    }
  };

  const removeScrollListener = () => {
    const tableRef = getTableRef();
    if (tableRef) {
      resetList(false);
      tableRef.removeEventListener('scroll', listenScroll);
    }
  };

  return {
    dataSource,
    pagination,
    resetList,
    initScrollListener,
    removeScrollListener,
  };
};
