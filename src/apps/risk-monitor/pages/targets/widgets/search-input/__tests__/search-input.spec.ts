import { mount, Wrapper } from '@vue/test-utils';
import { nextTick } from 'vue';
import SearchInput from '..';
import Icon from '@/shared/components/icon';

describe('SearchInput', () => {
  let wrapper: Wrapper<any>;
  const flushPromises = () => new Promise(setImmediate);

  beforeEach(() => {
    wrapper = mount(SearchInput, {
      components: {
        Icon,
      },
    });
  });

  it('renders input with placeholder', () => {
    const input = wrapper.find('input');
    expect(input.attributes('placeholder')).toBe('请输入企业名称');
  });

  it('emits search event with trimmed input value on handleSearch', async () => {
    wrapper.vm.modelValue = '  example  ';
    await nextTick();
    wrapper.vm.handleSearch();
    await flushPromises();
    expect(wrapper.emitted().search).toBeTruthy();
    expect(wrapper.emitted().search?.[0]).toEqual(['example']);
  });

  it('emits search event with undefined when input value is empty on handleSearch', async () => {
    wrapper.vm.modelValue = '';
    await nextTick();
    wrapper.vm.handleSearch();
    await flushPromises();
    expect(wrapper.emitted().search).toBeTruthy();
    expect(wrapper.emitted().search?.[0]).toEqual([undefined]);
  });

  it('emits clear event when input value is cleared', async () => {
    wrapper.vm.modelValue = '';
    await nextTick();
    wrapper.vm.handleChange();
    await flushPromises();
    await vi.waitFor(() => {
      expect(wrapper.emitted().clear).toBeTruthy();
    });
  });

  it('does not emit clear event when input value is not cleared', async () => {
    wrapper.vm.modelValue = 'example';
    await nextTick();
    wrapper.vm.handleChange();
    await flushPromises();
    expect(wrapper.emitted().clear).toBeFalsy();
  });

  it('trims input value on onChange', async () => {
    const input = wrapper.find('input');
    await input.setValue('  example  ');
    wrapper.vm.handleChange();
    await flushPromises();
    expect(wrapper.vm.modelValue).toBe('  example  ');
    await vi.waitFor(() => {
      expect(wrapper.emitted().search?.[0]).toEqual(['example']);
    });
  });

  it('emits search event on pressing Enter', async () => {
    const input = wrapper.find('input');
    await input.setValue('example');
    await input.trigger('keydown', { key: 'Enter' });
    await flushPromises();
    await vi.waitFor(() => {
      expect(wrapper.emitted().search).toBeTruthy();
      expect(wrapper.emitted().search?.[0]).toEqual(['example']);
    });
  });
});
