import { defineComponent, ref } from 'vue';
import { Input } from 'ant-design-vue';
import { debounce } from 'lodash';

import Icon from '@/shared/components/icon';

import styles from './search-input.module.less';

const SearchInput = defineComponent({
  name: 'SearchInput',
  emits: ['search', 'clear'],
  setup(props, { emit }) {
    const modelValue = ref('');

    const handleSearch = () => {
      emit('search', modelValue.value?.trim() ? modelValue.value.trim() : undefined);
    };
    const _handleChange = () => {
      handleSearch();
      if (!modelValue.value) {
        emit('clear');
      }
    };
    const handleChange = debounce(_handleChange, 500);
    return {
      modelValue,
      handleSearch,
      handleChange,
    };
  },
  render() {
    return (
      <div class={styles.searchInput}>
        <Input
          placeholder="请输入企业名称"
          v-model={this.modelValue}
          onPressEnter={this.handleSearch}
          allowClear={true}
          onChange={this.handleChange}
        >
          <Icon slot="addonBefore" type="icon-sousuo" />
        </Input>
      </div>
    );
  },
});

export default SearchInput;
