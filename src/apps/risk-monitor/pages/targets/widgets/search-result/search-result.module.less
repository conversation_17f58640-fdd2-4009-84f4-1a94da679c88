.container {
  .companyName {
    em{
      color: #f04040;
    }
  }

  .relatedTrends {
    display: flex;
    gap: 5px;
    border-radius: 2px;
    font-size: 12px;
    line-height: 22px;
    background: #eee;
    width: max-content;
    padding: 0 6px;
  }

  .action{
    cursor: pointer;
    color: #128bed;
    
    &:hover{
      color: #0069bf;
    }
  }

  :global {
    .ant-table-expanded-row {
      border-color: #e4eef6 !important;

      .ant-table-bordered .ant-table-thead > tr > th {
        background: #f3f3f3 !important;

        &:last-child{
          border-right: none !important;
        }
      }

      .ant-table-bordered .ant-table-tbody > tr > td {
        background: #fafafa !important;

        &:last-child{
          border-right: none !important;
        }
      }
    }

    .ant-table-bordered .ant-table-bordered table .ant-table-tbody > tr > td {
      border-right: 1px solid #e4eef6 !important;
    }

    .ant-dropdown-open{
      .anticon{
        transform: rotate(180deg);
      }
    }
  }
}

  .actionMenu{
    :global{
      .ant-dropdown-menu-item {
        display: flex;
        align-items: center;
        padding: 5px 15px;
      }

      .ant-dropdown-menu-item .ant-btn-link{
        color: #333 !important;
        padding: 0;
        height: 22px;
      }
    }
  }

  .groupManageBtn {
    i {
      color: #666;
    }

    &:hover {
      i {
        color: currentcolor;
      }
    }
  }