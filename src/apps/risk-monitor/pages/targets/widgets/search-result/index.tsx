import { computed, defineComponent, ref } from 'vue';
import { Button, Dropdown, Icon, Popconfirm, Menu } from 'ant-design-vue';
import { uniqBy, escape, cloneDeep, differenceBy } from 'lodash';

import QRichTable from '@/components/global/q-rich-table';
import QCard from '@/components/global/q-card';
import SearchCount from '@/components/search-count';
import DropdownButtonWapper from '@/shared/components/dropdown-button-wrapper';
import CompanyStatus from '@/components/global/q-company-status';
import DiligenceWarningPop from '@/components/diligence-warning-pop';
import CompanyLogo from '@/components/company-logo';
import EmptyWrapper from '@/shared/components/empty-wrapper';
import QIcon from '@/components/global/q-icon';
import AddCompany from '@/components/modal/supplier/add-company-modal';
import { Permission } from '@/config/permissions.config';
import { hasPermission } from '@/shared/composables/use-permission';

import styles from './search-result.module.less';
import AddRelatedCompanyAction from '../add-related-company-action';
import { openMoveGroup } from '../move-group-modal';
import CompanyRelatedPop from '../company-related-pop';

const SearchResult = defineComponent({
  name: 'SearchResult',
  props: {
    dataSource: {
      type: Array,
      required: true,
    },
    columns: {
      type: Array,
      required: true,
    },
    rowKey: {
      type: String,
      default: 'id',
    },
    pagination: {
      type: Object,
      required: true,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    selectedItems: {
      type: Array,
      default: () => [],
    },
    operatorList: {
      type: Array,
      default: () => [],
    },
    searchKey: {
      type: String,
      default: undefined,
    },
    showExtra: {
      type: Boolean,
      default: true,
    },
    scroll: {
      type: Object,
      required: false,
    },
    extraSlots: {
      type: Object,
      default: () => ({}),
    },
    emptyMinHeight: {
      type: String,
      required: false,
    },
    monitorGroupId: {
      type: String,
      default: undefined,
    },
    selectable: {
      type: Boolean,
      default: true,
    },
  },
  emits: [
    'groupManage',
    'refresh',
    'addRelateCompany',
    'moveItems',
    'sorterChange',
    'removeItems',
    'selectItems',
    'addItems',
    'changePage',
    'update',
    'expandCompany',
    'beforeRouterChange',
  ],
  setup(props, { emit }) {
    const selectedIds = ref<any[]>([]);
    const selectedRowsTotal = ref<any[]>([]);

    const rowSelection = computed(() => ({
      checkStrictly: false,
      selectedRowKeys: selectedIds.value,
      onChange: (selectedRowKeys, selectedRows) => {
        selectedRowsTotal.value = uniqBy(
          [...differenceBy(selectedRowsTotal.value, props.dataSource, props.rowKey), ...selectedRows],
          props.rowKey
        );
        selectedIds.value = selectedRowsTotal.value.map((item) => item[props.rowKey]);
        emit('selectItems', cloneDeep(selectedRowsTotal.value));
      },
    }));

    const resetSelect = () => {
      selectedRowsTotal.value = [];
      selectedIds.value = [];
    };

    const handlePageChange = (current, pageSize) => {
      emit('changePage', current, pageSize);
    };

    const paginationProps = computed(() => ({
      ...props.pagination,
      onChange: handlePageChange,
      onShowSizeChange: handlePageChange,
    }));

    const handleMoveGroup = async (record) => {
      const res = await openMoveGroup({
        record,
      });
      if (res) {
        emit('moveItems', {
          toMonitorGroupId: res,
          deleteMonitors: [
            {
              companyId: record.companyId,
              monitorGroupId: record.monitorGroupId,
            },
          ],
        });
      }
    };
    return {
      selectedIds,
      rowSelection,
      selectedRowsTotal,
      paginationProps,
      handleMoveGroup,
      resetSelect,
    };
  },
  render() {
    const { searchKey } = this;
    return (
      <QCard class={[styles.container, 'tableRef']} bodyStyle={{ padding: '15px' }}>
        <div slot="title">
          <SearchCount
            slot="title"
            // showSelects={this.needSelect}
            total={this.pagination.total}
            loading={this.isLoading}
            selectedIds={this.selectedIds}
            scopedSlots={{
              message: (content) => {
                return <div class="flex items-center">共找到{content}家企业</div>;
              },
            }}
          />
        </div>
        <div slot="extra" style={{ display: 'flex', gap: '10px' }}>
          {this.showExtra && [
            <DropdownButtonWapper
              v-permission={[Permission.MONITOR_ENTERPRISE_DELETE]}
              totalCount={this.pagination.total}
              btnText="删除列表"
              selectIdlength={this.selectedIds.length}
              onConfirm={(key) => {
                this.$emit('removeItems', key === 'all' ? [] : this.selectedRowsTotal);
              }}
            />,
            <Button
              v-permission={[Permission.MONITOR_ENTERPRISE_GROUP_MANAGE]}
              class={styles.groupManageBtn}
              onClick={() => {
                this.$emit('groupManage');
              }}
            >
              <q-icon type="icon-fenzuguanliicon"></q-icon>分组管理
            </Button>,
            <AddCompany
              v-permission={[Permission.MONITOR_ENTERPRISE_ADD]}
              monitorGroupId={this.monitorGroupId}
              on={{
                refresh: () => this.$emit('refresh'),
              }}
            />,
          ]}
        </div>
        <EmptyWrapper
          dataSource={this.dataSource}
          loading={this.isLoading}
          enableResetBtn={this.showExtra}
          emptyMinHeight={this.emptyMinHeight}
        >
          <QRichTable
            loading={this.isLoading}
            tableLayout="fixed"
            rowKey={this.rowKey}
            showIndex={false}
            dataSource={this.dataSource}
            columns={this.columns}
            customScroll={this.scroll}
            pagination={this.paginationProps}
            rowSelection={this.selectable ? this.rowSelection : null}
            onChange={({ sorter }) => {
              this.$emit('sorterChange', sorter);
            }}
            scopedSlots={{
              expandedTitleRender: (item) => {
                const companyName = escape(item.companyName || item.companyNameRelated).replace(searchKey, `<em>${searchKey}</em>`);
                return (
                  <div class="flex items-center" style={{ gap: '10px' }}>
                    <CompanyLogo
                      src={item.ImageUrl}
                      id={item.companyId || ''}
                      name={item.ShortName || item.companyName}
                      hasimage={item.HasImage}
                      class="shrink-0"
                    />
                    <div class="flex-1">
                      <span data-testid="company-name" class={[styles.companyName, 'mr-1']} domPropsInnerHTML={companyName} />
                      {/* 可能出现不同分组下同一家公司，所以只能用monitorCompanyId */}
                      {this.showExtra && [
                        <CompanyRelatedPop
                          class="mr-1 align-bottom"
                          typeCode={item.primaryObject}
                          totalCount={item.relatedPartyCount}
                          monitorGroupId={item.monitorGroupId}
                          companyInfo={{
                            companyId: item.companyId,
                            companyName: item.companyName,
                          }}
                          onOpenRelated={(option) => this.$emit('expandCompany', item, option)}
                        />,
                        item.isBothPrimaryAndRelated ? (
                          <CompanyRelatedPop
                            class="mr-1 align-bottom"
                            typeCode={Math.abs(item.primaryObject - 1)}
                            totalCount={item.relatedPartyCount}
                            monitorGroupId={item.monitorGroupId}
                            companyInfo={{
                              companyId: item.companyId,
                              companyName: item.companyName,
                            }}
                            onOpenRelated={(option) => this.$emit('expandCompany', item, option)}
                          />
                        ) : null,
                      ]}
                    </div>
                    <Button
                      v-permission={[Permission.RISK_TRENDS_VIEW]}
                      type="link"
                      onClick={() => {
                        this.$emit('beforeRouterChange');
                        this.$router.push({
                          path: `targets/detail/${item.companyId}`,
                          query: {
                            from: 'targets', // 面包屑导航
                            name: this.$route.meta?.title,
                            groupId: item.monitorGroupId,
                          },
                        });
                      }}
                    >
                      动态 <Icon type="right"></Icon>
                    </Button>
                  </div>
                );
              },
              companyStatus: (status) => {
                if (!status || status === '-') {
                  return '-';
                }
                return <CompanyStatus ghost status={status} />;
              },
              MonitorResult: (val, item) => {
                // 风险等级
                return <DiligenceWarningPop score={val} rowData={item} modelType={'monitor'} />;
              },
              MonitorGroup: (record) => {
                return record.monitorGroupEntity?.name || '-';
              },

              MonitorCreator: (userId) => {
                return (this.operatorList.find((item: any) => item.value === userId) as any)?.label || '-';
              },
              Action: (record) => {
                if (
                  !hasPermission([
                    Permission.MONITOR_ENTERPRISE_ADD,
                    Permission.MONITOR_ENTERPRISE_MOVE_GROUP,
                    Permission.MONITOR_ENTERPRISE_DELETE,
                  ])
                ) {
                  return '-';
                }
                return (
                  <Dropdown
                    trigger={['click']}
                    onClick={(e) => e.preventDefault()}
                    placement="bottomLeft"
                    getPopupContainer={() => document.getElementsByClassName('tableRef')[0]}
                    overlayClassName={styles.actionMenu}
                  >
                    <div class={styles.action}>
                      更多
                      <QIcon type="icon-a-shixinxia1x" />
                    </div>
                    <Menu slot="overlay">
                      <Menu.Item key="1" v-permission={[Permission.MONITOR_ENTERPRISE_ADD]}>
                        <AddRelatedCompanyAction
                          style={{ width: '100%', textAlign: 'left' }}
                          onUpdate={async () => {
                            this.$emit('refresh');
                          }}
                          currentGroupId={record.monitorGroupId}
                          item={record}
                        />
                      </Menu.Item>
                      <Menu.Item key="2" v-show={record.relatedPartyCount > 0}>
                        <Button
                          type="link"
                          style={{ width: '100%', textAlign: 'left' }}
                          onClick={() => this.$emit('expandCompany', record)}
                        >
                          查看监控的关联企业
                        </Button>
                      </Menu.Item>
                      <Menu.Item key="3" v-permission={[Permission.MONITOR_ENTERPRISE_MOVE_GROUP]}>
                        <Button type="link" style={{ width: '100%', textAlign: 'left' }} onClick={() => this.handleMoveGroup(record)}>
                          移组
                        </Button>
                      </Menu.Item>
                      <Menu.Item key="4" v-permission={[Permission.MONITOR_ENTERPRISE_DELETE]}>
                        <Popconfirm
                          title="您确定要删除该公司么?"
                          okText="确定"
                          cancelText="取消"
                          onConfirm={() => this.$emit('removeItems', [record])}
                        >
                          <Button type="link" style={{ width: '100%', textAlign: 'left' }}>
                            删除
                          </Button>
                        </Popconfirm>
                      </Menu.Item>
                    </Menu>
                  </Dropdown>
                );
              },
              InnerAction: (record) => {
                if (!hasPermission([Permission.MONITOR_ENTERPRISE_DELETE])) {
                  return '-';
                }
                return (
                  <Popconfirm
                    title="您确定要删除该公司么?"
                    okText="确定"
                    cancelText="取消"
                    onConfirm={() => this.$emit('removeItems', [record])}
                  >
                    <Button type="link">删除</Button>
                  </Popconfirm>
                );
              },
              ...this.extraSlots,
            }}
          />
        </EmptyWrapper>
      </QCard>
    );
  },
});

export default SearchResult;
