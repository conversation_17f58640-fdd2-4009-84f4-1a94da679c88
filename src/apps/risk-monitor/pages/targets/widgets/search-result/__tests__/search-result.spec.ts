import { mount } from '@vue/test-utils';
import SearchResult from '..';
import QRichTable from '@/components/global/q-rich-table';
import SearchCount from '@/components/search-count';
import AddCompany from '@/components/modal/supplier/add-company-modal';
import { Button, Dropdown } from 'ant-design-vue';
import CompanyRelatedPop from '../../company-related-pop';
import DropdownButtonWapper from '@/shared/components/dropdown-button-wrapper';
import EmptyWrapper from '@/shared/components/empty-wrapper';

const mockDataSource = [
  { id: 1, companyName: 'Company A', monitorGroupId: 'group1', companyId: '1', relatedPartyCount: 1 },
  { id: 2, companyName: 'Company B', monitorGroupId: 'group2', companyId: '2', relatedPartyCount: 0 },
];

const mockColumns = [
  { title: 'Company Name', dataIndex: 'companyName' },
  { title: 'Monitor Group', dataIndex: 'monitorGroupEntity.name' },
];

const mockPagination = {
  total: 2,
  current: 1,
  pageSize: 10,
};

const mockOperatorList = [
  { value: 'user1', label: 'User 1' },
  { value: 'user2', label: 'User 2' },
];

describe('SearchResult Component', () => {
  it('renders correctly with happy path data', () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: mockDataSource,
        columns: mockColumns,
        pagination: mockPagination,
        operatorList: mockOperatorList,
      },
    });

    expect(wrapper.findComponent(QRichTable).exists()).toBe(true);
    expect(wrapper.findComponent(SearchCount).exists()).toBe(true);
    expect(wrapper.findComponent(AddCompany).exists()).toBe(true);
    expect(wrapper.findComponent(DropdownButtonWapper).exists()).toBe(true);
    expect(wrapper.findComponent(Button).exists()).toBe(true);
    expect(wrapper.findComponent(CompanyRelatedPop).exists()).toBe(false);
    expect(wrapper.findComponent(Dropdown).exists()).toBe(true);
  });

  it('does not render extra actions when showExtra is false', () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: mockDataSource,
        columns: mockColumns,
        pagination: mockPagination,
        operatorList: mockOperatorList,
        showExtra: false,
      },
    });

    expect(wrapper.findComponent(DropdownButtonWapper).exists()).toBe(false);
    expect(wrapper.findComponent(Button).exists()).toBe(false);
    expect(wrapper.findComponent(AddCompany).exists()).toBe(false);
  });

  it('emits correct events on row selection', async () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: mockDataSource,
        columns: mockColumns,
        pagination: mockPagination,
        operatorList: mockOperatorList,
      },
    });

    await wrapper.vm.$nextTick();
    const rowSelection = wrapper.vm.rowSelection;
    rowSelection.onChange([1], [mockDataSource[0]]);

    expect(wrapper.emitted()['selectItems']).toBeTruthy();
    expect((wrapper.emitted()['selectItems'] as any)[0][0]).toEqual([mockDataSource[0]]);
  });

  it('emits correct events on page change', async () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: mockDataSource,
        columns: mockColumns,
        pagination: mockPagination,
        operatorList: mockOperatorList,
      },
    });

    const paginationProps = wrapper.vm.paginationProps;
    paginationProps.onChange(2, 10);

    expect(wrapper.emitted()['changePage']).toBeTruthy();
    expect((wrapper.emitted()['changePage'] as any)[0][0]).toBe(2);
    expect((wrapper.emitted()['changePage'] as any)[0][1]).toBe(10);
  });

  it('renders correctly with empty data source', () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: [],
        columns: mockColumns,
        pagination: mockPagination,
        operatorList: mockOperatorList,
      },
    });

    expect(wrapper.findComponent(EmptyWrapper).exists()).toBe(true);
    expect(wrapper.findComponent(QRichTable).exists()).toBe(false);
  });

  it('emits correct events on remove items', async () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: mockDataSource,
        columns: mockColumns,
        pagination: mockPagination,
        operatorList: mockOperatorList,
        selectedItems: [mockDataSource[0]],
      },
    });

    const dropdownButtonWapper = wrapper.findComponent(DropdownButtonWapper);
    dropdownButtonWapper.vm.$emit('confirm', 'selected');

    expect(wrapper.emitted()['removeItems']).toBeTruthy();
  });

  it('emits correct events on remove all items', async () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: mockDataSource,
        columns: mockColumns,
        pagination: mockPagination,
        operatorList: mockOperatorList,
        selectedItems: [mockDataSource[0]],
      },
    });

    const dropdownButtonWapper = wrapper.findComponent(DropdownButtonWapper);
    dropdownButtonWapper.vm.$emit('confirm', 'all');

    expect(wrapper.emitted()['removeItems']).toBeTruthy();
    expect((wrapper.emitted()['removeItems'] as any)[0][0]).toEqual([]);
  });

  it('emits correct events on group manage', async () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: mockDataSource,
        columns: mockColumns,
        pagination: mockPagination,
        operatorList: mockOperatorList,
      },
    });

    const groupManageButton = wrapper.find('.groupManageBtn');
    groupManageButton.vm.$emit('click');

    expect(wrapper.emitted()['groupManage']).toBeTruthy();
  });

  it('renders correctly with no search key', () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: mockDataSource,
        columns: mockColumns,
        pagination: mockPagination,
        operatorList: mockOperatorList,
        searchKey: undefined,
      },
    });

    expect(wrapper.findComponent(QRichTable).exists()).toBe(true);
  });

  it('renders correctly with search key', () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: mockDataSource,
        columns: mockColumns,
        pagination: mockPagination,
        operatorList: mockOperatorList,
        searchKey: 'Company A',
      },
    });

    expect(wrapper.findComponent(QRichTable).exists()).toBe(true);
  });

  it('renders correctly with no operator list', () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: mockDataSource,
        columns: mockColumns,
        pagination: mockPagination,
        operatorList: [],
      },
    });

    expect(wrapper.findComponent(QRichTable).exists()).toBe(true);
  });

  it('renders correctly with no selected items', () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: mockDataSource,
        columns: mockColumns,
        pagination: mockPagination,
        operatorList: mockOperatorList,
        selectedItems: [],
      },
    });

    expect(wrapper.findComponent(QRichTable).exists()).toBe(true);
  });

  it('renders correctly with no scroll configuration', () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: mockDataSource,
        columns: mockColumns,
        pagination: mockPagination,
        operatorList: mockOperatorList,
        scroll: undefined,
      },
    });

    expect(wrapper.findComponent(QRichTable).exists()).toBe(true);
  });

  it('renders correctly with no extra slots', () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: mockDataSource,
        columns: mockColumns,
        pagination: mockPagination,
        operatorList: mockOperatorList,
        extraSlots: {},
      },
    });

    expect(wrapper.findComponent(QRichTable).exists()).toBe(true);
  });

  it('renders correctly with custom empty min height', () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: [],
        columns: mockColumns,
        pagination: mockPagination,
        operatorList: mockOperatorList,
        emptyMinHeight: '300px',
      },
    });
    const emptyWrapper = wrapper.findComponent(EmptyWrapper);
    expect(emptyWrapper.props().emptyMinHeight).toBe('300px');
  });

  it('renders correctly with no monitor group id', () => {
    const wrapper = mount(SearchResult, {
      propsData: {
        dataSource: mockDataSource,
        columns: mockColumns,
        pagination: mockPagination,
        operatorList: mockOperatorList,
        monitorGroupId: undefined,
      },
    });

    expect(wrapper.findComponent(QRichTable).exists()).toBe(true);
  });
});
