@gap: 5px;
@color-arrow: #d8d8d8;

.pop-wrapper {
  :global {
    .ant-popover-inner-content {
      padding: 0;
    }
  }
}

.icon-wrapper {
  width: 22px;
  height: 22px;
  border-radius: 2px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  background: #fff;
  border: 1px solid #eee;
  cursor: pointer;

  &.main:hover {
    border: 1px solid #ffbd9b;
    background: #ffeee5;
  }

  &.sub:hover {
    border: 1px solid rgba(18, 139, 237, 0.4);
    background: #e2f1fd;
  }
}

.content-wrapper {
  min-width: 140px;
  transform: translateZ(0);
  will-change: transform;

  .tag {
    display: inline-block;
    text-align: center;
    height: 20px;
    line-height: 20px;
    border-radius: 4px;
    padding: 0 4px;
    font-size: 12px;
    white-space: nowrap;
    vertical-align: bottom;

    &.main {
      color: #ff722d;
      background: #ffeee5;
    }

    &.sub {
      color: #128bed;
      background: #e2f1fd;
    }
  }

  .btn-link {
    padding: 0;
    height: 18px;
    line-height: 18px;
    margin-top: 8px;
    font-size: 12px;
  }
}

.infinity-list {
  max-height: 400px;
  min-height: 54px;
  overflow-y: auto;
  width: 600px;
  padding: 8px 10px;
}

.relation-item {
  border-radius: 4px;
  padding: 8px;
  background: #f7f7f7;

  & + & {
    margin-top: 8px;
  }

  .company-link {
    margin-left: @gap;
  }

  .line-wrapper {
    display: inline-block;
    position: relative;
    margin: 0 @gap;
    max-width: 100%;

    .role-text {
      display: inline-block;
      max-width: 100%;
      font-size: 12px;
      line-height: 18px;
      color: #666;
      padding: 0 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: center;
      transform: translateY(-2px);
      margin-top: -4px;
    }
  }

  .line {
    height: 1px;
    background-color: @color-arrow;
    position: absolute;
    transform: translateY(4px);
    top: 50%;
    left: 10px;
    right: 10px;

    .arrow-mixin() {
      position: absolute;
      top: 50%;
      height: 0;
      width: 0;
      border: 0 solid rgba(0, 0, 0, 0);
      border-width: 3px 10px;
    }

    &.left {
      right: 0;
    }

    &.right {
      left: 0;
    }

    &.left.right {
      left: 10px;
      right: 10px;
    }

    &.left::before {
      content: '';
      .arrow-mixin();

      left: 0;
      transform: translate(-100%, -50%);
      border-right-color: @color-arrow;
    }

    &.right::after {
      content: '';
      .arrow-mixin();

      right: 0;
      transform: translate(100%, -50%);
      border-left-color: @color-arrow;
    }
  }
}
