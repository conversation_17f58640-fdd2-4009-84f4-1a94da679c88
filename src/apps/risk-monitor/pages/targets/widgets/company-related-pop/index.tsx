import { <PERSON><PERSON>, Pop<PERSON>, Spin, Tooltip } from 'ant-design-vue';
import { computed, defineComponent, PropType, ref } from 'vue';

import QIcon from '@/components/global/q-icon';

import styles from './company-related-pop.module.less';
import { monitor } from '@/shared/services';
import { useRequest } from '@/shared/composables/use-request';
import { useInfinityList } from '../add-related-company-action/modal/use-infinity-list';

const CompanyRelatedPop = defineComponent({
  name: 'CompanyRelatedPop',
  props: {
    // 0-关联方 1-主体
    typeCode: {
      type: Number,
      required: true,
    },
    totalCount: {
      type: [String, Number],
      default: 0,
    },
    companyInfo: {
      type: Object as PropType<{ companyName: string; companyId: string }>,
      required: true,
    },
    monitorGroupId: {
      type: [String, Number],
      required: true,
    },
  },
  emits: ['openRelated'],
  setup(props, { emit }) {
    const visible = ref<boolean>(false);
    const companyType = computed(() => (props.typeCode === 1 ? 'main' : 'sub'));

    const showRelatedCompanyList = () => {
      visible.value = false;
      emit('openRelated', { disableAction: true });
    };

    const fetchData = async (data) => {
      const res = await monitor.getRelatedDetail({
        monitorGroupId: props.monitorGroupId,
        companyId: props.companyInfo.companyId,
        ...data,
      });
      return res;
    };

    const { execute, isLoading } = useRequest(fetchData);

    const { dataSource, removeScrollListener, initScrollListener } = useInfinityList((payload) => execute(payload), '.infinity-list ');

    return {
      companyType,
      showRelatedCompanyList,
      visible,
      isLoading,
      dataSource,
      initScrollListener,
      removeScrollListener,
    };
  },
  render() {
    const renderMainContent = () => {
      return (
        <div class={styles.contentWrapper} style="padding: 8px 10px;">
          <span class={[styles.tag, styles[this.companyType]]}>企业主体</span>
          <div>
            <Button type="link" class={styles.btnLink} onClick={this.showRelatedCompanyList}>
              查看关联方({this.totalCount})
              <QIcon type="icon-wenzilianjiantou" />
            </Button>
          </div>
        </div>
      );
    };

    const renderSubContent = () => {
      // 刚打开弹窗时的loading不展示，接口请求太快会闪烁
      if (!this.dataSource?.length && this.isLoading) return null;
      return (
        <div class={styles.contentWrapper}>
          <Spin spinning={this.isLoading} size="small">
            <ul class={[styles.infinityList, 'infinity-list']}>
              {this.dataSource.map((item) => {
                const relatedTypeDesc = item.relatedTypeDescList?.join('，');
                return (
                  <li key={item.id} class={styles.relationItem}>
                    <span class={[styles.tag, styles.sub]}>关联方</span>
                    <span class={styles.companyLink}>{this.companyInfo.companyName}</span>
                    <div class={styles.lineWrapper}>
                      <div class={styles.roleText}>{relatedTypeDesc}</div>
                      <div class={[styles.line, styles.right]}></div>
                    </div>
                    <span class={[styles.tag, styles.main]}>企业主体</span>
                    <span class={styles.companyLink}>{item.primaryCompanyName}</span>
                  </li>
                );
              })}
            </ul>
          </Spin>
        </div>
      );
    };

    const renderContent = () => {
      if (this.companyType === 'main') {
        return renderMainContent();
      }
      return renderSubContent();
    };

    return (
      <Popover
        vModel={this.visible}
        trigger="hover"
        placement="bottom"
        destroyTooltipOnHide
        arrowPointAtCenter={true}
        overlayClassName={styles.popWrapper}
        onVisibleChange={(val) => {
          if (this.companyType === 'sub') {
            if (val) {
              this.initScrollListener();
            } else {
              this.removeScrollListener();
            }
          }
        }}
      >
        <div class={[styles.iconWrapper, styles[this.companyType]]}>
          <QIcon type={`icon-role-${this.companyType}`} />
        </div>
        <div slot="content">{renderContent()}</div>
      </Popover>
    );
  },
});
export default CompanyRelatedPop;
