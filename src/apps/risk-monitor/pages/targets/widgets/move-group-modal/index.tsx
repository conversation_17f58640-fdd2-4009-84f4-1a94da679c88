import { Button, Input, Radio, message } from 'ant-design-vue';
import { computed, defineComponent, onMounted, ref } from 'vue';

import QRichTableEmpty from '@/components/global/q-rich-table/components/empty';
import QModal from '@/components/global/q-modal';
import { createPromiseDialog } from '@/components/promise-dialogs';

import styles from './move-group-modal.module.less';
import { getGroupList } from '../../../../../../shared/composables/use-group-search';

export const MoveGroupModal = defineComponent({
  name: 'MoveGroupModal',

  props: {
    params: {
      type: Object,
    },
  },
  setup(props, { emit }) {
    const visible = ref(false);
    const searchName = ref(undefined);
    const selectId = ref(props.params?.record.monitorGroupId);

    const { groupList, monitorGroups } = getGroupList(false);

    const groups = computed(() => {
      if (!searchName.value) {
        return groupList.value;
      }
      return groupList.value.filter((item) => item.name.includes(searchName.value)) || [];
    });

    const handleSubmit = () => {
      if (!selectId.value) {
        message.warning('请选择一个分组');
        return;
      }

      emit('resolve', selectId.value);
    };

    const handleCancel = () => {
      emit('resolve', undefined);
    };

    onMounted(async () => {
      await monitorGroups.execute<any>({ pageIndex: 1, pageSize: 100 });
      visible.value = true;
    });

    return {
      visible,
      searchName,
      groups,
      groupList,
      selectId,
      handleSubmit,
      handleCancel,
    };
  },
  render() {
    return (
      <QModal
        title={this.params?.title ?? '组别移动至'}
        visible={this.visible}
        onCancel={this.handleCancel}
        footer={undefined}
        viewportDistance={250}
      >
        <div class={styles.container}>
          <header class={styles.header}>
            <Input.Search allowClear v-model={this.searchName} placeholder="请输入组别名称" suffix={null} />
          </header>
          <div class={styles.groups}>
            {this.groups?.length ? (
              <Radio.Group v-model={this.selectId}>
                {this.groups.map((item) => (
                  <div class={styles.groupItem}>
                    <Radio value={item.monitorGroupId}>
                      {item.name} <span>({item.companyCount})</span>
                    </Radio>
                  </div>
                ))}
              </Radio.Group>
            ) : (
              <QRichTableEmpty size={'100px'} minHeight={'250px'}>
                <span class={styles.empty}>
                  <div>暂无分组，请新增企业分组</div>
                </span>
              </QRichTableEmpty>
            )}
          </div>
        </div>
        <div slot="footer">
          <Button onClick={this.handleCancel}>取消</Button>
          <Button type="primary" onClick={this.handleSubmit} disabled={!this.groupList.length}>
            确定
          </Button>
        </div>
      </QModal>
    );
  },
});

export const openMoveGroup = createPromiseDialog(MoveGroupModal);
