import { RelatedTypeMap, SEARCH_RESULT_TABLE_COLUMNS, RelatedColoumns } from '../config/search-config';

describe('search-config', () => {
  describe('RelatedTypeMap', () => {
    it('should return the correct value for a given key', () => {
      expect(RelatedTypeMap.PrincipalControl).to.equal('主要人员控制企业');
      expect(RelatedTypeMap.LegalRepresentativeControl).to.equal('法定代表人控制企业');
      expect(RelatedTypeMap.ActualControllerControl).to.equal('实际控制人控制企业');
      expect(RelatedTypeMap.BeneficiaryControl).to.equal('受益人控制企业');
      expect(RelatedTypeMap.Branch).to.equal('分支机构');
      expect(RelatedTypeMap.MotherCompanyMajorityShareholder).to.equal('母公司（股东信息(股比>50%)）');
      expect(RelatedTypeMap.MotherCompanyControl).to.equal('母公司控制企业');
      expect(RelatedTypeMap.MajorityInvestment).to.equal('子公司（对外投资(>50%的企业)）');
      expect(RelatedTypeMap.RelatedMember).to.equal('关联方成员包含以上所有');
    });

    it('should return the key joined by commas if the key is not found in the map', () => {
      expect(RelatedTypeMap['UnknownKey']).to.be.undefined;
      expect(RelatedTypeMap['UnknownKey1,UnknownKey2']).to.equal(undefined);
    });
  });

  describe('SEARCH_RESULT_TABLE_COLUMNS', () => {
    it('should have the correct number of columns', () => {
      expect(SEARCH_RESULT_TABLE_COLUMNS.length).to.equal(7);
    });
  });
});
