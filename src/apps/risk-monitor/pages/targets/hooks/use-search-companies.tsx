import { computed, ref, Ref, watch } from 'vue';
import { isEqual } from 'lodash';

import { useRequest } from '@/shared/composables/use-request';
import { monitor as monitorService } from '@/shared/services';

export const useSearchCompanies = (
  filterValues: Ref<{ filters?: Record<string, any>; keywords?: string }>,
  searchfn = monitorService.searchCompanies
) => {
  /** 搜索企业 */
  const searchCompanies = useRequest(searchfn);

  //  排序
  const sortInfo = ref<{ sortField?: string; isSortAsc?: boolean }>({
    sortField: undefined,
    isSortAsc: undefined,
  });

  /** 分页 */
  const pagination = computed(() => {
    return {
      pageSize: searchCompanies.data.value?.pageSize ?? 10,
      current: searchCompanies.data.value?.pageIndex ?? 1,
      total: searchCompanies.data.value?.total ?? 0,
    };
  });
  const search = async (payload = {}) => {
    const { filters, keywords } = filterValues.value;
    await searchCompanies.execute({
      ...filters,
      keywords,
      groupIds: filters?.groupIds ? [filters?.groupIds] : undefined,
      createDate: filters?.createDate ? [filters?.createDate] : undefined,
      pageIndex: pagination.value.current,
      pageSize: pagination.value.pageSize,
      ...sortInfo.value,
      ...payload,
    });
  };

  watch(
    () => filterValues.value,
    (newV, oldV) => {
      const payload = { needAggs: 0 };
      if (newV?.filters?.groupIds || !newV?.filters?.groupIds) {
        if (searchCompanies.data?.value?.pageIndex) {
          searchCompanies.data.value.pageIndex = 1;
        }
        if (newV?.filters?.groupIds !== oldV?.filters?.groupIds || isEqual(newV.filters, oldV.filters)) {
          payload.needAggs = 1;
        }
        search(payload);
      }
    },
    { deep: true }
  );

  return {
    data: searchCompanies.data,
    search,
    sortInfo,
    isLoading: searchCompanies.isLoading,
    pagination,
  };
};
