import { useRequest } from '@/shared/composables/use-request';
import { monitor } from '@/shared/services';
import { useInfinityList } from '@/apps/risk-monitor/pages/targets/widgets/add-related-company-action/modal/use-infinity-list';
import { computed, onMounted, ref } from 'vue';

export const useTrendCompanies = (companyOrigin, className, searchFn = monitor.getDynamicContentList) => {
  // 通过displayContent判断新增和减少都存在
  const isDoubleDimension = computed(() => {
    return companyOrigin.displayContent.length === 2;
  });
  // 固定的参数
  const prePayload = ref<{
    dimensionKey: string;
    strategyId?: number;
    companyId: string;
    diligenceId: number;
    batchId: number;
    preBatchId: number;
    monitorGroupId: number;
    esFilter: {
      filter: {
        companyName?: string;
      };
    };
  }>({
    dimensionKey: companyOrigin.displayContent[0].dimensionKey,
    strategyId: companyOrigin.displayContent[0].strategyId,
    companyId: companyOrigin.companyId,
    diligenceId: companyOrigin.hashData.diligenceId,
    batchId: companyOrigin.hashData.batchId,
    preBatchId: companyOrigin.hashData.preBatchId ?? 0,
    monitorGroupId: companyOrigin.monitorGroupId,
    esFilter: {
      filter: {
        companyName: undefined,
      },
    },
  });

  const { execute, isLoading } = useRequest(searchFn);

  const minuesData = ref([]);

  const getMinuesData = async () => {
    if (isDoubleDimension.value) {
      const res = await execute({
        ...prePayload.value,
        strategyId: companyOrigin.displayContent[1].strategyId,
      });
      minuesData.value = res?.data ?? [];
    }
  };

  // 减少的也需要调用接口，只是和后端确认，数据量少于100条
  const existCompanies = computed(() =>
    minuesData.value
      .map((item: any) => ({
        ...item,
        operate: 1,
      }))
      ?.filter((_comp) => {
        if (prePayload.value.esFilter.filter.companyName) {
          return _comp.companyNameRelated.includes(prePayload.value.esFilter.filter.companyName);
        }
        return _comp;
      })
  );
  const { dataSource, pagination, removeScrollListener, initScrollListener, resetList } = useInfinityList(
    (payload) => execute({ ...prePayload.value, ...payload }),
    className
  );

  const addCompanies = computed(() => {
    return dataSource?.value?.map((item) => ({
      ...(item?.dimensionContent ?? {}),
      ...item,
    }));
  });
  const changeData = computed(() => {
    const data = [addCompanies.value];
    if (existCompanies.value) {
      data[1] = existCompanies.value;
    }
    return data;
  });

  const totalData = computed(() => {
    return [...(existCompanies.value ?? []), ...addCompanies.value];
  });

  onMounted(async () => {
    await getMinuesData();
  });
  return {
    changeData,
    totalData,
    removeScrollListener,
    initScrollListener,
    resetList,
    isLoading,
    prePayload,
    pagination,
  };
};
