import { cloneDeep, sortBy } from 'lodash';
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

import { useRequest } from '@/shared/composables/use-request';
import { monitor as monitorService, user as userService } from '@/shared/services';
import { RISK_LEVEL } from '@/config/risk.config';
import { DEFAULT_DATE_RANGE } from '@/config/tender.config';

export const useSearchFilter = () => {
  const route = useRoute();

  // 获取分组数据和操作人数据
  const monitorOperators = useRequest(userService.getUserList);
  const monitorAggsSearch = useRequest(monitorService.searchCompanies);

  const isInit = ref(true);

  const operators = computed(() => {
    return monitorOperators.data.value?.map((item) => ({
      value: item.userId,
      label: item.name,
    }));
  });

  const monitorAggsResMap = ref<any>({
    creator: [],
    monitorGroup: [],
    riskLevel: [],
    relatedExist: [],
  });

  const groupList = computed(() => {
    const initialAggs = monitorAggsSearch.data?.value?.aggsRes ?? {};
    return sortBy(
      initialAggs.monitorGroup?.map((item) => {
        return {
          monitorGroupId: item.monitorGroupId,
          name: item?.name,
          count: +item.count,
          label: item?.name,
          value: item.monitorGroupId,
        };
      }) ?? [],
      (item) => {
        return item.name === '默认分组';
      }
    ).reverse();
  });
  const operatorList = ref<any[]>([]);
  const riskLevelList = ref<any[]>([]);
  const relatedCompanyList = ref<any[]>([]);
  const companyRoleList = ref<any[]>([]);

  const defaultFilterValues = Object.freeze({
    keywords: undefined,
    filters: {},
  });

  const isFilterLoading = ref(false);

  // 上次搜索的参数
  const previewFilterValues = ref<any>({});

  const defaultGroupId = computed(() => {
    return groupList.value.find((item) => item.label === '默认分组')?.monitorGroupId;
  });
  const getFilterOptions = (config?, showAll = false) => {
    if (showAll) {
      isFilterLoading.value = true;
      return;
    }
    operatorList.value =
      monitorAggsResMap.value?.creator
        ?.map((item) => {
          const hitGroup = monitorOperators.data.value?.find((op) => op.userId === item.creator);
          if (hitGroup) {
            return {
              value: item.creator,
              label: hitGroup?.name ?? '未知',
              // count: +item.count,
            };
          }
          return false;
        })
        .filter(Boolean) ?? [];
    riskLevelList.value =
      sortBy(
        monitorAggsResMap.value?.riskLevel?.map((item) => {
          const hitValue = RISK_LEVEL.find((level) => level.value === item.riskLevel);
          if (hitValue) {
            return {
              value: item.riskLevel,
              label: RISK_LEVEL.find((level) => level.value === item.riskLevel)?.label ?? '-',
              // count1: +item.count,
            };
          }
          return false;
        }),
        'value'
      ).filter(Boolean) ?? [];
    const relatedExist = monitorAggsResMap.value?.relatedExist?.[0] ?? {};
    relatedCompanyList.value = [
      { label: '已监控', value: 1, count1: +relatedExist.existCount },
      { label: '未监控', value: 2, count1: +relatedExist.noEnterPriesCount },
    ].filter((v) => v.count1 > 0);
    companyRoleList.value = [
      { label: '企业主体', value: 1, count1: Number(monitorAggsResMap.value?.primaryExist?.primaryObject ?? 0) },
      { label: '关联方', value: 2, count1: Number(monitorAggsResMap.value?.primaryExist?.relatedObject ?? 0) },
    ].filter((v) => v.count1 > 0);
  };

  /** 搜索过滤配置 */
  const filterGroups = computed(() => {
    return [
      {
        field: 'groupIds',
        type: 'button',
        label: '所属分组',
        options: groupList.value,
        meta: {
          maxLength: 15,
          defaultButton: false,
        },
      },
      {
        field: 'filters',
        label: '筛选条件',
        type: 'groups',
        children: [
          {
            field: 'primaryObjects',
            type: 'multiple',
            label: '企业角色',
            options: companyRoleList.value,
            meta: {
              showFooterButton: true,
            },
          },
          {
            field: 'riskLevels',
            type: 'multiple',
            label: '风险等级',
            options: riskLevelList.value,
            meta: {
              showFooterButton: true,
            },
          },
          {
            field: 'operatorIds',
            type: 'multiple',
            label: '操作人',
            options: operatorList.value,
            meta: {
              showFilter: true,
              showFooterButton: true,
            },
          },
          {
            field: 'createDate',
            type: 'single',
            label: '时间筛选',
            options: [{ value: undefined, label: '不限' }, ...DEFAULT_DATE_RANGE],
            custom: {
              type: 'date-range',
            },
          },
          {
            field: 'isRelatedCompanyExist',
            type: 'multiple',
            label: '关联方监控状态',
            options: relatedCompanyList.value,
            meta: {
              showFooterButton: true,
            },
          },
        ],
      },
    ];
  });

  /** groupId改变时，重置其他筛选项 */
  const resetOtherFilters = (remainedKeys: string[], currentFilters = {}) => {
    const filters: any = remainedKeys.reduce((acc, key) => {
      acc[key] = currentFilters[key];
      return acc;
    }, {});
    return {
      ...defaultFilterValues,
      filters,
    };
  };

  /** 搜索过滤值 */
  const filterValues = ref({
    ...defaultFilterValues,
  });

  /** 更新搜索过滤 */
  const handleFilterChange = (values) => {
    if (values.filters?.groupIds !== previewFilterValues.value.filters?.groupIds) {
      const groupIds = cloneDeep(values.filters?.groupIds);
      const resetOther = resetOtherFilters([], values);
      resetOther.filters.groupIds = groupIds;
      filterValues.value = {
        filters: { ...resetOther.filters },
        keywords: undefined,
      };
    } else {
      filterValues.value = values;
    }
    previewFilterValues.value = cloneDeep(filterValues.value);
  };

  /** 重置搜索过滤 */
  const handleFilterReset = () => {
    filterValues.value = { ...defaultFilterValues, filters: { groupIds: defaultGroupId.value } };
  };

  const initFilterValues = () => {
    const monitorGroupId = route.query.monitorGroupId || defaultGroupId.value;
    filterValues.value.filters = {
      ...filterValues.value.filters,
      groupIds: monitorGroupId ? +monitorGroupId : undefined,
    };
    previewFilterValues.value = cloneDeep(filterValues.value);
  };

  const getGroups = async () => {
    await monitorAggsSearch.execute<any>({ needAggs: 1 });
  };

  /** 初始化 */
  onMounted(async () => {
    try {
      await monitorOperators.execute<any>();
      await getGroups();
      initFilterValues();
      isInit.value = false;
      getFilterOptions();
    } catch (error) {
      console.error(error);
    }
  });

  return {
    filterGroups,
    groupList,
    filterValues,
    handleFilterChange,
    handleFilterReset,
    monitorAggsResMap,
    operators,
    isFilterLoading,
    getFilterOptions,
    getGroups,
    isInit,
  };
};
