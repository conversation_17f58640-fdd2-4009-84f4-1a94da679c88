import { mount } from '@vue/test-utils';

import { monitor as monitorService } from '@/shared/services';

import AddFollowUp from '../index';

vi.mock('@/shared/services', () => ({
  monitor: {
    getFollowUpList: vi.fn().mockResolvedValue({ data: [], total: 0 }),
    addFollowUp: vi.fn().mockResolvedValue({}),
  },
}));

vi.mock('@/store', () => ({
  useStore: () => ({
    state: { user: { personList: [] } },
    dispatch: vi.fn(),
  }),
}));

const baseParams = {
  record: {
    uniqueHashkey: 'abc',
    companyId: 'cid',
    companyName: '测试公司',
    metricsName: '风险指标',
    status: 0,
  },
  handleOk: vi.fn(),
};

describe('AddFollowUp', () => {
  beforeEach(() => {
    vi.spyOn(console, 'error').mockImplementation(() => ({}));
  });
  afterEach(() => {
    vi.resetAllMocks();
  });
  it('渲染快照', async () => {
    const wrapper = mount(AddFollowUp, {
      propsData: { params: baseParams },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.html()).toMatchSnapshot();
  });

  it('点击取消按钮关闭抽屉', async () => {
    const wrapper = mount(AddFollowUp, {
      propsData: { params: baseParams },
    });
    await wrapper.vm.$nextTick();
    await wrapper.findAllComponents({ name: 'AButton' }).at(0).trigger('click');
    expect(wrapper.vm.visible).toBe(false);
  });

  it('点击确定按钮调用 update', async () => {
    const wrapper = mount(AddFollowUp, {
      propsData: { params: baseParams },
    });
    await wrapper.vm.$nextTick();
    wrapper.vm.form.comment = '备注';
    await wrapper.findAllComponents({ name: 'AButton' }).at(1).trigger('click');
    expect(monitorService.addFollowUp).toHaveBeenCalled();
  });
});
