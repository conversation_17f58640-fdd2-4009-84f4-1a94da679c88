import { mount } from '@vue/test-utils';

import FollowUpList from '../index';

vi.mock('moment', () => ({
  default: () => ({ format: () => '2024-01-01 12:00:00' }),
}));

describe('FollowUpList', () => {
  const list = [
    { id: 1, createDate: '2024-01-01', updateBy: 1, grade: 1, way: 1, comment: '备注' },
    { id: 2, createDate: '2024-01-02', updateBy: 2, grade: 2, way: 2, comment: '' },
  ];
  const operatorList = [
    { userId: 1, name: '张三' },
    { userId: 2, name: '李四' },
  ];

  it('渲染快照', () => {
    const wrapper = mount(FollowUpList, {
      propsData: { list, status: 2, operatorList },
    });
    expect(wrapper).toMatchSnapshot();
  });

  it('展示操作人和内容', () => {
    const wrapper = mount(FollowUpList, {
      propsData: { list, status: 2, operatorList },
    });
    const items = wrapper.findAll('[data-testid="follow-up-list-item"]');
    expect(items.length).toBe(2);
    expect(wrapper.text()).toContain('张三');
    expect(wrapper.text()).toContain('李四');
    expect(wrapper.text()).toContain('备注');
  });
});
