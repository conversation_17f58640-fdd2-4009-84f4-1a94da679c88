 .historyItemWrapper {
    display: flex;
    gap: 10px;
    padding: 0 0 20px;
  
    & ~ .historyItemWrapper {
      .step {
        .line {
          top: -30px;
        }
      }
    }
  
    .step {
      position: relative;

      .dot {
        position: relative;
        z-index: 100;
        margin-top: 5px;
        width: 10px;
        height: 10px;
        border-radius: 99px;
        background-color: #1677fe;
      }
  
      .line {
        position: absolute;
        width: 1px;
        inset: 15px 0 0;
        margin: 0 auto;
        background-color: #eee;
      }
    }
  
    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 18px;
      background: #9ea6e5;
      color: #fff;
      flex-shrink: 0;
    }
  
    .content {
      flex: 1;
      overflow: hidden;
  
      .title {
        color: #333;
      }
  
      .time {
        color: #999;
        margin-left: 15px;
      }
    }
  }
  
  .file {
    a {
      color: #999;

      &:hover {
        color: #128bed;
      }
    }
  }
  
  .form{
    display: flex;
    margin-bottom: 5px;
    line-height: 22px;

    &:last-child{
      margin-bottom: 0;
    }

    .formLabel{
      width: 70px;
      color: #666;
    }

    .formItem{
      flex: 1;
    }
  }