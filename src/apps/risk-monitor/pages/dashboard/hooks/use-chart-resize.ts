import { onMounted, onUnmounted } from 'vue';
import { debounce } from 'lodash';

export const useChartResize = (chartRef) => {
  const _handleResize = () => {
    const chartInstance = chartRef.value?.chart;
    chartInstance?.resize();
  };
  const handleResize = debounce(_handleResize, 200);
  onMounted(() => {
    window.addEventListener('resize', handleResize);
  });
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
  });
};
