import { throttle } from 'lodash';

export const useLabelTooltip = (
  wrapperRef,
  options: { boundary: { left: number; bottom: number }; exclude: string[] }
) => {
  const CLASS_NAME = 'axis-tooltip';

  const createTooltip = (x, y, text): HTMLElement => {
    const div = document.createElement('div');
    div.style.position = 'absolute';
    div.style.top = `${y}px`;
    div.style.left = `${x}px`;
    div.style.backgroundColor = 'rgba(255, 255, 255)';
    div.style.color = '#333';
    div.style.padding = '4px 8px';
    div.style.borderRadius = '4px';
    div.style.boxShadow = '0px 2px 4px 0px rgba(0, 0, 0, 0.2)';
    div.style.fontSize = '12px';
    div.innerText = text;
    div.setAttribute('class', CLASS_NAME);
    return div;
  };

  const isInRange = (pointX, pointY, text) => {
    if (options.exclude.includes(text)) return false;
    const { left: limitX, bottom } = options.boundary;
    const limitY = wrapperRef.value.offsetHeight - bottom;
    const isYAxis = pointX < limitX && pointY < limitY;
    const isXAxis = pointX > limitX && pointY > limitY;
    return isYAxis || isXAxis;
  };

  const getEl = (): HTMLElement | null => {
    return document.querySelector(`.${CLASS_NAME}`);
  };

  const getCurrentTooltip = (): string | undefined => {
    const el: HTMLElement | null = getEl();
    return el?.innerText;
  };

  const removeTooltip = () => {
    const el: HTMLElement | null = getEl();
    if (el) wrapperRef.value.removeChild(el);
  };

  const _addTooltip = (x, y, text) => {
    if (!text || !isInRange(x, y, text)) {
      removeTooltip();
      return;
    }
    if (getCurrentTooltip() === text) return;
    removeTooltip();
    const div = createTooltip(x, y, text);
    wrapperRef.value.appendChild(div);
  };

  const addTooltip = throttle(_addTooltip, 150);

  return {
    addTooltip,
    removeTooltip,
  };
};
