import { shallowMount } from '@vue/test-utils';

import { monitor as monitorService } from '@/shared/services';

import RiskMonitorDashboardPage from '..';
import { flushPromises } from '@/test-utils/flush-promises';

const routerMock = {
  push: vi.fn(),
};
vi.mock('vue-router/composables', () => {
  return {
    useRouter: () => routerMock,
  };
});

vi.mock('@/shared/services');

vi.mock('../widgets/heatmap-chart', () => {
  return {
    default: '<div class="heatmap-chart" />',
  };
});

vi.mock('../widgets/pie-chart', () => {
  return {
    default: '<div class="pie-chart" />',
  };
});

describe('RiskMonitorDashboardPage', () => {
  beforeEach(() => {
    vi.mocked(monitorService.getMonitorOverviewData).mockResolvedValue({
      '1_todayDynamicCardTotal': { value: 7 },
      '1_todayDynamicCardByTypes': {
        buckets: [
          { key: 4, doc_count: 6 },
          { key: 3, doc_count: 1 },
        ],
      },
      '2_monitorCompanyCardTotal': 10,
      '2_monitorCompanyCardFavoriteGroup': {
        doc_count: 6,
        companyCount: {
          value: 1,
        },
      },
      '2_monitorCompanyCardAwaitingTracking': {
        doc_count: 0,
        companyCount: {
          value: 0,
        },
      },
      '3_distributionCard': {
        doc_count_error_upper_bound: 0,
        sum_other_doc_count: 0,
        buckets: [
          {
            key: '154304',
            doc_count: 1,
          },
          {
            key: '154799',
            doc_count: 1,
          },
        ],
      },
      '4_metricsId': {
        doc_count_error_upper_bound: 0,
        sum_other_doc_count: 0,
        buckets: [
          {
            key: '154304',
            doc_count: 1,
            metricsName: {
              doc_count_error_upper_bound: 0,
              sum_other_doc_count: 0,
              buckets: [
                {
                  key: '行政处罚-税务-资质状态变化',
                  doc_count: 1,
                },
              ],
            },
          },
          {
            key: '154799',
            doc_count: 1,
            metricsName: {
              doc_count_error_upper_bound: 0,
              sum_other_doc_count: 0,
              buckets: [
                {
                  key: '动产抵押',
                  doc_count: 1,
                },
              ],
            },
          },
          {
            key: '154833',
            doc_count: 1,
            metricsName: {
              doc_count_error_upper_bound: 0,
              sum_other_doc_count: 0,
              buckets: [
                {
                  key: '民事案件-新增开庭公告-非金融涉诉',
                  doc_count: 1,
                },
              ],
            },
          },
          {
            key: '154834',
            doc_count: 1,
            metricsName: {
              doc_count_error_upper_bound: 0,
              sum_other_doc_count: 0,
              buckets: [
                {
                  key: '民事案件-新增立案信息-非金融涉诉',
                  doc_count: 1,
                },
              ],
            },
          },
          {
            key: '154837',
            doc_count: 1,
            metricsName: {
              doc_count_error_upper_bound: 0,
              sum_other_doc_count: 0,
              buckets: [
                {
                  key: '民事案件-新增法院公告-非金融涉诉',
                  doc_count: 1,
                },
              ],
            },
          },
          {
            key: '154846',
            doc_count: 1,
            metricsName: {
              doc_count_error_upper_bound: 0,
              sum_other_doc_count: 0,
              buckets: [
                {
                  key: '民事案件-新增送达公告-非金融涉诉',
                  doc_count: 1,
                },
              ],
            },
          },
          {
            key: '154851',
            doc_count: 1,
            metricsName: {
              doc_count_error_upper_bound: 0,
              sum_other_doc_count: 0,
              buckets: [
                {
                  key: '民事案件-新增判决文书-非金融涉诉',
                  doc_count: 1,
                },
              ],
            },
          },
        ],
      },
    });
    vi.mocked(monitorService.getChartSummary).mockResolvedValue({
      metricEntities: [
        {
          category: 1,
          metricsId: 154799,
          name: '动产抵押',
        },
      ],
      metricsIds: ['154799'],
    });
    vi.mocked(monitorService.getHighRiskChart).mockResolvedValue({
      items: [
        {
          xAxis: '154799',
          yAxis: '154799',
          count: 1,
          isItself: true,
        },
      ],
    });
    vi.mocked(monitorService.getAllGroups).mockResolvedValue({
      data: [{ monitorGroupId: 1, name: 'Group 1' }],
      total: 1,
    } as any);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('renders correctly', async () => {
    const wrapper = shallowMount(RiskMonitorDashboardPage);
    await flushPromises();
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.isDataEmpty).toBeFalsy();
    expect(wrapper).toMatchSnapshot();
  });

  it('renders loading spinner when isLoading is true', async () => {
    const wrapper = shallowMount(RiskMonitorDashboardPage);

    wrapper.setData({
      isLoading: true,
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent({ name: 'ASpin' }).exists()).toBe(true);
  });

  it('renders EmptyBlock when isDataEmpty is true', async () => {
    vi.mocked(monitorService.getMonitorOverviewData).mockResolvedValue({
      '2_monitorCompanyCardTotal': 0,
    });
    const wrapper = shallowMount(RiskMonitorDashboardPage);

    await flushPromises();
    expect(wrapper.vm.isDataEmpty).toBeTruthy();
    expect(wrapper.findComponent({ name: 'EmptyBlock' }).exists()).toBe(true);
  });

  it('redirects to targets page when handleCardClick is called when type is company', async () => {
    const wrapper = shallowMount(RiskMonitorDashboardPage);

    await flushPromises();
    expect(wrapper.vm.isDataEmpty).toBeFalsy();
    const cardWrapper = wrapper.findAllComponents({ name: 'RiskTrends' });
    cardWrapper.at(0).vm.$emit('goDetail', 'company');
    expect(routerMock.push).toHaveBeenCalledWith({
      path: '/risk-monitor/targets',
    });
  });

  const dateFilter = { currently: true, flag: 1, number: 1, unit: 'day' };
  const cardClickTestCases: [string, Record<string, any>][] = [
    ['focus', { groupId: 1 }],
    ['follow', { dataStatus: 2 }],
    ['monitor', { metricTypes: 3, createDate: JSON.stringify(dateFilter) }],
    ['business', { metricTypes: 4, createDate: JSON.stringify(dateFilter) }],
    ['trends', { createDate: JSON.stringify(dateFilter) }],
  ];

  it.each(cardClickTestCases)('redirects to trends page with correct query when type is %s', async (key, query) => {
    const wrapper = shallowMount(RiskMonitorDashboardPage);

    await flushPromises();
    expect(wrapper.vm.isDataEmpty).toBeFalsy();
    const cardWrapper = wrapper.findAllComponents({ name: 'RiskTrends' });
    cardWrapper.at(0).vm.$emit('goDetail', key);
    expect(routerMock.push).toHaveBeenCalledWith({
      path: '/risk-monitor/trends',
      query,
    });
  });

  it('redirects to trends page when click pie', async () => {
    const wrapper = shallowMount(RiskMonitorDashboardPage);

    await flushPromises();
    expect(wrapper.vm.isDataEmpty).toBeFalsy();
    expect(wrapper.vm.pieData.length).toBeGreaterThan(0);
    wrapper.vm.handlePieClick({ metricId: 154799 });
    expect(routerMock.push).toHaveBeenCalledWith({
      path: '/risk-monitor/trends',
      query: {
        createDate: JSON.stringify(dateFilter),
        groupId: '1',
        metricsIds: 154799,
      },
    });
  });
});
