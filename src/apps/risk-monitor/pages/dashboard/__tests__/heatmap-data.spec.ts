import { xAxisData, yAxisData, heatmapData } from '../config/heatmap-data';

describe('Heatmap Data Configuration', () => {
  it('should have xAxisData with 24 elements', () => {
    expect(xAxisData.length).toBe(24);
  });

  it('should have yAxisData that is a copy of xAxisData', () => {
    expect(yAxisData).toEqual(xAxisData);
  });

  it('should have heatmapData with 24 * 24 elements', () => {
    expect(heatmapData.length).toBe(24 * 24);
  });

  it('should have each element in heatmapData as an array with 3 elements', () => {
    heatmapData.forEach((item) => {
      expect(item.length).toBe(3);
    });
  });

  it('should have each element in heatmapData with x and y within the range of 0 to 23', () => {
    heatmapData.forEach(([x, y]) => {
      expect(x).toBeGreaterThanOrEqual(0);
      expect(x).toBeLessThanOrEqual(23);
      expect(y).toBeGreaterThanOrEqual(0);
      expect(y).toBeLessThanOrEqual(23);
    });
  });

  it('should have each element in heatmapData with value between 0 and 150', () => {
    heatmapData.forEach(([, , value]) => {
      expect(value).toBeGreaterThanOrEqual(0);
      expect(value).toBeLessThanOrEqual(150);
    });
  });

  it('should have unique heatmapData entries for each combination of x and y', () => {
    const uniqueEntries = new Set(heatmapData.map(([x, y]) => `${x},${y}`));
    expect(uniqueEntries.size).toBe(24 * 24);
  });
});
