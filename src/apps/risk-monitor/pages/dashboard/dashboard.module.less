@import '@/styles/token.less';

// 设计图只有这个页面是边距20px
.container {
  padding: 0 10px
}

.bg {
  &::after {
    content: "";
    position: absolute;
    inset: 0;
    background: url("./assets/bg-risk.png") no-repeat top right;
    background-size: 1920px 748px;
    z-index: -1;
  }
}

.title {
  font-size: 24px;
  font-weight: 500;
  line-height: 32px;
  color: #333;
  //border-bottom: 1px solid #E3E3E3;
  padding: 10px 0;
  margin: 0;
  // position: sticky;
  // top: 0;
  // z-index: 10;

  &:global(.sticky) {
    border: none;

    &::after {
      content: "";
      background-color: #FFF;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
      position: fixed;
      left: 0;
      right: 0;
      top: @header-height;
      width: 100%;
      height: 72px;
      z-index: -1;
    }
  }
}

.card-container {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr;
  gap: 15px;
  margin-bottom: 15px;
}

.pie-container {
  height: 100%;
  border-radius: 4px;
  background-color: #FFF;
}

.top10 {
  font-size: 12px;
  line-height: 18px;
  color: #666;
  margin-left: 4px;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 52px - 20px);
  background: #fff;
  border-radius: 2px;
}
