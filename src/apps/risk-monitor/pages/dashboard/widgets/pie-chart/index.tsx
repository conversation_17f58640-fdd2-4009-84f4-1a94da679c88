import { computed, defineComponent, PropType, ref } from 'vue';
import { use } from 'echarts/core';
import { Canvas<PERSON>enderer } from 'echarts/renderers';
import { Pie<PERSON><PERSON> as VPieChart } from 'echarts/charts';
import { TitleComponent, TooltipComponent } from 'echarts/components';
import VChart from 'vue-echarts';

import styles from './pie-chart.module.less';
import { COMMON_CHART_COLOR_THEME } from '../../config/chart.theme';
import { useChartResize } from '@/apps/risk-monitor/pages/dashboard/hooks/use-chart-resize';

use([<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ie<PERSON>hart, TitleComponent, TooltipComponent]);

const getChartOption = (data: any[]) => {
  const chartOption = {
    /** Tooltip */
    tooltip: {
      show: true,
      trigger: 'item',
      triggerOn: 'mousemove',
      borderWidth: 0,
      extraCssText: 'box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2); padding: 8px 12px;',
      formatter: (params: any) => {
        const containerCss = `display: flex; flex-direction: column; gap: 8px; color: #333; font-size: 14px; line-height: 22px;`;
        const flex = `display: flex; align-items: center;gap: 4px;`;
        const dotCss = `width: 8px; height: 8px; border-radius: 50%; background-color: ${params.color};`;
        return `
          <div style="${containerCss}">
            <div style="${flex}"><div style="${dotCss}"></div>${params.name}</div>
            <div style="${flex} padding-left: 12px;">
              风险动态：
              <span style="color: #808080;">${params.value}</span>
            </div>
          </div>
        `;
      },
    },

    /** Theme */
    color: COMMON_CHART_COLOR_THEME,

    series: [
      {
        type: 'pie',
        data,
        top: '0%',
        left: '10%',
        bottom: '0%',
        right: '10%',
        radius: ['43%', '65%'],
        roam: false, // 是否开启鼠标缩放和平移漫游
        animation: true,
        animationEasing: 'cubicOut',
        animationDuration: 150,
        animationDurationUpdate: 250,
        // 标签
        label: {
          show: true,
          position: 'outside',
          // formatter: '{b} ({c})',
          distanceToLabelLine: 5,
          fontSize: 12,
          color: '#333',
          overflow: 'none',
          formatter: ({ name, value }) => {
            const maxLength = 8;
            if (name.length > maxLength) {
              return `${name.substring(0, maxLength)}...(${value})`;
            }
            return `${name} (${value})`;
          },
        },
        // 标签引导线
        labelLine: {
          show: true,
          length: 30,
          length2: 15,
          lineStyle: {
            color: '#dcdee3',
          },
        },
      },
    ],
  };
  return Object.freeze(chartOption);
};

const PieChart = defineComponent({
  name: 'PieChart',
  props: {
    data: {
      type: Array as PropType<any[]>,
      required: true,
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
  },
  setup(props) {
    const chartRef = ref<any>(null);
    const chartOptions = computed(() => {
      return getChartOption(props.data);
    });

    useChartResize(chartRef);

    return {
      chartOptions,
      chartRef,
    };
  },
  render() {
    const { width, height, chartOptions } = this;
    return (
      <div
        class={styles.container}
        style={{
          width,
          height,
        }}
      >
        <VChart
          ref="chartRef"
          option={chartOptions}
          on={{
            click: (record) => {
              // data是option里的数据，record是图的数据
              this.$emit('click', record.data);
            },
          }}
        />
      </div>
    );
  },
});

export default PieChart;
