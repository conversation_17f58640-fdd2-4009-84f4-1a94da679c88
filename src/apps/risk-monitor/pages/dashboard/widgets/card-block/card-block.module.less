.card {
  border-radius: 8px;
  opacity: 1;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(60px);
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
  padding: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 12px;

  &:hover {
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .title {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #333;
  }

  .body {
    flex: 1;
  }
}

//.default-header {
//  cursor: pointer;
//}
.default-header:hover svg {
  color: #128bed;
}

.overview {
  padding-top: 24px;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .count {
    font-family: D-Din, sans-serif;
    font-size: 28px;
    font-weight: bold;
    color: #333;
  }

  svg {
    color: #999;
  }
}

.list {
  margin-top: 30px;

  .item {
    border-radius: 4px;
    background: #FFF;
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 8px;
    //cursor: pointer;
  }

  .arrow {
    display: none;
  }

  .item:hover .arrow {
    display: block;
  }

  .item:not(:last-child) {
    margin-bottom: 16px;
  }

  .title {
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    color: #999;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .info {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .count {
    font-family: D-Din, sans-serif;
    font-size: 20px;
    font-weight: bold;
    color: #666;
  }

  svg {
    color: #128bed;
  }
}

.risk-icon {
  width: 32px;
  height: 32px;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    right: 50%;
    top: 50%;
    width: 110px;
    height: 110px;
    transform: translate(50%, -50%);
    border-radius: 50%;
    opacity: 0.1;
    filter: blur(50px);
  }

  &.blue::after {
    background: #128BED;
  }

  &.orange::after {
    background: #FF722D;
  }
}