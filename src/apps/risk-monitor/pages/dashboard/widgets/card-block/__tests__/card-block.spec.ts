import { mount } from '@vue/test-utils';

import CardBlock, { RiskTrends } from '../index';

vi.mock('@/shared/composables/use-permission', () => ({
  hasPermission: () => true,
}));

describe('CardBlock', () => {
  it('渲染快照', () => {
    const wrapper = mount(CardBlock, {
      propsData: { title: '标题' },
      slots: { default: '<div>内容</div>' },
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});

describe('RiskTrends', () => {
  const dataSource = { monitor: 1, business: 2, focus: 3, follow: 4, total: 10 };
  it('渲染快照', () => {
    const wrapper = mount(RiskTrends, {
      propsData: { title: '风险趋势', dataSource },
    });
    expect(wrapper.html()).toMatchSnapshot();
  });

  it('点击列表项触发 goDetail', async () => {
    const wrapper = mount(RiskTrends, {
      propsData: { title: '风险趋势', dataSource },
    });
    await wrapper.findAll('li').at(0).trigger('click');
    expect(wrapper.emitted('goDetail')).toBeTruthy();
  });
});
