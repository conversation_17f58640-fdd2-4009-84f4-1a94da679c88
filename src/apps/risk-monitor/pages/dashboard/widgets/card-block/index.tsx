import { computed, defineComponent, PropType } from 'vue';

import QIcon from '@/components/global/q-icon';
import { hasPermission } from '@/shared/composables/use-permission';
import { Permission } from '@/config/permissions.config';

import IconCompany from './assets/risk-company.svg';
import IconWarning from './assets/risk-warning.svg';
import styles from './card-block.module.less';

const CardBlock = defineComponent({
  name: 'CardBlock',
  props: {
    title: {
      type: String,
      default: '',
    },
  },
  render() {
    return (
      <div class={styles.card}>
        {this.title || this.$slots.title || this.$slots.extra ? (
          <div class={styles.header}>
            <div class={styles.title}>{this.$slots.title || this.title}</div>
            <div class={styles.extra}>{this.$slots.extra}</div>
          </div>
        ) : null}
        <div class={styles.body}>{this.$slots.default}</div>
      </div>
    );
  },
});

const RiskTrends = defineComponent({
  name: 'RiskTrends',
  props: {
    title: {
      type: String,
      default: '',
    },
    dataSource: {
      type: Object as PropType<{
        monitor?: number;
        business?: number;
        focus?: number;
        focusGroupName?: string;
        follow?: number;
        total: number;
      }>,
      default: () => ({ monitor: 0, business: 0, focus: 0, follow: 0, total: 0 }),
    },
    type: {
      type: String as PropType<'trends' | 'company'>,
      default: 'trends',
    },
    permissions: {
      type: Array as PropType<Permission[]>,
      default: () => [],
    },
  },
  emit: ['goDetail'],
  setup(props, { emit }) {
    const isCompanyRisk = computed(() => props.type === 'company');
    const typeList = computed(() => {
      if (isCompanyRisk.value) {
        return [
          {
            label: '跟进中企业',
            key: 'follow',
            value: props.dataSource.follow,
            permission: [Permission.RISK_TRENDS_VIEW],
          },
          {
            label: `重点关注分组${props.dataSource.focusGroupName ? `：${props.dataSource.focusGroupName}` : ''}`,
            key: 'focus',
            value: props.dataSource.focus,
            permission: [Permission.RISK_TRENDS_VIEW],
          },
        ];
      }
      return [
        {
          label: '监管类动态',
          key: 'monitor',
          value: props.dataSource.monitor,
          permission: [Permission.RISK_TRENDS_VIEW],
        },
        {
          label: '业务类动态',
          key: 'business',
          value: props.dataSource.business,
          permission: [Permission.RISK_TRENDS_VIEW],
        },
      ];
    });

    const goDetail = (type: string, item = {} as any) => {
      if (item.permission && !hasPermission(item.permission)) {
        return;
      }
      emit('goDetail', type);
    };
    return {
      isCompanyRisk,
      typeList,
      goDetail,
    };
  },
  render() {
    return (
      <CardBlock>
        <div
          class={styles.defaultHeader}
          style={{ cursor: hasPermission(this.permissions) ? 'pointer' : 'normal' }}
          onClick={() => this.goDetail(this.type, { permission: this.permissions })}
        >
          <div class={styles.header}>
            <div class={styles.title}>{this.title}</div>
            <div class={[styles.riskIcon, this.isCompanyRisk ? styles.orange : styles.blue]}>
              <img src={this.isCompanyRisk ? IconCompany : IconWarning} />
            </div>
          </div>
          <div class={styles.overview}>
            <span class={styles.count}>{this.dataSource.total}</span>
            {hasPermission(this.permissions) ? <QIcon type="icon-arrow-right" /> : null}
          </div>
        </div>

        <ul class={styles.list}>
          {this.typeList.map((item) => (
            <li
              key={item.key}
              class={styles.item}
              style={{ cursor: hasPermission(item.permission) ? 'pointer' : 'normal' }}
              onClick={() => this.goDetail(item.key, item)}
            >
              <span class={styles.title}>{item.label}</span>
              <div class={styles.info}>
                <span class={styles.count}>{item.value}</span>
                {hasPermission(item.permission) ? <QIcon class={styles.arrow} type="icon-arrow-right" /> : null}
              </div>
            </li>
          ))}
        </ul>
      </CardBlock>
    );
  },
});

export default CardBlock;
export { RiskTrends };
