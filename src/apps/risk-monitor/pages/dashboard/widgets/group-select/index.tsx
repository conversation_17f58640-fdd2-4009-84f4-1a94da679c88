import { defineComponent, PropType } from 'vue';

import FilterSelect from '@/components/filter-select';

const GroupSelect = defineComponent({
  name: 'GroupSelect',
  props: {
    defaultValue: {
      type: Array as PropType<number[]>,
      required: false,
    },
    options: {
      type: Array as PropType<{ label: string; value: number }[]>,
      default: () => [],
    },
  },
  setup(props, { emit }) {
    const handleChange = (values) => {
      emit('change', values);
    };

    return {
      handleChange,
    };
  },
  render() {
    return (
      <div>
        <span style="color: #999;">当前组别：</span>
        <FilterSelect
          defaultValue={this.defaultValue}
          style="width: 150px"
          options={this.options}
          onChange={this.handleChange}
        ></FilterSelect>
      </div>
    );
  },
});

export default GroupSelect;
