import { shallowMount } from '@vue/test-utils';

import GroupSelect from '../index';

describe('GroupSelect', () => {
  const options = [
    { label: 'A', value: 1 },
    { label: 'B', value: 2 },
  ];
  it('渲染快照', () => {
    const wrapper = shallowMount(GroupSelect, {
      propsData: { options, defaultValue: [1] },
    });
    expect(wrapper.html()).toMatchSnapshot();
  });

  it('触发 change 事件', async () => {
    const wrapper = shallowMount(GroupSelect, {
      propsData: { options },
    });
    const selectWrapper = wrapper.findComponent({ name: 'FilterSelect' });
    selectWrapper.vm.$emit('change', [1, 2]);
    expect(wrapper.emitted('change')).toBeTruthy();
    expect(wrapper.emitted('change')?.[0][0]).toEqual([1, 2]);
  });
});
