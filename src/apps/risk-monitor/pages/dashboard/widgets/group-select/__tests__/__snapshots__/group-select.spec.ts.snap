// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`GroupSelect > 渲染快照 1`] = `
<div><span style="color: #999;">当前组别：</span>
  <select-stub defaultactivefirstoption="true" prefixcls="ant-select" transitionname="slide-up" optionlabelprop="children" optionfilterprop="value" choicetransitionname="zoom" placeholder="请选择" dropdownstyle="[object Object]" tokenseparators="" showaction="click" clearicon="[object Object]" inputicon="[object Object]" removeicon="[object Object]" menuitemselectedicon="[object Object]" dropdownrender="[Function]" dropdownmatchselectwidth="true" dropdownmenustyle="[object Object]" notfoundcontent="[object Object]" tabindex="0" autoclearsearchvalue="true" __propssymbol__="Symbol()" children="" class="defaultClass" style="min-width: 160px; width: 150px;"></select-stub>
</div>
`;
