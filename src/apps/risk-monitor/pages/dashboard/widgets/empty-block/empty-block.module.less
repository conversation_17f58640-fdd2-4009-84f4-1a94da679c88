.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(60px);
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
  height: calc(100vh - 164px);
}

.title {
  font-size: 18px;
  font-weight: 500;
  line-height: 26px;
  color: #333;
  margin: 4px 0;
}

.text {
  font-size: 14px;
  line-height: 22px;
  color: #999;
  margin-bottom: 16px;
}