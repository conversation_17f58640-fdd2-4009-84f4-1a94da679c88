import { defineComponent } from 'vue';
import { Button } from 'ant-design-vue';
import { useRouter } from 'vue-router/composables';

import Empty from '@/shared/components/empty';

import styles from './empty-block.module.less';

const EmptyBlock = defineComponent({
  name: 'EmptyBlock',
  setup() {
    const router = useRouter();
    const handleAddCompany = () => {
      router.push('/risk-monitor/targets');
    };
    return {
      handleAddCompany,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <Empty type="text">
          <p class={styles.title}>您还没有监控的企业</p>
          <p class={styles.text}>为您监控企业每日的动态</p>
          <Button icon="plus-circle" type="primary" onClick={this.handleAddCompany}>
            添加企业
          </Button>
        </Empty>
      </div>
    );
  },
});

export default EmptyBlock;
