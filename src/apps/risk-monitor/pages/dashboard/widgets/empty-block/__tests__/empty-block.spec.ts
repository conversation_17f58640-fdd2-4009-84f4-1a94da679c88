import { mount } from '@vue/test-utils';
import { useRouter } from 'vue-router/composables';

import EmptyBlock from '..';

vi.mock('vue-router/composables', () => ({
  useRouter: vi.fn(),
}));

describe('EmptyBlock', () => {
  it('渲染快照', () => {
    const wrapper = mount(EmptyBlock);
    expect(wrapper.html()).toMatchSnapshot();
  });

  it('点击按钮跳转', async () => {
    const mockPush = vi.fn();
    vi.mocked<any>(useRouter).mockReturnValue({ push: mockPush });
    const wrapper = mount(EmptyBlock);
    await wrapper.findComponent({ name: 'AButton' }).trigger('click');
    expect(mockPush).toHaveBeenCalledWith('/risk-monitor/targets');
  });
});
