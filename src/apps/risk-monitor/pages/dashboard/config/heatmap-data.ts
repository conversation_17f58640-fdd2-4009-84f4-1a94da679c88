import { random, range } from 'lodash';

export const xAxisData = [
  '经营状态变更',
  '破产重整',
  '失信被执行人',
  '限制高消费',
  '限制出境',
  '被执行人',
  '公安通告',
  '严重违法',
  '经营状态变更',
  '破产重整',
  '失信被执行人',
  '限制高消费',
  '限制出境',
  '被执行人',
  '公安通告',
  '严重违法',
  '经营状态变更',
  '破产重整',
  '失信被执行人',
  '限制高消费',
  '限制出境',
  '被执行人',
  '公安通告',
  '严重违法',
];

export const yAxisData = xAxisData.slice();

export const heatmapData = range(xAxisData.length).flatMap((x) => {
  return range(yAxisData.length).map((y) => {
    const value = random(0, 150);
    return [x, y, value];
  });
});
