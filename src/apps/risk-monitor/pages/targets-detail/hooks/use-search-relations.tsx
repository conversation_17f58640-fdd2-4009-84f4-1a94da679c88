import { computed, ref, Ref, watch } from 'vue';

import { useRequest } from '@/shared/composables/use-request';
import { monitor as monitorService } from '@/shared/services';

export const useSearchCompanies = (
  filterValues: Ref<{ groupId?: string; companyId?: string }>,
  searchfn = monitorService.getCompanyDetail
) => {
  /** 搜索企业 */
  const searchCompanies = useRequest(searchfn);

  //  排序
  const sortInfo = ref<{ sortField?: string; isSortAsc?: boolean }>({
    sortField: undefined,
    isSortAsc: undefined,
  });

  /** 分页 */
  const pagination = computed(() => {
    return {
      pageSize: searchCompanies.data.value?.pageSize ?? 10,
      current: searchCompanies.data.value?.pageIndex ?? 1,
      total: searchCompanies.data.value?.total ?? 0,
    };
  });

  const search = async (payload = {}) => {
    await searchCompanies.execute({
      ...filterValues.value,
      pageIndex: pagination.value.current,
      pageSize: pagination.value.pageSize,
      ...sortInfo.value,
      ...payload,
    });
  };

  watch(
    () => filterValues.value,
    (values) => {
      if (values.groupId && values.companyId) {
        if (searchCompanies.data?.value?.pageIndex) {
          searchCompanies.data.value.pageIndex = 1;
        }
        search();
      }
    },
    { immediate: true, deep: true }
  );

  return {
    data: searchCompanies.data,
    search,
    sortInfo,
    isLoading: searchCompanies.isLoading,
    pagination,
  };
};
