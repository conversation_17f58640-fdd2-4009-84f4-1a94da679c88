import { computed, defineComponent, onMounted, ref } from 'vue';
import { Breadcrumb } from 'ant-design-vue';
import { useRoute } from 'vue-router/composables';
import { sortBy } from 'lodash';

import Card from '@/shared/components/card';
import HeroicLayout from '@/shared/layouts/heroic';
import QCard from '@/components/global/q-card';
import { monitor } from '@/shared/services';
import { useRequest } from '@/shared/composables/use-request';
import FilterSelect from '@/components/filter-select';
import { useMonitorAggsSearch } from '@/shared/composables/use-monitor-aggs-search';
import { convertSortStructure } from '@/utils/data-type/convert-sort-structure';
import { RISK_LEVEL_MAP } from '@/config/risk.config';
import { openRelatinCourtModal } from '@/shared/components/related-court-drawer';
import SwitchButton from '@/shared/components/switch-button';
import { useViewMode } from '@/apps/risk-monitor/pages/targets-detail/hooks/use-view-mode';
import SearchCount from '@/components/search-count';

import BasicInfo from './widgets/basic-info';
import TabController from './widgets/tab-contoller';
import { getTableColumns } from './config/setting-config';
import SearchResult from './widgets/search-result';
import ChartView from './widgets/chart-view';
import styles from './targets-detail.module.less';

// tab切换需要重置的状态
const RESETSTATUS = {
  pageIndex: 1,
  pageSize: 10,
  sortField: undefined,
  isSortAsc: undefined,
  metricsIds: undefined,
  riskLevels: undefined,
};

const VIEW_MODE_OPTIONS = [
  {
    value: 'chart',
    label: '图谱',
    icon: 'icon-tupu',
  },
  {
    value: 'list',
    label: '表格',
    icon: 'icon-biaoge',
  },
];

const TargetsDetailPage = defineComponent({
  name: 'TargetsDetailPage',
  props: {
    id: {
      type: String,
      required: true,
    },
  },
  setup() {
    const route = useRoute();
    // 当前监控公司的id
    const companyId = ref(route.params.id);
    const monitorGroups = useRequest(monitor.getAllGroups);

    // 监控公司详情
    const { data: companyDetail, isLoading, execute: getCompanyData } = useRequest(monitor.getCompanyDetail);

    const currentGroupId = computed(() => Number(route.query.groupId));

    //  监控动态搜索条件
    const filters = ref({
      aggsField: [4],
      companyIds: [companyId.value],
      groupId: [currentGroupId.value],
      pageIndex: 1,
      metricsIds: undefined,
      riskLevels: undefined,
      pageSize: 10,
      sortField: undefined,
      isSortAsc: undefined,
      includeRelatedParty: 0,
    });

    // 查询列表信息
    const { dataSource, isLoading: tableLoading } = useMonitorAggsSearch(filters, monitor.searchDynamics);

    // 分组列表
    const companyGroupList = computed(() => {
      return (monitorGroups?.data?.value?.data || [])
        .map((item) => {
          return {
            label: item.name,
            value: item.monitorGroupId,
          };
        })
        .filter(Boolean);
    });

    // 风险等级列表
    const riskLevelList = computed(() => {
      const res = (dataSource.value?.aggsResponse?.['4_riskLevel']?.buckets || [])
        .map((item) => {
          return {
            label: RISK_LEVEL_MAP[item.key],
            value: +item.key,
            count: item.doc_count,
          };
        })
        .filter(Boolean);
      return sortBy(res, (o) => -o.value);
    });

    // 指标列表
    const metricNameList = computed(() => {
      return (dataSource.value?.aggsResponse?.['4_metricsId']?.buckets || [])
        .map((item) => {
          return {
            label: item.metricsName.buckets[0].key,
            value: +item.key,
            count: item.doc_count,
          };
        })
        .filter(Boolean);
    });
    // 是不是自身动态
    const isSelfTab = ref(true);

    const columns = computed(() => getTableColumns({ isSelf: isSelfTab.value }));

    const pagination = computed(() => ({
      total: dataSource.value?.total || 0,
      current: filters.value.pageIndex,
      pageSize: filters.value.pageSize,
    }));

    const dataSources = computed(() => {
      return dataSource.value?.data || [];
    });
    const init = ref(false);

    const { viewMode, setViewMode } = useViewMode('chart');

    onMounted(async () => {
      init.value = true;
      await monitorGroups.execute({ pageIndex: 1, pageSize: 100, companyIds: [companyId.value] });
      await getCompanyData(companyId.value);
      init.value = false;
    });

    const handleFilterSelectChange = (values) => {
      filters.value = {
        ...filters.value,
        ...RESETSTATUS,
        ...values,
      };
    };

    return {
      init,
      companyId,
      isLoading,
      isSelfTab,
      filters,
      companyDetail,
      columns,
      dataSources,
      pagination,
      companyGroupList,
      metricNameList,
      riskLevelList,
      tableLoading,
      viewMode,
      setViewMode,
      currentGroupId,
      handleFilterSelectChange,
    };
  },
  render() {
    return (
      <div>
        <div class="sticky-breadcrumb">
          <Breadcrumb>
            <Breadcrumb.Item>
              <router-link to={`/risk-monitor/${this.$route.query.from}?useCacheQuery=true`}>
                <q-icon type="icon-mianbaoxiefanhui"></q-icon>
                {this.$route.query.name}
              </router-link>
            </Breadcrumb.Item>
            <Breadcrumb.Item class="lastItem">{this.$route.meta?.title}</Breadcrumb.Item>
          </Breadcrumb>
        </div>

        <HeroicLayout
          loading={this.init}
          innerStyle={{
            minHeight: 'calc(100vh - 52px - 50px - 10px)',
          }}
        >
          <QCard
            slot="hero"
            bodyStyle={{
              padding: '16px',
            }}
          >
            {!this.isLoading && (
              <BasicInfo company={this.companyDetail || {}}>
                <div class="flex items-center justify-between" slot="extra">
                  <span>当前组别：</span>
                  <FilterSelect
                    defaultValue={this.filters.groupId}
                    style="width: 150px"
                    options={this.companyGroupList}
                    onChange={(values) =>
                      this.handleFilterSelectChange({
                        groupId: values,
                      })
                    }
                  ></FilterSelect>
                </div>
              </BasicInfo>
            )}
          </QCard>
          <Card bodyStyle={{ paddingTop: '0' }}>
            <TabController
              onTabChange={(key) => {
                this.isSelfTab = key === 'self';
                this.filters = {
                  ...this.filters,
                  ...RESETSTATUS,
                  includeRelatedParty: this.isSelfTab ? 0 : 2,
                };
              }}
            ></TabController>
            <div>
              <div class={styles.header}>
                <span class={styles.subTitle}>
                  {this.viewMode === 'chart' && !this.isSelfTab ? (
                    '最近24小时内动态数据'
                  ) : this.pagination.total ? (
                    <SearchCount class={styles.searchCount} total={this.pagination.total} loading={this.isLoading} />
                  ) : null}
                </span>
                <SwitchButton v-show={!this.isSelfTab} value={this.viewMode} options={VIEW_MODE_OPTIONS} onChange={this.setViewMode} />
              </div>
              {/* 自身动态 & 关联方动态 */}
              <SearchResult
                v-show={this.isSelfTab || this.viewMode === 'list'}
                // showSearchCount={this.viewMode === 'list'}
                key={this.isSelfTab ? 0 : 2}
                // emptyText={this.isSelfTab ? undefined : '最近24小时内暂无关联方动态数据'}
                columns={this.columns}
                dataSource={this.dataSources}
                isLoading={this.tableLoading}
                metricNameList={this.metricNameList}
                isRelatedTrends={false}
                riskLevelList={this.riskLevelList}
                pagination={this.pagination}
                emptyMinHeight={'calc(100vh - 340px)'}
                on={{
                  changePage: (pageIndex: number, pageSize: number) => {
                    this.filters.pageIndex = pageIndex;
                    this.filters.pageSize = pageSize;
                  },
                  conditionChange: ({ sorter: sorterData, filters: tableFilters }) => {
                    const { metricsName, riskLevel } = tableFilters;
                    this.handleFilterSelectChange({
                      metricsIds: metricsName,
                      riskLevels: riskLevel,
                      ...convertSortStructure(sorterData),
                      pageIndex: 1,
                    });
                  },
                  openCourtNotice: (data) => {
                    openRelatinCourtModal({ data });
                  },
                }}
              >
                <SwitchButton
                  v-show={!this.isSelfTab && this.viewMode === 'list'}
                  slot="extra"
                  value={this.viewMode}
                  options={VIEW_MODE_OPTIONS}
                  onChange={this.setViewMode}
                />
              </SearchResult>
              {/* 关联方图表 */}
              {!this.isSelfTab && this.viewMode === 'chart' && this.companyDetail?.KeyNo ? (
                <ChartView
                  groupId={this.filters.groupId[0]}
                  companyId={this.companyDetail.KeyNo}
                  companyName={this.companyDetail.Name}
                  // TODO: JS动态计算高度
                  height={'calc(100vh - 52px - 50px - 99px - 45px - 80px)'}
                />
              ) : null}
            </div>
          </Card>
        </HeroicLayout>
      </div>
    );
  },
});

export default TargetsDetailPage;
