import { Button } from 'ant-design-vue';
import { defineComponent } from 'vue';

import { calcTotalHits } from '../../config/basic-info-config';
import { openDimensionDetailDrawer } from '../../../../../../shared/components/dimenison-detail-drawer';

const OpenDetailButton = defineComponent({
  name: 'OpenDetailButton',
  props: {
    record: {
      type: Object,
      default: () => ({}),
    },
  },
  render() {
    const { metricsContent } = this.record;
    const { displayContent } = metricsContent || {};
    const totalHits = calcTotalHits(this.record);

    const disabled = !totalHits || !displayContent?.length;

    return (
      <Button
        type="link"
        disabled={disabled}
        onClick={() => {
          openDimensionDetailDrawer({
            record: this.record,
          });
        }}
      >
        详情
      </Button>
    );
  },
});

export default OpenDetailButton;
