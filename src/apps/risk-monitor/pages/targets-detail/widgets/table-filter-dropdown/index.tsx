import { defineComponent, ref } from 'vue';
import { Button, Checkbox } from 'ant-design-vue';

import QIcon from '@/components/global/q-icon';

import styles from './table-filter-dropdown.module.less';

const TableFilterDropdown = defineComponent({
  name: 'TableFilterDropdown',
  props: {
    options: {
      type: Array,
      required: true,
    },
    defaultValue: {
      type: Array,
      default: () => [],
    },
    valueKey: {
      type: String,
      default: 'value',
    },
    labelKey: {
      type: String,
      default: 'label',
    },
  },
  setup() {
    const value = ref([]);

    return {
      value,
    };
  },
  render() {
    const checkBoxOptions = this.options.map((item: any) => ({
      ...item,
      label: `${item[this.labelKey]}(${item.count})`,
      value: item[this.valueKey],
    }));
    return (
      <div class={styles.container}>
        <div class={styles.selectItem}>
          <div style={{ width: 'max-content' }}>
            <Checkbox.Group
              v-model={this.value}
              options={checkBoxOptions}
              onChange={(value) => {
                this.value = value;
              }}
            />
          </div>
        </div>
        <div class={styles.buttons}>
          <Button
            class={[styles.button, styles.cancel]}
            type="link"
            onClick={() => {
              this.value = [];
              this.$emit('update', this.value);
            }}
          >
            <QIcon type="icon-zhongzhishaixuanicon"></QIcon>重置
          </Button>
          <Button
            class={styles.button}
            type="link"
            onClick={() => {
              this.$emit('update', this.value);
            }}
          >
            确认
          </Button>
        </div>
      </div>
    );
  },
});

export default TableFilterDropdown;
