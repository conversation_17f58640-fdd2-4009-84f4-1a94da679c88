/**
 * 画布工具类
 */
export class CanvasUtil {
  textWidthCache: Map<string, number>;

  context: CanvasRenderingContext2D | null;

  canvas: HTMLCanvasElement;

  constructor(fontSize = '14px', fontFamily = 'sans-serif') {
    this.textWidthCache = new Map<string, number>();
    this.canvas = document.createElement('canvas');
    this.context = this.canvas.getContext('2d');
    if (this.context) {
      this.context.font = `${fontSize} ${fontFamily}`;
    }
  }

  measureTextWidth(text: string): number {
    if (this.textWidthCache.has(text)) {
      return this.textWidthCache.get(text) || 0;
    }
    if (!this.context) {
      return 0;
    }
    const width = this.context?.measureText(text)?.width ?? 0;
    this.textWidthCache.set(text, width);
    return width;
  }
}
