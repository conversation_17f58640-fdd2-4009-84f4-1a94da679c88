import * as d3 from 'd3v7';

import { CanvasUtil } from './util';
import HighRiskIcon from './icon/high-risk.icon.svg';
import MediumRiskIcon from './icon/medium-risk.icon.svg';
import LowRiskIcon from './icon/low-risk.icon.svg';
import LinkArrowIcon from './icon/link-arrow.icon.svg';

/**
 * 树图组件
 *
 * 功能特性：
 * - 支持控制子节点的默认最大显示数
 * - 自动保存全部原始children到allChildren字段
 * - 通过点击"展开更多"按钮展示当前层级的所有子节点
 * - 风险等级高亮显示
 * - 节点展开/收起切换
 * - 缩放和平移支持
 */

// 枚举和常量配置
enum RiskLevel {
  High = 2,
  Medium = 1,
  Low = 0,
  Unknown = -1,
}

enum NodeType {
  RelatedType = 'relatedType',
  Metric = 'metric',
  Company = 'company',
  ExpandButton = 'expandButton',
}

// 添加用于记录状态的字段
export interface TreeNode {
  name: string;
  type: NodeType; // 修改为枚举类型
  id: string;
  href: string;
  riskLevel?: 2 | 1 | 0;
  children?: TreeNode[] | d3.HierarchyNode<TreeNode>[] | undefined;
  _children?: TreeNode[] | d3.HierarchyNode<TreeNode>[] | undefined;
  allChildren?: TreeNode[] | d3.HierarchyNode<TreeNode>[] | undefined;
  hasMore?: boolean;
}

const RISK_LEVEL_ALIAS_MAP = {
  [RiskLevel.Low]: 'low',
  [RiskLevel.Medium]: 'medium',
  [RiskLevel.High]: 'high',
  [RiskLevel.Unknown]: 'unknown',
};

const RISK_ICON_MAP = {
  [RiskLevel.High]: HighRiskIcon,
  [RiskLevel.Medium]: MediumRiskIcon,
  [RiskLevel.Low]: LowRiskIcon,
};

const HIGHLIGHT_RISK_LINE_COLOR_MAP = {
  [RiskLevel.High]: '#ff6060', // 高风险
  [RiskLevel.Medium]: '#ffaa00', // 中风险
  [RiskLevel.Low]: '#00a3cc', // 低风险
  [RiskLevel.Unknown]: '#d8d8d8', // 未知风险
};

// 递归计算节点的风险等级
const calculateRiskLevel = (node, relatedIds?: Set<string>): RiskLevel => {
  if (!node.children?.length) {
    return node?.riskLevel ?? RiskLevel.Unknown;
  }
  let maxRiskLevel = RiskLevel.Unknown;
  node.children.forEach((child) => {
    if (relatedIds && relatedIds.has(child.id)) {
      const childRiskLevel = calculateRiskLevel(child, relatedIds);
      maxRiskLevel = Math.max(maxRiskLevel, childRiskLevel);
    } else {
      maxRiskLevel = Math.max(maxRiskLevel, child.riskLevel ?? RiskLevel.Unknown);
    }
  });
  return maxRiskLevel;
};

export class Tree {
  private dom: SVGSVGElement;

  private data: TreeNode;

  private width: number;

  private height: number;

  private root: d3.HierarchyNode<TreeNode>;

  private dx: number;

  private dy: number;

  private x0: number; // 画面中心偏移值

  private zoomRange: [number, number];

  private treeLayout: d3.TreeLayout<TreeNode>;

  private container!: d3.Selection<SVGGElement, unknown, null, undefined>;

  svg!: d3.Selection<SVGSVGElement, unknown, null, undefined>;

  zoom: d3.ZoomBehavior<SVGSVGElement, unknown> | null = null;

  zoomTransform: d3.ZoomTransform | null = null;

  dispatcher: any;

  on: any;

  /** 默认最大显示的子节点数，超过此数量将显示"展开更多"按钮 */
  maxChildren = 10;

  defaultZoomLevel: number = 1.0;

  defaultExpandDepth = 2;

  constructor(dom: SVGSVGElement, data: TreeNode, width: number, height: number, zoomRange: [number, number], defaultZoomLevel?: number) {
    this.dom = dom;
    this.width = width;
    this.height = height;
    this.defaultZoomLevel = defaultZoomLevel || 1.0;

    // 保存原始数据
    this.data = data;

    // 生成包含布局的数据结构
    this.root = d3.hierarchy(this.data);
    // 设置默认展开状态：默认展开2层
    this.setDefaultExpandState(this.root, this.defaultExpandDepth);
    // 设置默认子节点显示限制
    this.setDefaultChildrenLimit(this.root, this.maxChildren);

    // 布局参数
    this.dx = 32; // 节点垂直间距 nodeHeight
    // this.dy = this.width / (this.root.height + padding); // 节点水平间距
    this.dy = 0; // 节点水平间距
    // 缩放范围
    this.zoomRange = zoomRange;

    // 生成布局函数
    this.treeLayout = d3
      .tree<TreeNode>()
      .nodeSize([this.dx, this.dy])
      .separation((a, b) => (a.parent === b.parent ? 1.25 : 2));

    // 计算布局 (副作用写入 this.root)
    this.treeLayout(this.root);
    // 计算画布中心点位置
    this.x0 = this.getCenterX(this.root);

    // 添加事件分发器
    this.dispatcher = d3.dispatch('click', 'transform');
    this.on = this.dispatcher.on.bind(this.dispatcher);
  }

  render() {
    // 根据节点内容重置设置节点坐标
    this.calculateNodeCoordinates(this.root, this.root.descendants(), { nodeGap: 56 });

    const LINKS = this.root.links();
    const NODES = this.root.descendants();

    // 创建画布
    this.svg = d3
      .select(this.dom)
      .attr('xmlns', 'http://www.w3.org/2000/svg')
      .attr('xmlns:xlink', 'http://www.w3.org/1999/xlink')
      .attr('width', this.width)
      .attr('height', this.height);

    // 创建容器
    this.container = this.svg.append('g').attr('data-type', 'container');
    // 绘制节点
    this.renderNodes(NODES);
    // 绘制连线
    this.renderLinks(LINKS);
    // 设置缩放
    this.zoom = d3
      .zoom<SVGSVGElement, unknown>()
      .scaleExtent(this.zoomRange)
      .on('zoom', (event) => {
        const { transform } = event;
        this.zoomTransform = transform;
        this.container.attr('transform', transform);
        this.dispatcher.call('transform', this, transform); // 向外部传递缩放状态
      });

    this.svg.call(this.zoom).on('dblclick.zoom', null);
    this.moveToCenter();
    this.zoom.scaleBy(this.svg, this.defaultZoomLevel);
  }

  /**
   * 设置默认展开状态：只展开到指定深度
   * @param node 节点
   * @param depth 折叠深度，默认折叠到第2层
   */
  setDefaultExpandState(node: d3.HierarchyNode<TreeNode>, depth = 2) {
    if (!node.children) {
      return;
    }

    // 如果节点的深度超出限制深度，则折叠它
    if (node.depth >= depth) {
      node.data._children = node.children.slice() as d3.HierarchyNode<TreeNode>[];
      node.children = undefined;
      return;
    }

    // 递归处理子节点
    node.children.forEach((child) => {
      this.setDefaultExpandState(child, depth);
    });
  }

  /**
   * 设置默认子节点显示限制：保存所有子节点到allChildren，限制children显示数量
   * @param node 节点
   * @param maxChildren 最大显示的子节点数，默认为this.maxChildren
   */
  setDefaultChildrenLimit(node: d3.HierarchyNode<TreeNode>, maxChildren = this.maxChildren) {
    if (!node.children) {
      return;
    }

    // 首先递归处理所有子节点
    node.children.forEach((child) => {
      this.setDefaultChildrenLimit(child, maxChildren);
    });

    // 如果当前节点的子节点数量不超过限制，直接返回
    if (node.children.length <= maxChildren) {
      return;
    }

    // 保存所有原始子节点到allChildren
    node.data.allChildren = node.children.slice() as d3.HierarchyNode<TreeNode>[];

    // 限制显示的子节点数量
    const limitedChildren = node.children.slice(0, maxChildren) as d3.HierarchyNode<TreeNode>[];

    // 创建"展开更多"按钮节点
    const expandMoreButton: TreeNode = {
      type: NodeType.ExpandButton,
      name: `展开更多(${node.children.length - maxChildren})`,
      id: `${node.data.id}-expand-more`,
      href: '',
    };

    // 创建按钮的层次节点
    const buttonHierarchyNode = d3.hierarchy(expandMoreButton);
    // 设置父节点关系
    (buttonHierarchyNode as any).parent = node;
    // 通过重新计算来设置正确的depth和height
    buttonHierarchyNode.each((d) => {
      if (d.parent) {
        (d as any).depth = d.parent.depth + 1;
        (d as any).height = 0;
      }
    });

    // 设置children为限制后的数组加上展开按钮
    node.children = [...limitedChildren, buttonHierarchyNode];
    node.data.hasMore = true;
  }

  /**
   * 展开当前层级的所有子节点（替换children和allChildren）
   * @param node 要展开的节点
   */
  expandAllChildrenInLevel(node: d3.HierarchyNode<TreeNode>) {
    if (!node.data.hasMore || !node.data.allChildren) {
      return;
    }

    // 将所有原始子节点设置为当前children
    node.children = node.data.allChildren.slice() as d3.HierarchyNode<TreeNode>[];

    // 清除hasMore标记和allChildren备份
    node.data.hasMore = false;
    node.data.allChildren = undefined;

    // 对新展开的子节点重新应用子节点限制
    node.children.forEach((child) => {
      this.setDefaultChildrenLimit(child, this.maxChildren);
    });

    // 重新计算布局
    this.update();
  }

  /**
   * 将图表移到画布中心
   */
  moveToCenter() {
    const translateX = 0;
    const translateY = this.height / 2;
    this.zoom?.transform(this.svg, d3.zoomIdentity.translate(translateX, translateY));
  }

  update() {
    // 重新计算布局
    this.treeLayout(this.root);
    // 重新计算节点坐标
    this.calculateNodeCoordinates(this.root, this.root.descendants(), { nodeGap: 56 });

    if (this.svg && this.container) {
      // 完全清除并重新创建内容，确保状态一致性
      this.container.selectAll('*').remove();

      // 重新渲染节点和连线
      this.renderLinks(this.root.links());
      this.renderNodes(this.root.descendants());

      // 保持当前的变换状态
      if (this.zoomTransform) {
        this.container.attr('transform', `translate(${this.zoomTransform.x},${this.zoomTransform.y}) scale(${this.zoomTransform.k})`);
      }
    }
  }

  /**
   * 清除画布
   */
  clear() {
    if (this.svg) {
      this.svg.selectAll('g').remove();
    }
  }

  /**
   * 绘制节点
   */
  renderNodes(nodesData: d3.HierarchyNode<TreeNode>[]) {
    const nodeGroup = this.container.append('g').attr('data-type', 'node-group');

    // 直接创建新的节点元素
    const node = nodeGroup
      .selectAll('g')
      .data(nodesData)
      .enter()
      .append('g')
      .attr('class', (d) => {
        const classList = ['node', d.data.type];
        if (d.data.riskLevel !== undefined) {
          classList.push(`risk-${RISK_LEVEL_ALIAS_MAP[d.data.riskLevel]}`); // 'risk-high'
        }
        return classList.join(' ');
      })
      .attr('transform', (d) => `translate(${d.y || 0}, ${d.x || 0})`);

    // 添加节点文本
    node
      .append('text')
      .attr('x', 0)
      .attr('text-anchor', 'start')
      .attr('alignment-baseline', 'middle')
      .attr('transform', (d) => {
        if ([NodeType.Metric, NodeType.Company].includes(d.data.type) && d.data.riskLevel !== undefined) {
          return 'translate(24, 0)';
        }
        return 'translate(0, 0)';
      })
      .text((d) => d.data.name);

    // 添加鼠标事件 - 高亮相关节点
    node
      .on('mouseover', (event, d) => {
        this.highlightRelatedNodes(d);
      })
      .on('mouseout', () => {
        this.resetHighlight();
      });

    // 保存 Tree 实例的引用
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const treeInstance = this;

    // 添加背景矩形和其他元素
    node.each(function (d) {
      const currentNode = d3.select(this);
      const textElement = currentNode.select('text').node() as SVGTextElement;

      if (!textElement) {
        return;
      }

      const bbox = textElement.getBBox();
      const padding = { x: 16, y: 16 };
      // 使用类型断言解决 _children 属性不存在的问题
      const hasChildren = d.children || d.data._children;
      // 如果有子节点，增加矩形宽度以容纳按钮
      const extraWidth = hasChildren ? 16 + 4 : 0;

      // 处理文本节点宽度（特殊节点有有额外内容，需要计算偏移量）
      const handleTextWidth = (sd: any) => {
        const w = bbox.width + padding.x + extraWidth;
        if (sd.data.type === NodeType.Metric && sd.data.riskLevel !== undefined) {
          return w + 24;
        }
        if (sd.data.type === NodeType.Company) {
          return w + 40 + 24;
        }
        return w;
      };

      currentNode
        .insert('rect', 'text')
        .attr('x', bbox.x - padding.x / 2)
        .attr('y', bbox.y - padding.y / 2)
        .attr('height', bbox.height + padding.y)
        .attr('width', handleTextWidth);

      // 插入图标
      if ([NodeType.Metric, NodeType.Company].includes(d.data.type) && d.data.riskLevel !== undefined) {
        currentNode
          .append('image')
          .attr('xlink:href', RISK_ICON_MAP[d.data.riskLevel])
          .attr('x', bbox.x - padding.x / 2 + 8)
          .attr('y', bbox.y - 1)
          .attr('width', 18)
          .attr('height', 18);
      }

      if (d.data.type === NodeType.Company) {
        // 添加链接
        const companyLink = currentNode.append('a').attr('class', 'company-link');

        // companyLink
        //   .append('text')
        //   .attr('x', bbox.x + bbox.width + 4 + 24)
        //   .attr('y', bbox.y + bbox.height / 2 + 5)
        //   .text(`(${d.data.children?.length})`);

        companyLink
          .append('text')
          .attr('x', bbox.x + bbox.width + 4 + 24)
          .attr('y', bbox.y + bbox.height / 2 + 5)
          .text('动态');

        companyLink
          .append('image')
          .attr('xlink:href', LinkArrowIcon)
          .attr('x', bbox.x + bbox.width + 4 + 28 + 24)
          .attr('y', bbox.y + bbox.height / 2 - 5)
          .attr('width', 10)
          .attr('height', 10);

        // 添加点击事件处理
        companyLink.on('click', (event, companyNode) => {
          event.stopPropagation();
          treeInstance.dispatcher.call('click', null, companyNode, event);
        });
      }

      // 添加点击操作事件
      if (d.data.type === NodeType.ExpandButton) {
        // 展开当前层级的所有子节点
        currentNode.on('click', (event, expandButtonNode: any) => {
          treeInstance.expandAllChildrenInLevel(expandButtonNode?.parent);
        });
      } else {
        // 展开/收起子节点
        currentNode.on('click', function (event) {
          event.stopPropagation(); // 阻止事件冒泡
          treeInstance.toggleNode(d); // 使用外部保存的 Tree 实例引用
        });
      }

      // 添加展开/收起按钮
      if (d.children || d.data._children) {
        const buttonSize = 16;
        // 将按钮放在文本右侧，在矩形内部
        const buttonX = bbox.x + bbox.width + 4;

        // 添加按钮背景
        const button = currentNode.append('g').attr('class', 'toggle-button');
        button
          .append('circle')
          .attr('cx', (sd: any) => {
            if (sd.data.type === NodeType.Company) {
              return buttonX + buttonSize / 2 + 40 + 24;
            }
            return buttonX + buttonSize / 2;
          })
          .attr('cy', bbox.y + bbox.height / 2)
          .attr('r', buttonSize / 2 - 2);

        // 横线（对所有节点都有）
        button
          .append('line')
          .attr('x1', (sd: any) => {
            if (sd.data.type === NodeType.Company) {
              return buttonX + 5 + 40 + 24;
            }
            return buttonX + 5;
          })
          .attr('x2', (sd: any) => {
            if (sd.data.type === NodeType.Company) {
              return buttonX + buttonSize - 5 + 40 + 24;
            }
            return buttonX + buttonSize - 5;
          })
          .attr('y1', bbox.y + bbox.height / 2)
          .attr('y2', bbox.y + bbox.height / 2)
          .attr('class', 'toggle-button-line');

        // 添加 +/- 符号
        const isExpanded = !!d.children;

        // 竖线（只对折叠状态显示）
        if (!isExpanded) {
          button
            .append('line')
            .attr('x1', () => {
              if (d.data.type === NodeType.Company) {
                return buttonX + buttonSize / 2 + 40 + 24;
              }
              return buttonX + buttonSize / 2;
            })
            .attr('x2', () => {
              if (d.data.type === NodeType.Company) {
                return buttonX + buttonSize / 2 + 40 + 24;
              }
              return buttonX + buttonSize / 2;
            })
            .attr('y1', bbox.y + bbox.height / 2 - 3)
            .attr('y2', bbox.y + bbox.height / 2 + 3)
            .attr('class', 'toggle-button-line');
        }
      }
    });
  }

  /**
   * 绘制连线
   */
  renderLinks(linksData: d3.HierarchyLink<TreeNode>[]) {
    const reOrderedLinks = d3.reverse(linksData); // 让连线反向渲染，使得风险最高的线在最上面
    const linkGroup = this.container.append('g').attr('data-type', 'link-group');

    const link = linkGroup
      .selectAll('path')
      .data(reOrderedLinks)
      .enter()
      .append('path')
      .attr('class', 'link')
      .attr('d', (node) => {
        const sourceNodeWidth = this.getNodeWidth(node.source, '14px');

        // 节点图标宽度 + 节点间距 + 节点文本宽度
        const sourceOffsetX = 16 + 4 + 9;
        const targetOffsetX = 4 + 5;

        let sourceY = (node.source.y || 0) + sourceNodeWidth + sourceOffsetX;
        const targetY = (node.target.y || 0) - targetOffsetX;

        if (node.source.data.type === NodeType.Company) {
          sourceY += 40 + 24;
        }

        const source = { x: node.source.x, y: sourceY };
        const target = { x: node.target.x, y: targetY };
        return this.drawLinkPath(source, target);
      });

    // 添加鼠标事件 - 高亮相关节点
    link
      .on('mouseover', (event, d) => {
        this.highlightRelatedNodes(d.target);
      })
      .on('mouseout', () => {
        this.resetHighlight();
      });
  }

  /**
   * 绘制连线
   */
  drawLinkPath = (source: { x?: number; y?: number }, target: { x?: number; y?: number }) => {
    const midY = ((source.y || 0) + (target.y || 0)) / 2; // 中间点
    return `M${source.y},${source.x}
            L${midY},${source.x}
            L${midY},${target.x}
            L${target.y},${target.x}`;
  };

  /**
   * Effect FN: 获取中心点X坐标
   */
  getCenterX(root: d3.HierarchyNode<TreeNode>) {
    let x0 = Infinity;
    let x1 = -x0;
    root.each((d: Record<string, any>) => {
      if (d.x > x1) x1 = d.x;
      if (d.x < x0) x0 = d.x;
    });
    return x0;
  }

  /**
   * 获取指定深度节点最大宽度
   */
  getMaxWidthByDepth(nodes: d3.HierarchyNode<TreeNode>[], depth: number) {
    return nodes.reduce((max, node) => {
      if (node.depth === depth) {
        return Math.max(max, this.getNodeWidth(node, '14px'));
      }
      return max;
    }, 0);
  }

  /**
   * 获取节点实际宽度
   */
  getNodeWidth(
    node: d3.HierarchyNode<TreeNode>,
    fontSize = '14px',
    fontFamily = `Helvetica, Tahoma, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Heiti SC', 'Microsoft YaHei', sans-serif`
  ) {
    const canvasUtil = new CanvasUtil(fontSize, fontFamily);
    return canvasUtil.measureTextWidth(node.data.name);
  }

  /**
   * 计算节点坐标
   */
  calculateNodeCoordinates(
    treeNode: d3.HierarchyNode<TreeNode>,
    flatNodes: d3.HierarchyNode<TreeNode>[],
    options: {
      nodeGap?: number; // 层级之间的距离
      nodePadding?: { x: number; y: number }; // 边距
      nodeIconSize?: number; // 图标大小
      nodeCompanyIconSize?: number; // 公司图标大小
    } = {}
  ) {
    const settings = {
      nodeGap: options.nodeGap ?? 0, // 层级之间的距离
      nodePadding: options?.nodePadding ?? { x: 8, y: 6 }, // 边距
      nodeIconSize: options.nodeIconSize ?? 12 + 8, // 控制点图标大小 size + gap
      nodeCompanyIconSize: options.nodeCompanyIconSize ?? 28 + 8 + 5, // 公司图标大小 size + gap
    };

    const parentX = treeNode.parent?.y ?? 0;
    const parentWidth = this.getMaxWidthByDepth(flatNodes, treeNode.depth - 1);
    // 计算当前节点宽度
    treeNode.y = parentX + parentWidth + settings.nodePadding.x * 2 + settings.nodeIconSize + (treeNode.depth > 0 ? settings.nodeGap : 0);

    if (treeNode?.parent?.data?.type === NodeType.Company) {
      treeNode.y += settings.nodeCompanyIconSize;
    }

    treeNode.children?.forEach((child) => this.calculateNodeCoordinates(child, flatNodes, settings));
  }

  /**
   * 高亮相关节点
   */
  highlightRelatedNodes(node: d3.HierarchyNode<TreeNode>) {
    const DEFAULT_LINK_LINE_COLOR = '#d8d8d8';
    const TRANSITION_DURATION = 150; // 添加过渡时长
    const TRANSITION_EASE = d3.easeLinear;

    // 获取从根节点到当前节点的路径
    const ancestors = node.ancestors();
    // 获取当前节点的所有后代节点
    const descendants = node.descendants();

    // 创建一个包含所有相关节点ID的集合
    const relatedIds = new Set([...ancestors.map((d) => d.data.id), ...descendants.map((d) => d.data.id)]);

    // 获取所有连接线
    const links = this.container
      .selectAll('.link')
      .style('stroke', (d: any) => {
        const source = d.source.data.id;
        const target = d.target.data.id;
        let lineColor = DEFAULT_LINK_LINE_COLOR; // 未命中的线全部置灰
        if (relatedIds.has(source) && relatedIds.has(target)) {
          const maxRiskLevel = calculateRiskLevel(d.target.data, relatedIds); // 根据当前节点的风险等级，高亮所有关联路径的连线颜色
          lineColor = HIGHLIGHT_RISK_LINE_COLOR_MAP[maxRiskLevel];
        }
        return lineColor;
      })
      // 设置高亮样式: 线粗
      .style('stroke-width', (d: any) => {
        const source = d.source.data.id;
        const target = d.target.data.id;
        return relatedIds.has(source) && relatedIds.has(target) ? 2 : 1;
      })
      .style('opacity', (d: any) => {
        const source = d.source.data.id;
        const target = d.target.data.id;
        return relatedIds.has(source) && relatedIds.has(target) ? 1 : 0.3;
      });

    // 高亮连接线并按风险等级排序
    const linksArray = links.nodes();
    const sortedLinks = linksArray.sort((a, b) => {
      const aData = d3.select(a).datum() as any;
      const bData = d3.select(b).datum() as any;

      const aSource = aData.source.data.id;
      const aTarget = aData.target.data.id;
      const bSource = bData.source.data.id;
      const bTarget = bData.target.data.id;

      // 如果两个连接线都是高亮的,按风险等级排序
      if (relatedIds.has(aSource) && relatedIds.has(aTarget) && relatedIds.has(bSource) && relatedIds.has(bTarget)) {
        const aRiskLevel = calculateRiskLevel(aData.target.data, relatedIds);
        const bRiskLevel = calculateRiskLevel(bData.target.data, relatedIds);
        return aRiskLevel - bRiskLevel; // 风险等级低的在下面
      }

      // 非高亮的连接线放在最下面
      if (relatedIds.has(aSource) && relatedIds.has(aTarget)) return 1;
      if (relatedIds.has(bSource) && relatedIds.has(bTarget)) return -1;
      return 0;
    });
    // ReOrdered: 使用重新排序后的连接线，进行层级管理，重新插入到 DOM
    sortedLinks.forEach((link) => {
      d3.select(link).raise();
    });

    // 高亮相关节点
    const nodes = this.container
      .selectAll('.node')
      .transition()
      .duration(TRANSITION_DURATION)
      .ease(TRANSITION_EASE)
      .style('opacity', (d: any) => {
        return relatedIds.has(d.data.id) ? 1 : 0.3;
      });
  }

  /**
   * 恢复默认显示
   */
  resetHighlight() {
    const LINK_LINE_COLOR = '#d8d8d8';
    const TRANSITION_DURATION = 150; // 添加过渡时长
    const TRANSITION_EASE = d3.easeLinear;

    // 添加重置动画
    const links = this.container.selectAll('.link').style('stroke', LINK_LINE_COLOR).style('stroke-width', 1).style('opacity', 1);
    const nodes = this.container.selectAll('.node').transition().duration(TRANSITION_DURATION).ease(TRANSITION_EASE).style('opacity', 1);
  }

  /**
   * 递归折叠所有子节点
   */
  private collapseNodeRecursively(node: d3.HierarchyNode<TreeNode>): void {
    if (!node.children) {
      return;
    }

    // 先递归折叠所有子节点
    node.children.forEach((child) => {
      this.collapseNodeRecursively(child);
    });

    // 然后折叠当前节点
    node.data._children = node.children as d3.HierarchyNode<TreeNode>[];
    node.children = undefined;
  }

  /**
   * 切换节点展开/折叠状态（支持递归和状态保存）
   */
  toggleNode(node: d3.HierarchyNode<TreeNode>) {
    if (node.children) {
      // 如果节点已展开，则折叠它（递归折叠所有子节点）
      this.collapseNodeRecursively(node);
    } else if (node.data._children) {
      // 如果节点已折叠，则展开它
      node.children = node.data._children as d3.HierarchyNode<TreeNode>[];
      node.data._children = undefined;

      // 重新应用子节点限制（这会处理新展开的子节点）
      this.setDefaultChildrenLimit(node, this.maxChildren);
    }

    this.update();
  }
}
