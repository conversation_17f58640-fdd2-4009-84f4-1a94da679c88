.container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  > svg {
    width: 100%;
    height: 100%;
  }

  &:fullscreen {
    background: #fff;
  }

  :global {
    .node {
      cursor: pointer;
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;

      rect {
        rx: 2;
        ry: 2;
        fill: #f7f7f7;
        stroke: #d8d8d8;
      }

      text {
        font-size: 14px;
        font-weight: 400;
        fill: #333;
        // line-height: 22px;
      }

      &:hover {
        .toggle-button {
          circle {
            stroke: #128bed;
          }

          line {
            stroke: #128bed;
          }
        }
      }

      &.root {
        rect {
          fill: #128bed;
          stroke: #128bed;
        }

        text {
          fill: #fff;
          font-weight: 500;
        }

        .toggle-button {
          circle {
            stroke: #fff;
          }

          line {
            stroke: #fff;
          }

          &:hover {
            circle {
              stroke: #fff;
            }

            line {
              stroke: #fff;
            }
          }
        }
      }

      &.relatedType {
        rect {
          fill: #f7f7f7;
          stroke: #d8d8d8;
        }
      }

      &.company {
        rect {
          fill: #fff;
          stroke: #d8d8d8;
        }
      }

      &.metric.risk-high {
        rect {
          fill: #fff8f9;
          stroke: #ffecec;
        }
      }

      &.metric.risk-medium {
        rect {
          fill: rgba(255, 244, 224, 0.3);
          stroke: #fec;
        }
      }

      &.metric.risk-low {
        rect {
          fill: rgba(198, 237, 247, 0.2);
          stroke: #c6edf7;
        }
      }
    }

    .link {
      stroke: #d8d8d8;
      stroke-width: 1;
      fill: none;
      stroke-linecap: round;
      stroke-linejoin: round;
    }

    // 切换折叠按钮
    .toggle-button {
      circle {
        fill: transparent;
        stroke: #999;
        stroke-width: 1;
      }

      line {
        stroke: #999;
        stroke-width: 1;
      }

      &:hover {
        circle {
          stroke: #128bed;
        }

        line {
          stroke: #128bed;
        }
      }
    }

    // 公司链接
    .company-link {
      text {
        font-size: 14px;
        fill: #128bed;
      }

      &:hover {
        text {
          fill: #0069BF;
        }
      }
    }

    // 展开更多按钮
    .expandButton {
      rect {
        fill: #f3f3f3;
        stroke: #f3f3f3;
      }

      &:hover {
        text {
          fill: #128bed;
        }
      }
    }

    .ant-tooltip.ant-slider-tooltip .ant-tooltip-inner {
      background: #fff;
      color: #333;
      padding: 8px 10px;
      font-size: 14px;
      line-height: 22px;
    }
  }
}

// 添加全屏状态下的控制面板样式
:global(:fullscreen) {
  .container {
    .controlPanel {
      top: 20px;
      right: 20px;
    }
  }
}

// 添加控制面板样式
.controlPanel {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  z-index: 100;
  width: 48px;
  border-radius: 4px;

  .slider {
    height: 140px;
    display: flex;
    justify-content: center;
  }

  .group {
    display: flex;
    flex-direction: column;
    background: #fff;
    border: 1px solid #eee;
    border-radius: 2px;
  }

  .button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #666;
    gap: 2px;
    padding: 5px 0;
    font-size: 11px;
    line-height: 18px;
    cursor: pointer;
    user-select: none;

    .icon {
      font-size: 18px;
      padding: 3px;
    }

    span {
      min-height: 18px;
    }

    &:hover {
      color: #128bed;
    }
  }

  :global {
    .ant-slider {
      margin-left: 0;
      margin-right: 0;

      &:hover {
        .ant-slider-track {
          background: #128bed;
        }
      }
    }

    .ant-slider-rail {
      background: #f0f0f0;
    }

    .ant-slider-track {
      background: #128bed;
    }

    .ant-slider:hover .ant-slider-handle:not(.ant-tooltip-open) {
      border-color: #128bed;
    }

    .ant-slider-handle {
      border-color: #128bed;
    }
  }
}
