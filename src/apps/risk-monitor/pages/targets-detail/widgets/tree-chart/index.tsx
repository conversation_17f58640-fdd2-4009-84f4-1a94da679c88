import { defineComponent, onMounted, onUnmounted, PropType, ref, shallowRef } from 'vue';
import * as d3 from 'd3v7';
import { <PERSON>lider } from 'ant-design-vue';
import { throttle } from 'lodash';

import Icon from '@/shared/components/icon';

import styles from './tree-chart.module.less';
import { Tree, TreeNode } from './core';

const TreeChart = defineComponent({
  name: 'TreeChart',
  props: {
    data: {
      type: Object as PropType<TreeNode>,
      required: true,
    },
  },
  emits: ['click'],
  setup(props, { emit }) {
    const defaultZoomLevel = 0.8; // 默认缩放比例

    const chartRef = ref<SVGSVGElement>();
    const containerRef = ref<HTMLDivElement>();
    const isFullscreen = ref(false);
    const treeInstance = shallowRef<Tree | null>(null);
    const zoomRange: [number, number] = [0.2, 5]; // 缩放范围
    const zoomLevel = ref(defaultZoomLevel); // 缩放比例
    const setZoomLevel = (value: number) => {
      zoomLevel.value = value;
    };

    const zoomSliderTooltipFormatter = (value: number) => {
      // return `${Math.round(((value - zoomRange[0]) / (zoomRange[1] - zoomRange[0])) * 99 + 1)}%`;
      return `${Math.round(value * 100)}%`;
    };

    /**
     * 获取组件DOM节点的大小
     */
    const calcContainerSize = (element?: HTMLElement) => {
      if (!element) {
        return { width: 0, height: 0 };
      }
      const { width, height } = element.getBoundingClientRect();
      return { width, height };
    };

    /**
     * 图表的缩放事件
     */
    const handleTransform = throttle((transform: d3.ZoomTransform) => setZoomLevel(transform.k), 150);

    const init = () => {
      const { width, height } = calcContainerSize(containerRef.value);
      // 初始化树图
      const tree = new Tree(chartRef.value as SVGSVGElement, props.data, width, height, zoomRange, defaultZoomLevel);
      tree.render();
      tree.on('click', (node) => {
        emit('click', {
          type: node.data.type,
          data: node.data,
        });
      });
      tree.on('transform', handleTransform);

      treeInstance.value = tree;
    };

    // 监听全屏状态变化
    const handleFullscreenChange = () => {
      isFullscreen.value = !!document.fullscreenElement;
    };

    // TODO: 监听窗口大小变化
    // const handleResize = () => {
    //   const instance = treeInstance.value;
    //   if (instance) {
    //     instance.resize(props.width, props.height);
    //   }
    // };

    onMounted(() => {
      init();
      document.addEventListener('fullscreenchange', handleFullscreenChange);
      // window.addEventListener('resize', handleResize);
    });
    onUnmounted(() => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      // window.removeEventListener('resize', handleResize);
    });

    // 缩放控制条
    const handleZoomSliderChange = (value: number) => {
      zoomLevel.value = value;
      const instance = treeInstance.value;
      if (!instance) {
        return;
      }
      instance?.zoom?.scaleTo(instance.svg, value);
    };

    // 缩放控制
    const handleZoomIn = () => {
      const instance = treeInstance.value;
      if (!instance) {
        return;
      }
      instance?.zoom?.scaleBy(instance.svg, 1.2);
      if (instance.zoomTransform?.k !== undefined) {
        zoomLevel.value = Number(instance.zoomTransform?.k);
      }
    };

    const handleZoomOut = () => {
      const instance = treeInstance.value;
      if (!instance) {
        return;
      }
      instance?.zoom?.scaleBy(instance.svg, 0.8);
      if (instance.zoomTransform?.k !== undefined) {
        zoomLevel.value = Number(instance.zoomTransform?.k);
      }
    };

    const handleResetZoom = () => {
      const instance = treeInstance.value;
      if (!instance) {
        return;
      }
      instance?.zoom?.transform(instance.svg, d3.zoomIdentity);
      zoomLevel.value = 1.0;
    };

    // 全屏控制
    const toggleFullscreen = async () => {
      const container = containerRef.value;
      if (!container) {
        return;
      }

      if (!isFullscreen.value) {
        if (container.requestFullscreen) {
          await container.requestFullscreen();
        }
      } else if (document.exitFullscreen) {
        await document.exitFullscreen();
      }
    };

    return {
      chartRef,
      containerRef,
      handleZoomIn,
      handleZoomOut,
      handleResetZoom,
      toggleFullscreen,
      isFullscreen,

      zoomRange,
      zoomLevel,
      zoomSliderTooltipFormatter,
      handleZoomSliderChange,
    };
  },
  render() {
    return (
      <div ref="containerRef" class={styles.container}>
        <svg ref="chartRef"></svg>
        {/* 控制面板 */}
        <div class={styles.controlPanel}>
          <div class={styles.slider}>
            <Slider
              included
              tooltipPlacement="left"
              getTooltipPopupContainer={() => this.containerRef}
              vertical
              tipFormatter={this.zoomSliderTooltipFormatter}
              value={this.zoomLevel}
              max={this.zoomRange[1]}
              min={this.zoomRange[0]}
              step={0.2}
              onChange={this.handleZoomSliderChange}
            />
          </div>
          <div class={styles.group}>
            <div class={styles.button} onClick={this.toggleFullscreen} v-show={this.isFullscreen}>
              <span class={styles.icon}>
                <Icon type="icon-tuichu" />
              </span>
              <span>退出</span>
            </div>

            <div class={styles.button} onClick={this.toggleFullscreen} v-show={!this.isFullscreen}>
              <span class={styles.icon}>
                <Icon type="icon-quanping1" />
              </span>
              <span>全屏</span>
            </div>

            <div class={styles.button} onClick={this.handleZoomIn}>
              <span class={styles.icon}>
                <Icon type="icon-fangda" />
              </span>
              <span>放大</span>
            </div>

            <div class={styles.button} onClick={this.handleZoomOut}>
              <span class={styles.icon}>
                <Icon type="icon-suoxiao" />
              </span>
              <span>缩小</span>
            </div>
          </div>
        </div>
      </div>
    );
  },
});

export default TreeChart;
