import { computed, defineComponent, onMounted, ref, watch } from 'vue';
import { Spin } from 'ant-design-vue';
import moment from 'moment';
import { useRouter } from 'vue-router/composables';
import { orderBy } from 'lodash';

import { monitor as monitorService } from '@/shared/services';
import { useRequest } from '@/shared/composables/use-request';
import QRichTableEmpty from '@/components/global/q-rich-table/components/empty';
import { uuid } from '@/utils';

import styles from './chart-view.module.less';
import HighRiskIcon from './icon/high-risk.icon.svg';
import MediumRiskIcon from './icon/medium-risk.icon.svg';
import LowRiskIcon from './icon/low-risk.icon.svg';
import TreeChart from '../tree-chart';

/** 获取子节点最大风险等级 */
const getMaxRiskLevelByChildren = (children) => {
  if (children.length === 0) {
    return undefined;
  }
  return children.reduce((acc, curr) => Math.max(acc, curr.riskLevel), -1);
};

/** 获取子节点命中数量 */
const getHitCountByChildren = (children) => {
  if (children.length === 0) {
    return 0;
  }
  return children.reduce((acc, curr) => acc + curr.count, 0);
};

/** 子节点按风险等级排序 */
const sortChildrenByRiskLevel = (children) => {
  return orderBy(children, ['riskLevel'], ['desc']);
};

/** 递归构建节点 */
const buildNode = (data, type) => {
  if (type === 'metric') {
    const name = `${data.metricsName} (${data.metricsCount || 0})`;
    const node = {
      id: data?.metricsId || uuid(),
      type: 'metric',
      name,
      value: data.metricsCount,
      count: data.metricsCount,
      riskLevel: data.riskLevel,
    };
    return node;
  }

  if (type === 'company') {
    const children = data.companyMetrics.map((metric) => buildNode(metric, 'metric'));
    const riskLevel = getMaxRiskLevelByChildren(children);
    const count = getHitCountByChildren(children);
    const name = `${data.companyName} (${count})`;
    const node = {
      id: data.companyId || uuid(),
      type: 'company',
      name,
      value: data.companyId,
      count,
      children: sortChildrenByRiskLevel(children),
      riskLevel,
    };
    return node;
  }

  if (type === 'relatedType') {
    const children = data.relatedCompanys.map((company) => buildNode(company, 'company'));
    const riskLevel = getMaxRiskLevelByChildren(children);
    const count = getHitCountByChildren(children);
    const name = `${data.relatedName} (${count})`;

    const node = {
      id: data.relatedType,
      type: 'relatedType',
      name,
      count,
      children: sortChildrenByRiskLevel(children),
      riskLevel,
    };
    return node;
  }

  return null;
};

const ChartView = defineComponent({
  name: 'ChartView',
  props: {
    companyId: {
      type: String,
      required: true,
    },
    groupId: {
      type: Number,
      required: true,
    },
    companyName: {
      type: String,
      required: true,
    },
    height: {
      type: String,
      default: '100%',
    },
  },
  setup(props) {
    const groupId = ref(props.groupId);
    const toChartDataStruct = (items) => {
      if (items.length === 0) {
        return null;
      }

      const children = items.map((item) => buildNode(item, 'relatedType'));
      const count = getHitCountByChildren(children);
      const name = `${props.companyName} (${count})`;

      const root = {
        type: 'root',
        id: uuid(),
        name,
        value: children.length,
        children: sortChildrenByRiskLevel(children),
        count,
      };

      return root;
    };

    const request = async () => {
      try {
        const max = moment();
        const min = moment(max).subtract(24, 'hours');
        const { relatedCompanyChartItems = [] } = await monitorService.getTreeChartData({
          companyIds: [props.companyId],
          groupId: [groupId.value],
          includeRelatedParty: 2,
          createDate: [
            {
              currently: true,
              flag: 5,
              number: 1,
              unit: 'day',
              min,
              max,
            },
          ],
        });
        const nodes = toChartDataStruct(relatedCompanyChartItems);
        return nodes;
      } catch (error) {
        console.error(error);
        return [];
      }
    };

    const { execute, data, isLoading, isSuccess, isError } = useRequest(request);

    watch(
      () => props.groupId,
      () => {
        groupId.value = props.groupId;
        execute();
      }
    );

    const treeData = computed(() => {
      if (!data.value) {
        return null;
      }
      return data.value;
    });

    onMounted(() => {
      execute();
    });

    const router = useRouter();
    /**
     * 图表点击事件
     */
    const handleChartClick = async (payload: { type: string; data: any }) => {
      if (payload.type === 'company') {
        router.push({
          path: `/risk-monitor/trends/detail/${payload.data.value}`,
          query: {
            from: 'trends',
            name: '监控动态',
            groupId: props.groupId.toString(),
          },
        });
      }
    };

    return {
      data,
      treeData,
      handleChartClick,

      isLoading,
      isSuccess,
      isError,
    };
  },
  render() {
    const successRenderer = () => {
      return [
        <div class={styles.stage}>
          <div class={styles.chart}>
            <TreeChart data={this.treeData} onClick={this.handleChartClick} />
          </div>
          <div class={styles.legend}>
            <div class={[styles.item, styles.default]}>风险等级:</div>
            <div class={[styles.item, styles.high]}>
              <img src={HighRiskIcon} width="16" height="16" />
              <span>高风险</span>
            </div>
            <div class={[styles.item, styles.medium]}>
              <img src={MediumRiskIcon} width="16" height="16" />
              <span>中风险</span>
            </div>
            <div class={[styles.item, styles.low]}>
              <img src={LowRiskIcon} width="16" height="16" />
              <span>低风险</span>
            </div>
          </div>
        </div>,
      ];
    };

    const emptyRenderer = () => {
      return [
        <div class={styles.stage}>
          <QRichTableEmpty size={'100px'} minHeight={'100%'}>
            <div>最近24小时内暂无关联方动态数据</div>
          </QRichTableEmpty>
        </div>,
      ];
    };

    const strategyRenderer = () => {
      if (this.isSuccess) {
        return this.treeData ? successRenderer() : emptyRenderer();
      }
      return null;
    };

    return (
      <Spin spinning={this.isLoading}>
        <div
          class={styles.container}
          style={{
            height: this.height,
          }}
        >
          {strategyRenderer()}
        </div>
      </Spin>
    );
  },
});

export default ChartView;
