.container {
  display: flex;
  flex-direction: column;
  background: #fff;
  border: 1px solid #e7f0f7;

  .stage {
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .chart {
    flex: 1;
    max-height: 100%;
  }

  .legend {
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    background: #fff;
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 16px;
    padding: 10px;

    .item {
      line-height: 20px;
      padding: 1px 4px;
      color: #333;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
}
