@import '@/styles/token.less';

.company-title {
  display: flex;
  align-items: center;

  :global {
    .copy-btn-item {
      line-height: 30px;
    }
  }

  .title {
    margin-left: 12px;
    flex:1;

    > div {
      min-height: 30px;
    }

    .titlePart{
      margin-top: 5px;
    }
  }

  .title-inner {
    position: relative;
    font-size: 22px;
    font-weight: @qcc-font-bold;
    display: flex;
    align-items: flex-start;

    > a {
      margin-right: 5px;
      line-height: 30px;
      color: @qcc-color-black-600;

      &:hover {
        color: @qcc-color-blue-500;
      }
    }

    .enter-button {
      flex-shrink: 0;
      display: inline-block;
      height: 22px;
      padding: 0 6px;
      border-radius: 2px;
      background: #128bed;
      font-size: 12px;
      line-height: 22px;
      color: #fff;
      margin-top: 4px;

      &:hover {
        color: #fff;
      }
    }

    :global {
      .qcStatus {
        margin-right: 5px;
        margin-top: 4px;
      }
    }
  }

  .extra {
    display: flex;
    align-items: center;
    min-height: 30px;
    gap: 10px;

    :global {
      .ant-select-selection:not(:disabled) .ant-select-arrow .ant-select-arrow-icon,
      .ant-cascader-picker:not(:disabled) .ant-select-arrow .ant-select-arrow-icon{
        transform: scale(1.34);
        color: #666;
      }

      .ant-select-selection:not(:disabled):hover{
        svg{
          color: #128bed;
        }
      }
    }
  }
}

.company-info {
  position: relative;
  display: block;

  .copy-value,
  .small-copy-value {
    display: inline-block;
    line-height: 22px;
    max-width: 320px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;

    &:hover {
      background-color: #e5f2fd;
    }
  }

  .small-copy-value {
    max-width: 250px;
    margin-right: 5px;
  }

  .field-padding {
    margin-left: 8px;
  }

  .basic-header {
    display: inline-block;
    padding: 0 15px 0 0;
    margin-bottom: 0;
  }

  .company-basic {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-top: 15px;
    padding: 10px 15px 4px;
    vertical-align: middle;
    background: #F8FBFE;
  }

  .left-basic,
  .center-basic,
  .right-basic {
    display: flex;
    flex-direction: column;
    vertical-align: middle;
  }

  .line {
    width: 1px;
    height: 90px;
    background: #eee;
    margin-top: -5px;
  }

  .left-basic {
    padding-right: 15px;
  }

  .right-basic,
  .center-basic {
    padding: 0 15px;

    .icon {
      font-size: 14px;
      cursor: pointer;
      color: #999;
      margin-left: 0;
    }
  }

  .basic-item {
    display: flex;
    line-height: 20px;
    white-space: nowrap;

    .industry-info {
      display: inline-block;
      cursor: pointer;
      vertical-align: bottom;
      max-width: 215px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      line-height: 22px;
    }

    .basiclabel {
      display: inline-block;
      color: #666;
    }

    .basic-val {
      flex: 1;
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .scale-icon {
        font-size: 18px;
        margin-right: 4px;
        vertical-align: middle;
        cursor: pointer;
      }

      a {
        &:hover {
          color: #0d61a6;
        }
      }
    }

    i {
      margin-left: 5px;
    }
  }
}

.menu-popover {
  padding-top: 0;

  :global {
    .ant-popover-inner-content {
      padding: 5px 12px;
    }

    .ant-popover-content {
      max-width: 400px;
      border-radius: 2px;
    }

    .ant-popover-arrow {
      display: none;
    }

    .ant-popover-inner {
      background-color: rgba(0, 0, 0, 0.8);
    }
  }
}

.scale-popover {
  :global {
    .ant-popover-content {
      max-width: 400px;
      border-radius: 2px;
    }
  }
}

.industryTextWrapper {
  .industryText {
    line-height: 22px;
  }

  &:hover {
    span:nth-last-child(2) {
      margin-right: 10px;
    }

    .copyText {
      display: inline-block;
    }
  }

  .copyText {
    display: none;
    cursor: pointer;
    color: #128bed;
    border-radius: 2px;
    padding: 0;

    &:hover {
      color: #0069bf;
    }
  }
}

.needWrap {
  white-space: break-spaces;
}

.oper{
  :global{
    .ant-tooltip-inner{
      background: #fafafa;
      color: #666;
    }
  }
}

.operName{
  &:hover{
    background: #e5f2fd;
  }
}