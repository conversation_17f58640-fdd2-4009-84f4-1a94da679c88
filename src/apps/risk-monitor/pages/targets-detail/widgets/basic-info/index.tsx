import { useResizeObserver } from '@vueuse/core';
import { defineComponent, nextTick, ref } from 'vue';
import { get, isObject, isString } from 'lodash';
import { Tooltip } from 'ant-design-vue';

import CompanyLogo from '@/components/company-logo';
import QCompanyStatus from '@/components/global/q-company-status';
import QCopy from '@/components/global/q-copy';
import QEntityLink from '@/components/global/q-entity-link';
import { getOperTypeLabelMapper } from '@/utils/firm';
import { translateRegistCapiLabel } from '@/utils/transform';
import { dateFormat } from '@/utils/format';

import { basicInfoConfig } from '../../config/basic-info-config';
import styles from './basic-info.module.less';

const BasicInfo = defineComponent({
  name: 'BasicInfo',
  props: {
    company: {
      type: Object,
      required: true,
      defatul: () => ({}),
    },
  },
  setup(props, { emit }) {
    const IconType = ref('icon-a-xianduanxia');
    const snapshot = () => {
      emit('snapshot');
    };
    const addressContentRef = ref<Element>();
    const reportAddrRef = ref<Element>();
    const companyName = ref('');
    const needWrap = ref(false); // 是否需要换行
    const key = ref(0);
    companyName.value = props.company?.Name || '';

    // 如果公司名称过长需要换行显示，第二行开头不能是）
    const bodyWatch = () => {
      companyName.value = props.company?.Name;
      key.value++;
      nextTick(() => {
        const statusDiv = document.getElementsByClassName('qcStatus')[0]?.getClientRects()[0];
        if (statusDiv?.top > 215 && statusDiv?.left >= 205 && statusDiv?.left <= 215) {
          needWrap.value = true;
          const nameLength = props.company?.Name.length;
          const name = props.company?.Name.split('');
          const charCount = name[nameLength - 1] === ')' || name[nameLength - 1] === '）' ? 2 : 1;
          name.splice(nameLength - charCount, 0, '\n');
          companyName.value = name.join('');
        }
      });
    };

    useResizeObserver(document.body, () => {
      setTimeout(() => {
        bodyWatch();
      }, 0);
    });

    const changeIconType = (val) => {
      IconType.value = val ? 'icon-a-xianduanshang' : 'icon-a-xianduanxia';
    };

    return {
      IconType,
      changeIconType,

      key,
      companyName,
      needWrap,
      snapshot,
      addressContentRef,
      reportAddrRef,
    };
  },
  computed: {
    companyInfo() {
      return (this as any).company?.ContactInfo || {};
    },
  },
  methods: {},
  render() {
    const { company } = this;

    const labelRenderMap = {
      operTypeLabel: (val) => getOperTypeLabelMapper(val),
      registCapiLabel: () => {
        const isOrg = company?.KeyNo?.startsWith('s');
        return translateRegistCapiLabel(isOrg ? 'org' : company?.standardCode);
      },
    };

    const contentRenderMap = {
      // 法定代表人label
      operTypeContent: (val) => {
        const { Oper, MultipleOper } = company;
        const renderOps = MultipleOper?.OperList?.length === 1 ? [Oper] : MultipleOper?.OperList;
        return (
          <Tooltip mouseEnterDelay={Oper.Name?.length > 20 ? 0 : 9999} overlayClassName={styles.oper}>
            <div slot="title">
              <QEntityLink coyArr={renderOps}></QEntityLink>
            </div>
            <QEntityLink class={styles.operName} coyObj={Oper} style={{ maxWidth: '200px' }}></QEntityLink>
          </Tooltip>
        );
      },
      StartDate: (val) => {
        const time = dateFormat(val);
        return <span>{time}</span>;
      },
    };
    return (
      <div class={styles.companyInfo}>
        <div class={styles.companyTitle}>
          <CompanyLogo
            size={'60px'}
            src={company?.ImageUrl}
            id={company?.KeyNo || ''}
            name={company?.ShortName || company?.Name}
            hasimage={company?.HasImage}
          />
          <div class={styles.title}>
            <QCopy
              key={this.key}
              style={{ marginBottom: '5px', display: 'inline-flex' }}
              copyValue={company?.Name}
              copyButtonStyle={{
                whiteSpace: 'nowrap',
              }}
            >
              <span class={styles.titleInner} slot="contain">
                <a
                  href={`/embed/companyDetail?keyNo=${company?.KeyNo}&title=${company?.Name || ''}`}
                  class={{ [styles.copyTitle]: true, copyContent: true, [styles.needWrap]: this.needWrap }}
                  target="_blank"
                  domPropsInnerHTML={this.companyName || ''}
                ></a>
                <QCompanyStatus class="qcStatus" status={company?.ShortStatus} ghost />
                <a
                  class={styles.enterButton}
                  href={`/embed/companyDetail?keyNo=${company?.KeyNo}&title=${company?.Name || ''}`}
                  target="_blank"
                >
                  进入主页
                </a>
              </span>
            </QCopy>
            <div class={['flex justify-between items-center']}>
              <div class="flex justify-between items-center" style={{ gap: '32px' }}>
                {basicInfoConfig.map((item: any) => {
                  const { label, key, scopeSlots, render, hide } = item;
                  if (hide && hide(company)) {
                    return null;
                  }
                  const contentValue = get(company, key, undefined);
                  let cr = render;
                  let lr = null;
                  if (isString(scopeSlots)) {
                    cr = scopeSlots;
                  }
                  if (isObject(scopeSlots as any)) {
                    const { labelRender, contentRender } = scopeSlots;
                    cr = contentRender ?? cr;
                    lr = labelRender ?? lr;
                  }
                  const labelText = label || labelRenderMap?.[lr as any]?.(company?.Oper?.OperType);
                  return (
                    <div class={styles.basicItem}>
                      <span class={styles.basiclabel}>{labelText}：</span>
                      <span class={styles.basicVal}>{contentValue ? (contentRenderMap[cr] || cr)?.(contentValue) : '-'}</span>
                    </div>
                  );
                })}
              </div>
              <div class={styles.extra}>{this.$slots.extra}</div>
            </div>
          </div>
        </div>
      </div>
    );
  },
});

export default BasicInfo;
