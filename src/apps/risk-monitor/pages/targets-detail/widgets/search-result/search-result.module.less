@import '@/styles/token.less';

.container {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .companyName {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    max-width: 140px;
    display: inline-block;
  }

  .relatedTrends {
    display: flex;
    gap: 5px;
    border-radius: 2px;
    font-size: 12px;
    line-height: 22px;
    background: #eee;
    width: max-content;
    padding: 0 6px;
  }

  .riskOverViewAction {
    color: @qcc-color-black-300;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 3px;

    .dash {
      display: inline-block;
      background-color: @qcc-color-gray-700;
      width: 30px;
      height: 1px;
    }

    &:hover {
      color: @qcc-color-blue-500;

      .dash {
        background-color: currentcolor;
      }
    }
  }

  .searchCount {
    color: #333;
    font-weight: bold;
    margin: 8px 0;
    font-size: 16px;
  }

  :global {
    .ant-table-expanded-row {
      border-color: #e4eef6 !important;

      .ant-table-bordered .ant-table-thead > tr > th {
        background: #f3f3f3 !important;

        &:last-child {
          border-right: none !important;
        }
      }

      .ant-table-bordered .ant-table-tbody > tr > td {
        background: #fafafa !important;

        &:last-child {
          border-right: none !important;
        }
      }
    }

    .ant-table-bordered .ant-table-bordered table .ant-table-tbody > tr > td {
      border-right: 1px solid #e4eef6 !important;
    }

    .ant-table-thead > tr > th .anticon-filter,
    .ant-table-thead > tr > th .ant-table-filter-icon {
      position: relative;
      transform: translate(-4px, -7px);
    }

    tr:hover{
      .clamp-wrapped-content::after {
        background: linear-gradient(to top,  #f2f9ff, rgba(255,255,255, 0.1),);
      }
    }
  }
}
