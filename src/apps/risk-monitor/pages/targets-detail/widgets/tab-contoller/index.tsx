import { PropType, defineComponent, ref } from 'vue';
import { Tabs } from 'ant-design-vue';

import { TabSetting } from '../../config/setting-config';

import styles from './tab-contoller.module.less';

const TabController = defineComponent({
  name: 'Tab<PERSON>ontroller',
  props: {
    options: {
      type: Array as PropType<typeof TabSetting>,
      default: () => TabSetting,
    },
  },
  emits: ['tabChange'],
  setup(props, { emit }) {
    const activeKey = ref(props.options[0].key);
    const handleTabChange = (key: string) => {
      activeKey.value = key;
      emit('tabChange', key);
    };
    return {
      activeKey,
      handleTabChange,
    };
  },
  render() {
    return (
      <Tabs class={styles.container} onChange={this.handleTabChange} activeKey={this.activeKey}>
        {this.options.map((item) => (
          <Tabs.TabPane key={item.key} tab={item.title} disabled={item.disabled}></Tabs.TabPane>
        ))}
      </Tabs>
    );
  },
});

export default TabController;
