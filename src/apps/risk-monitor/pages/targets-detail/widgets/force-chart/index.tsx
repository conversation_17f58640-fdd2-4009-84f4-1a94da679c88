import { computed, defineComponent } from 'vue';
import ECharts from 'vue-echarts';
import 'echarts/lib/chart/graph';
import 'echarts/lib/component/title';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/legend';

import styles from './force-chart.module.less';

const exampleData = {
  nodes: [
    {
      id: '0',
      name: '乐视网信息技术（北京）股份有限公司',
      value: 28.685715,
      category: 0,
      symbolSize: 56,
    },
    {
      id: '1',
      name: '乐视云计算有限公司',
      value: 4,
      category: 0,
    },
    {
      id: '2',
      name: '乐视控股（北京）有限公司',
      value: 9.485714,
      category: 1,
    },
    {
      id: '3',
      name: '乐视影业（北京）有限公司',
      value: 9.485714,
      category: 1,
    },
    {
      id: '4',
      name: '乐视体育文化产业发展（北京）有限公司',
      value: 4,
      category: 0,
    },
    {
      id: '5',
      name: '乐视虚拟现实科技（北京）有限公司',
      value: 4,
      category: 0,
    },
    {
      id: '6',
      name: '乐视电子商务（北京）有限公司',
      value: 4,
      category: 0,
    },
    {
      id: '7',
      name: '乐视游戏科技（北京）有限公司',
      value: 4,
      category: 0,
    },
    {
      id: '8',
      name: '武汉目明乐视健康科技有限公司',
      value: 4,
      category: 0,
    },
    {
      id: '9',
      name: '霍尔果斯乐视新生代文化传媒有限公司',
      value: 4,
      category: 0,
    },
    {
      id: '10',
      name: '乐视移动智能信息技术（北京）有限公司',
      value: 4,
      category: 1,
    },
    {
      id: '11',
      name: '青岛乐弘基金销售有限公司',
      value: 100,
      category: 1,
    },
    {
      id: '12',
      name: '北京智驿信息技术有限责任公司',
      value: 6.742859,
      category: 1,
    },
    {
      id: '13',
      name: '乐融致新电子科技（天津）有限公司',
      value: 4,
      category: 1,
    },
    {
      id: '14',
      name: '北京优木乐视科技有限公司',
      value: 4,
      category: 1,
    },
    {
      id: '15',
      name: '重庆乐视小额贷款有限公司',
      value: 4,
      category: 1,
    },
    {
      id: '16',
      name: '深圳市乐视视频技术有限公司',
      value: 25.942856,
      category: 2,
    },
    {
      id: '17',
      name: '开承体育文化产业（重庆）有限公司',
      value: 20.457146,
      category: 2,
    },
    {
      id: '18',
      name: '乐视汽车（北京）有限公司',
      value: 20.457146,
      category: 2,
    },
    {
      id: '19',
      name: '乐卡汽车智能科技（北京）有限公司',
      value: 20.457146,
      category: 2,
    },
    {
      id: '20',
      name: '宁波乐士集团有限公司',
      value: 20.457146,
      category: 2,
    },
  ],
  links: [
    {
      source: '0',
      target: '1',
    },
    {
      source: '0',
      target: '2',
    },
    {
      source: '0',
      target: '3',
    },
    {
      source: '0',
      target: '4',
    },
    {
      source: '0',
      target: '5',
    },
    {
      source: '0',
      target: '6',
    },
    {
      source: '0',
      target: '7',
    },
    {
      source: '0',
      target: '8',
    },
    {
      source: '0',
      target: '9',
    },
    {
      source: '0',
      target: '10',
    },
    {
      source: '0',
      target: '11',
    },
    {
      source: '0',
      target: '12',
    },
    {
      source: '0',
      target: '13',
    },
    {
      source: '0',
      target: '14',
    },
    {
      source: '0',
      target: '15',
    },
    {
      source: '0',
      target: '16',
    },
    {
      source: '0',
      target: '17',
    },
    {
      source: '0',
      target: '18',
    },
    {
      source: '0',
      target: '19',
    },
    {
      source: '0',
      target: '20',
    },
  ],
  categories: [
    {
      name: '该企业',
    },
    {
      name: '主要人员',
    },
    {
      name: '持股/投资关联',
    },
    {
      name: '历史持股/投资关联',
    },
    {
      name: '历史主要人员',
    },
    {
      name: '分支机构',
    },
    {
      name: '实际控制人',
    },
    {
      name: '控制关系',
    },
    {
      name: '受益所有人',
    },
  ],
};

const getGraphChartOption = () => {
  const chartOption = {
    /** Tooltip */
    tooltip: {
      show: true,
      trigger: 'item',
      textStyle: {
        color: '#333',
        fontSize: 14,
      },
      backgroundColor: 'rgba(255, 255, 255, 1)',
      extraCssText: 'box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2); padding: 8px 12px;',
      formatter: (params, a, b) => {
        console.log(params, a, b);
        return `<div style="display: flex; flex-direction: column; gap: 8px; line-height: 22px;">
          <div>${params.name}</div>
          <div>风险动态: <i style="color: #808080">${params.value}</i></div>
        </div>`;
      },
    },

    /** Legend */
    legend: [
      {
        icon: 'roundRect',
        itemWidth: 10,
        itemHeight: 10,
        data: exampleData.categories.map((item) => item.name),
      },
    ],

    series: [
      {
        type: 'graph',
        layout: 'force',
        animation: false,
        force: {
          initLayout: 'circular',
          gravity: 0,
          friction: 0.2,
          repulsion: 400,
          edgeLength: 180,
        },
        // roam: true, // 是否开启拖拽/缩放
        // 数据
        data: exampleData.nodes,
        edges: exampleData.links.map((item) => ({
          ...item,
          lineStyle: { width: 5 },
          emphasis: { disabled: true },
          // tooltip: { show: false },
        })),
        categories: exampleData.categories,

        label: {
          show: true,
          position: 'inside',
          color: '#333',
          fontSize: 14,
          // distance: 5,
          textBorderWidth: 0,
        },

        // Legend 高亮
        // legendHoverLink: true,

        symbol: 'circle',
        symbolSize: 12,
        // Line
        lineStyle: {
          color: '#d8d8d8',
          width: 1,
        },
      },
    ],

    // 高亮
    emphasis: {
      disabled: false,
      scale: true,
      focus: 'coordinateSystem',
      blurScope: 'series',
      // label: {
      //   position: 'insideLeft',
      // },
    },

    /** Theme */
    color: [
      '#5b8ff9', // 该企业
      '#61ddaa', // 主要人员: 法代+董监高
      '#65789B', // 持股/投资关联
      '#f6bd16', // 历史持股/投资关联 #F6BD16
      '#7262fd', // 历史主要人员： 历史法代+董监高 #7262fd
      '#78d3f8', // 分支机构 #78D3F8
      '#9661bc', // 实际控制人 #9661BC
      '#f6903d', // 控制关系 #F6903D
      '#008685', // 受益所有人 #008685
    ],
  };
  return Object.freeze(chartOption);
};

const GraphChart = defineComponent({
  name: 'GraphChart',
  props: {
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
  },
  setup() {
    const chartOptions = computed(() => {
      return getGraphChartOption();
    });
    return {
      chartOptions,
    };
  },
  render() {
    const { width, height, chartOptions } = this;
    return (
      <div
        class={styles.container}
        style={{
          width,
          height,
        }}
      >
        <ECharts options={chartOptions} />
      </div>
    );
  },
});

export default GraphChart;
