export const TabSetting = [
  {
    title: '自身动态',
    key: 'self',
    disabled: false,
  },
  {
    title: '关联方动态',
    key: 'related',
    disabled: false,
  },
];

export const getTableColumns = ({ isSelf = true }) => {
  const columns = [
    {
      title: '企业名称',
      scopedSlots: { customRender: 'expandedTitleRender' },
    },
    {
      title: '风险等级',
      width: 120,
      dataIndex: 'riskLevel',
      scopedSlots: {
        filterDropdown: 'filterDropdown',
        filterIcon: 'filterIcon',
        customRender: 'MonitorResult',
      },
    },
    {
      title: '风险类型',
      width: 240,
      dataIndex: 'metricsName',
      scopedSlots: {
        filterDropdown: 'filterDropdown',
        filterIcon: 'filterIcon',
      },
    },
    {
      title: '风险内容',
      scopedSlots: { customRender: 'MonitorContent' },
    },
    {
      title: '更新日期',
      width: 180,
      sorter: true,
      dataIndex: 'createDate',
      scopedSlots: {
        customRender: 'date',
      },
      dateProps: {
        pattern: 'YYYY-MM-DD HH:mm:ss',
        defaultVal: '-',
        x1000: false,
      },
    },
  ];
  return columns.slice(Number(isSelf));
};
