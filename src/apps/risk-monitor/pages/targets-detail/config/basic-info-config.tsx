import { numberToHuman } from '@/utils/number-formatter';

export const basicInfoConfig = [
  {
    // label: '法定代表人',
    key: 'MultipleOper.OperType',
    scopeSlots: {
      labelRender: 'operTypeLabel',
      contentRender: 'operTypeContent',
    },
  },
  {
    key: 'RegistCapi',
    render: (data) => {
      return data ? numberToHuman(data) : '-';
    },
    scopeSlots: {
      labelRender: 'registCapiLabel',
    },
  },
  {
    label: '成立日期',
    key: 'StartDate',
    scopeSlots: 'StartDate',
    dateProps: {
      pattern: 'YYYY-MM-DD HH:mm:ss',
      defaultVal: '-',
      x1000: false,
    },
  },
];

// 计算命中的总数
export const calcTotalHits = (record) => {
  let totalHits = 0;
  const { metricsContent } = record;
  const { metricScorePO } = metricsContent || {};
  const { hitDetails, otherHitDetails = [] } = metricScorePO || {};

  // 主要策略和其他策略总命中数大于1就显示更多
  totalHits = [hitDetails, ...otherHitDetails].reduce((acc, cur) => acc + cur.totalHits, 0);
  return totalHits;
};
