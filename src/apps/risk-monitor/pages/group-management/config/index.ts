import { DEFAULT_DATE_RANGE } from '@/config/tender.config';

export const TABLE_COLUMNS = [
  {
    title: '组别名称',
    width: 169,
    dataIndex: 'name',
    scopedSlots: { customRender: 'groupName' },
  },
  {
    title: '组内企业数量',
    width: 107,
    sorter: true,
    dataIndex: 'companyCount',
  },
  {
    title: '模型名称',
    width: 169,
    scopedSlots: { customRender: 'modelConfig' },
  },
  {
    title: '监控状态',
    width: 107,
    dataIndex: 'monitorStatus',
    scopedSlots: { customRender: 'monitorStatus' },
  },
  {
    title: '操作人',
    dataIndex: 'updateBy',
    scopedSlots: { customRender: 'operatorName' },
    width: 104,
  },
  {
    title: '更新时间',
    width: 165,
    sorter: true,
    dataIndex: 'updateDate',
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD HH:mm:ss',
      defaultVal: '-',
      x1000: false,
    },
  },
  {
    title: '操作',
    width: 156,
    scopedSlots: { customRender: 'operation' },
  },
];

export const getFilterConfig = ({ monitorModel, monitorStatus, operator }) => [
  {
    field: 'filters',
    label: '筛选条件',
    type: 'groups',
    children: [
      {
        field: 'monitorModelIds',
        type: 'multiple',
        label: '模型名称',
        options: monitorModel,
        layout: 'inline',
      },
      {
        field: 'monitorStatus',
        type: 'multiple',
        label: '监控状态',
        options: monitorStatus,
        layout: 'inline',
      },
      {
        field: 'operatorIds',
        type: 'multiple',
        label: '操作人',
        options: operator,
        layout: 'inline',
      },
      {
        field: 'updateDate',
        type: 'single',
        label: '更新时间',
        options: [{ value: undefined, label: '不限' }, ...DEFAULT_DATE_RANGE],
        custom: {
          type: 'date-range',
        },
      },
    ],
  },
];
