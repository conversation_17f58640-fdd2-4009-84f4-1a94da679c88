import { computed, ref, Ref, watch } from 'vue';
import { isEmpty } from 'lodash';

import { useRequest } from '@/shared/composables/use-request';
import { monitor as monitorService } from '@/shared/services';

export const useSearchGroups = (
  filterValues: Ref<{ filters: Record<string, any>; keywords?: string }>,
  sortInfo: Ref<{ sortField?: string; isSortAsc?: boolean }>
) => {
  const searchGroups = useRequest(monitorService.getAllGroups);

  const pagination = ref({
    pageSize: 10,
    current: 1,
    total: 0,
  });

  // 是否达到上限，不能用totalount来判断，因为totalount是服务端返回的总数，可能不准确
  const isLimited = ref(false);
  const search = async (payload = {}) => {
    const res = await searchGroups.execute<any>({
      pageIndex: pagination.value.current,
      pageSize: pagination.value.pageSize,
      needAggs: 1,
      ...filterValues.value.filters,
      keywords: filterValues.value.keywords,
      updateDate: filterValues.value.filters?.updateDate ? [filterValues.value.filters.updateDate] : undefined,
      ...sortInfo.value,
      ...payload,
    });
    pagination.value.total = res.total;
    pagination.value.current = res.pageIndex;
    pagination.value.pageSize = res.pageSize;
    // 初始状态下
    if (!filterValues.value.keywords && isEmpty(filterValues.value.filters)) {
      isLimited.value = res.total >= 50;
    }
  };

  const dataSource = computed(() => {
    return searchGroups.data.value?.data ?? [];
  });

  watch(
    () => filterValues.value,
    () => {
      pagination.value.current = 1;
      search();
    },
    { deep: true, immediate: true }
  );

  return {
    isLimited,
    search,
    dataSource,
    pagination,
    data: searchGroups.data,
    isLoading: searchGroups.isLoading,
  };
};
