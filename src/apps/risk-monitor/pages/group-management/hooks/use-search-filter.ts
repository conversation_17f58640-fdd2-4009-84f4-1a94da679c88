import { computed, onMounted, reactive, ref } from 'vue';

import { getFilterConfig } from '@/apps/risk-monitor/pages/group-management/config';
import { user as userService } from '@/shared/services';
import { get } from 'lodash';

export const useSearchFilter = () => {
  const isInit = ref(true);

  const operatorList = ref([]);
  const getOperatorList = async () => {
    const data = await userService.getUserList();
    operatorList.value = data.concat([{ userId: -1, name: '系统自动创建' }]);
  };

  const defaultFilterValues = Object.freeze({
    keywords: undefined,
    filters: {},
  });

  const filterValues = ref({
    ...defaultFilterValues,
  });

  const sortInfo = ref<{ sortField?: string; isSortAsc?: boolean }>({
    sortField: undefined,
    isSortAsc: undefined,
  });

  const aggsFilterOptions = reactive({
    monitorModel: [],
    operator: [],
    monitorStatus: [],
  });
  const formatFilterOptions = reactive<{
    monitorModel: any[];
    operator: any[];
    monitorStatus: any[];
  }>({
    monitorModel: [],
    operator: [],
    monitorStatus: [],
  });

  const filterGroups = computed(() => {
    return getFilterConfig(formatFilterOptions);
  });

  const handleFilterChange = (values) => {
    filterValues.value = {
      ...defaultFilterValues,
      ...values,
    };
  };

  const handleFilterReset = () => {
    filterValues.value = { ...defaultFilterValues };
  };

  const isFilterLoading = ref(false);
  const getFilterOptions = (config?, showAll = false) => {
    if (showAll) {
      isFilterLoading.value = true;
      return;
    }
    formatFilterOptions.monitorModel = aggsFilterOptions.monitorModel.map((v: any) => {
      return {
        ...v,
        label: v.modelName,
        value: v.monitorModelId,
        count: +v.count,
      };
    });
    formatFilterOptions.operator = aggsFilterOptions.operator.map((v: any) => {
      const target = operatorList.value.find((item: any) => item.userId === v.operator);
      const label = get(target, 'name', '-');
      return {
        ...v,
        label,
        value: v.operator,
        count: +v.count,
      };
    });
    formatFilterOptions.monitorStatus = aggsFilterOptions.monitorStatus.map((v: any) => {
      return {
        ...v,
        label: v.monitorStatus === 1 ? '开启' : '关闭',
        value: v.monitorStatus,
        count: +v.count,
      };
    });
  };

  onMounted(async () => {
    await getOperatorList();
    isInit.value = false;
  });

  return {
    sortInfo,
    filterValues,
    filterGroups,
    handleFilterChange,
    handleFilterReset,
    aggsFilterOptions,
    getFilterOptions,
    operatorList,
    isFilterLoading,
    isInit,
  };
};
