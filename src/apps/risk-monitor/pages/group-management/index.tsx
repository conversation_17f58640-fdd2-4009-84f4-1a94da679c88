import { defineComponent, watch } from 'vue';
import { Breadcrumb } from 'ant-design-vue';

import HeroicLayout from '@/shared/layouts/heroic';
import CommonSearchFilter from '@/components/common/common-search-filter';
import QCard from '@/components/global/q-card';
import { convertSortStructure } from '@/utils/data-type/convert-sort-structure';

import { useSearchGroups } from './hooks/use-search-groups';
import { useSearchFilter } from './hooks/use-search-filter';
import { TABLE_COLUMNS } from './config';
import SearchResult from './widgets/search-result';

const GroupManagement = defineComponent({
  name: 'GroupManagement',
  setup() {
    const {
      sortInfo,
      filterValues,
      filterGroups,
      handleFilterChange,
      handleFilterReset,
      operatorList,
      getFilterOptions,
      aggsFilterOptions,
      isFilterLoading,
      isInit,
    } = useSearchFilter();
    const { pagination, isLimited, dataSource, isLoading, search, data } = useSearchGroups(filterValues, sortInfo);

    watch(
      () => data.value?.aggsRes,
      (val) => {
        if (val) {
          Object.assign(aggsFilterOptions, val);
        }
      },
      { deep: true, immediate: true }
    );

    watch([isFilterLoading, isLoading], () => {
      if (isFilterLoading.value && !isLoading.value) {
        getFilterOptions();
        isFilterLoading.value = false;
      }
    });

    return {
      sortInfo,
      isLimited,
      filterValues,
      filterGroups,
      pagination,
      dataSource,
      isLoading,
      search,
      handleFilterChange,
      handleFilterReset,
      operatorList,
      getFilterOptions,
      isInit,
    };
  },
  render() {
    return (
      <div>
        <Breadcrumb class="sticky-breadcrumb">
          <Breadcrumb.Item>
            <router-link to={`/risk-monitor/targets`}>
              <q-icon type="icon-mianbaoxiefanhui"></q-icon>
              监控企业
            </router-link>
          </Breadcrumb.Item>
          <Breadcrumb.Item class="lastItem">{this.$route.meta?.title}</Breadcrumb.Item>
        </Breadcrumb>
        <HeroicLayout loading={this.isInit} innerStyle={{ minHeight: 'calc(100vh - 53px - 60px)' }}>
          <QCard
            slot="hero"
            title={this.$route.meta?.title}
            bodyStyle={{
              paddingTop: 0,
            }}
          >
            <CommonSearchFilter
              placeholder="请输入组别名称"
              isRemoteSearch={true}
              filterConfig={this.filterGroups}
              onChange={this.handleFilterChange}
              defaultValue={this.filterValues}
              // onReset={this.handleFilterReset}
              onGetOptions={this.getFilterOptions}
            />
          </QCard>
          <SearchResult
            isLoading={this.isLoading}
            rowKey="monitorGroupId"
            searchKey={this.filterValues.keywords}
            isLimited={this.isLimited}
            columns={TABLE_COLUMNS}
            dataSource={this.dataSource}
            pagination={this.pagination}
            operatorList={this.operatorList}
            on={{
              changePage: (pageIndex: number, pageSize: number) => {
                this.search({ pageIndex, pageSize });
              },
              refresh: (backToFirstPage: boolean) => {
                this.search(backToFirstPage ? { pageIndex: 1 } : undefined);
              },
              sorterChange: (sorter) => {
                this.pagination.current = 1;
                this.sortInfo = convertSortStructure(sorter);
                this.search();
              },
            }}
          />
        </HeroicLayout>
      </div>
    );
  },
});

export default GroupManagement;
