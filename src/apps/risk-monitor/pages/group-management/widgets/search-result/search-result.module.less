.off,
.on {
  display: flex;
  align-items: center;
}

.dot(@color) {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: @color;
  margin-right: 5px;
}

.off::before {
  .dot(#808080);
}

.on::before {
  .dot(#4DAA6C);
}

.emphasis em {
  color: #F04040;
}

.btn-group {
  display: flex;
  align-items: center;

  :global {
    .ant-divider + .ant-divider {
      display: none;
    }
  }
}