import { computed, onMounted, reactive, ref } from 'vue';
import { message } from 'ant-design-vue';
import { useSearch } from '@/shared/composables/use-search';
import { batchImport } from '@/shared/services';

export const useUploadConfirm = (batchId: number, pageType: string) => {
  const init = ref(false);
  const selectedIds = ref([]);
  const pagination = reactive({
    pageSize: 10,
    current: 1,
    total: 0,
  });
  const statistic = reactive({
    duplicatedCount: 0,
    errorCount: 0,
    successCount: 0,
    failedEmptyCount: 0,
  });

  const dataSource = ref([]);

  const searchRequest = async () => {
    if (!batchId) {
      message.error('请先上传企业');
      return;
    }
    const res = await batchImport.getUploadData(
      {
        batchId: batchId,
        pageSize: pagination.pageSize,
        pageIndex: pagination.current,
      },
      pageType
    );
    dataSource.value = res.data || [];
    pagination.current = res.pageIndex || 0;
    pagination.total = res.total || 0;
    pagination.pageSize = res.pageSize || 0;
    Object.assign(statistic, res.statistic);
  };
  const { search, isLoading } = useSearch(searchRequest);

  const pageChange = (current, pageSize) => {
    pagination.current = current;
    pagination.pageSize = pageSize;
    search();
  };

  onMounted(async () => {
    if (!batchId) {
      message.error('请先上传企业');
    } else {
      await search();
    }
    init.value = false;
  });
  return {
    init,
    dataSource,
    isLoading,
    pagination,
    selectedIds,
    statistic,
    search,
    pageChange,
  };
};
