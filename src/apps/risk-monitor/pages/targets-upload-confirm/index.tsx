import { computed, defineComponent, ref, unref } from 'vue';
import { <PERSON>read<PERSON>rumb, Button, message } from 'ant-design-vue';

import { batchImport } from '@/shared/services';
import HeroicLayout from '@/shared/layouts/heroic';

import { isLatestPage } from '@/utils/pagination';
import { useUploadConfirm } from './use-upload-confirm';
import { useRouter } from 'vue-router/composables';
import { getRouterConfig } from '@/apps/risk-monitor/pages/targets-upload-confirm/config';
import QCard from '@/components/global/q-card';
import ResultCountInfo from '@/shared/components/result-count-info';
import BatchConfirmResult from '@/shared/components/batch-confirm-result';

import styles from './upload-confirm.module.less';

const TargetsUploadConfirm = defineComponent({
  name: 'TargetsUploadConfirm',
  props: {
    pageType: {
      type: String,
      required: true,
    },
    batchId: {
      type: Number,
      required: true,
    },
    groupId: {
      type: Number,
      required: true,
    },
  },
  setup(props) {
    const router = useRouter();
    const pageConfig = computed(() => getRouterConfig(props.pageType));

    const { init, isLoading, dataSource, pagination, pageChange, search, statistic, selectedIds } = useUploadConfirm(
      props.batchId,
      props.pageType
    );
    const btnLoading = ref(false);

    const isDisabled = computed(() => statistic.successCount <= 0);
    const filteredCount = computed(() => {
      return (statistic.failedEmptyCount ?? 0) + (statistic.duplicatedCount ?? 0);
    });

    // 是否需要返回第一页
    const needGoFirstPage = computed(() => {
      const delLength = selectedIds.value.length || 1;
      return isLatestPage(pagination) && dataSource.value.length <= delLength;
    });

    const updateData = (data) => {
      batchImport.updateCompany(data, props.pageType).then(() => {
        message.success('修改成功');
        search();
      });
    };

    const handleDelete = async (flag = false) => {
      const param = flag
        ? {
            flag,
            batchId: props.batchId,
          }
        : {
            itemIds: unref(selectedIds),
            batchId: props.batchId,
          };
      await batchImport.deleteCompany(param, props.pageType);
      message.success('移除成功');
      if (needGoFirstPage.value) {
        pagination.current = 1;
      }
      selectedIds.value = [];
      search();
    };

    const executeBatch = () => {
      btnLoading.value = true;
      batchImport
        .executeBatch({ monitorImportId: props.batchId, monitorGroupId: props.groupId }, props.pageType)
        .then(() => {
          message.success('批量任务已执行！请稍后在批量排查任务中查看排查结果');
          router.push(pageConfig.value.backRoute);
        })
        .finally(() => {
          btnLoading.value = false;
        });
    };

    const execute = async () => {
      if (unref(btnLoading)) {
        return;
      }
      if (statistic.errorCount > 0) {
        btnLoading.value = true;
        await batchImport.deleteCompany(
          {
            flag: true,
            batchId: props.batchId,
          },
          props.pageType
        );
      }
      executeBatch();
    };

    return {
      init,
      dataSource,
      isLoading,
      pagination,
      selectedIds,
      statistic,
      updateData,
      search,
      handleDelete,
      pageChange,
      execute,
      pageConfig,
      btnLoading,
      isDisabled,
      filteredCount,
    };
  },
  render() {
    const pagination = {
      ...this.pagination,
      onChange: this.pageChange,
      onShowSizeChange: this.pageChange,
    } as any;
    return (
      <div>
        <Breadcrumb class="sticky-breadcrumb">
          <Breadcrumb.Item>
            <router-link to={this.pageConfig.backRoute}>
              <q-icon type="icon-mianbaoxiefanhui"></q-icon> {this.pageConfig.breadLabel}
            </router-link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>企业核实</Breadcrumb.Item>
        </Breadcrumb>
        <HeroicLayout loading={this.init} innerStyle={{ minHeight: 'calc(100vh - 53px - 60px)' }}>
          <QCard bodyStyle={{ padding: '12px 16px 16px' }}>
            <div class="flex justify-between items-center">
              <div class={styles.title}>{this.$route.meta?.title}</div>
              {/*<Button
                disabled={this.statistic.errorCount <= 0}
                onClick={() => {
                  this.handleDelete(true);
                }}
              >
                移除匹配失败数据
              </Button>*/}
            </div>
            <ResultCountInfo
              style={{ margin: '4px 0 12px' }}
              config={[
                { prefix: '当前匹配成功', suffix: '家企业，', count: this.statistic.successCount, theme: 'success' },
                { prefix: '匹配失败', suffix: '条数据，', count: this.statistic.errorCount, theme: 'fail' },
                { prefix: '已过滤掉', suffix: '条重复或必填项为空的数据', count: this.filteredCount, theme: 'warning' },
              ]}
              isLoading={this.isLoading}
            />
            <BatchConfirmResult
              dataSource={this.dataSource}
              columns={this.pageConfig.columns}
              pagination={pagination}
              loading={this.isLoading}
              onSelect={(values) => {
                this.selectedIds = values.map((item) => {
                  return item.id;
                });
              }}
              onChange={(data) => {
                this.updateData(data);
              }}
              onUpdate={() => {
                this.search();
              }}
              onDelete={(ids) => {
                this.selectedIds = ids;
                this.handleDelete();
              }}
            ></BatchConfirmResult>

            <div class={styles.fixbtn}>
              <Button type="primary" disabled={this.isDisabled} onClick={this.execute} loading={this.btnLoading}>
                {this.pageConfig.confirmLabel}
              </Button>
            </div>
          </QCard>
        </HeroicLayout>
      </div>
    );
  },
});

export default TargetsUploadConfirm;
