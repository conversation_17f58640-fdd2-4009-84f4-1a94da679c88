const riskMonitorColumns = [
  {
    title: '企业名称',
    width: 500,
    scopedSlots: {
      customRender: 'company',
    },
  },
  {
    title: '统一社会信用代码',
    width: 214,
    dataIndex: 'compCreditCode',
    scopedSlots: {
      customRender: 'creditCode',
    },
  },
  {
    title: '登记状态',
    width: 200,
    scopedSlots: {
      customRender: 'companyStatus',
    },
  },
  {
    title: '操作',
    width: 88,
    scopedSlots: {
      customRender: 'action',
    },
  },
];

export const RouterConfigMap = {
  monitor: {
    backRoute: { name: 'risk-monitor-targets' },
    breadLabel: '批量导入',
    columns: riskMonitorColumns,
    confirmLabel: '监控匹配成功数据',
  },
};

export const getRouterConfig = (type) => {
  const config = RouterConfigMap[type];
  if (config) {
    return config;
  }
  return RouterConfigMap.monitor;
};
