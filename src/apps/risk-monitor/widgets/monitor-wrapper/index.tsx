import { defineComponent, ref } from 'vue';

import HeroicLayout from '@/shared/layouts/heroic';
import QCard from '@/components/global/q-card';
import QTabs from '@/components/global/q-tabs';
import { useUserStore } from '@/shared/composables/use-user-store';

import GroupSelect from './widgets/group-select';
import styles from './monitor-wrapper.module.less';

const MonitorWrapper = defineComponent({
  name: 'MonitorWrapper',
  props: {
    groups: {
      type: Array,
      default: () => [],
    },
  },
  setup(props, { emit }) {
    const store = useUserStore();
    const { monitorViewType, updateViewType } = store;
    const tabs = ref([
      {
        key: 'timeView',
        label: '时序视图',
      },
      {
        key: 'levelView',
        label: '层级视图',
      },
    ]);

    const groupChange = (data) => {
      emit('groupChange', data);
    };

    const handleTabChange = (v) => {
      updateViewType(v);
    };

    return {
      tabs,
      monitorViewType,
      groupChange,
      handleTabChange,
    };
  },
  render() {
    return (
      <HeroicLayout class={styles.container}>
        <QCard headerStyle={{ padding: '0' }} bodyStyle={{ padding: '0' }}>
          <div slot="title" style={{ padding: '0 16px' }}>
            <div class={styles.tabHeader}>
              {this.groups?.length && <GroupSelect groupList={this.groups} onGroupIdChange={this.groupChange} />}
              {this.$slots.extra ? <div class={styles.extra}>{this.$slots.extra}</div> : null}
            </div>
            <QTabs
              value={this.monitorViewType}
              tabs={this.tabs}
              style={{ margin: '0 0 -9px 0', height: '40px', lineHeight: '40px' }}
              onChange={this.handleTabChange}
            />
          </div>
          {this.monitorViewType === 'timeView' ? this.$slots.timeView : this.$slots.levelView}
        </QCard>
      </HeroicLayout>
    );
  },
});

export default MonitorWrapper;
