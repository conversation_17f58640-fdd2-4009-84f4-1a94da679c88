import { defineComponent, ref, unref } from 'vue';
import { Dropdown, message, Popconfirm, Popover, Tooltip } from 'ant-design-vue';

import { monitor } from '@/shared/services';
import { openGroupModal } from '@/shared/components/add-group';

import styles from './group-select.module.less';
import { useRouter } from 'vue-router/composables';
import { Permission } from '@/config/permissions.config';

const GroupSelect = defineComponent({
  name: 'GroupSelect',
  props: {
    groupList: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['groupIdChange'],
  setup(props, { emit }) {
    const router = useRouter();
    const selectOption = ref<any>(props.groupList[0]);

    /**
     * 新建分组
     */
    const handleAddGroup = async () => {
      const form: any = await openGroupModal({
        title: '新建分组',
        data: {},
      });
      if (!form) {
        return;
      }
      await monitor.addGroup({
        ...unref(form),
        groupName: unref(form).name.trim(),
      });
      message.success('新增成功！');
    };

    /** 打开分组管理页面 */
    const openGroupManage = async () => {
      router.push({
        path: 'targets/group-management',
      });
    };
    /**
     * 菜单点击
     */
    const handleMenuClick = (group) => {
      selectOption.value = props.groupList.find((item: any) => item.value === group.value);
      emit('groupIdChange', group.value);
    };

    return {
      selectOption,
      handleMenuClick,
      handleAddGroup,
      openGroupManage,
    };
  },
  render() {
    const { selectOption } = this;
    return (
      <Dropdown destroyPopupOnHide={true} trigger={['click']}>
        <div class={styles.selector}>
          <div class={styles.selectorIcon}>
            <q-icon type="icon-switch"></q-icon>
          </div>
          <div class={styles.selectorText}>{selectOption.name}</div>
        </div>

        <div slot="overlay" class={styles.menu}>
          <div class={styles.addGroup}>
            <span>切换企业分组</span>
            <Tooltip title="新建分组" overlayClassName={styles.popToolTip}>
              <q-icon type="icon-a-tianjiaxian" onClick={this.handleAddGroup}></q-icon>
            </Tooltip>
          </div>

          <div class={styles.menuList}>
            {this.groupList?.map((group: any) => (
              <div
                key={group.value}
                class={{
                  [styles.menuItem]: true,
                  [styles.menuItemActive]: group.value === selectOption.value,
                }}
                onClick={() => this.handleMenuClick(group)}
              >
                {group.name}（{group.count}）
              </div>
            ))}
          </div>

          <div class={styles.manageGroup} onClick={this.openGroupManage} v-permission={[Permission.MONITOR_ENTERPRISE_GROUP_MANAGE]}>
            <q-icon type="icon-fenzuguanliicon"></q-icon>
            <span>分组管理</span>
          </div>
        </div>
      </Dropdown>
    );
  },
});

export default GroupSelect;
