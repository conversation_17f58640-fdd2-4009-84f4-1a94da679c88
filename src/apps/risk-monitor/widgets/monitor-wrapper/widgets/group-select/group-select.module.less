.selector {
    display: flex;
    align-items: center;
    font-size: 18px;
    color: #333;


    .selectorIcon {
        width: 22px;
        height: 22px;
        margin-right: 8px;
        border: 1px solid #EEE;
        border-radius: 2px;
        cursor: pointer;
        text-align: center;
        transform: translateY(1px);

        :global {
            .anticon {
                color: #128bed;
            }
        }

        &:hover {
            background-color: #E2F1FD;
            border: 1px solid rgba(18, 139, 237, 0.4)
        }
    }

    .selectorText {
        font-weight: bold;
    }
}

.menu {
    width: 300px;
    border-radius: 4px;
    border: 1px solid #eee;
    background: #fff;
    padding-bottom: 8px;

    .addGroup {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 48px;
        padding: 13px 16px;
        border-bottom: 1px solid #eee;

        >span {
            font-weight: bold;
        }

        :global {
            .anticon:hover {
                color: #128BED;
            }
        }
    }

    .menuList {
        max-height: 408px;
        overflow: auto;
        position: relative;
        padding: 8px 12px;

        .menuItem {
            height: 36px;
            line-height: 36px;
            padding: 0 8px;
            margin-top: 4px;
            cursor: pointer;

            &:hover {
                background-color: #E5F2FD;
                border-radius: 4px;
            }

            &:first-child {
                margin-top: 0;
            }
        }

        .menuItemActive {
            color: #128BED;
            background-color: #E5F2FD;
        }
    }

    .manageGroup {
        margin: 0 12px;
        height: 32px;
        line-height: 30px;
        text-align: center;
        background: #fff;
        border: 1px solid #eee;
        cursor: pointer;

        &:hover {
            color: #128BED;
            border-color: #128BED;
        }
    }

}

.popToolTip {
    :global {
        .ant-tooltip-inner {
            background-color: #fff;
            color: #333;
        }
    }
}
