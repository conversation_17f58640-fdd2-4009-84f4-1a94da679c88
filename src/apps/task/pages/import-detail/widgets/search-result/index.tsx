import { defineComponent, PropType } from 'vue';
import QRichTable from '@/components/global/q-rich-table';
import QEntityLink from '@/components/global/q-entity-link';

enum ImportStatus {
  Undo = 0,
  Success = 1,
  Failed = 3,
  Duplicate = 2,
}

const ImportStatusMap = {
  [ImportStatus.Undo]: {
    text: '失败',
    color: '#F04040',
  },
  [ImportStatus.Success]: {
    text: '成功',
    color: '#333333',
  },
  [ImportStatus.Failed]: {
    text: '失败',
    color: '#F04040',
  },
  [ImportStatus.Duplicate]: {
    text: '失败',
    color: '#F04040',
  },
};

const ImportDetailColumns = [
  {
    title: '企业名称',
    width: 371,
    scopedSlots: {
      customRender: 'company',
    },
  },
  {
    title: '统一社会信用代码',
    width: 307,
    dataIndex: 'compCreditCode',
  },
  {
    title: '导入结果',
    width: 307,
    dataIndex: 'isSuccess',
    scopedSlots: {
      customRender: 'importResult',
    },
  },
];

const SearchResult = defineComponent({
  name: 'SearchResult',
  props: {
    dataSource: {
      type: Array,
      default: () => [],
    },
    pagination: {
      type: Object as PropType<{ current: number; pageSize: number; total: number }>,
      default: () => ({}),
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { emit }) {
    const handlePageChange = (current, pageSize) => {
      emit('pageChange', { current, pageSize });
    };

    return {
      handlePageChange,
    };
  },
  render() {
    const paginationProps = {
      ...this.pagination,
      onChange: this.handlePageChange,
      onShowSizeChange: this.handlePageChange,
    };

    return (
      <QRichTable
        loading={this.isLoading}
        rowKey={'id'}
        dataSource={this.dataSource}
        columns={ImportDetailColumns}
        emptySize={'100px'}
        emptyMinHeight={'calc(100vh - 52px - 40px - 109px - 10px - 50px - 30px - 40px - 15px )'}
        customScroll={{ x: false, y: 'calc(100vh - 310px)' }}
        pagination={paginationProps}
        scopedSlots={{
          importResult: (code) => {
            return <span style={{ color: ImportStatusMap[code]?.color }}>{ImportStatusMap[code]?.text || '-'}</span>;
          },
          company: (record) => {
            if (record.isSuccess === ImportStatus.Success) {
              return <QEntityLink ellipsis={false} coyObj={{ KeyNo: record.companyId, Name: record.companyName }} />;
            }
            return (
              <div>
                <div>{record.companyName}</div>
                <em class="text-#f04040">{record.errorMsg || '系统异常，请稍后再试'}</em>
              </div>
            );
          },
        }}
      />
    );
  },
});

export default SearchResult;
