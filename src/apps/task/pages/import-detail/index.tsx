import { defineComponent, onMounted, reactive, ref, unref } from 'vue';
import { <PERSON><PERSON><PERSON>rumb, But<PERSON>, message } from 'ant-design-vue';
import HeroicLayout from '@/shared/layouts/heroic';
import QCard from '@/components/global/q-card';
import ResultCountInfo from '@/shared/components/result-count-info';
import styles from '@/apps/identity-verification/pages/identity-verification-verify/identity-verification-verify.module.less';
import SearchResult from '@/apps/task/pages/import-detail/widgets/search-result';
import { batchImport } from '@/shared/services';
import { useFetchState } from '@/hooks/use-fetch-state';

const ImportDetailPage = defineComponent({
  name: 'ImportDetailPage',
  props: {
    moduleType: {
      type: String,
      required: false,
    },
    batchId: {
      type: Number,
      required: true,
    },
  },
  setup(props) {
    const init = ref(true);
    const statistic = reactive({
      successCount: 0,
      errorCount: 0,
      paidCount: 0,
    });

    const pagination = reactive({
      pageSize: 10,
      current: 1,
      total: 0,
    });

    const fetchData = async () => {
      const res = await batchImport.getResult({
        pageSize: pagination.pageSize,
        pageIndex: pagination.current,
        batchId: props.batchId,
      });
      pagination.total = res.total;
      pagination.current = res.pageIndex;
      Object.assign(statistic, res?.statistic);
      return res;
    };

    const { isLoading, result, execute } = useFetchState(fetchData);

    const handleExportFailed = async () => {
      const res = await batchImport.exportFailedMonitor({
        batchId: props.batchId,
        isSuccessList: [0, 2, 3],
      });
      message.success('正在导出，稍后可前往任务列表查看进度');
      return res;
    };

    const failedExport = useFetchState(handleExportFailed);

    onMounted(async () => {
      await execute();
      init.value = false;
    });

    return {
      init,
      statistic,
      isLoading,
      result,
      pagination,
      execute,
      failedExport,
    };
  },
  render() {
    return (
      <div>
        <Breadcrumb class="sticky-breadcrumb">
          <Breadcrumb.Item>
            <router-link to="/tasklist/import-task-list">
              <q-icon type="icon-mianbaoxiefanhui" />
              <span>导入任务</span>
            </router-link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>{this.$route.meta?.title}</Breadcrumb.Item>
        </Breadcrumb>
        <HeroicLayout loading={this.init} innerStyle={{ minHeight: 'calc(100vh - 53px - 60px)' }}>
          <QCard bodyStyle={{ padding: '16px' }}>
            <div class="flex justify-between items-center">
              <div class={styles.title}>{this.$route.meta?.title}</div>
              <Button
                disabled={this.statistic.errorCount <= 0}
                loading={unref(this.failedExport.isLoading)}
                onClick={this.failedExport.execute}
              >
                导出全部失败数据
              </Button>
            </div>
            <ResultCountInfo
              style={{ margin: '4px 0 12px' }}
              config={[
                { prefix: '当前导入成功', suffix: '家企业，', count: this.statistic.successCount, theme: 'success' },
                { prefix: '导入失败', suffix: '条数据，', count: this.statistic.errorCount, theme: 'fail' },
                { prefix: '消耗额度', count: this.statistic.paidCount },
              ]}
              isLoading={this.isLoading}
            />
            <SearchResult
              isLoading={this.isLoading}
              dataSource={this.result?.data}
              pagination={this.pagination}
              onPageChange={({ current, pageSize }) => {
                this.pagination.current = current;
                this.pagination.pageSize = pageSize;
                this.execute();
              }}
            />
          </QCard>
        </HeroicLayout>
      </div>
    );
  },
});

export default ImportDetailPage;
