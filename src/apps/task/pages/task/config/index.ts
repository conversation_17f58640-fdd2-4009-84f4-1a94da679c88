import moment from 'moment';
import { cloneDeep } from 'lodash';

import type { Option } from '@/components/global/q-select/interface';
import type { IQRichTableColumn } from '@/components/global/q-rich-table';
import { DEFAULT_DATE_RANGE } from '@/config/tender.config';
import { hasPermission } from '@/shared/composables/use-permission';
import { Permission } from '@/config/permissions.config';
import { BatchBusinessTypeEnums } from '@/config/batch.config';

export const BUSINESS_TYPE = [{ value: BatchBusinessTypeEnums.DiligencePDFExport, label: '风险洞察报告' }];

export const BUSINESS_TYPE_VALUES = BUSINESS_TYPE.map((item) => item.value);

// 导入任务类型
export const identityVerificationTypeOption = [
  // { value: 10, label: '批量排查-文件导入' },
  // { value: 11, label: '批量排查-公司ID' },
  // { value: 12, label: '批量排查-合作伙伴选择' },
  // { value: 13, label: '批量排查-合作伙伴选择' },
  { value: BatchBusinessTypeEnums.VerificationRegularImport, label: '人企核验-常规核验' },
  { value: BatchBusinessTypeEnums.VerificationDeepImport, label: '人企核验-深度核验' },
];

export const importTaskTypeOption = [{ value: BatchBusinessTypeEnums.MonitorImport, label: '监控企业-批量导入' }];

// 导出任务类型
export const ExportTaskTypeOption = [
  {
    value: BatchBusinessTypeEnums.VerificationResultRegularExport,
    label: '人企核验-常规核验结果-导出',
    permissionCode: [Permission.IDENTITY_VERIFICATION_CHECK],
  },
  {
    value: BatchBusinessTypeEnums.VerificationResultDeepExport,
    label: '人企核验-深度核验结果-导出',
    permissionCode: [Permission.IDENTITY_VERIFICATION_CHECK],
  },
  {
    value: BatchBusinessTypeEnums.VerificationRecordExport,
    label: '人企核验-核验记录-导出',
    permissionCode: [Permission.IDENTITY_VERIFICATION_CHECK],
  },
  {
    value: BatchBusinessTypeEnums.VerificationPackageExport,
    label: '人企核验-消费详情-导出',
    permissionCode: [Permission.IDENTITY_VERIFICATION_CHECK],
  },
  {
    value: BatchBusinessTypeEnums.MonitorFailedExport,
    label: '监控企业-导入结果-导出',
    permissionCode: [Permission.MONITOR_ENTERPRISE_ADD],
  },
];
export const getLabelByValue = (value: number | string, arr: any[] = []) => {
  const type = arr.find((v) => v.value === value);
  if (!type) return '';
  return type.label;
};

// 导出列表表格列
export const ExportTableColumns = [
  {
    title: '任务类型',
    dataIndex: 'businessType',
    width: 140,
    customRender: (item) => {
      return getLabelByValue(item, ExportTaskTypeOption) || '-';
    },
  },
  {
    title: '任务名称',
    width: 240,
    scopedSlots: {
      customRender: 'fileDownload',
    },
  },
  {
    title: '任务状态',
    width: 120,
    scopedSlots: {
      customRender: 'taskStatus',
    },
  },
  {
    title: '操作时间',
    sorter: true,
    width: 170,
    dataIndex: 'createDate',
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD HH:mm:ss',
      defaultVal: '-',
      x1000: false,
    },
  },
  {
    title: '操作人',
    width: 100,
    dataIndex: 'createBy',
    scopedSlots: {
      customRender: 'operator',
    },
  },
];

// 导入列表表格列
export const identityVerificationColumns = [
  {
    title: '任务类型',
    dataIndex: 'businessType',
    width: 140,
    customRender: (item) => {
      return getLabelByValue(item, identityVerificationTypeOption) || '-';
    },
  },
  {
    title: '任务名称',
    width: 240,
    scopedSlots: {
      customRender: 'fileDownload',
    },
  },
  {
    title: '任务状态',
    width: 80,
    scopedSlots: {
      customRender: 'taskStatus',
    },
  },
  {
    title: '操作时间',
    sorter: true,
    width: 160,
    dataIndex: 'createDate',
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD HH:mm:ss',
      defaultVal: '-',
      x1000: false,
    },
  },
  {
    title: '操作人',
    width: 80,
    dataIndex: 'createBy',
    scopedSlots: {
      customRender: 'operator',
    },
  },
  {
    title: '生成时间',
    sorter: true,
    width: 160,
    dataIndex: 'updateDate',
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD HH:mm:ss',
      defaultVal: '-',
      x1000: false,
    },
  },
  {
    title: '操作',
    width: 60,
    scopedSlots: {
      customRender: 'detailAction',
    },
  },
];

export const importTaskTableColumns = [
  {
    title: '任务类型',
    dataIndex: 'businessType',
    width: 140,
    customRender: (item) => {
      return getLabelByValue(item, importTaskTypeOption) || '-';
    },
  },
  {
    title: '文件名称',
    width: 188,
    scopedSlots: {
      customRender: 'fileDownload',
    },
  },
  {
    title: '结果统计',
    width: 240,
    scopedSlots: {
      customRender: 'taskStatistics',
    },
  },
  {
    title: '任务状态',
    width: 92,
    scopedSlots: {
      customRender: 'taskStatus',
    },
  },
  {
    title: '最近操作时间',
    sorter: true,
    width: 160,
    dataIndex: 'createDate',
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD HH:mm:ss',
      defaultVal: '-',
      x1000: false,
    },
  },
  {
    title: '操作人',
    width: 80,
    dataIndex: 'createBy',
    scopedSlots: {
      customRender: 'operator',
    },
  },
  {
    title: '操作',
    width: 80,
    scopedSlots: {
      customRender: 'importAction',
    },
  },
];

// 报告列表表格列
export const ReportTableColumns = [
  {
    title: '报告类型',
    dataIndex: 'businessType',
    width: 106,
    customRender: (item) => {
      return getLabelByValue(item, BUSINESS_TYPE) || '-';
    },
  },
  {
    title: '报告名称',
    dataIndex: 'fileName',
    scopedSlots: {
      customRender: 'reportName',
    },
  },
  {
    title: '任务状态',
    width: 100,
    scopedSlots: {
      customRender: 'taskStatus',
    },
  },
  {
    title: '操作时间',
    width: 165,
    customRender: (item) => {
      return moment(item.createDate).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '操作人',
    width: 100,
    dataIndex: 'createBy',
    scopedSlots: {
      customRender: 'operator',
    },
  },
  {
    title: '生成时间',
    width: 165,
    customRender: (item) => {
      return moment(item.updateDate).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '操作',
    width: 100,
    scopedSlots: {
      customRender: 'taskAction',
    },
  },
];

export const TaskStatus = [
  { value: 0, label: '待生成' },
  { value: 1, label: '生成中' },
  { value: 2, label: '已完成' },
  { value: 3, label: '生成失败' },
];

const BASECONFIG = {
  field: 'filters',
  label: '筛选条件',
  type: 'groups',
  children: [
    {
      field: 'status',
      type: 'multiple',
      label: '任务状态',
      options: [
        { value: 0, label: '待生成' },
        { value: 1, label: '生成中' },
        { value: 2, label: '已完成' },
        { value: 3, label: '生成失败' },
      ],
      layout: 'inline',
    },
    {
      field: 'createUsers',
      type: 'multiple',
      label: '操作人',
      options: [],
      layout: 'inline',
      meta: {
        showFilter: true,
      },
    },
    {
      field: 'createDate',
      type: 'single',
      label: '操作时间',
      options: [{ value: undefined, label: '不限' }, ...DEFAULT_DATE_RANGE],
      custom: {
        type: 'date-range',
      },
    },
  ],
};
export const getConfig = ({ taskField, taskStatus }, operatorList): Array<any> => {
  const config = cloneDeep(BASECONFIG);
  config.children[1].options = operatorList;
  if (taskField) {
    taskField.options = taskField.options
      .map((item) => {
        return {
          ...item,
          hide: item.permissionCode ? !hasPermission(item.permissionCode) : false,
        };
      })
      .filter((item) => !item.hide);
    config.children.unshift(taskField);
  }
  if (taskStatus) {
    const target = config.children.find((item) => item.field === 'ekc');
    if (target) {
      target.options = taskStatus;
    }
  }

  return [config];
};

type dataSetting = {
  taskField: false | Record<string, any>; // 额外的筛选条件
  taskStatus: false | Array<Option>; // 额外的筛选项
  columns?: Array<IQRichTableColumn>; // 表格列
  permissionCode: Array<number>; // 权限点
  excludeFilters: Array<string>; // 表格列
  batchTypes: Array<0 | 1 | 99>; // batchType
  title: string; // 标题
  searchFn?: (payload: Record<string, any>) => Promise<any>; // 搜索方法
};

export const TASK_CONFIG_MAP: Record<string, dataSetting> = {
  'identity-verification': {
    taskField: {
      field: 'businessType',
      type: 'multiple',
      label: '任务类型',
      options: identityVerificationTypeOption,
      layout: 'inline',
    },
    taskStatus: false,
    permissionCode: [],
    excludeFilters: [],
    batchTypes: [0, 99],
    columns: identityVerificationColumns,
    title: '核验任务',
  },
  'import-task-list': {
    taskField: {
      field: 'businessType',
      type: 'multiple',
      label: '任务类型',
      options: importTaskTypeOption,
      layout: 'inline',
    },
    taskStatus: false,
    permissionCode: [],
    excludeFilters: [],
    batchTypes: [0, 99],
    columns: importTaskTableColumns,
    title: '导入任务',
  },
  'export-task-list': {
    taskField: {
      field: 'businessType',
      type: 'multiple',
      label: '任务类型',
      options: ExportTaskTypeOption,
      layout: 'inline',
    },
    taskStatus: false,
    permissionCode: [],
    excludeFilters: [],
    batchTypes: [1],
    columns: ExportTableColumns,
    title: '导出任务',
  },
  'report-task-list': {
    taskField: {
      field: 'businessType',
      type: 'multiple',
      label: '报告类型',
      options: BUSINESS_TYPE,
      layout: 'inline',
    },
    taskStatus: false,
    permissionCode: [],
    excludeFilters: ['businessType'],
    batchTypes: [1],
    columns: ReportTableColumns,
    title: '报告任务',
  },
};
