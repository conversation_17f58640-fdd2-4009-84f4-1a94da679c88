.expired,
.in-wait,
.in-progress,
.success,
.failed {
  display: flex;
  align-items: center;
}

.dot(@color) {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: @color;
  margin-right: 5px;
}

.in-progress::before,
.in-wait::before {
  .dot(#ff8900);
}

.expired::before {
  .dot(#bbb);
}

.success::before {
  .dot(#00ad65);
}

.failed::before {
  .dot(#F04040);
}

.emphasis em {
  color: #F04040;
}

.taskFileName {
  color: #1890ff;
  cursor: pointer;
}

.download-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .download-icon {
    color: #999;
    width: 22px;
    height: 22px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 16px;

    &:hover {
      color: #128BED;
      background: #E2F1FD;
    }
  }
}