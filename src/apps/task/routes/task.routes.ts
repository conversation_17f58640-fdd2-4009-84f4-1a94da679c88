import type { RouteConfig } from 'vue-router';

import SidebarMenuLayout from '@/shared/layouts/sidebar-menu';
import { Permission } from '@/config/permissions.config';

const APP_MENU_CONFIG = [
  {
    key: 'tasklist',
    icon: 'icon-renwu<PERSON><PERSON>o',
    label: 'Tasks List',
    children: [
      {
        key: '/tasklist/identity-verification',
        label: 'Verification Tasks',
      },
      {
        key: '/tasklist/import-task-list',
        label: 'Import Tasks',
      },
      {
        key: '/tasklist/export-task-list',
        label: 'Export tasks',
      },
      {
        key: '/tasklist/report-task-list',
        label: 'Report tasks',
      },
    ],
  },
];

export const taskRoutes = (): RouteConfig[] => [
  {
    path: '/tasklist',
    component: SidebarMenuLayout,
    props: {
      pageTitle: '任务中心',
      menu: APP_MENU_CONFIG,
    },
    meta: {
      title: '任务列表',
    },
    children: [
      {
        path: '',
        redirect: '/tasklist/identity-verification',
      },
      {
        path: 'identity-verification',
        name: 'task-identity-verification',
        component: () => import('../pages/task/index'),
        meta: {
          title: '核验任务',
          permission: [Permission.IDENTITY_VERIFICATION_CHECK, Permission.IDENTITY_VERIFICATION_BATCH],
        },
        props: () => {
          return {
            pageType: 'identity-verification',
          };
        },
      },
      {
        path: 'import-task-list',
        name: 'import-task-list',
        component: () => import('../pages/task/index'),
        meta: {
          title: '导入任务',
          permission: [Permission.MONITOR_ENTERPRISE_ADD],
        },
        props: () => {
          return {
            pageType: 'import-task-list',
          };
        },
      },
      {
        path: 'import-task-list/:batchId',
        name: 'import-task-detail',
        component: () => import('../pages/import-detail'),
        meta: {
          title: '导入结果',
          permission: [Permission.MONITOR_ENTERPRISE_ADD],
        },
        props: (route) => {
          return {
            moduleType: route.query.moduleType?.toString(),
            batchId: parseInt(route.params.batchId.toString(), 10),
          };
        },
      },
      {
        path: 'export-task-list',
        name: 'export-task-list',
        component: () => import('../pages/task/index'),
        meta: {
          title: '导出任务',
          permission: [Permission.IDENTITY_VERIFICATION_EXPORT],
        },
        props: () => {
          return {
            pageType: 'export-task-list',
          };
        },
      },
      {
        path: 'report-task-list',
        name: 'report-task-list',
        component: () => import('../pages/task/index'),
        meta: {
          title: '报告任务',
          permission: [Permission.INVESTIGATION_REPORT],
        },
        props: () => {
          return {
            pageType: 'report-task-list',
          };
        },
      },
    ],
  },
];
