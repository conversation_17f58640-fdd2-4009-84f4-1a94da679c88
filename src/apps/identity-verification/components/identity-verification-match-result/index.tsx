import { defineComponent, PropType, ref } from 'vue';

import QIcon from '@/components/global/q-icon';
import QRichTable from '@/components/global/q-rich-table';
import QEntityLink from '@/components/global/q-entity-link';
import CompanyStatus from '@/components/global/q-company-status';
import { Edge, Node, PathEdge, PathNode } from '@/components/relational-path';
import DropdownButton from '@/components/dropdown-button';

import styles from './identity-verification-match-result.module.less';

// 表格列定义
interface ITableColumn {
  title: string;
  dataIndex?: string;
  key: string;
  width?: number | string;
  scopedSlots?: {
    customRender: string;
  };
}

export interface IRelationPath {
  [key: string]: any;
}

// 关联企业数据类型
export interface IRelatedCompanyData {
  compType: 1 | 2;
  relationPath?: IRelationPath[][];
  verificationDetailId: number;
  verificationId: number;
  compName: string;
  compId: string;
  compCreditCode: string;
  compRegistStatus: string;
  personRole: string;
  verificationResult: number;
  createBy: number;
  updateBy: number;
  createDate: string;
  updateDate: string;
  status: number;
  orgId: number;
}

interface IVerificationDetailRelationPath {
  path: string;
  companyDetail: {
    keyNo: string;
    companyName: string;
  }[];
  [key: string]: any;
}

// 验证结果数据类型
export interface IVerificationDetail {
  verificationDetailId: number;
  verificationId: number;
  compType: number;
  compName: string;
  compId: string;
  compCreditCode: string;
  compRegistStatus: string;
  personRole: string;
  verificationResult: number;
  relationPath: IVerificationDetailRelationPath | null;
  createBy: number;
  updateBy: number;
  createDate: string;
  updateDate: string;
  status: number;
  orgId: number;
}

// 组件属性
export interface IVerificationResultItemProps {
  type: 'company' | 'relatedCompany';
  verificationData: IVerificationDetail[];
}

enum RelationPathDirectionSymbol {
  LEFT = '<-',
  RIGHT = '->',
  BIDIRECTIONAL = '-',
}

/**
 * 获取关系方向
 * @param relation 关系
 * @returns 方向
 */
const getDirection = (content: string): PathEdge['direction'] => {
  if (content.includes(RelationPathDirectionSymbol.RIGHT)) {
    return 'right';
  }
  if (content.includes(RelationPathDirectionSymbol.LEFT)) {
    return 'left';
  }
  return 'bidirectional';
};

/**
 * 解析关系路径文本，将文本转换为关系路径
 * @param relationPath 关系路径
 * @param companyDetail 企业详情映射
 * @example 北京乐漾影视传媒有限公司(100%)->霍尔果斯乐漾影视传媒有限公司<-(50%)北京乐漾影视传媒有限公司
 */
const parseRelationPathText = (relationPath: string, companyDetail: IVerificationDetailRelationPath['companyDetail'] = []) => {
  const paths: (PathNode | PathEdge)[] = [];

  if (!relationPath) {
    return paths;
  }

  const toNode = (content: string): PathNode => {
    // 专业版接口返回的企业名称括号用的是半角，会导致与 companyDetail 中的正确名称匹配不上：乐视网信息技术(北京)股份有限公司 -> 乐视网信息技术(北京)股份有限公司
    const companyName = content.replace(/\(/g, '（').replace(/\)/g, '）');
    const company = companyDetail.find((item) => item.companyName === companyName);

    return {
      type: 'node',
      id: company?.keyNo as string,
      name: companyName,
      label: companyName,
    };
  };

  const toEdge = (content: string): PathEdge => {
    const stockPercent = content.match(/\(([\d.]+%?)\)/);
    const direction = getDirection(content);
    return {
      type: 'edge',
      direction,
      roles: stockPercent ? [stockPercent[1]] : [],
    };
  };

  // 将path转换为关系路径, 通过->和<-来判断方向，通过`(100%)`来判断持股比例
  const symbolPattern = /(?:<-\([\d.]+%\)|\([\d.]+%\)->)/;

  let currentPath = relationPath;
  let done = false;

  // 从左向右依次匹配股权占比与关系
  while (!done) {
    const match = currentPath.match(symbolPattern);
    if (match) {
      // 保存节点(前节点)
      paths.push(toNode(currentPath.slice(0, match.index)));

      const currentEdge = match[0];
      // 保存路径
      paths.push(toEdge(currentEdge));

      currentPath = currentPath.slice((match.index ?? 0) + currentEdge.length);
    } else {
      // 保存剩余路径
      paths.push(toNode(currentPath));

      done = true;
    }
  }
  return paths;
};

// 表格列配置
const TABLE_COLUMNS: ITableColumn[] = [
  {
    title: '核验结果',
    dataIndex: 'verificationResult',
    key: 'verificationResult',
    // width: 120,
    width: '12.15%',
    scopedSlots: {
      customRender: 'verificationResult',
    },
  },
  {
    title: '人员类型/职务',
    dataIndex: 'personRole',
    key: 'personRole',
    width: '20.24%',
    // width: 200,
  },
  {
    title: '企业名称',
    key: 'compName',
    scopedSlots: {
      customRender: 'companyName',
    },
  },
  {
    title: '统一社会信用代码',
    dataIndex: 'compCreditCode',
    key: 'compCreditCode',
    width: '20.24%',
    // width: 200,
  },
];

const TARGET_COMPANY_TABLE_COLUMNS: ITableColumn[] = [
  ...TABLE_COLUMNS,
  {
    title: '登记状态',
    dataIndex: 'compRegistStatus',
    key: 'compRegistStatus',
    width: '22.27%',
    // width: 220,
    scopedSlots: {
      customRender: 'registrationStatus',
    },
  },
];

const RELATED_COMPANY_TABLE_COLUMNS: ITableColumn[] = [
  ...TABLE_COLUMNS,
  {
    title: '登记状态',
    dataIndex: 'compRegistStatus',
    key: 'compRegistStatus',
    width: '12.15%',
    // width: 120,
    scopedSlots: {
      customRender: 'registrationStatus',
    },
  },
  {
    title: '关键路径',
    dataIndex: 'relationPath',
    key: 'relationPath',
    width: '10.12%',
    // width: 100,
    scopedSlots: {
      customRender: 'relationPath',
    },
  },
];

// 验证结果类型
export interface IVerificationType {
  title: string;
  description: string;
  icon: string;
  columns: ITableColumn[];
}

const IdentityVerificationMatchResult = defineComponent({
  name: 'IdentityVerificationMatchResult',
  props: {
    type: {
      type: String as PropType<'company' | 'relatedCompany'>,
      required: true,
      default: 'company',
    },
    verificationData: {
      type: Array as PropType<IVerificationDetail[]>,
      required: true,
      default: () => [],
    },
  },
  setup(props) {
    // 展开/折叠状态
    const isExpanded = ref(true);

    // 切换展开/折叠状态
    const toggleExpand = () => {
      isExpanded.value = !isExpanded.value;
    };

    // 根据type类型获取验证信息
    const getVerificationType = (): IVerificationType => {
      if (props.type === 'company') {
        return {
          title: '指定企业核验',
          description: '核验自然人在【指定企业】中是否担任法定代表人、高管、股东',
          icon: 'icon-enterprise',
          columns: TARGET_COMPANY_TABLE_COLUMNS,
        };
      }
      return {
        title: '关联企业核验',
        description: '核验自然人在【指定企业的关联企业（总持股比例≥5%）】中担任法定代表人、高管、股东，且仅展示有任职信息的关联企业',
        icon: 'icon-guanlianqiye',
        columns: RELATED_COMPANY_TABLE_COLUMNS,
      };
    };

    return {
      isExpanded,
      toggleExpand,
      getVerificationType,
    };
  },
  render() {
    const { verificationData, isExpanded, toggleExpand } = this;
    const verificationInfo = this.getVerificationType();

    return (
      <div class={styles.container}>
        <div
          class={{
            [styles.expand]: true,
            [styles.isCollapsed]: !isExpanded,
          }}
          onClick={toggleExpand}
        >
          <QIcon type="icon-a-xianduanshang" class={styles.expandIcon} />
          <span class={styles.expandLine}></span>
        </div>

        <div class={styles.content}>
          <div class={styles.description}>
            <div>
              <span class={styles.icon}>
                <QIcon type={verificationInfo.icon} />
              </span>
              <span class={styles.title}>{verificationInfo.title}</span>
            </div>
            <span class={styles.text}>{verificationInfo.description}</span>
          </div>

          {/* 核验结果表格 */}
          <div class={styles.table} v-show={isExpanded}>
            <QRichTable
              tableLayout="fixed"
              showIndex={false}
              bordered={false}
              columns={verificationInfo.columns}
              dataSource={verificationData}
              emptyMinHeight={'40px'}
              pagination={false}
              rowKey="verificationDetailId"
              scopedSlots={{
                /** 企业名称 */
                companyName: (record) => {
                  return <QEntityLink class={styles.companyName} coyObj={{ KeyNo: record.compId, Name: record.compName }}></QEntityLink>;
                },
                /** 核验结果 */
                verificationResult: (status) => {
                  const success = status === 1;
                  const info = success
                    ? {
                        label: '匹配',
                        icon: 'icon-chenggong',
                      }
                    : {
                        label: '不匹配',
                        icon: 'icon-guanbi',
                      };

                  return (
                    <div
                      class={{
                        [styles.matchResult]: true,
                        [styles.success]: success,
                        [styles.failed]: !success,
                      }}
                    >
                      <QIcon class={styles.icon} type={info.icon} />
                      <span class={styles.label}>{info.label}</span>
                    </div>
                  );
                },
                /** 登记状态 */
                registrationStatus: (statusText?: string) => {
                  if (!statusText) {
                    return '-';
                  }
                  return <CompanyStatus status={statusText} ghost />;
                },
                /** 关联关系 */
                relationPath: (relationPath: IVerificationDetailRelationPath) => {
                  if (!relationPath || !relationPath.path) {
                    return '-';
                  }

                  const paths = parseRelationPathText(relationPath.path, relationPath.companyDetail);

                  return (
                    <DropdownButton text="查看详情" buttonProps={{ type: 'link' }}>
                      <div slot="overlay" class={styles.relationOverlay}>
                        {paths.map((path, index) => {
                          if (path.type === 'edge') {
                            return <Edge value={path} key={`edge-${index}`} />;
                          }
                          return <Node value={path} key={`node-${index}`} />;
                        })}
                      </div>
                    </DropdownButton>
                  );
                },
              }}
            />
          </div>
        </div>
      </div>
    );
  },
});

export default IdentityVerificationMatchResult;
