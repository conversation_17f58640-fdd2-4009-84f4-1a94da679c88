import { defineComponent, ref, onMounted, PropType, computed } from 'vue';
import { Breadcrumb } from 'ant-design-vue';

import { user as userService } from '@/shared/services';
import { dateFormat } from '@/utils/format';

import IdentityVerificationOperationInfo from '../identity-verification-operation-info';
import styles from './identity-verification-breadcrumb.module.less';

const IdentityVerificationBreadcrumb = defineComponent({
  name: 'IdentityVerificationBreadcrumb',
  props: {
    operationInfo: {
      type: Object as PropType<{ createBy: number; createDate: string }>,
      required: false,
    },
    pageType: {
      type: String,
      default: 'history',
    },
  },
  setup(props) {
    const userList = ref([]);
    const getUserList = async () => {
      try {
        userList.value = await userService.getUserList();
      } catch (error) {
        console.error(error);
      }
    };
    onMounted(async () => {
      await getUserList();
    });

    const operator = computed(() => {
      if (!props?.operationInfo?.createBy) {
        return null;
      }
      const user = userList.value.find(({ userId }) => userId === props.operationInfo?.createBy);
      return user;
    });

    const operationTime = computed(() => {
      if (!props?.operationInfo?.createDate) {
        return null;
      }
      return dateFormat(props.operationInfo.createDate, { pattern: 'YYYY-MM-DD HH:MM:ss' });
    });

    return {
      operator,
      operationTime,
    };
  },
  render() {
    return (
      <div class={['sticky-breadcrumb', styles.breadcrumb]}>
        <div class={styles.path}>
          <Breadcrumb>
            {this.pageType === 'start' ? (
              <Breadcrumb.Item>
                <router-link to="/identity-verification/start">
                  <q-icon type="icon-mianbaoxiefanhui" />
                  <span>人企核验</span>
                </router-link>
              </Breadcrumb.Item>
            ) : (
              <Breadcrumb.Item>
                <router-link to="/identity-verification/history?useCacheQuery=true">
                  <q-icon type="icon-mianbaoxiefanhui" />
                  <span>核验记录</span>
                </router-link>
              </Breadcrumb.Item>
            )}
            <Breadcrumb.Item>人企核验结果</Breadcrumb.Item>
          </Breadcrumb>
        </div>
        <div class={styles.info}>
          {this.operator && this.operationTime ? (
            <IdentityVerificationOperationInfo operator={this.operator} operationTime={this.operationTime} />
          ) : null}
        </div>
      </div>
    );
  },
});

export default IdentityVerificationBreadcrumb;
