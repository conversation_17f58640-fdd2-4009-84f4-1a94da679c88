.container {
  width: 100%;
  background-color: #fff;
  border: solid #e4eef6;
  border-width: 1px 0;
}

.condition {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e4eef6;
  background-color: #f2f9fc;

  .action {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 34px;
  }

  .info {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 16px 10px;
    gap: 40px;
  }
}

.personInfo {
  display: flex;
  gap: 16px;

  .name {
    font-size: 16px;
    font-weight: bold;
    line-height: 24px;
    // color: #128BED;
  }

  .idNumber {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #808080;
  }
}

.companyInfo {
  display: flex;
  gap: 8px;

  .name {
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    color: #333;
  }

  .label {
    font-size: 14px;
    line-height: 22px;
    color: #808080;
  }
}

.result {
  padding-left: 34px;
}
