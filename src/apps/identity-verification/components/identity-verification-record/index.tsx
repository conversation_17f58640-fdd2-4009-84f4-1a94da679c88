import { defineComponent, PropType } from 'vue';
import { Checkbox } from 'ant-design-vue';

import QEntityLink from '@/components/global/q-entity-link';

import styles from './identity-verification-record.module.less';
import IdentityVerificationMatchResult, { IVerificationDetail } from '../identity-verification-match-result';

// 个人信息数据类型
export interface IPersonInfo {
  name: string;
  idNumber: string;
  keyNo?: string;
  verificationType?: number;
}

// 企业信息数据类型
export interface ICompanyInfo {
  name: string;
  creditCode?: string;
}

// 验证结果数据类型
export interface IVerificationData {
  companyData: IVerificationDetail[];
  relatedCompanyData: IVerificationDetail[];
}

// 组件属性
export interface IIdentityVerificationResultProps {
  personInfo: IPersonInfo;
  companyInfo: ICompanyInfo;
  verificationData: IVerificationData;
}

const IdentityVerificationRecord = defineComponent({
  name: 'IdentityVerificationRecord',
  props: {
    checked: {
      type: Boolean,
      default: false,
    },
    personInfo: {
      type: Object as PropType<IPersonInfo>,
      required: true,
      default: () => ({
        name: '',
        idNumber: '',
      }),
    },
    companyInfo: {
      type: Object as PropType<ICompanyInfo>,
      required: true,
      default: () => ({
        name: '',
        creditCode: '',
      }),
    },
    verificationDetails: {
      type: Object as PropType<IVerificationData>,
      required: true,
      default: () => ({
        companyData: [],
        relatedCompanyData: [],
      }),
    },
  },
  emits: ['check'],
  setup(props, { emit }) {
    const handleCheck = (ev: Event) => {
      emit('check', (ev.target as HTMLInputElement).checked);
    };
    return {
      handleCheck,
    };
  },
  render() {
    const { personInfo, companyInfo, verificationDetails, checked, handleCheck } = this;

    return (
      <div class={styles.container}>
        {/* 查询条件 */}
        <div class={styles.condition}>
          {/* 勾选框 */}
          <div class={styles.action}>
            <Checkbox checked={checked} onChange={handleCheck} />
          </div>
          {/* 个人信息 */}
          <div class={styles.info}>
            <div class={styles.personInfo}>
              <span class={styles.name}>
                <QEntityLink coyObj={{ KeyNo: personInfo.keyNo, Name: personInfo.name }}></QEntityLink>
              </span>
              <span class={styles.idNumber}>{personInfo.idNumber}</span>
            </div>
            {/* 指定企业 */}
            <div class={styles.companyInfo}>
              <span class={styles.label}>指定企业：</span>
              <span class={styles.name}>{companyInfo.name}</span>
            </div>
          </div>
        </div>

        {/* 核验结果 */}
        <div class={styles.result}>
          {/* 指定企业核验结果 */}
          {verificationDetails.companyData.length > 0 && (
            <IdentityVerificationMatchResult type="company" verificationData={verificationDetails.companyData} />
          )}

          {/* 关联企业核验结果 */}

          {personInfo.verificationType !== 1 && (
            <IdentityVerificationMatchResult type="relatedCompany" verificationData={verificationDetails.relatedCompanyData} />
          )}
        </div>
      </div>
    );
  },
});

export default IdentityVerificationRecord;
