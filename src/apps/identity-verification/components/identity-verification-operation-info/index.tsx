import { defineComponent } from 'vue';

import styles from './identity-verification-operation-info.module.less';

export interface IOperationInfoProps {
  name: string;
  phone?: string;
}

const IdentityVerificationOperationInfo = defineComponent({
  functional: true,
  props: {
    operator: {
      type: Object as () => IOperationInfoProps,
      required: true,
    },
    operationTime: {
      type: String,
      required: true,
    },
  },
  render(h, { props }) {
    const { operator, operationTime } = props;
    return (
      <div class={styles.container}>
        <span>
          操作人：{operator.name}
          {operator.phone ? `（${operator.phone}）` : null}
        </span>
        <span>操作时间：{operationTime}</span>
      </div>
    );
  },
});
export default IdentityVerificationOperationInfo;
