import type { RouteConfig } from 'vue-router';

import SidebarMenuLayout from '@/shared/layouts/sidebar-menu';
import { APP_MENU_CONFIG } from '@/config/menu.config';
import { Permission } from '@/config/permissions.config';
import { hasPermission } from '@/shared/composables/use-permission';

export const identityVerificationRoutes = (): RouteConfig[] => [
  {
    path: '/identity-verification',
    component: SidebarMenuLayout,
    redirect: () => {
      if (hasPermission([Permission.IDENTITY_VERIFICATION_CHECK])) {
        return {
          name: 'identity-verification-start',
        };
      }
      return {
        name: 'identity-verification-history',
      };
    },
    props: {
      pageTitle: '人企核验',
      menu: APP_MENU_CONFIG,
    },
    meta: {
      title: '人企核验',
    },
    children: [
      {
        path: 'start',
        name: 'identity-verification-start',
        component: () => import('../pages/identity-verification-start'),
        meta: {
          title: '人企核验',
          permission: [Permission.IDENTITY_VERIFICATION_CHECK],
        },
      },
      {
        path: 'history',
        name: 'identity-verification-history',
        component: () => import('../pages/identity-verification-history'),
        meta: {
          title: '核验记录',
          permission: [Permission.IDENTITY_VERIFICATION_VIEW],
        },
      },
      {
        path: ':type(history|start)/loading',
        name: 'identity-verification-loading',
        component: () => import('../pages/identity-verification-loading'),
        props: (route) => {
          return {
            batchId: parseInt(route.query.batchId.toString(), 10),
          };
        },
        meta: {
          title: '人企核验结果',
          permission: [Permission.IDENTITY_VERIFICATION_VIEW],
        },
      },
      {
        path: ':pageType(history|start)/detail/:recordId([0-9]+)',
        name: 'identity-verification-detail',
        component: () => import('../pages/identity-verification-detail'),
        props: (route) => {
          return {
            pageType: route.params.pageType,
            recordId: parseInt(route.params.recordId, 10),
            type: route.query.type ?? 'record', // 'record' | 'batch'
          };
        },
        meta: {
          title: '人企核验结果',
          permission: [Permission.IDENTITY_VERIFICATION_VIEW],
        },
      },
      {
        path: ':type(start)/verify',
        name: 'identity-verification-verify',
        component: () => import('../pages/identity-verification-verify'),
        meta: {
          title: '企业核实',
          permission: [Permission.IDENTITY_VERIFICATION_CHECK],
        },
      },
    ],
  },
];
