import { computed, defineComponent, onMounted, reactive, ref, unref } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { Breadcrumb, Button, message } from 'ant-design-vue';
import { cloneDeep } from 'lodash';

import { isLatestPage, isLatestRow } from '@/utils/pagination';
import { batchImport } from '@/shared/services';
import { useSearch } from '@/shared/composables/use-search';
import HeroicLayout from '@/shared/layouts/heroic';
import QCard from '@/components/global/q-card';
import { useTrack } from '@/config/tracking-events';

import BatchConfirmResult from '@/shared/components/batch-confirm-result/index';
import styles from './identity-verification-verify.module.less';
import { VerifyTableColumns } from './identity-verification-verify.config';
import ResultCountInfo from '@/shared/components/result-count-info';
import QIcon from '@/components/global/q-icon';

const IdentityVerificationVerify = defineComponent({
  name: 'IdentityVerificationVerify',
  setup() {
    const track = useTrack();
    const init = ref(false);
    const route = useRoute();
    const router = useRouter();
    const batchId = computed(() => (route.query.batchId ? Number(route.query.batchId) : null));
    const selectedIds = ref([]);
    const pagination = reactive({
      pageSize: 10,
      current: 1,
      total: 0,
    });
    const statistic = reactive({
      duplicatedCount: 0,
      errorCount: 0,
      successCount: 0,
      failedEmptyCount: 0,
    });

    const filteredCount = computed(() => {
      return (statistic.failedEmptyCount ?? 0) + (statistic.duplicatedCount ?? 0);
    });

    const btnLoading = ref<number | null>(null);
    const isUpdateMode = ref(true);

    const dataSource = ref([]);

    const getParams = () => {
      return {
        batchId: batchId.value,
        pageSize: pagination.pageSize,
        pageIndex: pagination.current,
      };
    };

    const searchRequest = async () => {
      if (!batchId.value) {
        message.error('请先上传企业');
        return;
      }
      const res = await batchImport.getUploadData(getParams());
      dataSource.value = res.data || [];
      pagination.current = res.pageIndex || 0;
      pagination.total = res.total || 0;
      pagination.pageSize = res.pageSize || 0;
      Object.assign(statistic, res.statistic);
    };
    const { search, isLoading } = useSearch(searchRequest);
    // 是否需要返回第一页
    const isNeedGoFirst = computed(() => {
      if (isLatestRow(pagination)) return true;
      return isLatestPage(pagination) && unref(dataSource)?.length > 0 && unref(dataSource).length === unref(selectedIds).length;
    });

    const pageChange = (current, pageSize) => {
      pagination.current = current;
      pagination.pageSize = pageSize;
      selectedIds.value = [];
      search();
    };

    const updateData = async (data) => {
      try {
        await batchImport.updateCompany(data);
        message.success('修改成功');
        search();
      } catch (e) {
        // 为了能在接口报错时触发cell-edit更新回原始值，先这么写
        dataSource.value = cloneDeep(dataSource.value);
      }
    };

    const isDisabled = computed(() => statistic.successCount <= 0);

    const isSubmitError = computed(() => statistic.errorCount > 0);

    const handleDelete = async (flag = false) => {
      const param = flag
        ? {
            flag,
            batchId: batchId.value,
          }
        : {
            itemIds: unref(selectedIds),
            batchId: batchId.value,
          };
      await batchImport.deleteCompany(param);
      message.success('移除成功');
      pagination.current = 1;
      selectedIds.value = [];
      search();
    };

    const execute = async (type: number) => {
      if (unref(btnLoading)) {
        return;
      }

      if (isSubmitError.value) {
        btnLoading.value = type;
        await batchImport.deleteCompany({
          flag: true,
          batchId: batchId.value,
        });
      }
      try {
        btnLoading.value = type;
        const res = await batchImport.executeBatch({
          verificationType: type,
          verificationImportId: batchId.value,
          itemIds: selectedIds.value.length ? selectedIds.value : undefined,
        });
        if (res.batchId) {
          await router.push({
            name: 'identity-verification-loading',
            params: {
              type: 'start',
            },
            query: {
              batchId: res.batchId,
              creator: JSON.stringify({
                createBy: res.createBy,
                createDate: res.createDate,
              }),
            },
          });
        }
      } catch (e) {
        console.log('批量任务执行失败：', e);
      } finally {
        btnLoading.value = null;
      }
    };

    onMounted(async () => {
      if (!batchId.value) {
        message.error('请先上传企业');
      } else {
        await search();
      }
      init.value = false;
    });
    return {
      init,
      route,
      dataSource,
      isLoading,
      pagination,
      selectedIds,
      statistic,
      isSubmitError,
      updateData,
      search,
      handleDelete,
      pageChange,
      execute,
      btnLoading,
      isUpdateMode,
      isDisabled,
      filteredCount,
    };
  },
  render() {
    const { dataSource, isLoading, selectedIds } = this;
    const pagination = {
      ...this.pagination,
      onChange: this.pageChange,
      onShowSizeChange: this.pageChange,
    } as any;
    return (
      <div>
        <Breadcrumb class="sticky-breadcrumb">
          <Breadcrumb.Item>
            <router-link to="/identity-verification/start">
              <q-icon type="icon-mianbaoxiefanhui" />
              <span>人企核验</span>
            </router-link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>{this.$route.meta?.title}</Breadcrumb.Item>
        </Breadcrumb>
        <HeroicLayout loading={this.init} innerStyle={{ minHeight: 'calc(100vh - 53px - 60px)' }}>
          <QCard bodyStyle={{ padding: '12px 16px 16px' }}>
            <div class="flex justify-between items-center">
              <div class={styles.title}>{this.$route.meta?.title}</div>
              {/*<Button
                disabled={!this.isSubmitError}
                onClick={() => {
                  this.handleDelete(true);
                }}
              >
                移除匹配失败数据
              </Button>*/}
            </div>
            <ResultCountInfo
              style={{ margin: '4px 0 12px' }}
              config={[
                { prefix: '当前匹配成功', suffix: '家企业，', count: this.statistic.successCount, theme: 'success' },
                { prefix: '匹配失败', suffix: '条数据，', count: this.statistic.errorCount, theme: 'fail' },
                { prefix: '已过滤掉', suffix: '条重复或必填项为空的数据', count: this.filteredCount, theme: 'warning' },
              ]}
              isLoading={this.isLoading}
            />
            <BatchConfirmResult
              selectedIds={selectedIds}
              dataSource={dataSource}
              columns={VerifyTableColumns}
              pagination={pagination}
              loading={isLoading}
              onSelect={(values) => {
                this.selectedIds = values.map((item) => {
                  return item.id;
                });
              }}
              onChange={(data) => {
                this.updateData(data);
              }}
              onUpdate={() => {
                this.search();
              }}
              onDelete={(ids) => {
                this.selectedIds = ids;
                this.handleDelete();
              }}
            ></BatchConfirmResult>

            <div class={styles.fixbtn}>
              <div class="text-#D8D8D8">
                <QIcon type="icon-a-shuomingxian" class="mr-1" />
                <span class="text-#999999">仅支持核验匹配成功的数据</span>
              </div>
              <Button disabled={this.isDisabled || !!this.btnLoading} onClick={() => this.execute(1)} loading={this.btnLoading === 1}>
                常规核验{this.statistic.successCount}家
              </Button>
              <Button
                disabled={this.isDisabled || !!this.btnLoading}
                type="primary"
                onClick={() => this.execute(2)}
                loading={this.btnLoading === 2}
              >
                深度核验{this.statistic.successCount}家
              </Button>
            </div>
          </QCard>
        </HeroicLayout>
      </div>
    );
  },
});

export default IdentityVerificationVerify;
