import { computed, defineComponent, PropType, ref, unref } from 'vue';
import { differenceBy, uniqBy, escape } from 'lodash';
import { Button } from 'ant-design-vue';

import QRichTable from '@/components/global/q-rich-table';
import QCard from '@/components/global/q-card';
import SearchCount from '@/components/search-count';
import DropdownButtonWrapper from '@/components/dropdown-button-wrapper';
import { Permission } from '@/config/permissions.config';

import QEntityLink from '@/components/global/q-entity-link';

const SearchResult = defineComponent({
  name: 'SearchResult',
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    dataSource: {
      type: Array,
      default: () => [],
    },
    pagination: {
      type: Object,
      default: () => ({}),
    },
    rowKey: {
      type: String,
      default: 'id',
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    operatorList: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    searchKey: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const selection = ref<any[]>([]);
    const rowSelection = computed(() => ({
      selectedRowKeys: unref(selection).map((item) => item[props.rowKey]),
      checkStrictly: false,
      onChange: (rowKeys: (string | number)[], rows: Record<string, any>[]) => {
        selection.value = uniqBy([...differenceBy(unref(selection), props.dataSource, props.rowKey), ...rows], props.rowKey);
        emit('updateSelection', unref(selection));
      },
    }));
    return {
      selection,
      rowSelection,
    };
  },
  render() {
    const pagigation: any = {
      ...this.pagination,
      onChange: (current, pageSize) => {
        this.$emit('changePage', current, pageSize);
      },
      onShowSizeChange: (current, pageSize) => {
        this.$emit('changePage', current, pageSize);
      },
    };
    return (
      <QCard bodyStyle={{ padding: '15px' }}>
        <div slot="title">
          <SearchCount slot="title" showSelects={true} total={pagigation.total} loading={this.isLoading} selectedIds={this.selection} />
        </div>
        <div slot="extra">
          <div>
            <DropdownButtonWrapper
              v-permission={[Permission.IDENTITY_VERIFICATION_EXPORT]}
              totalCount={this.pagination.total}
              btnText="导出列表"
              needPopConfirm={false}
              selectIdlength={this.selection.length}
              menuItems={[
                {
                  label: '选中导出',
                  key: 'batch',
                },
                {
                  label: '全部导出',
                  key: 'export',
                },
              ]}
              onConfirm={(key) => {
                this.$emit(
                  'export',
                  key,
                  this.selection.map((item) => item[this.rowKey])
                );
              }}
            />
          </div>
        </div>
        <QRichTable
          rowKey={this.rowKey}
          tableLayout="fixed"
          showIndex={false}
          columns={this.columns}
          dataSource={this.dataSource}
          customScroll={{ y: 'calc(100vh - 360px)' }}
          pagination={pagigation}
          rowSelection={this.rowSelection}
          loading={this.isLoading}
          onChange={({ sorter: antdSorter }) => {
            this.$emit('changeSort', antdSorter);
          }}
          scopedSlots={{
            companyName: (record) => {
              const name = escape(record.compName).replace(this.searchKey, `<em>${this.searchKey}</em>`);
              return <QEntityLink class="emphasis" coyObj={{ KeyNo: record.compId, Name: name }} ellipsis={false}></QEntityLink>;
            },
            personName: (record) => {
              const name = escape(record.personName).replace(this.searchKey, `<em>${this.searchKey}</em>`);
              return <QEntityLink coyObj={{ KeyNo: record.personKeyNo, Name: name }} ellipsis={false}></QEntityLink>;
            },
            verifyType: (type) => {
              return type === 1 ? '常规核验' : '深度核验';
            },
            verifyResult: (score) => {
              // 取值说明：
              // 10常规核验通过，无关联方核验;
              // 11常规核验通过，关联方核验通过;
              // 12常规核验通过，关联方核验不通过；
              // 20常规核验不通过，无关联方核验;
              // 21常规核验不通过，关联方核验通过；
              // 22常规核验不通过，关联方核验不通过；
              const isSuccess = score < 20;
              return (
                <div
                  style={{
                    color: isSuccess ? '#00AD65' : '#F04040',
                    gap: '5px',
                  }}
                  class="flex items-center"
                >
                  <q-icon type={isSuccess ? 'icon-chenggong' : 'icon-guanbi'}></q-icon>
                  <span>{isSuccess ? '匹配' : '不匹配'}</span>
                </div>
              );
            },
            actor: (id) => {
              return this.operatorList.find((item) => item.userId === id)?.name || '-';
            },
            action: (record) => {
              return (
                <Button
                  type="link"
                  onClick={() => {
                    this.$router.push({
                      path: `history/detail/${record.verificationId}`,
                    });
                  }}
                >
                  详情
                </Button>
              );
            },
          }}
        />
      </QCard>
    );
  },
});

export default SearchResult;
