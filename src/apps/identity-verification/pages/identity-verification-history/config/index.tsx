import { DEFAULT_DATE_RANGE } from '@/config/tender.config';

const getFilterConfig = ({ resultOptions, operatorOptions, recordVerificationResultOptions }) => {
  return [
    {
      field: 'filters',
      label: '筛选条件',
      type: 'groups',
      children: [
        {
          field: 'recordVerificationResults',
          type: 'multiple',
          label: '核验结果',
          options: resultOptions,
          layout: 'inline',
        },
        {
          field: 'verificationTypeList',
          type: 'multiple',
          label: '核验类型',
          options: recordVerificationResultOptions,
          layout: 'inline',
        },
        {
          field: 't',
          type: 'multiple',
          label: '操作人',
          options: operatorOptions,
          layout: 'inline',
        },
        {
          field: 'sd',
          type: 'single',
          label: '排查时间',
          options: [{ value: undefined, label: '不限' }, ...DEFAULT_DATE_RANGE],
          custom: {
            type: 'date-range',
          },
        },
      ],
    },
  ];
};

const getTableConfig = (screenWidth) => [
  {
    title: '企业名称',
    scopedSlots: {
      customRender: 'companyName',
    },
  },
  {
    title: '统一社会信用代码',
    width: screenWidth > 1680 ? 180 : 160,
    dataIndex: 'compCreditCode',
  },
  {
    title: '人员姓名',
    width: 80,
    scopedSlots: {
      customRender: 'personName',
    },
  },
  {
    title: '证件号码',
    width: screenWidth > 1680 ? 180 : 130,
    dataIndex: 'personIdcardMask',
  },
  {
    title: '核验结果',
    width: 80,
    dataIndex: 'verificationResult',
    scopedSlots: {
      customRender: 'verifyResult',
    },
  },
  {
    title: '人员类型/职务',
    dataIndex: 'personRole',
  },
  {
    title: '核验类型',
    width: 80,
    dataIndex: 'verificationType',
    scopedSlots: {
      customRender: 'verifyType',
    },
  },
  {
    title: '操作人',
    width: 80,
    dataIndex: 'createBy',
    scopedSlots: {
      customRender: 'actor',
    },
  },
  {
    title: '操作时间',
    dataIndex: 'createDate',
    sorter: true,
    width: screenWidth > 1680 ? 180 : 100,
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    title: '操作',
    width: screenWidth > 1680 ? 100 : 70,
    scopedSlots: {
      customRender: 'action',
    },
  },
];

export { getFilterConfig, getTableConfig };
