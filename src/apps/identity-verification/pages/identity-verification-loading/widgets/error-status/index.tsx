import { defineComponent } from 'vue';
import { Button } from 'ant-design-vue';

import ErrorStatusIcon from './assets/loading-error.icon.svg';
import styles from './error-status.module.less';

export interface ErrorStatusProps {
  errorText?: string;
  showRefreshButton?: boolean;
  onRefresh?: () => void;
}

const ErrorStatus = defineComponent({
  name: 'ErrorStatus',
  props: {
    showRefreshButton: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['refresh'],
  setup(props, { emit }) {
    const handleRefresh = () => {
      emit('refresh');
    };

    return {
      handleRefresh,
    };
  },
  render() {
    return (
      <div class={styles.error}>
        <div class={styles.icon}>
          <img src={ErrorStatusIcon} />
        </div>
        <div class={styles.text}>{this.$slots.default}</div>
        {this.showRefreshButton && (
          <div class={styles.action}>
            <Button type="primary" class={styles.refresh} onClick={this.handleRefresh}>
              刷新
            </Button>
          </div>
        )}
      </div>
    );
  },
});

export default ErrorStatus;
