import { defineComponent } from 'vue';

import QLoading from '@/components/global/q-loading';

import styles from './loading-status.module.less';

const LoadingStatus = defineComponent({
  name: 'LoadingStatus',
  render() {
    return (
      <div class={styles.loading}>
        <div class={styles.icon}>
          <QLoading />
        </div>
        <div class={styles.text}>{this.$slots.default}</div>
      </div>
    );
  },
});

export default LoadingStatus;
