import { computed, defineComponent, onMounted, ref } from 'vue';
import { Icon, message as Message } from 'ant-design-vue';
import { isEmpty, pick } from 'lodash';

import Card from '@/shared/components/card';
import HeroicLayout from '@/shared/layouts/heroic';
import { useInfiniteScroll } from '@/shared/composables/use-infinite-scroll';
import { useRequest } from '@/shared/composables/use-request';
import { identityVerification as identityVerificationService } from '@/shared/services';

import styles from './identity-verification-detail.module.less';
import IdentityVerificationSearchResult from './widgets/search-result';
import IdentityVerificationBreadcrumb from '../../components/identity-verification-breadcrumb';
import SearchAction from './widgets/search-action';

const useSelectIds = () => {
  const data = ref<number[]>([]);
  /** 处理选中/取消选中单个结果 */
  const update = (id: number, checked: boolean) => {
    if (checked) {
      data.value = [...data.value, id];
    } else {
      data.value = data.value.filter((item) => item !== id);
    }
  };

  /** 取消全选 */
  const clear = () => {
    data.value = [];
  };
  return { data, update, clear };
};

const IdentityVerificationDetail = defineComponent({
  name: 'IdentityVerificationDetail',
  props: {
    /**
     * 核验记录ID(可能为单个核验记录ID，也可能为批次核验记录ID)
     */
    recordId: {
      type: Number,
      required: true,
    },
    /**
     * 核验类型
     * - record: 单个核验
     * - batch: 批次核验
     */
    type: {
      type: String, // 'record' | 'batch'
      required: true,
    },
  },
  setup(props) {
    const dataSource = ref<any>([]);

    const { execute, data, isLoading, isSuccess, isError } = useRequest(identityVerificationService.getVerificationRecord);
    const isInit = ref(true);
    const searchFilter = ref<{ searchKey: string | undefined; recordVerificationResults: any }>({
      searchKey: undefined,
      recordVerificationResults: undefined,
    });

    /** 分页 */
    const pagination = computed(() => {
      return {
        pageSize: data.value?.pageSize ?? 5,
        pageIndex: data.value?.pageIndex ?? 1,
        total: data.value?.total ?? 0,
      };
    });

    /** 搜索 */
    const request = async (
      payload: Partial<{
        searchKey: string;
        pageSize: number;
        pageIndex: number;
        recordVerificationResults: number[];
      }> = {}
    ) => {
      const {
        searchKey,
        pageSize = pagination.value.pageSize,
        pageIndex = pagination.value.pageIndex,
        recordVerificationResults,
      } = payload;

      const params: any = {
        searchKey,
        recordVerificationResults,
        needAgg: 1,
        pageSize,
        pageIndex,
        recordId: undefined,
        batchId: undefined,
      };

      if (props.type === 'record') {
        params.recordId = props.recordId;
      } else {
        params.batchId = props.recordId;
      }

      const res = await execute<any>(params);
      if (pageIndex === 1) {
        dataSource.value = res.data ?? [];
      } else {
        dataSource.value.push(...(res.data ?? []));
      }
      return res;
    };

    /** 选中的核验结果ID */
    const selectedIds = useSelectIds();

    /** 搜索关键字 */
    const search = async () => {
      await request({
        ...searchFilter.value,
        pageIndex: 1,
      });
      selectedIds.clear();
    };

    /** 搜索 */
    const handleSearch = (payload: Partial<{ searchKey: string; recordVerificationResults: number }> = {}) => {
      searchFilter.value.searchKey = payload.searchKey;
      if (payload.recordVerificationResults && payload.recordVerificationResults > -1) {
        searchFilter.value.recordVerificationResults = [payload.recordVerificationResults];
      } else {
        searchFilter.value.recordVerificationResults = undefined;
      }
      search();
    };

    /** 操作人信息 */
    const operationInfo = computed(() => {
      const result = pick<{ createBy: number; createDate: string }>(dataSource.value?.[0] ?? {}, ['createBy', 'createDate']);
      if (isEmpty(result)) {
        return null;
      }
      return result;
    });

    /** 聚合结果 */
    const recordVerificationResult = computed(() => {
      return data.value?.aggsRes?.recordVerificationResult ?? [];
    });

    /** 核验结果 */
    const resultFilterOptions = computed(() => {
      const mapping = {
        // 普通核验
        '1': {
          '1': '匹配',
          '2': '不匹配',
        },
        // 深度核验
        '2': {
          '1': '指定企业匹配',
          '2': '指定企业不匹配',
          '21': '指定企业不匹配，关联企业匹配',
        },
      };
      // 根据不同 type 返回不同的 options
      const restOptions = recordVerificationResult.value.reduce((acc, { count, recordVerificationResult: key }) => {
        const { verificationType } = data.value ?? {};
        if (!verificationType) {
          return acc;
        }

        const label = mapping[verificationType]?.[key];
        if (!label) {
          return acc;
        }
        const option = {
          label,
          key,
          count,
        };
        return [...acc, option];
      }, []);

      const defaultOption = {
        label: '不限',
        key: -1,
        count: null,
      };
      const options = [defaultOption, ...restOptions];
      return options;
    });

    /** 单条核验导出 */
    const recordExport = (
      key: 'exportByIds' | 'export',
      requestFn:
        | typeof identityVerificationService.exportVerificationRecordRegular
        | typeof identityVerificationService.exportVerificationRecordDeep
    ) => {
      // 导出选中的记录
      if (key === 'exportByIds') {
        return requestFn({
          recordIds: selectedIds.data.value,
        });
      }
      // 导出所有记录
      return requestFn({
        ...searchFilter.value, // 导出全部时需要传入当前的筛选条件
        recordIds: [props.recordId],
      });
    };

    /** 批量核验导出 */
    const recordExportBatch = (
      key: 'exportByIds' | 'export',
      requestFn: typeof identityVerificationService.exportVerificationRecordDeep
    ) => {
      // 导出选中的记录
      if (key === 'exportByIds') {
        return requestFn({
          recordIds: selectedIds.data.value,
        });
      }
      // 导出所有记录
      return requestFn({
        ...searchFilter.value, // 导出全部时需要传入当前的筛选条件
        batchId: props.recordId,
      });
    };

    /** 处理导出 */
    const handleExport = async (key: 'exportByIds' | 'export') => {
      if (key === 'exportByIds' && selectedIds.data.value.length === 0) {
        Message.warning('请选择需要导出的记录');
        return;
      }

      let exportRequestFn:
        | typeof identityVerificationService.exportVerificationRecordDeep
        | typeof identityVerificationService.exportVerificationRecordRegular;
      if (data.value.verificationType === 1) {
        // 普通核验
        exportRequestFn = identityVerificationService.exportVerificationRecordRegular;
      } else {
        // 深度核验
        exportRequestFn = identityVerificationService.exportVerificationRecordDeep;
      }

      let exportRequest;
      // 单条核验
      if (props.type === 'record') {
        exportRequest = recordExport(key, exportRequestFn);
      }

      // 批次核验
      if (props.type === 'batch') {
        exportRequest = recordExportBatch(key, exportRequestFn);
      }

      if (exportRequest) {
        try {
          await exportRequest;
          selectedIds.clear();
          Message.success('正在导出，稍后可前往任务列表查看进度');
        } catch (error) {
          console.error(error);
          Message.error('导出失败, 请稍后再试');
        }
      }
    };

    /** 无限滚动 */
    const handleLoadMore = async () => {
      // 请求
      return request({
        ...searchFilter.value,
        pageIndex: pagination.value.pageIndex + 1,
      });
    };

    const { isLoadingMore, resetScroll, initScroll } = useInfiniteScroll(handleLoadMore, {
      direction: 'bottom',
      canLoadMore: () => {
        return pagination.value.total > dataSource.value.length;
      },
    });

    onMounted(async () => {
      const container = document.getElementById('workbench-layout-main');
      if (container) {
        initScroll(container);
      }
      await request();
      isInit.value = false;
    });

    return {
      data,
      dataSource,
      isInit,
      isLoading,
      isSuccess,
      isError,
      pagination,

      operationInfo,

      selectedIds,

      handleExport,
      isLoadingMore,
      resetScroll,

      handleSearch,
      resultFilterOptions,
    };
  },
  render() {
    return (
      <div>
        {/* 面包屑导航 */}
        <IdentityVerificationBreadcrumb
          pageType={this.$route.params.pageType}
          operationInfo={this.operationInfo as { createBy: number; createDate: string } | undefined}
        />

        <HeroicLayout
          loading={this.isInit}
          innerStyle={{
            minHeight: 'calc(100vh - 52px - 60px)',
          }}
        >
          <div class={styles.container}>
            {/* 核验结果 */}
            <Card>
              <div slot="title" class={styles.cardTitle}>
                共找到
                <Icon v-show={this.isLoading} type="sync" spin />
                <em v-show={!this.isLoading}>{this.pagination.total}</em>
                条相关结果
                {/* ，其中
                <Icon v-show={this.isLoading} type="sync" spin />
                <em v-show={!this.isLoading} class={styles.matched}>
                  matched
                </em>
                条匹配 */}
              </div>

              <SearchAction
                slot="extra"
                pagination={this.pagination}
                selectedIds={this.selectedIds.data.value}
                resultFilterOptions={this.resultFilterOptions}
                onSubmit={this.handleSearch}
                onExport={this.handleExport}
              />

              {/* 有核验结果 */}
              <IdentityVerificationSearchResult
                dataSource={this.dataSource}
                selectedIds={this.selectedIds.data.value}
                onSelect={this.selectedIds.update}
              />

              {/* 加载更多 */}
              <div class={styles.loadMore} v-show={this.isLoadingMore}>
                <Icon type="sync" spin />
                <span>加载更多...</span>
              </div>
            </Card>
          </div>
        </HeroicLayout>
      </div>
    );
  },
});

export default IdentityVerificationDetail;
