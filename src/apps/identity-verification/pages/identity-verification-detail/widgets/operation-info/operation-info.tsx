import { defineComponent } from 'vue';

import styles from './operation-info.module.less';

interface IOperationInfoProps {
  operator: {
    name: string;
    phone: string;
  };
  operationTime: string;
}

const OperationInfo = defineComponent({
  functional: true,
  props: {
    operator: {
      type: Object as () => IOperationInfoProps['operator'],
      required: true,
    },
    operationTime: {
      type: String,
      required: true,
    },
  },
  render(h, { props }) {
    const { operator, operationTime } = props;

    return (
      <div class={styles.container}>
        <span class={styles.operatorInfo}>
          操作人：{operator.name}（{operator.phone}）
        </span>
        <span class={styles.operationTime}>操作时间：{operationTime}</span>
      </div>
    );
  },
});
export default OperationInfo;
