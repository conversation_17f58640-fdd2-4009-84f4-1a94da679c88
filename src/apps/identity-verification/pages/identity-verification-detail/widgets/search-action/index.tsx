import { computed, defineComponent, PropType, ref } from 'vue';
import { Menu } from 'ant-design-vue';
import { find, isNil } from 'lodash';

import TinySearch from '@/components/global/q-tiny-search';
import DropdownButtonWrapper from '@/shared/components/dropdown-button-wrapper';
import DropdownButton from '@/components/dropdown-button';
import { EXPORTITEMS } from '@/config/record.config';
import { Permission } from '@/config/permissions.config';

import styles from './search-action.module.less';

const SearchAction = defineComponent({
  name: 'SearchAction',
  props: {
    resultFilterOptions: {
      type: Array as PropType<any>,
      default: () => [],
    },
    // searchFilter: {
    //   type: Object as PropType<any>,
    //   default: () => ({}),
    // },
    pagination: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
    selectedIds: {
      type: Array as PropType<any>,
      default: () => [],
    },
  },
  emits: ['submit', 'export'],
  setup(props, { emit }) {
    const state = ref<{ searchKey?: string; recordVerificationResults?: string }>({
      searchKey: undefined,
      recordVerificationResults: undefined,
    });

    /** 下拉菜单选中显示文本 */
    const dropdownSelectedText = computed(() => {
      const defaultLabel = '核验结果';

      const target = find(props.resultFilterOptions, {
        key: state.value.recordVerificationResults,
      });
      if (!target || target.label === '不限') {
        return defaultLabel;
      }
      return target.label;
    });

    const submit = () => {
      emit('submit', state.value);
    };

    const handleResultFilter = (recordVerificationResults) => {
      state.value.recordVerificationResults = recordVerificationResults;
      submit();
    };
    const handleSearchKey = (searchKey?: string) => {
      state.value.searchKey = searchKey;
      submit();
    };

    const handleExport = (exportType: string) => {
      emit('export', exportType);
    };

    return {
      handleSearchKey,
      handleResultFilter,

      handleExport,
      state,
      dropdownSelectedText,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <TinySearch
          placeholder="请输入人名或指定企业名称"
          modeType="Default"
          searchPosition="left"
          inputWidth="256px"
          v-model={this.state.searchKey}
          onSearch={(searchKey?: string) => {
            this.handleSearchKey(searchKey);
          }}
          onCancel={() => {
            this.handleSearchKey();
          }}
        />
        <DropdownButton text={this.dropdownSelectedText}>
          <Menu
            slot="overlay"
            onClick={({ key }) => {
              this.handleResultFilter(key);
            }}
          >
            {this.resultFilterOptions.map((item) => (
              <Menu.Item key={item.key}>
                <div class={styles.filterMenuItem}>
                  <span>{item.label}</span>
                  <em v-show={!isNil(item.count)}>({item.count})</em>
                </div>
              </Menu.Item>
            ))}
          </Menu>
        </DropdownButton>
        <DropdownButtonWrapper
          v-permission={[Permission.IDENTITY_VERIFICATION_EXPORT]}
          needPopConfirm={false}
          totalCount={this.pagination.total}
          selectIdlength={this.selectedIds.length}
          btnText="导出列表"
          menuItems={EXPORTITEMS}
          onConfirm={(key) => {
            this.handleExport(key);
            // if (key === 'export') {
            //   this.$track(createTrackEvent(8169, '批量特定利益关系排查结果', '全部导出'));
            // } else {
            //   this.$track(createTrackEvent(8169, '批量特定利益关系排查结果', '选中导出'));
            // }
          }}
        />
      </div>
    );
  },
});

export default SearchAction;
