import { defineComponent, PropType } from 'vue';

import Empty from '@/shared/components/empty';
import { IVerificationDetail } from '@/apps/identity-verification/components/identity-verification-match-result';

import IdentityVerificationRecord from '../../../../components/identity-verification-record';
import styles from './search-result.module.less';

export interface IDataSourceItem {
  verificationId: number;
  verificationType: number;
  compName: string;
  compId: string;
  compCreditCode: string;
  personName: string;
  personIdcardEncrypted: string;
  personIdcardMask: string;
  personKeyNo?: string;
  personRole: string;
  verificationResult: number;
  createBy: number;
  updateBy: number;
  createDate: string;
  updateDate: string;
  status: number;
  orgId: number;
  verificationDetails: IVerificationDetail[];
}

const IdentityVerificationSearchResult = defineComponent({
  name: 'IdentityVerificationSearchResult',
  props: {
    dataSource: {
      type: Array as PropType<IDataSourceItem[]>,
      required: true,
      default: () => [],
    },
    selectedIds: {
      type: Array as PropType<number[]>,
      required: true,
      default: () => [],
    },
  },
  emits: ['select'],
  setup(props, { emit }) {
    const handleSelectResult = (id: number, checked: boolean) => {
      emit('select', id, checked);
    };

    return {
      handleSelectResult,
    };
  },
  render() {
    const { dataSource, selectedIds, handleSelectResult } = this;
    if (dataSource.length === 0) {
      return (
        <div class={styles.empty}>
          <Empty type="search" description="暂时没有找到相关数据" />
        </div>
      );
    }

    return (
      <div class={styles.result}>
        {dataSource.map((record) => {
          const personInfo = {
            name: record.personName,
            idNumber: record.personIdcardMask,
            keyNo: record.personKeyNo,
            verificationType: record.verificationType,
          };
          const companyInfo = {
            name: record.compName,
            idNumber: record.compCreditCode,
          };
          const verificationDetails = {
            companyData: record.verificationDetails.filter((detail) => detail.compType === 1),
            relatedCompanyData: record.verificationDetails.filter((detail) => detail.compType === 2),
          };
          return (
            <IdentityVerificationRecord
              key={record.verificationId}
              checked={selectedIds.includes(record.verificationId)}
              onCheck={(checked) => handleSelectResult(record.verificationId, checked)}
              personInfo={personInfo}
              companyInfo={companyInfo}
              verificationDetails={verificationDetails}
            />
          );
        })}
      </div>
    );
  },
});

export default IdentityVerificationSearchResult;
