import type { RouteConfig } from 'vue-router';

import SidebarMenuLayout from '@/shared/layouts/sidebar-menu';

/**
 * 个人中心菜单配置
 */
const ACCOUNT_MENU_CONFIG = [
  {
    key: 'account',
    icon: 'icon-renyuan<PERSON><PERSON><PERSON>',
    label: 'Account',
    children: [
      {
        key: '/account/settings',
        label: 'Account Settings',
      },
      {
        key: '/account/organization',
        label: 'Account Organization',
      },
    ],
  },
];

export const accountRoutes = (): RouteConfig[] => [
  // 个人中心
  {
    path: '/account',
    component: SidebarMenuLayout,
    props: {
      menu: ACCOUNT_MENU_CONFIG,
      pageTitle: '个人中心',
    },
    meta: {
      title: '个人中心',
    },
    children: [
      {
        path: 'settings',
        name: 'account-settings',
        component: () => import('../pages/account-settings'),
        meta: {
          title: '个人中心',
        },
      },
      {
        path: 'organization',
        name: 'account-organization',
        component: () => import('../pages/account-organization'),
        meta: {
          title: '组织信息',
        },
      },
    ],
  },
];
