import { Button, message as Message, Modal } from 'ant-design-vue';
import { computed, defineComponent, ref, unref } from 'vue';
import type { WrappedFormUtils } from 'ant-design-vue/types/form/form';

import QModal from '@/components/global/q-modal';
import { user as userService } from '@/shared/services';
import { useStore } from '@/store';
import RiskTrendsTab from '@/components/tabs';

import ChangePasswordForm from './form';
import styles from './change-password-modal.module.less';

const DEFAULT_TABS = [
  { label: '手机验证码修改', value: 'phone' },
  { label: '邮箱验证码修改', value: 'email' },
];

const ChangePasswordModal = defineComponent({
  name: 'ChangePasswordModal',
  model: {
    prop: 'visible',
    event: 'change',
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
      default: 600,
    },
  },
  emits: ['change', 'success'],
  setup(props, { emit }) {
    // Store
    const store = useStore();
    const userProfile = computed(() => store.getters['user/profile']);

    const submitStatus = ref<'idle' | 'pending' | 'success' | 'error'>('idle');
    const formRef = ref<WrappedFormUtils>();
    const showTab = computed(() => userProfile.value.phone && userProfile.value.email);
    const currentTab = ref(userProfile.value.phone ? 'phone' : 'email');

    const strategies = {
      // 提交成功
      success: () => {
        // 成功提示消息
        Modal.success({
          title: '修改成功！',
          okText: '知道了',
        });
        // 关闭弹窗口
        emit('change', false);
        // 修改成功
        emit('success');
      },

      // Axios error
      axiosError: (error) => {
        if (error?.response?.data?.code === 200416) {
          // 已开通帐号
          Message.warning('当前手机号已开通，无需重复申请');
        } else {
          // 错误消息
          Message.error(error?.response?.data?.error ?? '操作出错，请稍后再试');
        }
      },

      // 其它错误
      commonError: (error) => {
        Message.warning(error?.message ?? '操作出错，请稍后再试');
      },
    };

    /**
     * 表单验证
     * @param formError
     * @param formData
     */
    const formValidator = async (formError, formData) => {
      if (formError) {
        Message.error('请填写必填项！');
        return;
      }
      try {
        submitStatus.value = 'pending';
        const res = await userService.changePassword({
          email: formData.email,
          phone: formData.phone,
          newPassword: formData.password,
          verifyCode: formData.verifyCode,
          confirmPassword: formData.confirmPassword,
        });
        if (res?.status === 200) {
          submitStatus.value = 'success';
          strategies.success();
        } else {
          throw new Error('操作出错，请稍后再试');
        }
      } catch (error) {
        submitStatus.value = 'error';
      }
    };

    /**
     * 提交表单
     */
    const handleFormSubmit = async () => {
      const formRefRaw = unref(formRef);
      formRefRaw?.validateFields?.(formValidator);
    };

    const handleClose = () => {
      emit('change', false);
    };

    const handleTabChange = (val) => {
      currentTab.value = val;
    };

    return {
      formRef,
      handleFormSubmit,
      handleClose,
      submitStatus,

      userProfile,
      currentTab,
      showTab,
      handleTabChange,
    };
  },
  render() {
    return (
      <QModal
        destroyOnClose={true}
        visible={this.visible}
        size="medium"
        bodyStyle={{ padding: 0 }}
        onCancel={this.handleClose}
        title="修改密码"
      >
        <div slot="footer">
          <Button onClick={this.handleClose}>取消</Button>
          <Button type="primary" onClick={this.handleFormSubmit} loading={this.submitStatus === 'pending'}>
            确定
          </Button>
        </div>

        <div class={styles.form}>
          {this.showTab && (
            <RiskTrendsTab tabs={DEFAULT_TABS} value={this.currentTab} onChange={this.handleTabChange} class={styles.tabs} />
          )}
          <ChangePasswordForm userProfile={this.userProfile} ref="formRef" formType={this.currentTab} />
        </div>
      </QModal>
    );
  },
});

export default ChangePasswordModal;
