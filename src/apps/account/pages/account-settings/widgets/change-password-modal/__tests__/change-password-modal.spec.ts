import { mount, shallowMount } from '@vue/test-utils';
import { Button } from 'ant-design-vue';

import RiskTrendsTab from '@/components/tabs';
import { flushPromises } from '@/test-utils/flush-promises';

import ChangePasswordModal from '..';

vi.mock('@/store', () => ({
  useStore: () => ({
    getters: {
      'user/profile': {
        name: 'NAME',
        phone: 'PHONE',
        email: 'EMAIL',
      },
    },
  }),
}));

vi.mock('@/shared/services');

describe('ChangePasswordModal', () => {
  let wrapper: ReturnType<typeof shallowMount>;

  beforeEach(() => {
    wrapper = shallowMount<InstanceType<typeof ChangePasswordModal>>(ChangePasswordModal, {
      propsData: {
        visible: true,
      },
    });
  });

  test('render', () => {
    expect(wrapper).toMatchSnapshot();
  });

  test('events: change tab', async () => {
    wrapper.findComponent(RiskTrendsTab).vm.$emit('change', 'email');
    await flushPromises();
    expect(wrapper).toMatchSnapshot();
  });

  test('events: cancel', async () => {
    wrapper = mount(ChangePasswordModal, {
      propsData: {
        visible: true,
      },
    });
    await wrapper.vm.$nextTick();
    const button = wrapper.findAllComponents(Button);
    button.at(1).trigger('click');
    expect(wrapper.emitted('change')).toEqual([[false]]);
  });
});
