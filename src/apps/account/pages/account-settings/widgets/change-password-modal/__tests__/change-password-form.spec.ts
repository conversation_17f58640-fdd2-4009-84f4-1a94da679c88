import { shallowMount } from '@vue/test-utils';
import { Button } from 'ant-design-vue';

import { user as userService } from '@/shared/services';

import ChangePasswordForm from '../form';

vi.mock('@/store', () => ({
  useStore: () => ({
    getters: {
      'user/profile': {
        name: 'NAME',
        phone: 'PHONE',
        email: 'EMAIL',
      },
    },
  }),
}));

describe('ChangePasswordForm', () => {
  let wrapper: ReturnType<typeof shallowMount>;
  const mockFormValidator = vi.fn();

  beforeEach(() => {
    wrapper = shallowMount<InstanceType<typeof ChangePasswordForm>>(ChangePasswordForm, {
      propsData: {
        form: {
          getFieldValue: mockFormValidator,
          getFieldDecorator: vi.fn(),
        },
      },
    });
  });

  test('render', () => {
    expect(wrapper).toMatchSnapshot();
  });

  test('render: formType - email', async () => {
    await wrapper.setProps({
      formType: 'email',
    });
    expect(wrapper).toMatchSnapshot();
  });
});
