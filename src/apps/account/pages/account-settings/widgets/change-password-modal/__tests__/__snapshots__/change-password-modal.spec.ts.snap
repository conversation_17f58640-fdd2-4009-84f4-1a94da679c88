// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`ChangePasswordModal > events: change tab 1`] = `
<anonymous-stub afterclose="[Function]" visible="true" destroyonclose="true" title="[object Object]" footer="[object Object]" transitionname="zoom" masktransitionname="fade" bodystyle="[object Object]" prefixcls="ant-modal" wrapclassname="" width="600" dialogstyle="[object Object]" dialogclass="" closeicon="[object Object]" class="">
  <div class="form">
    <div class="container tabs">
      <div data-testid="手机验证码修改" class="tab">手机验证码修改</div>
      <div data-testid="邮箱验证码修改" class="tab active">邮箱验证码修改</div>
    </div>
    <changepasswordform-stub form="[object Object]" userprofile="[object Object]" formtype="email"></changepasswordform-stub>
  </div>
</anonymous-stub>
`;

exports[`ChangePasswordModal > render 1`] = `
<anonymous-stub afterclose="[Function]" visible="true" destroyonclose="true" title="[object Object]" footer="[object Object]" transitionname="zoom" masktransitionname="fade" bodystyle="[object Object]" prefixcls="ant-modal" wrapclassname="" width="600" dialogstyle="[object Object]" dialogclass="" closeicon="[object Object]" class="">
  <div class="form">
    <div class="container tabs">
      <div data-testid="手机验证码修改" class="tab active">手机验证码修改</div>
      <div data-testid="邮箱验证码修改" class="tab">邮箱验证码修改</div>
    </div>
    <changepasswordform-stub form="[object Object]" userprofile="[object Object]" formtype="phone"></changepasswordform-stub>
  </div>
</anonymous-stub>
`;
