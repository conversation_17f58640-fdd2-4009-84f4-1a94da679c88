import { Form, Input, message as Message } from 'ant-design-vue';
import { get } from 'lodash';
import { defineComponent, PropType, reactive, ref, unref } from 'vue';
import type { WrappedFormUtils } from 'ant-design-vue/types/form/form';

import NoCaptcha from '@/components/global/q-captcha';
import CountDown from '@/components/global/q-count-down';
import env from '@/shared/config/env';
import { user as userService } from '@/shared/services';
import { createTrackEvent, useTrack } from '@/config/tracking-events';

import styles from './change-password-form.module.less';

const ChangePasswordForm = defineComponent({
  name: 'ChangePasswordForm',
  props: {
    form: {
      type: Object as PropType<WrappedFormUtils>,
      required: false,
    },
    userProfile: {
      type: Object,
      required: false,
    },
    formType: {
      type: String as PropType<'email' | 'phone'>,
      default: 'phone',
    },
  },
  setup(props) {
    const isCaptchaValid = ref(false);
    const verifyToken = ref<string>();

    const noCaptchaRef = ref<HTMLElement>();
    const track = useTrack();
    /**
     * 人机验证回调
     */
    const handleCaptchaSuccess = (token: string) => {
      if (token) {
        verifyToken.value = token;
        isCaptchaValid.value = true;
      } else {
        isCaptchaValid.value = false;
      }
    };

    /**
     * 发送验证码
     */
    const handleSendVerifyCode = async (callback) => {
      const account = props.form?.getFieldValue(props.formType);
      if (!verifyToken.value) {
        Message.warning('请拖动滑块进行人机验证');
        return false;
      }
      track(createTrackEvent(7687, '账号设置', '发送验证码'));
      try {
        const res = await userService.sendChangePasswordSmsCode({
          [props.formType]: account,
          token: verifyToken.value,
        });
        if (!res?.[props.formType]) {
          Message.error('验证码发送失败，请重试');
          return false;
        }
        Message.success('验证码发送成功');
        callback(); // 回调进行计时
        return true;
      } catch (error) {
        // Message.error('验证码发送失败，请重试');
        const code = get(error, 'response.data.code');
        if (code === 200502) {
          unref<any>(noCaptchaRef)?.reset?.();
        }
        return false;
      }
    };

    return {
      handleCaptchaSuccess,
      isCaptchaValid,
      noCaptchaRef,
      handleSendVerifyCode,
    };
  },
  render() {
    const labelCol = { flex: '0 0 78px' };
    const wrapperCol = { flex: 1 };
    const fieldLabel = this.formType === 'email' ? '邮箱' : '手机';

    return (
      <Form layout="horizontal" hideRequiredMark={true} colon={false} form={this.form} class={styles.container}>
        {this.formType === 'email' ? (
          <Form.Item label="电子邮箱" labelCol={labelCol} wrapperCol={wrapperCol}>
            <Input
              disabled={this.userProfile?.email !== undefined}
              placeholder="请输入联系人邮箱"
              size="large"
              allowClear
              maxLength={100}
              v-decorator={[
                'email',
                {
                  initialValue: this.userProfile?.email,
                  rules: [
                    {
                      required: true,
                      pattern: /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/,
                      message: '请输入正确的邮箱地址',
                    },
                  ],
                },
              ]}
            />
          </Form.Item>
        ) : (
          <Form.Item label="手机号" labelCol={labelCol} wrapperCol={wrapperCol}>
            <Input
              disabled={this.userProfile?.phone !== undefined}
              placeholder="请输入联系人手机号"
              size="large"
              allowClear
              maxLength={11}
              v-decorator={[
                'phone',
                {
                  initialValue: this.userProfile?.phone,
                  rules: [
                    {
                      required: true,
                      pattern: /^1[3-9]\d{9}$/,
                      message: '请输入正确的手机号码',
                    },
                  ],
                },
              ]}
            />
          </Form.Item>
        )}
        <Form.Item label="完成验证" labelCol={labelCol} wrapperCol={wrapperCol}>
          <NoCaptcha
            ref="noCaptchaRef"
            sceneId={env.AFS_SCENE_ID}
            size="lg"
            onSuccess={(data) => {
              this.handleCaptchaSuccess(data);
              this.form?.setFieldsValue({ verifyCode: undefined });
            }}
          />
        </Form.Item>
        <Form.Item label="验证码" labelCol={labelCol} wrapperCol={wrapperCol}>
          <Input
            placeholder={`请输入${fieldLabel}验证码`}
            size="large"
            allowClear
            maxLength={6}
            v-decorator={[
              'verifyCode',
              {
                rules: [
                  {
                    required: true,
                    pattern: /^\d{6}$/,
                    message: `请输入正确的${fieldLabel}验证码`,
                  },
                ],
              },
            ]}
          >
            <CountDown
              ref="CountDown"
              slot="addonAfter"
              value={60}
              title="获取验证码"
              format="{{count}}秒后重发"
              disabled={!this.isCaptchaValid}
              onClick={this.handleSendVerifyCode}
            />
          </Input>
        </Form.Item>
        <Form.Item label="新密码" labelCol={labelCol} wrapperCol={wrapperCol}>
          <Input.Password
            autocomplete="new-password"
            size="large"
            placeholder="请输入新密码"
            maxLength={40}
            visibilityToggle
            v-decorator={[
              'password',
              {
                rules: [
                  {
                    required: true,
                    validator: (rule, value, callback) => {
                      if (value === undefined || value.length < 8) {
                        callback('密码必须至少有8个字符');
                      } else if (value.length > 18) {
                        callback('密码不能超过18个字符');
                      } else if (!/[A-Z]/.test(value)) {
                        callback('密码至少包含一个大写字母');
                      } else if (!/[a-z]/.test(value)) {
                        callback('密码至少包含一个小写字母');
                      } else if (!/[0-9]/.test(value)) {
                        callback('密码至少包含一个数字');
                      } else if (!/[~!@#$%^&*()\-=_+{}[\]<>,.?/\\]/.test(value)) {
                        callback('密码至少包含一个特殊符号');
                      } else {
                        callback();
                      }
                    },
                  },
                ],
              },
            ]}
          />
        </Form.Item>
        <Form.Item label="确认密码" labelCol={labelCol} wrapperCol={wrapperCol} extra="注：密码由8-18位大小写字母+数字+特殊符号组成">
          <Input.Password
            autocomplete="new-password"
            size="large"
            placeholder="请再次输入新密码"
            maxLength={40}
            visibilityToggle
            v-decorator={[
              'confirmPassword',
              {
                rules: [
                  {
                    required: true,
                    validator: (rule, value, callback) => {
                      const password = this.form?.getFieldValue('password'); // 密码
                      if (password !== value) {
                        callback('两次密码不一致');
                      } else {
                        callback();
                      }
                    },
                  },
                ],
              },
            ]}
          />
          {/* <div class={styles.inlineTip}>注：密码格式为8-18位大小写字母+数字+特殊符号</div> */}
        </Form.Item>
      </Form>
    );
  },
});

export default Form.create({})(ChangePasswordForm);
