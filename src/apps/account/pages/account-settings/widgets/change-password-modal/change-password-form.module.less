.container {
  :global {
    .ant-row {
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .ant-form-item-label {
      text-align: left;
    }

    .ant-form-item-control {
      line-height: 40px;
    }

    .ant-form-item-required::before {
      position: static;
    }

    // 错误提示
    .ant-form-explain {
      margin-top: 10px;
    }

    // 帮助提示: extra
    .ant-form-extra {
      margin-top: 10px;
      padding: 0;
      font-size: 13px;
      line-height: 20px;
      color: #999;
    }

    // 输入框右方按钮：获取验证码
    .ant-input-group-addon {
      background: #fff;
    }

    // 密码隐藏图标
    .ant-input-password-icon {
      &:hover {
        color: #128bed;
      }
    }

    .ant-row.ant-form-item {
      display: flex;
    }
  }
}
