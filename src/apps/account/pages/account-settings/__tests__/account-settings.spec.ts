import { shallowMount } from '@vue/test-utils';

import AccountSettings from '..';
import ChangePasswordModal from '../widgets/change-password-modal';

vi.mock('@/store', () => ({
  useStore: () => ({
    getters: {
      'user/profile': {
        name: 'NAME',
        phone: 'PHONE',
        email: 'EMAIL',
      },
    },
  }),
}));

describe('AccountSettings', () => {
  let wrapper: ReturnType<typeof shallowMount>;

  beforeEach(() => {
    // Arrange
    wrapper = shallowMount<InstanceType<typeof AccountSettings>>(AccountSettings);
  });

  test('render', () => {
    expect(wrapper).toMatchSnapshot();
  });

  test('events: change password', async () => {
    // Act
    const modifyButton = wrapper
      .findAll('a')
      .filter((node) => node.text() === '修改')
      .at(0);

    // Assert
    expect(modifyButton.exists()).toBe(true);
    expect(wrapper.findComponent(ChangePasswordModal).props('visible')).toBe(false);
    await modifyButton.trigger('click'); // 显示修改密码弹窗
    expect(wrapper.findComponent(ChangePasswordModal).props('visible')).toBe(true);
  });

  test('events: change password success', async () => {
    // Arrange
    Object.defineProperty(window, 'location', {
      value: { href: 'http://localhost/#/account/settings' },
      writable: true,
    });

    // Act
    wrapper.findComponent(ChangePasswordModal).vm.$emit('success');
    expect(window.location.href).toBe('/qcc/user/buser/logout');
  });
});
