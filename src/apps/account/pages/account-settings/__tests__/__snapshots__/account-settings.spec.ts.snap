// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`AccountSettings > render 1`] = `
<hero-layout-stub>
  <div class="root">
    <div class="header">
      <div class="wrapper border">
        <div class="title">帐号信息</div>
      </div>
    </div>
    <div class="body" style="padding: 15px;">
      <div class="form">
        <div class="row">
          <div class="label">姓名</div>
          <anonymous-stub prefixcls="ant-input" inputtype="input" value="NAME" element="[object Object]" handlereset="[Function]" disabled="true" class="input"></anonymous-stub>
        </div>
        <div class="row">
          <div class="label">手机号码</div>
          <anonymous-stub prefixcls="ant-input" inputtype="input" value="PHONE" element="[object Object]" handlereset="[Function]" disabled="true" class="input"></anonymous-stub>
        </div>
        <div class="row">
          <div class="label">电子邮箱</div>
          <anonymous-stub prefixcls="ant-input" inputtype="input" value="EMAIL" element="[object Object]" handlereset="[Function]" disabled="true" class="input"></anonymous-stub>
        </div>
        <div class="row">
          <div class="label">登录密码</div>
          <anonymous-stub prefixcls="ant-input" inputtype="input" value="*****" element="[object Object]" handlereset="[Function]" disabled="true" class="input password"></anonymous-stub><a class="action">修改</a>
        </div>
      </div>
    </div>
  </div>
  <anonymous-stub afterclose="[Function]" destroyonclose="true" title="[object Object]" footer="[object Object]" transitionname="zoom" masktransitionname="fade" bodystyle="[object Object]" prefixcls="ant-modal" wrapclassname="" width="600" dialogstyle="[object Object]" dialogclass="" closeicon="[object Object]" class="">
    <div class="form">
      <div class="container tabs">
        <div data-testid="手机验证码修改" class="tab active">手机验证码修改</div>
        <div data-testid="邮箱验证码修改" class="tab">邮箱验证码修改</div>
      </div>
      <changepasswordform-stub form="[object Object]" userprofile="[object Object]" formtype="phone"></changepasswordform-stub>
    </div>
  </anonymous-stub>
</hero-layout-stub>
`;
