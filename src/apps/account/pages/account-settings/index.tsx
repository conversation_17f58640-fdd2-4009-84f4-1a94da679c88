import { Input } from 'ant-design-vue';
import { computed, defineComponent, ref } from 'vue';

import QCard from '@/components/global/q-card';
import HeroicLayout from '@/shared/layouts/heroic';
import { useStore } from '@/store';
import { logoutURI } from '@/shared/composables/use-auth';
import { createTrackEvent, useTrack } from '@/config/tracking-events';

import styles from './account-settings.module.less';
import ChangePasswordModal from './widgets/change-password-modal';

const AccountSettingsPage = defineComponent({
  name: 'AccountSettingsPage',
  model: {
    prop: 'visible',
    event: 'change',
  },
  width: {
    type: String,
    default: '600px',
  },
  setup() {
    // Store
    const store = useStore();
    const track = useTrack();
    const userProfile = computed(() => store.getters['user/profile']);
    // 修改密码弹窗
    const accountModalVisible = ref(false);
    const updateAccountModalVisible = (isVisible: boolean) => {
      accountModalVisible.value = isVisible;
      track(createTrackEvent(7687, '账号设置', '修改密码'));
    };
    const submitStatus = ref<'idle' | 'pending' | 'success' | 'error'>('idle');

    const handleSuccess = () => {
      window.location.href = logoutURI();
      track(createTrackEvent(7687, '账号设置', '修改密码确认'));
    };

    return {
      userProfile,
      submitStatus,

      accountModalVisible,
      updateAccountModalVisible,
      handleSuccess,
    };
  },
  render() {
    return (
      <HeroicLayout align={undefined}>
        <QCard bodyStyle={{ padding: '15px' }} title="帐号信息">
          <div class={styles.form}>
            <div class={styles.row}>
              <div class={styles.label}>姓名</div>
              <Input class={styles.input} value={this.userProfile.name} disabled />
            </div>
            {this.userProfile.phone && (
              <div class={styles.row}>
                <div class={styles.label}>手机号码</div>
                <Input class={styles.input} value={this.userProfile.phone} disabled />
              </div>
            )}
            {this.userProfile.email && (
              <div class={styles.row}>
                <div class={styles.label}>电子邮箱</div>
                <Input class={styles.input} value={this.userProfile.email} disabled />
              </div>
            )}
            <div class={styles.row}>
              <div class={styles.label}>登录密码</div>
              <Input class={[styles.input, styles.password]} value="*****" disabled />
              <a class={styles.action} onClick={() => this.updateAccountModalVisible(true)}>
                修改
              </a>
            </div>
          </div>
        </QCard>
        <ChangePasswordModal v-model={this.accountModalVisible} onSuccess={this.handleSuccess} />
      </HeroicLayout>
    );
  },
});

export default AccountSettingsPage;
