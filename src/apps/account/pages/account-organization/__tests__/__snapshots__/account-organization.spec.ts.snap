// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`AccountOrganization > render 1`] = `
<hero-layout-stub>
  <div class="root">
    <div class="header">
      <div class="wrapper border">
        <div class="title">组织信息</div>
      </div>
    </div>
    <div class="body">
      <div class="groupList">
        <div class="everyGroup">
          <div class="avatar" style="background: #E79177;">O</div>
          <div class="leftEveryGroup">
            <div class="companyName">ORG_NAME_1</div>
            <div class="companyOtherInfo"><span style="color: #999;">超级管理员, 管理员</span><span class="time">2020-06-18 09:48:39&nbsp;&nbsp;加入</span></div>
          </div>
        </div>
        <div class="everyGroup">
          <div class="avatar" style="background: #97a2e2;">O</div>
          <div class="leftEveryGroup">
            <div class="companyName">ORG_NAME_2</div>
            <div class="companyOtherInfo"><span style="color: #999;">超级管理员</span><span class="time">2021-02-18 16:37:07&nbsp;&nbsp;加入</span></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</hero-layout-stub>
`;
