import { shallowMount } from '@vue/test-utils';

import AccountOrganization from '..';

vi.mock('@/store', () => ({
  useStore: () => ({
    getters: {
      'user/organizations': [
        {
          organizationId: 1,
          name: 'ORG_NAME_1',
          createDate: '2020-06-18T01:48:39.000Z',
          roles: [
            {
              roleName: '超级管理员',
            },
            {
              roleName: '管理员',
            },
          ],
        },
        {
          organizationId: 2,
          name: 'ORG_NAME_2',
          createDate: '2021-02-18T08:37:07.000Z',
          roles: [
            {
              roleName: '超级管理员',
            },
          ],
        },
      ],
    },
  }),
}));

describe('AccountOrganization', () => {
  test('render', () => {
    const wrapper = shallowMount<InstanceType<typeof AccountOrganization>>(AccountOrganization);
    expect(wrapper).toMatchSnapshot();
  });
});
