.groupList {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  .everyGroup {
    display: flex;
    align-items: center;
    width: 49.5%;
    margin-bottom: 20px;
    padding: 20px;
    text-align: center;
    border: 1px solid #eee;
    border-radius: 4px;
  }

  .everyGroup .avatar {
    width: 60px;
    height: 60px;
    margin-right: 15px;
    color: #fff;
    font-size: 32px;
    line-height: 60px;
    text-align: center;
    border-radius: 50%;
  }

  .everyGroup .leftEveryGroup {
    width: 90%;
  }

  .everyGroup .leftEveryGroup .companyName {
    color: #333;
    font-weight: normal;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0;
    text-align: left;
  }

  .everyGroup .leftEveryGroup .companyOtherInfo {
    display: flex;
    justify-content: space-between;
    margin-top: 4px;

    span:nth-of-type(2) {
      color: #999;
    }

    .time {
      color: #999;
    }
  }
}
