import moment from 'moment';
import { computed, defineComponent } from 'vue';

import QCard from '@/components/global/q-card';
import { THEME_COLOR } from '@/shared/config/theme-color.config';
import HeroicLayout from '@/shared/layouts/heroic';
import { useStore } from '@/store';

import styles from './account-organization.module.less';

export default defineComponent({
  name: 'PersonalOrganization',
  setup() {
    const store = useStore();
    const newOrganizations = computed(() =>
      store.getters['user/organizations'].map((item, index) => {
        item.themeColor = `background:${THEME_COLOR[index % 4].color}`;
        return item;
      })
    );

    return {
      newOrganizations,
    };
  },
  render() {
    return (
      <HeroicLayout>
        <QCard title="组织信息">
          <div class={styles.groupList}>
            {this.newOrganizations.length > 0 &&
              this.newOrganizations.map((v: any) => (
                <div class={styles.everyGroup} key={v.organizationId}>
                  <div class={styles.avatar} style={v.themeColor}>
                    {v.name.slice(0, 1)}
                  </div>
                  <div class={styles.leftEveryGroup}>
                    <div class={styles.companyName}>{v.name}</div>
                    <div class={styles.companyOtherInfo}>
                      <span style={'color: #999'}>{v.roles.map(({ roleName }) => roleName).join(', ')}</span>
                      <span class={styles.time}>{moment(v.createDate).format('YYYY-MM-DD HH:mm:ss')}&nbsp;&nbsp;加入</span>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </QCard>
      </HeroicLayout>
    );
  },
});
