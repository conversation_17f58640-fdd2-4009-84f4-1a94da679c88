import type { RouteConfig } from 'vue-router';

import SidebarMenuLayout from '@/shared/layouts/sidebar-menu';
import { APP_MENU_CONFIG } from '@/config/menu.config';
import { Permission } from '@/config/permissions.config';
import { hasPermission } from '@/shared/composables/use-permission';

export const preLoanVerificationRoutes = (): RouteConfig[] => [
  {
    path: '/pre-loan-verification',
    component: SidebarMenuLayout,
    redirect: () => {
      if (hasPermission([Permission.PRE_LOAN_VERIFICATION_VIEW])) {
        return {
          name: 'pre-loan-verification-start',
        };
      }
      return {
        name: 'pre-loan-verification-history',
      };
    },
    props: {
      pageTitle: '贷前尽调',
      menu: APP_MENU_CONFIG,
    },
    meta: {
      title: '贷前尽调',
    },
    children: [
      {
        path: 'start',
        name: 'pre-loan-verification-start',
        component: () => import('../pages/identity-verification-start'),
        meta: {
          title: '贷前尽调',
          permission: [Permission.PRE_LOAN_VERIFICATION_CHECK],
        },
      },
      {
        path: 'history',
        name: 'pre-loan-verification-history',
        component: () => import('../pages/identity-verification-history'),
        meta: {
          title: '尽调记录',
          permission: [Permission.PRE_LOAN_VERIFICATION_HISTORY],
        },
      },
      {
        path: ':pageType(history|start)/detail/:recordId([0-9]+)',
        name: 'pre-loan-verification-detail',
        component: () => import('../pages/identity-verification-detail'),
        props: (route) => {
          return {
            pageType: route.params.pageType,
            recordId: parseInt(route.params.recordId, 10),
            type: route.query.type ?? 'record', // 'record' | 'batch'
          };
        },
        meta: {
          title: '尽调结果',
          permission: [Permission.PRE_LOAN_VERIFICATION_VIEW],
        },
      },
    ],
  },
];
