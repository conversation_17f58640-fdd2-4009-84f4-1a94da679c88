import { defineComponent, PropType } from 'vue';
import { Form, Input } from 'ant-design-vue';
import type { WrappedFormUtils } from 'ant-design-vue/types/form/form';

import { residentIdentityPattern } from '@/utils/validator';
import CompanySelect from '@/components/modal/supplier/company-select';
import { getHTMLText } from '@/utils';

import styles from './verification-form.module.less';

const SingleForm = defineComponent({
  name: 'SingleForm',
  props: {
    form: {
      type: Object as PropType<WrappedFormUtils>,
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
  },
  render() {
    return (
      <Form form={this.form} class={styles.formFields} hideRequiredMark colon={false} layout="inline" autocomplete="off">
        <Form.Item label="人员姓名">
          <Input
            placeholder="请输入核验人员姓名"
            maxLength={50}
            class={styles.input}
            v-decorator={[
              'name',
              {
                initialValue: this.formData.name,
                rules: [{ required: true, message: '请输入核验人员姓名' }],
                getValueFromEvent: (e) => {
                  return e.target.value.trim();
                },
              },
            ]}
            onChange={(e) => {
              this.$emit('change');
            }}
          />
        </Form.Item>
        <Form.Item label="证件号码">
          <Input
            placeholder="请输入核验人员证件号码"
            class={styles.input}
            v-decorator={[
              'idNumber',
              {
                initialValue: this.formData.idNumber,
                rules: [
                  { required: true, message: '请输入核验人员证件号码' },
                  { pattern: residentIdentityPattern, message: '请输入正确的证件号码' },
                ],
              },
            ]}
            onChange={(e) => {
              this.$emit('change');
            }}
          />
        </Form.Item>
        <Form.Item label="企业名称" class={styles.lastFormItem}>
          <CompanySelect
            showWarning={true}
            showArrow={false}
            class={styles.select}
            placeholder="请输入企业名称或统一社会信用代码"
            size="large"
            v-decorator={[
              'companyName',
              {
                initialValue: this.formData.companyName,
                rules: [{ required: true, message: '请输入企业名称' }],
              },
            ]}
            onChange={(_, option) => {
              this.form?.setFields({
                companyName: { value: getHTMLText(option?.label) },
                companyId: { value: option?.KeyNo },
              });
              this.$emit('change');
            }}
          />
        </Form.Item>

        <Form.Item v-show={false}>
          <Input v-decorator={['companyId', { initialValue: this.formData.companyId }]} />
        </Form.Item>
      </Form>
    );
  },
});

const FormWrapper = Form.create({})(SingleForm);

export default FormWrapper;
