import { mount } from '@vue/test-utils';

import { identityVerification as identityService } from '@/shared/services';

import VerificationForm from '..';
import { flushPromises } from '@/test-utils/flush-promises';
import { pick } from 'lodash';

vi.mock('vue-router/composables', () => ({
  useRouter: () => ({ push: vi.fn() }),
}));
vi.mock('@/shared/services');

describe('VerificationForm', () => {
  let wrapper;
  const formData = {
    index: 1,
    name: '张三',
    idNumber: '320481199911112222',
    companyName: '公司A',
    companyId: 'cida',
  };
  beforeEach(() => {
    wrapper = mount(VerificationForm, {
      stubs: {
        CompanySelect: true,
      },
    });
    vi.mocked(identityService.identityVerify).mockResolvedValue({ batchId: 'bid' });
  });
  afterEach(() => {
    vi.resetAllMocks();
  });

  it('点击添加表单', async () => {
    wrapper.vm.handleAddForm();
    expect(wrapper.vm.forms.length).toBe(2);
  });

  it('点击删除表单', async () => {
    wrapper.vm.handleAddForm();
    wrapper.vm.handleDeleteForm(0);
    expect(wrapper.vm.forms.length).toBe(1);
  });

  it('点击批量上传触发事件', async () => {
    wrapper.vm.handleBatchVerify();
    expect(wrapper.emitted('batchVerify')).toBeTruthy();
  });

  it('点击重置表单', async () => {
    wrapper.vm.handleAddForm();
    wrapper.vm.handleReset();
    expect(wrapper.vm.forms.length).toBe(1);
  });

  it('常规核验', async () => {
    wrapper.vm.handleFormChange([formData]);
    await wrapper.vm.$nextTick();
    const btnWrapper = wrapper.findAllComponents({ name: 'AButton' });
    expect(btnWrapper.at(3).props('disabled')).toBeFalsy();
    btnWrapper.at(3).vm.$emit('click');
    await flushPromises(20);
    expect(identityService.identityVerify).toHaveBeenCalledWith({
      type: 1,
      items: [
        {
          ...pick(formData, ['name', 'companyName', 'companyId']),
          idCardMasked: '320481****2222',
          idCardMd5: '3f2f2452c0a87199fa66f969702c020b',
        },
      ],
    });
  });
  it('深度核验', async () => {
    wrapper.vm.handleFormChange([formData]);
    await wrapper.vm.$nextTick();
    const btnWrapper = wrapper.findAllComponents({ name: 'AButton' });
    expect(btnWrapper.at(4).props('disabled')).toBeFalsy();
    btnWrapper.at(4).vm.$emit('click');
    await flushPromises(20);
    expect(identityService.identityVerify).toHaveBeenCalledWith({
      type: 2,
      items: [
        {
          ...pick(formData, ['name', 'companyName', 'companyId']),
          idCardMasked: '320481****2222',
          idCardMd5: '3f2f2452c0a87199fa66f969702c020b',
        },
      ],
    });
  });
});
