import { computed, defineComponent, PropType, ref } from 'vue';
import { Button, message } from 'ant-design-vue';
import md5 from 'crypto-js/md5';
import { compact, debounce, omit } from 'lodash';
import { useRouter } from 'vue-router/composables';

import QIcon from '@/components/global/q-icon';
import WarningInfo from '@/shared/components/warning-info';
import { preLoanVerification } from '@/shared/services';
import { Permission } from '@/config/permissions.config';

import FormWrapper from './form-wrapper';
import styles from './verification-form.module.less';
import { useStore } from '@/store';

export interface VerificationFormData {
  index: number;
  name: string;
  idNumber: string;
  companyName: string;
  companyId: string;
}

const VerificationForm = defineComponent({
  name: 'VerificationForm',
  props: {
    maxForms: {
      type: Number as PropType<number>,
      default: 100,
    },
  },
  setup(props, { emit }) {
    const btnLoading = ref<number | null>(null);
    const router = useRouter();
    const currentIndex = ref(1);

    const defaultFormValue = Object.freeze({
      index: currentIndex.value,
      name: '',
      idNumber: '',
      companyName: '',
      companyId: '',
    });
    const forms = ref<VerificationFormData[]>([{ ...defaultFormValue }]);
    const formRefs = ref<any[]>([]);

    const isEmpty = computed(() => {
      return forms.value.every((item) => Object.values(omit(item, ['index'])).every((v) => !v));
    });
    const canSubmit = computed(() => {
      return forms.value.some((item) => Object.values(item).every((v) => v));
    });

    const handleFormChange = (newForms: VerificationFormData[]) => {
      forms.value = newForms;
    };

    /**
     * 新增核验
     */
    const handleAddForm = () => {
      if (forms.value.length < props.maxForms) {
        currentIndex.value++;
        const newForms = [...forms.value, { ...defaultFormValue, index: currentIndex.value }];
        handleFormChange(newForms);
      }
    };

    /**
     * 单条删除
     * @param index
     */
    const handleDeleteForm = (index: number) => {
      const newForms = [...forms.value];
      newForms.splice(index, 1);
      formRefs.value.splice(index, 1);
      handleFormChange(newForms);
    };

    /**
     * 批量上传
     */
    const handleBatchVerify = () => {
      emit('batchVerify');
    };

    const getFormData = async (skipEmpty = true) => {
      const res = await Promise.all(
        formRefs.value.map((formRef: any, index) => {
          return new Promise((resolve, reject) => {
            if (!formRef) {
              resolve(null);
              return;
            }
            formRef.validateFields((err, values) => {
              if (skipEmpty && index > 0 && Object.values(values).every((v) => !v)) {
                resolve(null);
              }
              if (err) reject(err);
              resolve(values);
            });
          });
        })
      );
      return res as VerificationFormData[];
    };

    /**
     * 重置
     */
    const handleReset = () => {
      currentIndex.value = 1;
      handleFormChange([{ ...defaultFormValue }]);
      if (formRefs.value.length > 0) {
        formRefs.value[0].resetFields();
        formRefs.value = [formRefs.value[0]];
      }
    };

    /**
     * 数据结构转换: 适配接口
     * @param formData
     * @returns
     */
    const encryptIdNumber = (formData: VerificationFormData[]) => {
      const res = formData.map((data) => {
        const personIdCardMasked = `${data.idNumber.slice(0, 6)}****${data.idNumber.slice(-4)}`;
        const personIdMd5 = md5(data.idNumber).toString();
        return {
          companyName: data.companyName,
          companyId: data.companyId,
          personName: data.name,
          personIdMd5,
          personIdCardMasked,
        };
      });
      return res;
    };

    const _handleValueChange = async () => {
      const res = formRefs.value.map((formRef: any) => {
        const otherData = formRef.formData;
        const data = formRef.getFieldsValue();
        return { ...otherData, ...data };
      });
      handleFormChange(res);
    };
    const handleValueChange = debounce(_handleValueChange, 100);

    const store = useStore();
    /**
     * 深度核验 & 常规核验
     */
    const handleCheck = async (type: number) => {
      try {
        const res = await getFormData();
        const submitData = compact(res);
        if (!submitData.length) {
          return;
        }
        handleFormChange(submitData);
        const items = encryptIdNumber(submitData);
        btnLoading.value = type;

        const result = await preLoanVerification.executeVerify({
          items,
        });

        // NOTE: 更新套餐额度用量
        await store.dispatch('user/getUsage');

        // 接口调用失败
        if (result.failureCount > 0) {
          const errorMessage = result?.failures?.[0]?.errorMessage || '尽调失败, 请稍后再试';
          message.warn(errorMessage);
          throw new Error(errorMessage);
        }

        // 单条尽调
        if (result.successCount === 1) {
          const preLoanDueId = result.successies?.[0]?.preloanDueId;
          router.push({
            name: 'pre-loan-verification-detail',
            params: {
              pageType: 'start',
              recordId: preLoanDueId.toString(),
            },
            query: {
              type: 'record',
            },
          });
        } else {
          message.warn('暂不支持多条尽调');
        }

        // if (batchId) {
        //   await router.push({
        //     name: 'pre-loan-verification-detail',
        //     params: {
        //       pageType: 'start',
        //       recordId: batchId,
        //     },
        //     query: {
        //       type: 'batch',
        //     },
        //   });
        // }
      } catch (error) {
        console.error(error);
      } finally {
        btnLoading.value = null;
      }
    };

    return {
      btnLoading,
      forms,
      formRefs,
      handleAddForm,
      handleDeleteForm,
      handleBatchVerify,
      handleFormChange,
      handleReset,
      handleCheck,
      handleValueChange,
      canSubmit,
      isEmpty,
    };
  },
  render() {
    const { forms = [] } = this;

    return (
      <div class={styles.container}>
        <div class={styles.top}>
          <div class={styles.formContainer} style={{ paddingRight: forms.length > 3 ? '8px' : 0 }}>
            {forms.map((formItem, index, arr) => (
              <div key={formItem.index} class={styles.formItem}>
                <FormWrapper
                  class={{
                    [styles.singleForm]: true,
                    [styles.shrink]: arr.length > 1,
                  }}
                  ref={(el) => {
                    if (el) {
                      this.formRefs[index] = el;
                    }
                  }}
                  formData={formItem}
                  onChange={this.handleValueChange}
                />
                <QIcon
                  v-show={forms.length > 1}
                  type="icon-icon_yichu"
                  class={styles.deleteIcon}
                  onClick={() => this.handleDeleteForm(index)}
                />
              </div>
            ))}
          </div>
          {/* <div class={styles.buttonContainer}>
            <Button icon="plus" class={styles.addButton} onClick={this.handleAddForm} disabled={forms.length >= maxForms}>
              新增核验
              <span class={styles.count}>
                &nbsp;({forms.length}/{maxForms})
              </span>
            </Button>
            <Button
              v-permission={[Permission.PRE_LOAN_VERIFICATION_BATCH]}
              class={styles.batchButton}
              type="primary"
              ghost
              onClick={this.handleBatchVerify}
            >
              <QIcon type="icon-upload" />
              批量核验
            </Button>
          </div> */}
        </div>
        <div class={styles.bottom}>
          <WarningInfo text="企查查高度重视客户的隐私信息，证件数据会在不可逆加密后传输和使用" />
          <div class="flex align-center" style={{ gap: '16px' }}>
            <Button disabled={!!this.btnLoading || this.isEmpty} onClick={this.handleReset}>
              重置
            </Button>
            <Button
              v-permission={[Permission.PRE_LOAN_VERIFICATION_CHECK]}
              type="primary"
              disabled={!!this.btnLoading || !this.canSubmit}
              loading={this.btnLoading === 1}
              onClick={() => this.handleCheck(1)}
            >
              一键尽调
            </Button>

            {/* <Button disabled={!!this.btnLoading || !this.canSubmit} loading={this.btnLoading === 1} onClick={() => this.handleCheck(1)}>
              常规核验
            </Button>
            <Button
              disabled={!!this.btnLoading || !this.canSubmit}
              loading={this.btnLoading === 2}
              type="primary"
              onClick={() => this.handleCheck(2)}
            >
              深度核验
            </Button> */}
          </div>
        </div>
      </div>
    );
  },
});

export default VerificationForm;
