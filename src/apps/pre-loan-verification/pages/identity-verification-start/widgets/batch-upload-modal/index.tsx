import { defineComponent, ref } from 'vue';

import QModal from '@/components/global/q-modal';
import { IMPORT_IDENTITY_TEMPLATE_URL } from '@/config/template.config';
import BatchUploadFrame from '@/shared/components/batch-upload-frame';
import DownloadLink from '@/shared/components/download-link';
import FileUpload from '@/shared/components/file-upload';
import { createPromiseDialog } from '@/components/promise-dialogs';

import { MAX_FORMS } from '../../identity-verification-start.config';

const BatchUploadModal = defineComponent({
  name: 'BatchUploadModal',
  setup(props, { emit }) {
    const visible = ref(true);
    const close = () => {
      visible.value = false;
      emit('resolve', false);
    };
    return {
      visible,
      close,
    };
  },
  render() {
    return (
      <QModal
        {...{
          props: {
            title: '上传文件批量核验',
            visible: this.visible,
            destroyOnClose: true,
            width: 600,
            footer: null,
          },
          on: {
            cancel: this.close,
          },
        }}
      >
        <BatchUploadFrame>
          <FileUpload
            ref="fileUpload"
            action={(file) => `/insights/batch/import/verification/excel?fileName=${file.name}`}
            height="134px"
            placeholder="点击或拖拽文件到此上传"
            showUploadList={false}
            // beforeFileUpload={this.beforeFileUpload}
            onSuccess={(e) => {
              this.$emit('resolve', { toDetail: true, ...e });
            }}
            theme="lighter"
          />
          <ul slot="description">
            <li>
              <DownloadLink href={IMPORT_IDENTITY_TEMPLATE_URL} download="批量核验模版.xlsx">
                下载模板
              </DownloadLink>
              <span>并按照样式编辑好数据，切勿增减列</span>
            </li>
            <li>上传文件不超过2M，仅支持Excel</li>
            <li>
              批量核验一次支持最多不超过<em>&nbsp;{MAX_FORMS}&nbsp;</em>家企业，支持自动模糊匹配对应企业
            </li>
          </ul>
        </BatchUploadFrame>
      </QModal>
    );
  },
});
export default BatchUploadModal;

export const openBatchUploadModal = createPromiseDialog(BatchUploadModal);
