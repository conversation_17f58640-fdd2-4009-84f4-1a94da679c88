.title {
  font-size: 22px;
  font-weight: 500;
  line-height: 30px;
  color: #333;
  margin: 0;
}

.description {
  font-size: 14px;
  line-height: 22px;
  color: #666;
  margin-top: 6px;
}

.buttonContainer {
  display: flex;
  gap: 16px;
}

.imgContainer {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #FAFAFA;

  img {
    width: 960px;
  }
}

.consumption {
  display: inline-block;
  border-radius: 2px;
  background: #E2F1FD;
  padding: 2px 6px;
  color: #128BED;
}

.usageHint {
  cursor: pointer;
  margin: 0 8px 0 4px;

  &:hover {
    i {
      color: #128bed;
    }
  }

  i {
    color: #bbb;
  }
}

.usageHintContent {
  white-space: nowrap;
  line-height: 24px;
  font-size: 14px;
  color: #666;
}