import { computed, defineComponent } from 'vue';
import { useRouter } from 'vue-router/composables';
import { Popover } from 'ant-design-vue';

import HeroicLayout from '@/shared/layouts/heroic';
import QCard from '@/components/global/q-card';
import QIcon from '@/components/global/q-icon';
import { useUserStore } from '@/shared/composables/use-user-store';

import { openBatchUploadModal } from './widgets/batch-upload-modal';
import styles from './identity-verification-start.module.less';
import VerificationForm from './widgets/verification-form';
import BgIdentity from '../assets/bg-pre-credit-verification.png';
import IconIdentity from '../assets/icon-identity.svg';

const IdentityVerificationStart = defineComponent({
  name: 'IdentityVerificationStart',
  setup() {
    const router = useRouter();
    const { usage } = useUserStore();
    const preLoanQuantity = computed(() => usage.value?.preLoanQuantity);
    const handleBatchVerify = async () => {
      // 处理批量核验逻辑
      const res = (await openBatchUploadModal({})) as any;
      if (!res) return;
      if (res.toDetail && res.batchId) {
        router.push({
          name: 'pre-credit-verification-verify',
          params: { type: 'start' },
          query: { batchId: res.batchId },
        });
      }
    };

    return {
      handleBatchVerify,
      preLoanQuantity,
    };
  },
  render() {
    return (
      <HeroicLayout>
        <QCard slot="hero" bodyStyle={{ padding: '16px' }}>
          <div class="flex justify-between">
            <h2 class={styles.title}>
              <img src={IconIdentity} width={32} />
              贷前尽调
            </h2>
            {this.preLoanQuantity ? (
              <div>
                <span>消耗额度</span>
                <Popover placement="bottomRight">
                  <div slot="content" class={styles.usageHintContent}>
                    <div>核验一条数据消耗1个额度</div>
                  </div>
                  <span class={styles.usageHint}>
                    <QIcon type="icon-a-shuomingxian" />
                  </span>
                </Popover>
                <span class={styles.consumption}>
                  {this.preLoanQuantity.limitation - this.preLoanQuantity.stock}/{this.preLoanQuantity.limitation}
                </span>
              </div>
            ) : null}
          </div>
          <div>
            <div class={styles.description}>
              基于企业标识和人员标识，提供数据贷前尽调服务，返回指定自然人在目标企业及其关联企业中担任法人、董监高和股东的信息，帮助客户快速尽调贷前企业关联关系。
            </div>
          </div>
          <VerificationForm onBatchVerify={this.handleBatchVerify} maxForms={20} />
        </QCard>
        <QCard rootStyle={{ display: 'flex', flex: 1 }} bodyStyle={{ display: 'flex', flex: 1, padding: '16px' }}>
          <div class={styles.imgContainer}>
            <img src={BgIdentity} />
          </div>
        </QCard>
      </HeroicLayout>
    );
  },
});

export default IdentityVerificationStart;
