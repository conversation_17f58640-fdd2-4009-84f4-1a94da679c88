import { defineComponent, onMounted, Ref, ref } from 'vue';
import { message } from 'ant-design-vue';
import { useResizeObserver } from '@vueuse/core';
import { debounce, omit } from 'lodash';

import CommonSearchFilter from '@/components/common/common-search-filter';
import HeroicLayout from '@/shared/layouts/heroic';
import QCard from '@/components/global/q-card';
import { user as userService, batchImport as batchImportService, preLoanVerification as preCreditVerification } from '@/shared/services';
import { convertSortStructure } from '@/utils/data-type/convert-sort-structure';

import { getTableConfig } from './config';
import SearchResult from './widgets/search-result';
import { useSearchHook } from './hooks/use-search-hook';

const IdentityVerificationHistory = defineComponent({
  name: 'IdentityVerificationHistory',
  setup() {
    const init = ref(true);

    const columns = ref(getTableConfig(1280));

    const getTableColumnsConfig = debounce((width) => {
      columns.value = getTableConfig(width);
    }, 200);

    const {
      dataSource,
      operatorList,
      pagination,
      isLoading,
      execute,
      filters,
      filterParams,
      filterGroups,
      handleFilterChange,
      handleSortChange,
    } = useSearchHook(preCreditVerification.getVerifyHistory);

    const selectedIds = ref([]);

    useResizeObserver(document.documentElement, () => {
      getTableColumnsConfig(document.documentElement.clientWidth);
    });
    // TODO: 导出功能待实现
    const handleExport = async (key) => {
      const params =
        key === 'export' ? { ...omit(filterParams.value, ['companyTypeList']), pageSize: 5000 } : { recordIds: selectedIds.value };
      try {
        await batchImportService.exportIdentityVerification(params);
        message.success('正在导出，稍后可前往任务列表查看进度');
      } catch (error) {
        console.error(error);
      }
    };

    const getOperatorList = async () => {
      const data = await userService.getUserList();
      operatorList.value = data;
    };

    onMounted(async () => {
      await getOperatorList();
      init.value = false;
    });

    return {
      filterGroups,
      init,
      pagination,
      dataSource,
      isLoading,
      selectedIds,
      filters,
      handleFilterChange,
      handleSortChange,
      execute,
      handleExport,
      operatorList,
      columns,
    };
  },
  render() {
    return (
      <HeroicLayout loading={this.init && this.isLoading}>
        <QCard
          slot="hero"
          title={this.$route.meta?.title}
          bodyStyle={{
            paddingTop: 0,
          }}
        >
          <CommonSearchFilter
            placeholder="请输入企业名称"
            filterConfig={this.filterGroups}
            onChange={this.handleFilterChange}
            defaultValue={this.filters}
          />
        </QCard>
        <SearchResult
          rowKey={'preLoanDueId'}
          dataSource={this.dataSource}
          columns={this.columns}
          pagination={this.pagination}
          isLoading={this.isLoading}
          searchKey={this.filters.keywords}
          operatorList={this.operatorList}
          on={{
            changePage: (pageIndex: number, pageSize: number) => {
              this.execute({ pageIndex, pageSize });
            },
            changeSort: (sorter) => {
              const sorterObj = convertSortStructure(sorter);
              this.handleSortChange(sorterObj);
            },
            export: (key, selectedIds) => {
              this.selectedIds = selectedIds;
              this.handleExport(key);
            },
          }}
        ></SearchResult>
      </HeroicLayout>
    );
  },
});

export default IdentityVerificationHistory;
