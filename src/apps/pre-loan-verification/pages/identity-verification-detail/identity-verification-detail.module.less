.container {
  .cardTitle {
    display: flex;
    align-items: center;
    // gap: 4px;
    i {
      color: #bbb;
    }

    em {
      color: #f04040;
      // &.matched {
      //   color: #00ad65;
      // }
    }
  }

  .loadMore {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 20px 0;
    color: #bbb;
  }
}

.breadcrumb {
  display: flex;
  justify-content: space-between;

  .path {
    width: auto;
  }
}


.empty {
  margin-top: calc(25vh + (134px / 2));
}
