import { computed, defineComponent, PropType, ref } from 'vue';
import { Menu } from 'ant-design-vue';
import { find, isNil } from 'lodash';

import DropdownButton from '@/components/dropdown-button';

import styles from './search-action.module.less';

const SearchAction = defineComponent({
  name: 'SearchAction',
  props: {
    resultFilterOptions: {
      type: Array as PropType<any>,
      default: () => [],
    },
    selectedIds: {
      type: Array as PropType<any>,
      default: () => [],
    },
  },
  emits: ['submit'],
  setup(props, { emit }) {
    const state = ref<{ roleType?: number }>({
      roleType: undefined,
    });

    /** 下拉菜单选中显示文本 */
    const dropdownSelectedText = computed(() => {
      const defaultLabel = '任职角色';

      const target = find(props.resultFilterOptions, {
        key: state.value.roleType,
      });
      if (!target || target.label === '不限') {
        return defaultLabel;
      }
      return target.label;
    });

    const submit = () => {
      emit('submit', state.value);
    };

    const handleChangeFilter = (roleType: number) => {
      state.value.roleType = roleType;
      submit();
    };

    return {
      handleChangeFilter,

      state,
      dropdownSelectedText,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <DropdownButton text={this.dropdownSelectedText}>
          <Menu
            slot="overlay"
            onClick={({ key }) => {
              this.handleChangeFilter(key);
            }}
          >
            {this.resultFilterOptions.map((item) => (
              <Menu.Item key={item.key}>
                <div class={styles.filterMenuItem}>
                  <span>{item.label}</span>
                  <em v-show={!isNil(item.count)}>({item.count})</em>
                </div>
              </Menu.Item>
            ))}
          </Menu>
        </DropdownButton>
      </div>
    );
  },
});

export default SearchAction;
