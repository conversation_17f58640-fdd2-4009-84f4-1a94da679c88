import { useRequest } from '@/shared/composables/use-request';
import { preLoanVerification as preLoanVerificationService } from '@/shared/services';
import { computed, defineComponent, PropType, ref, watch } from 'vue';
import VerificationMatchResult from '../verification-match-result';

const MatchCompanyResult = defineComponent({
  name: 'MatchCompanyResult',
  props: {
    /**
     * 发起请求
     */
    service: {
      type: Function as PropType<(params: any) => Promise<any>>,
      // required: false,
      default: preLoanVerificationService.getVerifyDetail,
    },
    /**
     * 当前所处
     */
    positionType: {
      type: Number,
      required: true,
    },
    /**
     * 贷前尽调记录id
     */
    preLoanDueId: {
      type: Number as PropType<number>,
      required: true,
    },
    /**
     * 角色类型
     */
    roleType: {
      type: Number,
      required: false,
    },
  },
  setup(props) {
    const request = async (
      payload: Partial<{
        pageSize: number;
        pageIndex: number;
      }> = {}
    ) => {
      const { pageSize = pagination.value.pageSize, pageIndex = pagination.value.pageIndex } = payload;
      const params = {
        roleTypes: typeof props.roleType === 'number' && props.roleType > -1 ? [props.roleType] : undefined,
        positionTypes: [props.positionType],
        preLoanDueId: props.preLoanDueId,
        needAgg: 1,
        pageSize,
        pageIndex,
      };
      return props.service(params);
    };

    const { isLoading, data, execute } = useRequest(request);

    /** 分页 */
    const pagination = computed(() => {
      return {
        pageSizeOptions: ['5', '10', '30'],
        pageSize: data.value?.pageSize ?? 5,
        pageIndex: data.value?.pageIndex ?? 1,
        total: data.value?.total ?? 0,
        current: data.value?.pageIndex ?? 1,
      };
    });
    /**
     * 分页事件回调
     */
    const handleChangePage = (pageIndex: number, pageSize: number) => {
      execute({ pageIndex, pageSize });
    };

    watch(
      () => props.roleType,
      () => {
        execute({
          pageIndex: 1,
        });
      },
      {
        immediate: true,
      }
    );

    return {
      data,
      pagination,
      isLoading,
      handleChangePage,
    };
  },
  render() {
    return (
      <VerificationMatchResult
        isLoading={this.isLoading}
        positionType={this.positionType}
        aggsData={this.data?.aggsRes ?? {}}
        dataSource={this.data?.data ?? []}
        pagination={this.pagination}
        onChangePage={this.handleChangePage}
      />
    );
  },
});

export default MatchCompanyResult;
