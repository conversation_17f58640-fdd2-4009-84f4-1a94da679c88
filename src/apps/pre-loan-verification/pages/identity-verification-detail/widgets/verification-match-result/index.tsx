import { defineComponent, PropType, ref } from 'vue';
import QIcon from '@/components/global/q-icon';
import QRichTable, { IQRichTablePagination } from '@/components/global/q-rich-table';
import QEntityLink from '@/components/global/q-entity-link';
import CompanyStatus from '@/components/global/q-company-status';

import styles from './verification-match-result.module.less';

// 表格列定义
interface ITableColumn {
  title: string;
  dataIndex?: string;
  key: string;
  width?: number | string;
  scopedSlots?: {
    customRender: string;
  };
}

// 验证结果数据类型
export interface IVerificationDetail {
  preLoanDueDetailId: number;
  preLoanDueId: number;
  enterpriseName: string;
  enterpriseId: string;
  enterpriseStartDate: string;
  enterpriseStatus: string;
  positionType: number;
  roleType: number;
  roleTypeDesc: string;
  roleValue: string;
  tenurePeriodStart: string;
  tenurePeriodEnd: string;
  createBy: number;
  updateBy: number;
  createDate: string;
  updateDate: string;
  status: number;
  orgId: number;
  product: string;
  tenurePeriodDesc: string;
}

// 表格列配置
const TABLE_COLUMNS: ITableColumn[] = [
  {
    title: '企业名称',
    key: 'compName',
    scopedSlots: {
      customRender: 'companyName',
    },
  },
  {
    title: '登记状态',
    dataIndex: 'enterpriseStatus',
    key: 'enterpriseStatus',
    width: '12.15%',
    // width: 120,
    scopedSlots: {
      customRender: 'registrationStatus',
    },
  },
  {
    title: '任职角色',
    key: 'roleTypeDesc',
    width: '20.24%',
    // width: 200,
    scopedSlots: {
      customRender: 'roleType',
    },
  },
  {
    title: '任职时间',
    dataIndex: 'tenurePeriodDesc',
    key: 'tenurePeriodDesc',
    width: '12.15%',
    scopedSlots: {
      customRender: 'tenurePeriodDesc',
    },
  },
];

// 验证结果类型
export interface IVerificationType {
  /** 命中类型: 当前所在企业/历史所在企业 */
  type: 'current' | 'history';
  title: string;
  description: string;
  icon: string;
  columns: ITableColumn[];
}

// 根据type类型获取验证信息
const getVerificationType = (positionType: number): IVerificationType => {
  if (positionType === 1) {
    return {
      type: 'current',
      title: '当前任职企业',
      description: '尽调自然人在 <em>当前任职企业</em> 中担任法定代表人、董监高、股东',
      icon: 'icon-enterprise',
      columns: TABLE_COLUMNS,
    };
  }
  return {
    type: 'history',
    title: '历史任职企业',
    description: '尽调自然人在 <em>历史任职企业</em> 中担任法定代表人、董监高、股东',
    icon: 'icon-enterprise',
    columns: TABLE_COLUMNS,
  };
};

const VerificationMatchResult = defineComponent({
  name: 'VerificationMatchResult',
  props: {
    /**
     * 1: 当前所在企业
     * 2: 历史所在企业
     */
    positionType: {
      type: Number,
      required: true,
    },
    dataSource: {
      type: Array as PropType<IVerificationDetail[]>,
      default: () => [],
    },
    /**
     * 聚合数据
     */
    aggsData: {
      type: Object,
      default: () => ({}),
    },
    pagination: {
      type: Object as PropType<IQRichTablePagination>,
      required: false,
    },
    isLoading: {
      type: Boolean,
      required: false,
    },
  },
  emits: ['changePage'],
  setup(props) {
    // 展开/折叠状态
    const isExpanded = ref(true);

    // 切换展开/折叠状态
    const toggleExpand = () => {
      isExpanded.value = !isExpanded.value;
    };

    return {
      isExpanded,
      toggleExpand,
      getVerificationType,
    };
  },
  render() {
    const { dataSource, isExpanded, toggleExpand } = this;
    const verificationInfo = this.getVerificationType(this.positionType);

    /** 分页项 */
    const pagination = {
      ...this.pagination,
      onChange: (current, pageSize) => {
        this.$emit('changePage', current, pageSize);
      },
      onShowSizeChange: (current, pageSize) => {
        this.$emit('changePage', current, pageSize);
      },
    };

    return (
      <div
        class={{
          [styles.container]: true,
          [styles.current]: verificationInfo.type === 'current',
          [styles.history]: verificationInfo.type === 'history',
        }}
      >
        <div
          class={{
            [styles.expand]: true,
            [styles.isCollapsed]: !isExpanded,
          }}
          onClick={toggleExpand}
        >
          <QIcon type="icon-a-xianduanshang" class={styles.expandIcon} />
          <span class={styles.expandLine}></span>
        </div>

        <div class={styles.content}>
          <div class={styles.header}>
            <div class={styles.description}>
              <div>
                <span class={styles.icon}>
                  <QIcon type={verificationInfo.icon} />
                </span>
                <span class={styles.title}>
                  {verificationInfo.title}
                  {pagination?.total ? `（${pagination.total}）` : ''}
                </span>
              </div>
              <span class={styles.text} domPropsInnerHTML={verificationInfo.description}></span>
            </div>
          </div>

          {/* 核验结果表格 */}
          <div class={styles.table} v-show={isExpanded}>
            <QRichTable
              loading={this.isLoading}
              tableLayout="fixed"
              showIndex={false}
              bordered={false}
              columns={verificationInfo.columns}
              dataSource={dataSource}
              emptyMinHeight={'40px'}
              pagination={pagination}
              rowKey="preLoanDueDetailId"
              scopedSlots={{
                /** 企业名称 */
                companyName: (record) => {
                  return (
                    <QEntityLink
                      class={styles.companyName}
                      coyObj={{ KeyNo: record.enterpriseId, Name: record.enterpriseName }}
                    ></QEntityLink>
                  );
                },
                /** 登记状态 */
                registrationStatus: (statusText?: string) => {
                  if (!statusText) {
                    return '-';
                  }
                  return <CompanyStatus status={statusText} ghost />;
                },
                /** 任职角色 */
                roleType({ roleTypeDesc, roleValue }) {
                  const filteredPair: string[] = [];
                  if (roleTypeDesc) {
                    filteredPair.push(roleTypeDesc);
                  }
                  if (roleValue) {
                    filteredPair.push(roleValue);
                  } else if (roleTypeDesc) {
                    filteredPair.push('-');
                  }
                  if (filteredPair.length > 0) {
                    return <span>{filteredPair.join('：')}</span>;
                  }
                  return <span>-</span>;
                },
                /** 任职时间 */
                tenurePeriodDesc: (tenurePeriodDesc) => {
                  if (!tenurePeriodDesc || tenurePeriodDesc === '未知') {
                    return '-';
                  }
                  return <span>{tenurePeriodDesc}</span>;
                },
              }}
            />
          </div>
        </div>
      </div>
    );
  },
});

export default VerificationMatchResult;
