.container {
  width: 100%;
  display: flex;

  &:not(:last-child) {
    border-bottom: 1px solid #e4eef6;
  }
}

.expand {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 16px 0 9px;
  width: 36px;
  user-select: none;
  flex: none;

  &.isCollapsed {
    .expandIcon {
      transform: rotate(90deg);
    }

    .expandLine {
      display: none;
    }
  }

  .expandIcon {
    font-size: 16px;
    transition: transform 0.3s;
    color: #bbb;

    &:hover {
      color: #128bed;
    }
  }

  .expandLine {
    flex: 1;
    content: '';
    width: 1px;
    height: 100%;
    background-color: #d8d8d8;
  }
}

.content {
  flex: 1;
}

.description {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 13px 0;

  .icon {
    font-size: 16px;
    color: #666;
    padding: 0 4px;
  }

  .title {
    font-size: 14px;
    font-weight: bold;
    line-height: 22px;
    color: #333;
  }

  .text {
    font-size: 12px;
    line-height: 18px;
    color: #999;
  }
}

.table {
  padding: 0;

  :global {
    .ant-table-thead > tr > th {
      border-top: 1px solid #eee;
      background-color: #f7f7f7;
    }

    .ant-table-tbody > tr:last-child > td {
      border-bottom: none;
    }

    .ant-table-empty {
      .ant-table-placeholder {
        padding: 0;
        border-bottom: none;

        > div {
          padding: 0;

          img {
            display: none;
          }

          & > div {
            margin-top: 0;
            padding: 9px 0;
          }
        }
      }
    }
  }
}

.matchResult {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;

  .icon {
    font-size: 16px;
  }

  .label {
    font-size: 13px;
  }

  &.success {
    color: #00ad65;
  }

  &.failed {
    color: #f04040;
  }
}

.relationOverlay {
  background: #fff;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
  width: 380px;
  padding: 5px 10px;
  border-radius: 2px;
  overflow: hidden;
}

.relationWrapper {
  &:not(:last-child) {
    margin-bottom: 10px;
  }

  .title {
    color: #333;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    // padding: 10px 10px 5px;
    // // background: #f8f8f8;
    // border-radius: 4px;
  }
}

.relationButton {
  i {
    font-size: 16px;
    margin-left: 2px !important;
  }
}

.company-name {
  :global(> span) {
    white-space: unset !important;
  }
}
