import { defineComponent, PropType } from 'vue';
import { Checkbox } from 'ant-design-vue';
import QEntityLink from '@/components/global/q-entity-link';

import styles from './match-company-result-wrapper.module.less';

// 个人信息数据类型
export interface IPersonInfo {
  name: string;
  idNumber: string;
  keyNo?: string;
}

// 企业信息数据类型
export interface ICompanyInfo {
  name: string;
  creditCode?: string;
  relateCompanyCount?: number;
}

// 验证结果数据类型
const MatchCompanyResultWrapper = defineComponent({
  name: 'MatchCompanyResultWrapper',
  props: {
    personInfo: {
      type: Object as PropType<IPersonInfo>,
      default: () => ({}),
    },
    companyInfo: {
      type: Object as PropType<ICompanyInfo>,
      default: () => ({}),
    },
  },
  emits: ['check'],
  setup(props, { emit }) {
    const handleCheck = (ev: Event) => {
      emit('check', (ev.target as HTMLInputElement).checked);
    };
    return {
      handleCheck,
    };
  },
  render() {
    const { personInfo, companyInfo } = this;

    return (
      <div class={styles.container}>
        {/* 查询条件 */}
        <div class={styles.condition}>
          {/* 个人信息 */}
          <div class={styles.info}>
            <div class={styles.personInfo}>
              <span class={styles.name}>
                <QEntityLink coyObj={{ KeyNo: personInfo.keyNo, Name: personInfo.name }}></QEntityLink>
              </span>
              <span class={styles.idNumber}>{personInfo.idNumber}</span>
            </div>
            {/* 指定企业 */}
            <div class={styles.companyInfo}>
              <span class={styles.label}>尽调企业：</span>
              <span class={styles.name}>{companyInfo.name}</span>
            </div>
          </div>
        </div>

        {/* 核验结果 */}
        <div class={styles.result}>{this.$slots.default}</div>
      </div>
    );
  },
});

export default MatchCompanyResultWrapper;
