import { computed, defineComponent, onMounted, ref } from 'vue';
import { Icon } from 'ant-design-vue';

import Card from '@/shared/components/card';
import HeroicLayout from '@/shared/layouts/heroic';
import { useRequest } from '@/shared/composables/use-request';
import { preLoanVerification } from '@/shared/services';

import styles from './identity-verification-detail.module.less';
import IdentityVerificationBreadcrumb from '../../components/identity-verification-breadcrumb';
import SearchAction from './widgets/search-action';
import MatchCompanyResult from './widgets/match-company-result';
import MatchCompanyResultWrapper from './widgets/match-company-result-wrapper';
import { isEmpty, pick } from 'lodash';
import Empty from '@/shared/components/empty';

const IdentityVerificationDetail = defineComponent({
  name: 'IdentityVerificationDetail',
  props: {
    /**
     * 核验记录ID(可能为单个核验记录ID，也可能为批次核验记录ID)
     */
    recordId: {
      type: Number,
      required: true,
    },
    /**
     * 核验类型
     * - record: 单个核验
     * - batch: 批次核验
     */
    type: {
      type: String, // 'record' | 'batch'
      required: true,
    },
  },
  setup(props) {
    const isLoading = ref(false);

    const { execute, data } = useRequest(preLoanVerification.getVerifyDetail);

    /** 角色筛选选项 */
    const currentRoleType = ref<number>();
    /** 分页 */
    const pagination = computed(() => {
      return {
        pageSize: data.value?.pageSize ?? 1,
        pageIndex: data.value?.pageIndex ?? 1,
        total: data.value?.total ?? 0,
      };
    });

    /** 默认请求参数 */
    const DEFAULT_PAYLOAD = {
      roleTypes: undefined,
      needAgg: 1,
      pageSize: pagination.value.pageSize,
      pageIndex: pagination.value.pageIndex,
      positionTypes: [1, 2], // 获取所有类型的统计数量
      preLoanDueId: props.recordId,
    };

    /** 搜索 */
    const getBasicInfo = async (roleTypes?: number[]) => {
      const res = await execute({
        ...DEFAULT_PAYLOAD,
        roleTypes,
      });

      // 分页
      pagination.value.total = res.total ?? 0;
      pagination.value.pageIndex = res.pageIndex ?? 1;
      pagination.value.pageSize = res.pageSize ?? 1;

      return res;
    };

    /** 角色筛选选项 */
    const roleFilterOptions = ref<{ label: string; key: number; count: number }[]>([]);

    /** 设置角色筛选选项 */
    const setRoleFilterOptions = () => {
      const roleTypes = data.value?.aggsRes?.roleType ?? [];
      const roleTypeOptions = roleTypes.map(({ roleType, count }) => {
        const roleTypeLabelMap = {
          0: '法定代表人',
          1: '股东',
          2: '董监高',
        };
        const label = roleTypeLabelMap[roleType];
        return {
          key: roleType,
          label,
          count,
        };
      });

      if (roleTypeOptions.length > 0) {
        roleFilterOptions.value = [
          {
            key: -1,
            label: '不限',
            count: undefined,
          },
          ...roleTypeOptions,
        ];
      } else {
        roleFilterOptions.value = [];
      }
    };

    /** 可用 positionTypes */
    const availablePositionTypes = computed(() => {
      const positionTypes = data.value?.aggsRes?.positionType ?? [];
      const availableTypes = positionTypes.map(({ positionType }) => positionType);
      const orderedAvailableTypes = availableTypes.slice().sort((a: number, b: number) => a - b);
      return orderedAvailableTypes;
    });

    /** 角色筛选 */
    const handleChangeRoleType = (payload: Partial<{ roleType: number }> = {}) => {
      if (typeof payload.roleType === 'number' && payload.roleType > -1) {
        currentRoleType.value = payload.roleType;
        getBasicInfo([payload.roleType]);
      } else {
        currentRoleType.value = undefined;
        getBasicInfo();
      }
    };

    /** 操作人信息 */
    const operationInfo = computed(() => {
      const result = pick<{ createBy: number; createDate: string }>(data.value?.data?.[0] ?? {}, ['createBy', 'createDate']);
      if (isEmpty(result)) {
        return null;
      }
      return result;
    });

    /** 查询条件-基础信息 */
    const baseInfo = computed(() => {
      const preLoanInfo = data.value?.data?.[0]?.preLoanDueRecord ?? {};
      return {
        personInfo: {
          name: preLoanInfo.personName,
          keyNo: preLoanInfo.personKeyNo,
          idNumber: preLoanInfo.personIdcardMask,
        },
        companyInfo: {
          name: preLoanInfo.enterpriseName,
          keyNo: preLoanInfo.enterpriseId,
          creditCode: preLoanInfo.unifiedCreditCode,
          relateCompanyCount: preLoanInfo.relateCompanyCount,
        },
      };
    });

    onMounted(async () => {
      try {
        isLoading.value = true;
        await getBasicInfo();
        setRoleFilterOptions();
      } catch (error) {
        console.error(error);
      } finally {
        isLoading.value = false;
      }
    });

    return {
      data,
      isLoading,
      pagination,
      operationInfo,
      baseInfo,
      handleChangeRoleType,
      currentRoleType,
      roleFilterOptions,
      availablePositionTypes,
    };
  },
  render() {
    return (
      <div>
        {/* 面包屑导航 */}
        <IdentityVerificationBreadcrumb
          pageType={this.$route.params.pageType}
          operationInfo={this.operationInfo as { createBy: number; createDate: string } | undefined}
        />

        <HeroicLayout
          loading={this.isLoading}
          innerStyle={{
            minHeight: 'calc(100vh - 52px - 60px)',
          }}
        >
          <div class={styles.container}>
            {/* 核验结果 */}
            <Card>
              <div slot="title" class={styles.cardTitle}>
                共找到
                <Icon v-show={this.isLoading} type="sync" spin />
                <em v-show={!this.isLoading}>{this.pagination.total}</em>
                条相关结果
              </div>

              {/* 角色筛选 */}
              {this.roleFilterOptions.length > 0 ? (
                <SearchAction slot="extra" resultFilterOptions={this.roleFilterOptions} onSubmit={this.handleChangeRoleType} />
              ) : null}

              {/* 匹配企业结果 */}
              {this.availablePositionTypes.length > 0 ? (
                <MatchCompanyResultWrapper personInfo={this.baseInfo.personInfo} companyInfo={this.baseInfo.companyInfo}>
                  {this.availablePositionTypes.map((positionType) => {
                    return (
                      <MatchCompanyResult
                        key={positionType}
                        positionType={positionType}
                        preLoanDueId={this.recordId}
                        roleType={this.currentRoleType}
                      />
                    );
                  })}
                </MatchCompanyResultWrapper>
              ) : (
                <div class={styles.empty}>
                  <Empty type="search" description="暂时没有找到相关数据" />
                </div>
              )}
            </Card>
          </div>
        </HeroicLayout>
      </div>
    );
  },
});

export default IdentityVerificationDetail;
