import { type RouteConfig } from 'vue-router';

import SidebarMenuLayout from '@/shared/layouts/sidebar-menu';

const APP_MENU_CONFIG = [
  {
    key: 'notice',
    icon: 'icon-xiaoxizhongxin',
    label: '消息中心',
    children: [
      {
        key: '/notice/all-remind',
        label: '全部提醒',
      },
    ],
  },
];

export const notificationRoutes = (): RouteConfig[] => [
  {
    path: '/notice',
    component: SidebarMenuLayout,
    props: {
      pageTitle: '消息中心',
      menu: APP_MENU_CONFIG,
    },
    meta: {
      title: '消息中心',
    },
    children: [
      {
        path: '',
        redirect: '/notice/all-remind',
      },
      {
        path: ':pageType(all-remind)',
        component: () => import('../pages/notification'),
        props: ({ params }) => {
          return {
            pageType: params.pageType,
          };
        },
        meta: {
          title: '全部提醒',
        },
      },
    ],
  },
];
