import { mount, shallowMount } from '@vue/test-utils';

import Notification from '..';

vi.mock('@/shared/services', () => ({
  message: {
    search: vi
      .fn()
      .mockResolvedValueOnce({
        data: undefined,
        pageIndex: 1,
        total: 0,
      })
      .mockResolvedValue({
        data: [
          {
            testName: 'RenderTask',
            value: {
              id: 653352,
              title: '批量排查-从第三方列表中选择',
              content:
                "您上传的 <b>从第三方列表中选择</b> 文件任务已经完成，导入成功 <em class='success'>10</em> 条，导入失败 <em class='failed'>0</em> 条，下载文件查看导入结果",
              userId: 100202,
              msgType: 1,
              objectId: '15731',
              url: '/supplier/batch-investigation/detail/15731',
              status: 2,
              createDate: '2024-05-23T15:06:49.000Z',
              updateDate: '2024-05-27T01:42:27.000Z',
            },
          },
        ],
        pageIndex: 1,
        total: 1,
      })
      .mockResolvedValue({
        data: [
          {
            testName: 'RenderTask',
            value: {
              id: 653352,
              title: '批量排查-从第三方列表中选择',
              content:
                "您上传的 <b>从第三方列表中选择</b> 文件任务已经完成，导入成功 <em class='success'>10</em> 条，导入失败 <em class='failed'>0</em> 条，下载文件查看导入结果",
              userId: 100202,
              msgType: 1,
              objectId: '15731',
              url: '/supplier/batch-investigation/detail/15731',
              status: 2,
              createDate: '2024-05-23T15:06:49.000Z',
              updateDate: '2024-05-27T01:42:27.000Z',
            },
          },
        ],
        pageIndex: 1,
        total: 1,
      }),
  },
}));

describe('Notification', () => {
  let $route;
  beforeEach(() => {
    $route = {
      path: '/',
      hash: '',
      meta: { title: '消息中心' },
      query: {},
      params: {},
    };
  });
  test('Renders with nodata', async () => {
    const wrapper = mount(Notification, {
      propsData: {
        pageType: 'task-remind',
      },
      mocks: {
        $route,
      },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.vm.dataSource.length).toBe(0);
    expect(wrapper).toMatchSnapshot();
  });

  test('Renders with data', async () => {
    const wrapper = mount(Notification, {
      propsData: {
        pageType: 'task-remind',
      },
      mocks: {
        $route,
      },
    });
    await wrapper.vm.pageChange(1, 5);
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.dataSource.length).toBe(1);
  });

  test('clickCalled', async () => {
    const wrapper2 = shallowMount(Notification, {
      propsData: {
        pageType: 'task-remind',
      },
      mocks: {
        $route,
      },
    });
    await wrapper2.vm.$nextTick();
    await wrapper2.vm.search();
    await wrapper2.vm.$nextTick();
    const readAllRef = wrapper2.find('[data-testid="readAll"]');
    // const readAllSpy = vi.spyOn(wrapper2.vm, 'handleReadAllMessages');
    // const searchSpy = vi.spyOn(wrapper2.vm, 'search');
    expect(readAllRef.exists()).toBe(true);
  });
});
