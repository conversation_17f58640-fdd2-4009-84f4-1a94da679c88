import { Breadcrumb, Pagination, Spin } from 'ant-design-vue';
import { Ref, defineComponent, onMounted, reactive, ref } from 'vue';

import QCard from '@/components/global/q-card';
import QRichTableEmpty from '@/components/global/q-rich-table/components/empty';
import { useSearch } from '@/shared/composables/use-search';
import HeroicLayout from '@/shared/layouts/heroic';
import { message } from '@/shared/services';
import { useStore } from '@/store';
import QLoading from '@/components/global/q-loading';
import QIcon from '@/components/global/q-icon';

import { NOTIFICATION_CONFIG_MAP } from '../../config';
import styles from './notification.page.module.less';
import NotificationItem from './widgets/notification-item';

const NoticeBasic = defineComponent({
  name: 'NoticeBasic',
  props: {
    pageType: {
      type: String,
      default: 'all-remind',
    },
  },
  setup(props) {
    const dataSource = ref([]);
    const init = ref(false);
    const contentDom = ref<HTMLElement | null>(null);
    const { msgStatus, msgType } = NOTIFICATION_CONFIG_MAP[props.pageType];

    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
    });

    const getMessageList = async () => {
      const params = {
        msgType,
        msgStatus,
        pageSize: pagination.pageSize,
        pageIndex: pagination.current,
      };
      const res = await message.search(params);
      dataSource.value = res?.data || [];
      pagination.current = res.pageIndex;
      pagination.total = res.total;
    };

    const { search, isLoading } = useSearch(getMessageList);

    const pageChange = async (current, pageSize) => {
      pagination.current = current;
      pagination.pageSize = pageSize;
      await search();
      setTimeout(() => {
        contentDom.value?.scrollTo({
          top: 0,
          left: 0,
          behavior: 'smooth',
        });
      }, 0);
    };

    const store = useStore();
    onMounted(async () => {
      await search();
      init.value = true;
    });

    /**
     * 全部已读
     */
    const handleReadAllMessages = async () => {
      await store.dispatch(`message/readAllMessages`, { msgType });
      await search();
    };

    return {
      dataSource,
      pagination,
      isLoading,
      init,
      contentDom,
      pageChange,
      search,
      handleReadAllMessages,
    };
  },
  render() {
    const renderContent = () => {
      if (!this.dataSource.length) {
        return (
          <QRichTableEmpty size={'100px'} minHeight={'calc(100vh - 150px)'}>
            <span class={styles.empty}>
              <div>暂无相关提醒</div>
            </span>
          </QRichTableEmpty>
        );
      }
      return (
        <Spin spinning={this.isLoading}>
          <div class={styles.noticelist} ref="contentDom">
            {this.dataSource.map((item: any) => {
              return <NotificationItem key={item.id} value={item} />;
            })}
          </div>
          {this.pagination.total > this.pagination.pageSize && (
            <div class={styles.pagination}>
              <Pagination
                showQuickJumper
                showSizeChanger
                hideOnSinglePage
                pageSizeOptions={['10', '30', '50']}
                total={this.pagination.total}
                current={this.pagination.current}
                pageSize={this.pagination.pageSize}
                onChange={(this as any).pageChange}
                onShowSizeChange={(this as any).pageChange}
              />
            </div>
          )}
        </Spin>
      );
    };
    return (
      <HeroicLayout innerStyle={{ minHeight: 'calc(100vh - 112px)' }}>
        <QCard title={this.$route.meta?.title}>
          <template slot="extra">
            <span data-testid="readAll" class={styles.abtn} onClick={(this as any).handleReadAllMessages}>
              <QIcon type="icon-querenruwei1" style={{ color: '#128BED' }}></QIcon> 全部已读
            </span>
          </template>
          {!this.init ? (
            <div class={styles.loading}>
              <QLoading size="fullsize" />
            </div>
          ) : (
            renderContent()
          )}
        </QCard>
      </HeroicLayout>
    );
  },
});

export default NoticeBasic;
