@import '@/styles/token.less';

.notice-item {
  display: flex;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;

  &:not(:first-child) {
    padding-top: 15px;
  }

  .new {
    display: flex;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    border-radius: 50%;
    margin-top: 7px;
    background-color: #fd485d;
  }

  .svg-icon {
    margin-top: 2px;
    margin-right: 6px;
    font-size: 18px;
  }

  .notice {
    flex: 1;
  }

  .notice-header {
    display: flex;
    align-items: center;
    position: relative;
    line-height: 22px;

    .title {
      font-size: 14px;
      color: #333;
    }

    .time {
      position: absolute;
      right: 0;
      color: #999;
    }
  }

  .notice-content {
    color: #666;
    line-height: 22px;
    margin: 5px 0;

    :global {
      b {
        font-weight: @qcc-font-bold;
      }

      em {
        &.success {
          color: @qcc-color-green-500;
        }

        &.failed {
          color: @qcc-color-red-500;
        }
      }
    }
  }

  .noticeStatus {
    display: flex;
    align-items: center;
    line-height: 22px;

    .label {
      color: #666;
    }

    .value {
      display: inline-flex;
      align-items: center;
      color: #333;

      &::before {
        display: inline-block;
        content: '';
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: #ff8900;
        margin-right: 5px;
      }
    }
  }

  .action {
    a {
      font-size: 14px;
      margin-right: 25px;

      :global {
        .anticon {
          font-size: 16px;
          position: relative;
          top: 1px;
        }
      }
    }
  }
}
