import { mount, shallowMount } from '@vue/test-utils';

import NotificationItem from '..';

vi.mock('@/shared/services');

const DATAMAP: Array<any> = [
  {
    testName: 'RenderTask',
    value: {
      id: 653352,
      title: '批量排查-从第三方列表中选择',
      content:
        "您上传的 <b>从第三方列表中选择</b> 文件任务已经完成，导入成功 <em class='success'>10</em> 条，导入失败 <em class='failed'>0</em> 条，下载文件查看导入结果",
      userId: 100202,
      msgType: 1,
      objectId: '15731',
      url: '/supplier/batch-investigation/detail/15731',
      status: 2,
      createDate: '2024-05-23T15:06:49.000Z',
      updateDate: '2024-05-27T01:42:27.000Z',
    },
  },
  {
    testName: 'RenderTask',
    value: {
      id: 653352,
      title: '批量排查-从第三方列表中选择',
      content:
        "您上传的 <b>从第三方列表中选择</b> 文件任务已经完成，导入成功 <em class='success'>10</em> 条，导入失败 <em class='failed'>0</em> 条，下载文件查看导入结果",
      userId: 100202,
      msgType: 1,
      objectId: '15731',
      url: '',
      status: 2,
      createDate: '',
      updateDate: '',
    },
  },
  {
    testName: 'RenderDownLoad',
    value: {
      id: 668979,
      title: '风险排查报告-上海歆铎电子科技有限公司',
      content: '报告生成成功',
      userId: 100202,
      msgType: 2,
      objectId: '15760',
      url: '/tasklist/report-task-list?batchId=15760',
      status: 1,
      createDate: '2024-05-27T01:53:50.000Z',
      updateDate: '2024-05-27T01:53:50.000Z',
    },
  },
  {
    testName: 'testReminder',
    value: {
      id: 682501,
      title: '投标预警订阅',
      content:
        '您订阅的"全飞秒2"更新了8条标讯，快来查看<br/>大庆油田总医院卡尔蔡司VisuMax激光角膜手术仪维保服务结果公告<br/>浙江国际招投标有限公司关于浙江大学医学院附属第二医院蔡司眼科设备保修项目中标(成交)结果公告<br/>普信国际工程咨询有限公司关于无锡市第二人民医院蔡司眼科设备维保（2年）竞争性磋商公告',
      userId: 100202,
      msgType: 3,
      objectId: '63',
      url: '/supplier/bidding-warning?settingId=63',
      status: 1,
      createDate: '2024-05-30T01:00:04.000Z',
      updateDate: '2024-05-30T01:00:04.000Z',
    },
  },
];

describe('NotificationTem', () => {
  DATAMAP.forEach((testObj) => {
    const { testName, value } = testObj;
    test(testName, () => {
      const wrapper = shallowMount(NotificationItem, {
        propsData: {
          value,
        },
      });
      expect(wrapper).toMatchSnapshot();
      expect(wrapper.findComponent({ ref: 'newDot' }).exists()).toBe(value.status === 1);
    });
  });

  test('linkClick router.push', async () => {
    const value = DATAMAP[0].value;
    const wrapper = mount(NotificationItem, {
      propsData: {
        value,
      },
    });
    const spyon = vi.spyOn(wrapper.vm, 'handleReadMessages');
    const link = wrapper.findComponent({ ref: 'actionlink' });
    await link.trigger('click');
    await wrapper.vm.$nextTick();
    expect(spyon).toHaveBeenCalled();
  });

  test('linkClick dispatch', async () => {
    const value = DATAMAP[2].value;
    const wrapper = mount(NotificationItem, {
      propsData: {
        value,
      },
    });
    const spyon = vi.spyOn(wrapper.vm, 'handleReadMessages');
    const link = wrapper.findComponent({ ref: 'actionlink' });
    await link.trigger('click');
    await wrapper.vm.$nextTick();
    expect(spyon).toHaveBeenCalled();
  });
});
