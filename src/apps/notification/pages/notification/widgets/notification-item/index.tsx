import moment from 'moment';
import { defineComponent } from 'vue';
import { useRouter } from 'vue-router/composables';

import QIcon from '@/components/global/q-icon';
import { useTranslateZeiss } from '@/shared/composables/use-translate';
import { getMsgIcon } from '@/apps/notification/config';
import store from '@/store';

import styles from './notification-item.module.less';

const NotificationItem = defineComponent({
  name: 'NoticeItem',
  props: {
    value: {
      type: Object,
      required: true,
    },
  },
  setup(props, { emit }) {
    const router = useRouter();
    const handleReadMessages = async ({ url, status, id }: any) => {
      if (status === 1) {
        await store.dispatch('message/readMessage', id);
      }
      router?.push(url);
      emit('afterRead');
    };
    return { handleReadMessages };
  },
  render() {
    const { value } = this;
    const { t } = useTranslateZeiss();

    const isAssessment = value.title?.split('-')[0] === '批量排查';
    return (
      <div class={styles.noticeItem} key={value.id}>
        {value.status === 1 ? <span ref="newDot" class={styles.new}></span> : null}
        <q-icon type={getMsgIcon(value.msgType, value.content)} class={styles.svgIcon}></q-icon>
        <div class={styles.notice}>
          <div class={styles.noticeHeader}>
            <span class={styles.title}>{t(value.title)}</span>
            <span class={styles.time}>{value.createDate ? moment(value.createDate).format('YYYY-MM-DD HH:mm:ss') : ''}</span>
          </div>
          <div class={styles.noticeContent} domPropsInnerHTML={value.content}></div>
          {isAssessment && value.url === '' ? (
            <div class={styles.noticeStatus}>
              <span class={styles.label}>状态：</span>
              <span class={styles.value}>正在执行</span>
            </div>
          ) : (
            <div class={styles.action}>
              {value.url ? (
                <a onClick={() => this.handleReadMessages(value)} ref="actionlink">
                  查看详情<QIcon type="icon-wenzilianjiantou"></QIcon>
                </a>
              ) : null}
            </div>
          )}
        </div>
      </div>
    );
  },
});

export default NotificationItem;
