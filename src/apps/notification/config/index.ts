export const NOTIFICATION_CONFIG_MAP = {
  'all-remind': {
    msgType: 0,
    msgStatus: 0,
  },
  'active-remind': {
    msgType: 3,
    msgStatus: 0,
  },
  'download-remind': {
    msgType: 2,
    msgStatus: 0,
  },
  'task-remind': {
    msgType: 1,
    msgStatus: 0,
  },
};

const ICONMAP = {
  1: 'icon-msg-task',
  2: 'icon-msg-download',
  3: 'icon-msg-monitor',
  // msgType 4 对应的是 SystemMsg，最开始人企核验批量排查的msgType是4，现在改成1了，这里icon可能需要修改，但线上环境有部分数据的msgType是4，所以先保留
  // 4: 'icon-msg-expired',
  4: 'icon-msg-task',
  5: 'icon-msg-error',
};

// 如果是失败状，则显示不图标
export const getMsgIcon = (msgType: number, msgContent?: string) => {
  // 其他时候消息都是成功的状态
  const iconKey = msgContent?.includes('报告生成失败') ? 5 : msgType;
  return ICONMAP[iconKey];
};
