import * as Sentry from '@sentry/vue';
import { VueConstructor } from 'vue';

const IGNORE_ERRORS = [
  /^_ignore/,
  /Request failed with status code (401|403|404)/,
  /ChunkLoadError/,
  'Network Error',
  'Request aborted',
  'NavigationDuplicated', // 重复进入同一路由
  'Navigation cancelled', // 路由跳转取消
  "Cannot read property 'style' of undefined",
  "SyntaxError: Unexpected token '<'",
  "SyntaxError: expected expression, got '<'",
  'Cancel', // Promisify modal
  'false', // Promisify modal
  'ResizeObserver loop completed with undelivered notifications.', // ResizeObserver
  /^Redirected when going from ".+" to ".+" via a navigation guard\./, // Vue router warning
  /Loading CSS chunk \d+ failed\./, // CSS chunk error
  "Cannot read properties of null (reading 'getBoundingClientRect')", // Antd Table
];

export function install(Vue: VueConstructor, { router }) {
  const { APP_SENTRY_ENVIRONMENT, APP_SENTRY_RELEASE, APP_SENTRY_DSN } = import.meta.env;
  if (import.meta.env.NODE_ENV === 'production' && APP_SENTRY_DSN) {
    Sentry.init({
      Vue,
      dsn: APP_SENTRY_DSN,
      environment: APP_SENTRY_ENVIRONMENT,
      release: APP_SENTRY_RELEASE,
      integrations: [
        new Sentry.BrowserTracing({
          routingInstrumentation: Sentry.vueRouterInstrumentation(router, {
            routeLabel: 'path',
          }),
          tracePropagationTargets: ['localhost', /^\//],
        }),
        new Sentry.Replay({
          maskAllText: false,
          blockAllMedia: true,
        }),
      ],
      tracesSampleRate: 0.6,
      replaysSessionSampleRate: 0.1,
      replaysOnErrorSampleRate: 0.6,

      tracingOptions: {
        trackComponents: true,
        hooks: ['mount', 'update', 'destroy'], // Sentry 追踪 activate 状态有问题
        // hooks: ['create', 'destroy', 'mount', 'update'], // Antd 的 `LocaleReceiver` activate 状态有问题
        timeout: 500,
      },

      maxBreadcrumbs: 50,
      debug: false,
      // autoSessionTracking: true,
      allowUrls: [/(qcc|greatld|qichacha)\.com/],
      ignoreErrors: IGNORE_ERRORS,
    });
  }
}
