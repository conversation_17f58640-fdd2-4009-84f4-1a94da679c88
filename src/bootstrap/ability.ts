import { get } from 'lodash';
import { message } from 'ant-design-vue';

import { PERMISSION_VALIDATE_MESSAGES } from '@/config/permissions.config';
import { AbilityRule } from '@/libs/plugins/user-ability';
import { UserInfo } from '@/libs/plugins/user-ability/types';
import store from '@/store';

type PatternOption = {
  realtime?: boolean;
  message?: string;
  [x: string]: any;
};

export const ABILITY_RULES = [
  new AbilityRule({
    name: 'permission',
    pattern: async (user: UserInfo, permissions) => {
      return (permissions as number[]).every((code) => user.permissions.includes(code));
    },
  }),
  new AbilityRule<UserInfo>({
    name: 'stock',
    /**
     * @param user 用户基础信息
     * @param feature 规则名称（前端维护）
     * @returns
     */
    pattern: async (user, features: string[], options: PatternOption = {}) => {
      const { realtime, message: msg } = options;
      if (realtime) {
        try {
          await store.dispatch('user/getUsage');
        } catch (error) {
          return false;
        }
      }

      let hasAbility = true;
      for (let i = 0; i < features.length; i++) {
        const featureName = features[i];
        const module = PERMISSION_VALIDATE_MESSAGES[featureName];
        const usage = get(user.usage, module.path);
        if (!module) {
          hasAbility = false;
          break;
        }

        // limitation为-1时，不限制额度
        hasAbility = usage?.stock > 0 || usage.limitation < 0;
        if (!hasAbility) {
          message.warn(msg || module.message || '使用额度已达上限，请联系管理员！');
          break;
        }
      }
      return hasAbility;
    },
  }),
];
