import { HttpClient } from '@/utils';

const BASE_URL = '/preloan';
const PRELOAN_VERIFY = `${BASE_URL}/verify`;
const PRELOAN_VERIFY_DETAIL = `${BASE_URL}/detail`;
const PRELOAN_VERIFY_HISTORY = `${BASE_URL}/list`;

interface IPreLoanVerifyResultSuccess {
  success: boolean; // 是否成功
  companyId: string; // 企业ID
  companyName: string; // 企业名称
  personName: string; // 人员名称
  preloanDueId: number; // 贷前尽调ID
}

interface IPreLoanVerifyResultFailure {
  companyId: string; // 企业ID
  companyName: string; // 企业名称
  personName: string; // 人员名称
  errorCode: string; // 错误码
  errorMessage: string; // 错误信息
}

export interface IPreLoanVerifyResult {
  totalCount: number;
  successCount: number;
  failureCount: number;
  successies: IPreLoanVerifyResultSuccess[];
  failures: IPreLoanVerifyResultFailure[];
}

export interface IPreLoanVerifyPayload {
  items: {
    companyName: string; // 企业名称
    companyId: string; // 企业ID
    personName: string; // 人员名称
    personIdMd5: string; // 人员身份证MD5
    personIdCardMasked: string; // 人员身份证掩码
  }[];
}

export const createService = (httpClient: HttpClient) => ({
  // 执行核验
  executeVerify(data: IPreLoanVerifyPayload): Promise<IPreLoanVerifyResult> {
    return httpClient.post(PRELOAN_VERIFY, data);
  },
  // 根据公司搜索对应的供应商客户
  getVerifyHistory(data) {
    return httpClient.post(PRELOAN_VERIFY_HISTORY, data);
  },
  // 获取核验详情
  getVerifyDetail(data) {
    return httpClient.post(PRELOAN_VERIFY_DETAIL, data);
  },
});
