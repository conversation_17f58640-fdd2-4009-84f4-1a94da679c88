import { PropType, defineComponent } from 'vue';
import { Button } from 'ant-design-vue';

import { FILE_ICON_MAP } from '@/shared/config/file-icon.config';

import styles from './file-logo.module.less';
import { getOssUrl } from '@/utils';

interface FileData {
  FileUrl?: string;
  osskey?: string;
  OssUrl?: string;
  FileType?: string;
}
const getExtByString = (url: string) => {
  if (!url || url.lastIndexOf('.') === -1) {
    return '';
  }
  return url.slice(url.lastIndexOf('.') + 1);
};

const getIcon = (d) => {
  const iconType = getExtByString(d.FileUrl || d.osskey || d.OssUrl) || d.FileType;
  return FILE_ICON_MAP[iconType] || FILE_ICON_MAP.other;
};

const getUrl = (d) => {
  if (d.FileUrl) {
    return d.FileUrl;
  }
  if (d.osskey) {
    return `https://qccdata.qichacha.com/CaseSupervisePunish/${d.osskey}`;
  }
  if (d.OssUrl) {
    return getOssUrl(d.OssUrl);
  }
  return null;
};

const FileLogo = defineComponent({
  functional: true,
  props: {
    fileData: {
      type: Object as PropType<FileData>,
    },
  },
  render(h, { props }) {
    const { fileData } = props;
    const url = getUrl(fileData);
    if (!url) {
      return <div>-</div>;
    }

    const iconObj = getIcon(fileData);
    return (
      <div class={styles.container}>
        <q-icon type={iconObj?.icon} data-testid={`${iconObj?.icon}-type`} />
        <Button class={styles.svgBtn} onClick={() => window.open(url)} type="link">
          查看{iconObj?.text}
        </Button>
      </div>
    );
  },
});

export default FileLogo;
