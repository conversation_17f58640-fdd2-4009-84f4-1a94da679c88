import { shallowMount, mount } from '@vue/test-utils';
import { Button } from 'ant-design-vue';

import { FILE_ICON_MAP } from '@/shared/config/file-icon.config';

import FileLogo from '..';

describe('FileLogo', () => {
  it('renders correctly with FileUrl', async () => {
    const fileData = { FileUrl: 'http://example.com/file.pdf' };
    window.open = vi.fn();
    const wrapper = mount(FileLogo, {
      propsData: { fileData },
    });
    const imgWrapper = wrapper.find(`[data-testid="icon-pdf-type"]`);
    expect(imgWrapper.attributes('type')).toBe(FILE_ICON_MAP.pdf.icon);
    const btn = wrapper.findComponent(Button);
    expect(btn.text()).toContain('查看PDF');
    await btn.trigger('click');
    expect(window.open).toHaveBeenCalled();
  });

  it('renders correctly with osskey', () => {
    const fileData = { osskey: 'file.docx' };
    const wrapper = shallowMount(FileLogo, {
      propsData: { fileData },
    });
    const imgWrapper = wrapper.find(`[data-testid="icon-docx-type"]`);
    expect(imgWrapper.attributes('type')).toBe(FILE_ICON_MAP.docx.icon);
    expect(wrapper.findComponent(Button).text()).toContain('查看Word');
  });

  it('renders correctly with OssUrl', () => {
    const fileData = { OssUrl: 'http://example.com/file.xlsx' };
    const wrapper = shallowMount(FileLogo, {
      propsData: { fileData },
    });
    const imgWrapper = wrapper.find(`[data-testid="icon-excel-type"]`);
    expect(imgWrapper.attributes('type')).toBe(FILE_ICON_MAP.xlsx.icon);
    expect(wrapper.findComponent(Button).text()).toContain('查看Excel');
  });

  it('renders correctly with FileType', () => {
    const fileData = { FileType: 'docx', FileUrl: 'aaaaa' };
    const wrapper = shallowMount(FileLogo, {
      propsData: { fileData },
    });
    const imgWrapper = wrapper.find(`[data-testid="icon-docx-type"]`);
    expect(imgWrapper.attributes('type')).toBe(FILE_ICON_MAP.docx.icon);
    expect(wrapper.findComponent(Button).text()).toContain('查看Word');
  });

  it('renders "-" when no url is available', () => {
    const fileData = {};
    const wrapper = shallowMount(FileLogo, {
      propsData: { fileData },
    });
    expect(wrapper.text()).toBe('-');
  });

  it('handles edge case with empty string in FileUrl', () => {
    const fileData = { FileUrl: '' };
    const wrapper = shallowMount(FileLogo, {
      propsData: { fileData },
    });
    expect(wrapper.text()).toBe('-');
  });

  it('handles edge case with no extension in FileUrl', () => {
    const fileData = {};
    const wrapper = shallowMount(FileLogo, {
      propsData: { fileData },
    });
    expect(wrapper.text()).toBe('-');
  });
});
