import { Select } from 'ant-design-vue';
import { isArray, isEqual } from 'lodash';
import { PropType, watch, defineComponent, onMounted, ref, unref } from 'vue';

import QIcon from '@/components/global/q-icon';

import styles from './filter-select.module.less';

const FilterSelect = defineComponent({
  name: 'FilterSelect',
  props: {
    options: {
      type: Array as PropType<any[]>,
      require: true,
    },
    defaultValue: {
      type: Array as PropType<any[]> | undefined,
    },
    labelKey: {
      type: String,
      default: 'label',
    },
    valueKey: {
      type: String,
      default: 'value',
    },
  },
  setup(props, { emit }) {
    // 转化ops，方便去搜索
    const newOptions = ref<any[]>([]);

    const transfornOptions = (options) => {
      newOptions.value = options.map((ops) => {
        return {
          label: ops[props.labelKey],
          _value: ops[props.valueKey],
          value: ops[props.labelKey],
        };
      });
    };

    const selectVal = ref();

    const emitChange = (values) => {
      // return newValues，区分单选还是多选，防止出现非上市公司 上市公司的情况
      let newValues;
      if (isArray(values)) {
        newValues = unref(newOptions)
          .filter((val) => values.includes(val.value))
          .map((item) => item._value);
      } else {
        newValues = unref(newOptions)
          .filter((val) => isEqual(values, val.value))
          .map((item) => item._value);
      }
      emit('change', newValues);
    };

    const transfornValue = (propValues) => {
      if (!propValues?.length) {
        selectVal.value = undefined;
        return;
      }
      selectVal.value = unref(newOptions)
        .filter((val) => propValues.includes(val._value))
        .map((item) => item.value);
    };

    watch([() => props.options, () => props.defaultValue], ([val1, val2]) => {
      transfornOptions(val1);
      transfornValue(val2);
    });

    onMounted(() => {
      transfornOptions(props.options);
      transfornValue(props.defaultValue);
    });
    return {
      newOptions,
      selectVal,
      emitChange,
    };
  },
  render() {
    const { newOptions, selectVal } = this;
    return (
      <Select
        class={styles.defaultClass}
        style={{
          minWidth: '160px',
        }}
        placeholder="请选择"
        options={newOptions}
        value={selectVal}
        {...{
          props: {
            ...this.$attrs,
          },
        }}
        onChange={this.emitChange}
      >
        <QIcon type="icon-jiantouxia" slot="suffixIcon" />
      </Select>
    );
  },
});
export default FilterSelect;
