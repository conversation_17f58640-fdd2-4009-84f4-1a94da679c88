import { mount } from '@vue/test-utils';
import { Select } from 'ant-design-vue';

import FilterSelect from '..';

describe('FilterSelect', () => {
  test('renders correctly with default props', async () => {
    const wrapper = mount(FilterSelect, {
      propsData: {
        options: [
          { label: 'Option 1', value: '1' },
          { label: 'Option 2', value: '2' },
        ],
      },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent(Select)).toBeTruthy();
  });

  test('transforms options correctly', async () => {
    const wrapper = mount(FilterSelect, {
      propsData: {
        options: [{ name: 'Option 1', id: '1' }],
        labelKey: 'name',
        valueKey: 'id',
      },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.newOptions).toEqual([{ label: 'Option 1', _value: '1', value: 'Option 1' }]);
  });

  test('emits change event with correct values', async () => {
    const wrapper = mount(FilterSelect, {
      propsData: {
        options: [
          { label: 'Option 1', value: '1' },
          { label: 'Option 2', value: '2' },
        ],
      },
    });
    await wrapper.vm.$nextTick();
    wrapper.vm.emitChange(['Option 1']);
    expect(wrapper.emitted().change[0]).toEqual([['1']]);
  });

  test('handles defaultValue correctly', async () => {
    const wrapper = mount(FilterSelect, {
      propsData: {
        options: [
          { label: 'Option 1', value: '1' },
          { label: 'Option 2', value: '2' },
        ],
        defaultValue: ['1'],
      },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.selectVal).toEqual(['Option 1']);
  });

  test('updates options and defaultValue on prop change', async () => {
    const wrapper = mount(FilterSelect, {
      propsData: {
        options: [{ label: 'Option 1', value: '1' }],
        defaultValue: ['1'],
      },
    });
    await wrapper.vm.$nextTick();
    wrapper.setProps({
      options: [{ label: 'Option 2', value: '2' }],
      defaultValue: ['2'],
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.newOptions).toEqual([{ label: 'Option 2', _value: '2', value: 'Option 2' }]);
    expect(wrapper.vm.selectVal).toEqual(['Option 2']);
  });
});
