import { defineComponent, PropType, ref } from 'vue';
import Q<PERSON>hart from '@/components/global/q-chart';
import styles from './legend-chart.module.less';

type LegendItem = {
  name: string;
  value: string | number;
  color: string;
  percent: number | string;
  [prop: string]: any;
};

const LegendChart = defineComponent({
  name: 'LegendChart',
  props: {
    option: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
    legend: {
      type: Array as PropType<LegendItem[]>,
      default: () => [],
    },
    width: {
      type: String,
      required: true,
    },
    height: {
      type: String,
      required: true,
    },
    showCount: {
      type: Boolean,
      default: true,
    },
  },
  setup(props, { emit }) {
    const pieChart = ref<any>();
    const activeName = ref<string | undefined>();

    // 清空高亮
    const clearLegendHighlight = (...rest) => {
      emit('mouseout', ...rest);
      activeName.value = undefined;
    };

    // 选中饼图时，对应的legend要高亮
    const setLegendHighlight = (chart, data) => {
      emit('mouseover', chart, data);
      activeName.value = data.name;
    };

    const handleClick = (...rest) => {
      emit('click', ...rest);
    };

    const setPieHighlight = (item) => {
      if (!pieChart.value?.chart) return;
      activeName.value = item.name;
      emit('highlight', pieChart.value.chart, item);
      pieChart.value.chart.dispatchAction({
        type: 'highlight',
        name: item.name,
      });
    };

    const clearPieHighlight = (item) => {
      if (!pieChart.value?.chart) return;
      activeName.value = undefined;
      emit('downplay', pieChart.value.chart, item);
      pieChart.value.chart.dispatchAction({
        type: 'downplay',
        name: item.name,
      });
    };

    return {
      activeName,
      pieChart,
      handleClick,
      setLegendHighlight,
      clearLegendHighlight,
      setPieHighlight,
      clearPieHighlight,
    };
  },
  render() {
    const renderLegend = () => {
      return (
        <ul class={styles.legend} style={{ maxHeight: this.height }}>
          {this.legend.map((item) => {
            return (
              <li
                key={item.name}
                class={{
                  [styles.active]: this.activeName === item.name,
                }}
                onClick={() => this.handleClick(this.pieChart.chart, item)}
                onMouseover={() => this.setPieHighlight(item)}
                onMouseout={() => this.clearPieHighlight(item)}
              >
                <div class={styles.dot} style={{ backgroundColor: item.color }}></div>
                <span class="flex-1 text-#333">
                  {item.name}
                  {this.showCount ? <i>({item.value})</i> : null}
                </span>
                <span class="text-#666">{item.percent ?? 0}%</span>
              </li>
            );
          })}
        </ul>
      );
    };
    return (
      <div class="flex items-center">
        <QChart
          ref="pieChart"
          onClick={this.handleClick}
          onMouseover={this.setLegendHighlight}
          onMouseout={this.clearLegendHighlight}
          height={this.height}
          width={this.width}
          option={this.option}
          class={styles.pieChart}
        />
        {this.$slots.default ? this.$slots.default : renderLegend()}
      </div>
    );
  },
});

export default LegendChart;
