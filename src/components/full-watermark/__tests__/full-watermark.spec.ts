import { shallowMount } from '@vue/test-utils';

import FullWatermark from '..';

vi.mock('@/store', () => ({
  useStore: () => ({
    getters: {
      'user/profile': {},
    },
  }),
}));

vi.mock('../use-watermark');

describe('FullWatermark', () => {
  test('render', () => {
    const wrapper = shallowMount(FullWatermark, {
      propsData: {
        content: 'AAA',
        width: 500,
        height: 500,
        visible: true,
      },
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});
