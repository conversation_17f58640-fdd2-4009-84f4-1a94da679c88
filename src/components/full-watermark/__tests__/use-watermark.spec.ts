import { useWatermark } from '../use-watermark';

describe('useWatermark', () => {
  let canvas: HTMLCanvasElement;
  let defaultOptions;
  let canvasContext;

  beforeEach(() => {
    defaultOptions = {
      style: '', // canvas 的样式
      text: '', // 填充文本
      width: 0, // 宽度
      height: 0, // 高度
      offset: {
        x: 0, // 文本横向偏移
        y: 0, // 文本纵向偏移
      },
      fontColor: '', // 文本颜色
      fontSize: 0, // 文本颜色
      fillExtraRows: 0, // 额外渲染行数（避免旋转后上下空白）
      rotate: 0, // 画度旋转角度
    };
    canvasContext = {
      translate: vi.fn(),
      rotate: vi.fn(),
      fillText: vi.fn(),
    };
    canvas = {
      setAttribute: vi.fn(),
      getContext: vi.fn(() => canvasContext),
    } as any;
  });

  test('should throw an error if canvas is not provided', () => {
    expect(() => {
      useWatermark(null, defaultOptions);
    }).toThrow('必须指定要渲染的 canvas 元素');
  });

  test('should set canvas width and height', () => {
    const options = {
      ...defaultOptions,
      width: 500,
      height: 300,
      text: 'ABC',
      offset: {
        x: 0, // 文本横向偏移
        y: 0, // 文本纵向偏移
      },

      fillExtraRows: 5,
      fontSize: 16,
      rotate: -45,
    };

    useWatermark(canvas, options);
    expect(canvas.setAttribute).toHaveBeenCalledWith('width', `${options.width}px`);
    expect(canvas.setAttribute).toHaveBeenCalledWith('height', `${options.height}px`);
    expect(canvas.setAttribute).toHaveBeenCalledWith('style', options.style);

    expect(canvasContext.translate).toHaveBeenCalledWith(options.width / 2, options.height / 2);
    expect(canvasContext.rotate).toHaveBeenCalledWith((options.rotate * Math.PI) / 180);
    expect(canvasContext.translate).toHaveBeenCalledWith((options.width * -1) / 2, (options.height * -1) / 2);

    expect(canvasContext.fillText).toHaveBeenCalledWith('ABC', -240, -80);
    expect(canvasContext.fillText).toHaveBeenCalledWith('ABC', -240, -64);
    expect(canvasContext.fillText).toHaveBeenCalledWith('ABC', -240, -48);
  });
});
