import { PropType, defineComponent, onMounted, ref, unref, computed } from 'vue';

import { useStore } from '@/store';

import { useWatermark } from './use-watermark';

const DEFAULT_ROTATE = -45;
const DEFAULT_FILL_EXTRA_ROWS = 5;

const FullWatermark = defineComponent({
  name: 'FullWatermark',
  props: {
    /**
     * 是否显示水印
     */
    visible: {
      type: Boolean,
      default: true,
    },
    /**
     * 水印内容
     */
    content: {
      type: String,
      required: false,
    },
    /**
     * 文本颜色
     */
    fontColor: {
      type: String,
      default: `rgba(153, 153, 153, 0.3)`,
    },
    /**
     * 水印字体大小
     */
    fontSize: {
      type: Number,
      default: 16,
    },
    /**
     * 偏移距离
     */
    fillOffset: {
      type: Object as PropType<any>,
      default: () => ({
        x: 300,
        y: 300,
      }),
    },
    /**
     * 偏移距离（Canvas）
     */
    offset: {
      type: Object as PropType<any>,
      default: () => ({
        x: 0,
        y: 0,
      }),
    },
    /**
     * 层级
     */
    zIndex: {
      type: Number,
      default: 999,
    },
    width: {
      type: Number,
      required: false,
    },
    height: {
      type: Number,
      required: false,
    },
  },
  setup(props) {
    const store = useStore();
    const userProfile = computed(() => store.getters['user/profile'] || {});

    const container = ref<HTMLElement>();
    const canvas = ref<HTMLCanvasElement>();

    const draw = (el?: HTMLCanvasElement): void => {
      if (!el) {
        return;
      }
      const { orgName = '', name = '', phone = '', email = '' } = userProfile.value;
      const textContent = props.content || [orgName, name, phone?.slice(phone.length - 4) || email].join('  |  ');

      useWatermark(el, {
        width: props.width ?? container.value?.scrollWidth ?? 0,
        height: props.height ?? container.value?.scrollHeight ?? 0,
        fillExtraRows: DEFAULT_FILL_EXTRA_ROWS,
        text: textContent,
        fontColor: props.fontColor,
        fontSize: props.fontSize,
        offset: props.fillOffset,
        style: `
          position: fixed;
          top: ${props.offset.y}px;
          left: ${props.offset.x}px;
          z-index: ${props.zIndex};
          pointer-events: none;
        `,
        rotate: DEFAULT_ROTATE,
      });
    };

    onMounted(() => {
      if (props.visible) {
        draw(unref(canvas));
      }
    });

    return {
      container,
      canvas,
    };
  },
  render() {
    return (
      <div ref="container">
        {this.$slots.default}
        <canvas ref="canvas" />
      </div>
    );
  },
});

export default FullWatermark;
