type Options = {
  style: string; // canvas 的样式
  text: string; // 填充文本
  width: number; // 宽度
  height: number; // 高度
  offset: {
    x: number; // 文本横向偏移
    y: number; // 文本纵向偏移
  };
  fontColor: string; // 文本颜色
  fontSize: number; // 文本颜色
  fillExtraRows: number; // 额外渲染行数（避免旋转后上下空白）
  rotate: number; // 画度旋转角度
};

/**
 * Canvas 水印
 * @param canvas DOM
 * @param {string} options.style canvas 的样式
 * @param {string} options.text 填充文本
 * @param {number} options.width 宽度
 * @param {number} options.height 高度
 * @param {number} options.offset.x 文本横向偏移
 * @param {number} options.offset.y 文本竖向偏移
 * @param {string} options.fontColor 文本颜色
 * @param {number} options.fontSize 文本颜色
 * @param {number} options.fillExtraRows 额外渲染行数（避免旋转后上下空白）
 * @param {number} options.rotate 画度旋转角度
 * @returns {HTMLCanvasElement}
 */
export function useWatermark(canvas: HTMLCanvasElement, options: Options) {
  if (!canvas) {
    throw new Error('必须指定要渲染的 canvas 元素');
  }
  const { width, height, text, fontColor, style, offset, fontSize = 16, fillExtraRows = 5, rotate = -45 } = options;

  canvas.setAttribute('width', `${width}px`);
  canvas.setAttribute('height', `${height}px`);
  canvas.setAttribute('style', style);

  // Context
  const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;
  ctx.fillStyle = fontColor; // 文本颜色
  ctx.font = `normal 300 ${fontSize}px Helvetica, Tahoma, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Heiti SC', 'Microsoft YaHei', sans-serif`; // 文本样式
  ctx.textBaseline = 'middle';

  // 字宽
  const textWidth = fontSize * text.length;
  const textHeight = fontSize;
  // 列宽: 字宽 + 列间距
  const colWidth = offset.x + textWidth;
  // 行高: 字宽 + 行间距
  const rowHeight = offset.y + textHeight;
  // 计算列数和行数
  const colCount = Math.ceil(width / colWidth) + fillExtraRows * 2;
  const rowCount = Math.ceil(height / rowHeight) + fillExtraRows * 2;
  // 旋转 Canvas
  ctx.translate(width / 2, height / 2);
  ctx.rotate((rotate * Math.PI) / 180);
  ctx.translate(-width / 2, -height / 2);
  // 填充文本
  for (let i = -fillExtraRows; i < colCount; i++) {
    for (let j = -fillExtraRows; j < rowCount; j++) {
      ctx.fillText(text, colWidth * i, rowHeight * j);
    }
  }

  return canvas;
}
