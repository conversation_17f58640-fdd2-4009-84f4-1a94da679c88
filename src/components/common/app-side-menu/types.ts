/* eslint-disable no-use-before-define */
export type IMenuMode = 'normal' | 'collapse';

export type IMenuItemGroup = {
  key: string;
  group?: string;
  children: IMenuItem[];
};

export type IMenuItem = {
  icon?: string;
  key: string;
  label: string;
  children?: IMenuItemGroup[];
  meta?: Record<string, any>;
  [x: string]: any;
};

export type IMenuKey = string | number;

export type IMenuKeys = Array<IMenuKey>;
