import { computed, defineComponent, type PropType } from 'vue';
import { Popover } from 'ant-design-vue';

import QIcon from '@/components/global/q-icon';
import { useUserStore } from '@/shared/composables/use-user-store';
import { useI18n } from '@/shared/composables/use-i18n';
import { createTrackEvent } from '@/config/tracking-events';

import type { IMenuMode, IMenuItem, IMenuKey, IMenuKeys } from './types';
import styles from './app-side-menu-item.module.less';

const MenuItemLabel = (context) => {
  const { listeners, props } = context;
  const { isZeiss } = useUserStore();
  const { tc } = useI18n();
  return (
    <div
      class={{
        [styles.menuItemLabel]: true,
        [styles.collapse]: props.mode === 'collapse',
        [styles.active]: props.currentRootKey === props.rootKey,
      }}
      {...{
        on: listeners,
      }}
    >
      <span class={styles.icon} v-show={props.item.icon}>
        <QIcon type={props.item.icon} />
      </span>
      <span class={styles.content}>{tc(props.item.label, Number(isZeiss.value) + 1)}</span>
      <span class={styles.mark} v-show={props.collapseable}>
        <QIcon type={props.active ? 'icon-a-xianduanshang' : 'icon-a-xianduanxia'} />
      </span>
    </div>
  );
};

const MenuSection = defineComponent({
  props: {
    /**
     * 激活的菜单项
     */
    activeKey: {
      type: [String, Number] as PropType<IMenuKey>,
      required: false,
    },
    /**
     * 展开的菜单组
     */
    openKeys: {
      type: Array as PropType<IMenuKeys>,
      default: () => [],
    },
    item: {
      type: Object as PropType<IMenuItem>,
      required: true,
    },
  },
  emits: ['change', 'collapse'],
  setup(props, { emit }) {
    const collapse = computed(() => !props.openKeys.includes(props.item.key));
    const active = computed(() => props.openKeys.includes(props.item.key));
    const toggleCollapse = () => {
      emit('collapse', collapse.value ? props.item.key : null);
    };
    const { isZeiss } = useUserStore();
    return {
      collapse,
      active,
      toggleCollapse,
      isZeiss,
    };
  },
  methods: {
    renderItem(item, activeKey) {
      const { tc } = useI18n();
      // const matchedSubRoute =

      let activated = false;
      if (Array.isArray(activeKey)) {
        activated = activeKey.includes(item.key);
      } else if (activeKey === item.key) {
        activated = true;
      } else {
        // matched sub routes
        activated = activeKey?.startsWith(`${item.key}/`);
      }

      return (
        <div
          key={item.key}
          class={{
            [styles.item]: true,
            [styles.active]: activated,
          }}
          style={item.style}
          onClick={() => {
            this.$track(createTrackEvent(6891, '三方风险左侧导航', tc(item.label, Number(this.isZeiss) + 1)));

            this.$emit('change', item.key);
          }}
        >
          {tc(item.label, Number(this.isZeiss) + 1)}
        </div>
      );
    },
    renderGroup(group, activeKey) {
      return (
        <div>
          <div class={styles.label}>
            <span>{group.group}</span>
          </div>
          {group.children.map((item) => this.renderItem(item, activeKey))}
        </div>
      );
    },
  },
  render() {
    const { activeKey, item, renderGroup, renderItem } = this;
    return (
      <div
        class={{
          [styles.menuItemSection]: true,
          [styles.collapse]: this.collapse,
          [styles.active]: this.active,
        }}
      >
        {item.children?.length ? (
          <div class={styles.group}>
            <div class={styles.menuGroup}>
              {item.children.map((child) => {
                const isGroup = !!child.group;
                if (isGroup) {
                  return (
                    <div key={child.key} class={styles.block}>
                      {renderGroup(child, activeKey)}
                    </div>
                  );
                }
                return renderItem(child, activeKey);
              })}
            </div>
          </div>
        ) : null}
      </div>
    );
  },
});

const AppSideMenuItem = defineComponent({
  name: 'AppSideMenuItem',
  props: {
    mode: {
      type: String as PropType<IMenuMode>,
      default: 'normal',
    },
    /**
     * 激活的菜单项
     */
    activeKey: {
      type: [String, Number] as PropType<IMenuKey>,
      required: false,
    },
    // 当前选中菜单的根节点菜单
    currentRootKey: {
      type: String,
      default: '',
    },
    /**
     * 展开的菜单组
     */
    openKeys: {
      type: Array as PropType<IMenuKeys>,
      default: () => [],
    },
    item: {
      type: Object as PropType<IMenuItem>,
      required: true,
    },
  },
  emits: ['change', 'collapse'],
  setup(props, { emit }) {
    const collapse = computed(() => !props.openKeys.includes(props.item.key));
    const active = computed(() => props.openKeys.includes(props.item.key));
    const toggleCollapse = () => {
      if (props.mode === 'collapse') {
        return;
      }
      emit('collapse', props.item.key, collapse.value);
    };
    return {
      collapse,
      active,
      toggleCollapse,
    };
  },
  render() {
    const { mode, item } = this;
    const defaultPopoverProps = {
      placement: 'rightTop',
      overlayClassName: styles.menuPopover,
      align: {
        offset: [13, 0],
      },
      ...(mode === 'collapse' ? {} : { visible: false }),
    };
    return (
      <div
        class={{
          [styles.menuItem]: true,
          [styles.collpase]: mode === 'collapse',
        }}
        {...{
          on: this.$listeners,
        }}
      >
        <Popover
          {...{
            props: defaultPopoverProps,
          }}
        >
          <template slot="content">
            <MenuItemLabel collapseable={false} item={item} mode="normal" />
            <MenuSection
              activeKey={this.activeKey}
              item={item}
              openKeys={this.openKeys}
              onCollapse={(val) => {
                this.$emit('collapse', val);
              }}
              onChange={(val) => {
                this.$emit('change', val);
              }}
            />
          </template>
          <MenuItemLabel
            collapseable
            active={this.active}
            rootKey={this.item.key}
            currentRootKey={this.currentRootKey}
            item={item}
            mode={mode}
            onClick={this.toggleCollapse}
          />
        </Popover>
        {/* default mode: (label + section) */}
        <MenuSection
          v-show={this.mode === 'normal'}
          activeKey={this.activeKey}
          item={item}
          openKeys={this.openKeys}
          onCollapse={(val) => {
            this.$emit('collapse', val);
          }}
          onChange={(val) => {
            this.$emit('change', val);
          }}
        />
      </div>
    );
  },
});

export default AppSideMenuItem;
