.menu-item-label {
  padding: 0 10px;
  font-size: 14px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  width: 100%;
  transition: width 0.3s ease;
  margin-bottom: 4px;

  &:hover {
    background-color: #e2f1fd;
    border-radius: 4px;
  }

  &.active {
    color: #128bed;
  }

  .icon {
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    font-size: 18px;
    width: 24px;
  }

  .content {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    font-weight: bold;
  }

  &.collapse {
    width: 40px;
    height: 40px;
    border-radius: 4px;

    &:hover {
      color: #128bed;
    }

    .icon {
      transform: translateX(3px);
    }

    .mark {
      display: none;
    }

    .content {
      width: 0;
      display: none;
    }
  }

  &.collapse.active {
    background: #E2F1FD;
  }
}

.menu-item-section {
  line-height: 22px;
  background-color: #fafafa;
  color: #666;
  border-radius: 4px;

  .label {
    color: #999;
    padding: 4px 10px;
    font-size: 12px;
    display: flex;
    align-items: center;

    &::after {
      flex: 1;
      height: 1px;
      content: '';
      background-color: #e3e3e3;
      margin-left: 10px;
    }
  }

  .item {
    color: #666;
    padding: 7px 14px 7px 34px;
    cursor: pointer;
    font-size: 14px;
    font-weight: normal;

    &.active,
    &:hover {
      background-color: #e5f2fd;
      border-radius: 4px;
    }

    &.active {
      color: #128bed;
    }
  }

  .label,
  .item:not(:last-child) {
    margin-bottom: 4px;
  }

  .group {
    will-change: opacity, max-height;
    overflow-y: auto;
    //max-height: 300px;
    opacity: 1;
    transition: opacity 0.2s ease, max-height 0.2s ease;
    margin-bottom: 4px;
  }

  &.collapse {
    .group {
      opacity: 0;
      max-height: 0;
    }
  }
}

.menu-popover {
  :global(.ant-popover-inner-content) {
    padding: 2px 10px 10px;
    width: 180px;
  }
}
