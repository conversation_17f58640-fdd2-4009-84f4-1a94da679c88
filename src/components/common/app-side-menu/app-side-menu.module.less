.menu-popover {
  :global(.ant-popover-inner-content) {
    padding: 2px 10px 10px;
    width: 180px;
  }
}

.menu-item-label {
  color: #333;
  padding: 0 10px;
  font-size: 14px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  width: 100%;
  transition: width 0.3s ease;

  &:hover {
    color: #128bed;
  }

  .icon {
    font-size: 18px;
    width: 24px;
  }

  .content {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    font-weight: bold;
  }

  &.collpase {
    width: 40px;
    border-radius: 4px;

    .mark {
      display: none;
    }

    .content {
      width: 0;
      display: none;
    }
  }
}

.menu-icon-item {
  // width: 40px;

  .icon {
    border-radius: 4px;
    height: 40px;
    font-size: 16px;
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &:hover,
  &.active {
    background-color: #e5f2fd;
  }

  &.active {
    > .icon {
      color: #128bed;
    }
  }
}

.menu-item {
  line-height: 22px;
  display: flex;
  align-items: center;

  &:not(:last-child) {
    margin-bottom: 4px;
  }

  .icon {
    font-size: 18px;
    width: 24px;
  }

  .content {
    flex: 1;
  }

  > .label {
    color: #333;
    padding: 0 10px;
    font-size: 14px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;

    &:hover {
      color: #128bed;
    }
  }

  &.active > .label {
    color: #128bed;
  }

  .mark {
    transition: all 0.3s ease;
  }

  .group {
    will-change: opacity, max-height;
    overflow-y: auto;
    max-height: 300px;
    opacity: 1;
    transition: opacity 0.2s ease, max-height 0.2s ease;
  }

  &.collapse {
    .mark {
      transform: rotate(180deg);
    }

    .group {
      opacity: 0;
      max-height: 0;

      // height: 0;
      // visibility: hidden;
      // display: none;
    }
  }
}

.menu-group {
  line-height: 22px;
  background-color: #fafafa;
  color: #666;
  border-radius: 4px;

  .label {
    color: #999;
    padding: 4px 10px;
    font-size: 12px;
    display: flex;
    align-items: center;

    &::after {
      flex: 1;
      height: 1px;
      content: '';
      background-color: #e3e3e3;
      margin-left: 10px;
    }
  }

  .item {
    color: #666;
    padding: 7px 0 7px 34px;
    cursor: pointer;

    &.active,
    &:hover {
      background-color: #e5f2fd;
      border-radius: 4px;
    }

    &.active {
      color: #128bed;
    }
  }

  .label,
  .item:not(:last-child) {
    margin-bottom: 4px;
  }
}

.menu-item {
  padding: 8px 10px;
  cursor: pointer;
  border-radius: 4px;

  &:hover {
    background: #e5f2fd;
  }

  &.active {
    background-color: #e5f2fd;
    color: #128bed;
  }
}

.menu-border {
  overflow: hidden;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  gap: 8px;
  margin: 4px 0;

  .menu-border-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    color: #808080;
  }

  .menu-border-line {
    flex: 1;
    height: 1px;
    background-color: #E3E3E3;
  }
}

.menuCollapse {
  &:hover {
    color: #128bed;
  }

  &.collapse {
    :global(.anticon) {
      transform: scaleX(-1);
      transition: all 0.3s;
    }
  }
}
