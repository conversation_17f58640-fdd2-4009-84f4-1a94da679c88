import { defineComponent, ref, type PropType, type Ref, computed, watch, onMounted } from 'vue';
import { isFunction, isString } from 'lodash';
import { useRouter } from 'vue-router/composables';

import QIcon from '@/components/global/q-icon';
import { useI18n } from '@/shared/composables/use-i18n';
import { hasPermission } from '@/shared/composables/use-permission';
import { useUserStore } from '@/shared/composables/use-user-store';
import { createTrackEvent } from '@/config/tracking-events';

import type { IMenuItem, IMenuKey, IMenuKeys, IMenuMode } from './types';
import styles from './app-side-menu.module.less';
import AppSideMenuItem from './app-side-menu-item';

/**
 * 遍历查找当前激活菜单项
 */
const menuItemsTraverse = (items: Array<IMenuItem>, targetKey?: IMenuKey) => {
  const iterator = (children, matchKey) => {
    if (!Array.isArray(children) || !matchKey) {
      return null;
    }
    let i = 0;
    while (i < children.length) {
      const item = children[i];
      if (matchKey.includes(item.key)) {
        return item;
      }
      const result = iterator(item.children, matchKey);
      if (result) {
        return result;
      }
      i++;
    }
    return null;
  };

  // let level = 0;
  let i = 0;
  while (i < items.length) {
    const item = items[i];
    if (item.key === targetKey) {
      return item;
    }
    const hit = iterator(item.children, targetKey);
    if (hit) {
      return item;
    }
    i++;
  }
  return null;
};

const AppSideMenu = defineComponent({
  name: 'AppSideMenu',
  props: {
    mode: {
      type: String as PropType<IMenuMode>,
      default: 'normal',
    },
    items: {
      type: Array as PropType<IMenuItem[]>,
      default: () => [],
    },
    activeKey: {
      type: [String, Number] as PropType<IMenuKey>,
      required: false,
    },
    expandAll: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['change', 'collapse'],
  setup(props) {
    const isCollapsed = ref(false);
    const { isZeiss } = useUserStore();
    const currentActiveItem = computed(() => menuItemsTraverse(props.items, props.activeKey));
    const getCurrentRootKey = () => {
      return props.items.find((item) => {
        const activeKey = props.activeKey as string;
        return item.children?.find((child) => activeKey?.startsWith(child.key));
      })?.key;
    };
    const currentRootKey = computed(getCurrentRootKey);
    const openKeys: Ref<IMenuKeys> = ref(currentActiveItem.value ? [currentActiveItem.value.key] : []);
    const updateOpenKeys = (keys: IMenuKeys) => {
      openKeys.value = keys;
    };
    const handleMenuCollapse = (currentKey: IMenuKey, expand = true) => {
      if (!props.expandAll) {
        updateOpenKeys(expand ? [currentKey] : []);
        return;
      }
      if (expand) {
        const expandKeys = openKeys.value.includes(currentKey) ? openKeys.value : [...openKeys.value, currentKey];
        updateOpenKeys(expandKeys);
      } else {
        updateOpenKeys(openKeys.value.filter((key) => key !== currentKey));
      }
    };
    const router = useRouter();

    watch(currentActiveItem, (menuitem: any) => {
      handleMenuCollapse(menuitem?.key);
    });

    // 是否需要展示菜单
    const isHidden = (item) => {
      // 通过路由meta判断当前路由是否有权限，没有权限返回true，即隐藏菜单
      const resolvedRoute = router.resolve({ path: item.key });
      const { permission, hidden } = resolvedRoute.route?.meta ?? {};
      if (permission && !hasPermission(permission)) {
        return true;
      }
      if (!hidden) {
        return false;
      }
      if (isFunction(hidden)) {
        return hidden();
      }
      return false;
    };

    const getFilterItems = (items: any[]) => {
      return items
        .filter((item) => !isHidden(item))
        .map((item) => {
          if (item.children) {
            return {
              ...item,
              children: getFilterItems(item.children),
            };
          }
          return item;
        });
    };
    const filterItems = computed(() => getFilterItems(props.items));

    const showAllKeys = () => {
      if (props.expandAll) {
        const keys = filterItems.value.map((item) => item.key);
        updateOpenKeys(keys);
      }
    };

    onMounted(() => {
      showAllKeys();
    });

    return {
      openKeys,
      handleMenuCollapse,
      currentRootKey,
      filterItems,
      isZeiss,
      isCollapsed,
      showAllKeys,
    };
  },
  render() {
    const { tc } = useI18n();
    return (
      <div>
        {this.filterItems.map((item) => {
          if (item.collapse) {
            return (
              <div
                class={{ [styles.menuItem]: true, [styles.menuCollapse]: true, [styles.collapse]: this.isCollapsed }}
                style={{ fontWeight: 'bold', ...item.style, height: '40px' }}
                onClick={() => {
                  this.$track(createTrackEvent(6891, '三方风险左侧导航', tc(item.label, Number(this.isZeiss) + 1)));
                  this.isCollapsed = !this.isCollapsed;
                  this.showAllKeys();
                  this.$emit('collapse', this.isCollapsed);
                }}
              >
                <QIcon type={item.icon} style={{ marginRight: '8px', fontSize: '18px' }} />
                {this.isCollapsed ? '' : '收起导航'}
              </div>
            );
          }
          if (item.border) {
            return (
              <div class={styles.menuBorder}>
                {item.label ? <span class={styles.menuBorderTitle}>{item.label}</span> : null}
                <div class={styles.menuBorderLine}></div>
              </div>
            );
          }
          if (item.children) {
            return (
              <AppSideMenuItem
                key={item.key}
                item={item}
                openKeys={this.openKeys}
                activeKey={this.activeKey}
                currentRootKey={this.currentRootKey}
                mode={this.mode}
                onCollapse={this.handleMenuCollapse}
                onChange={(key: IMenuKey) => {
                  this.$emit('change', key);
                }}
              />
            );
          }
          return (
            <div
              class={[styles.menuItem, { [styles.active]: this.activeKey === item.key }]}
              style={{ fontWeight: 'bold', ...item.style }}
              onClick={() => {
                this.$track(createTrackEvent(6891, '三方风险左侧导航', tc(item.label, Number(this.isZeiss) + 1)));

                this.$emit('change', item.key);
              }}
            >
              {isString(item.icon) ? (
                <QIcon type={item.icon} style={{ marginRight: '8px', fontSize: '16px' }} />
              ) : (
                <QIcon component={item.icon} style={{ marginRight: '8px', fontSize: '16px' }} />
              )}

              {tc(item.label, Number(this.isZeiss) + 1)}
            </div>
          );
        })}
      </div>
    );
  },
});

export default AppSideMenu;
