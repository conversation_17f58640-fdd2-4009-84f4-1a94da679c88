import { mount } from '@vue/test-utils';

import AppSideMenuItem from '../app-side-menu-item';

describe('AppSideMenuItem', () => {
  test('render', () => {
    const wrapper = mount<InstanceType<typeof AppSideMenuItem>>(AppSideMenuItem, {
      //   currentRootKey: 'access',
      propsData: {
        mode: 'normal',
        openKeys: ['a'],
        activeKey: '1-2',
        item: {
          key: 'a',
          label: 'A',
          icon: 'ICON',
          children: [
            {
              key: '1-1',
              label: '1-1',
            },
            {
              key: '1-2',
              label: '1-2',
            },
          ],
        },
      },
    });
    expect(wrapper).toMatchInlineSnapshot(`
      <div class="menuItem">
        <div class="menuItemLabel"><span class="icon"><q-icon-stub type="ICON"></q-icon-stub></span><span class="content">A</span><span class="mark"><q-icon-stub type="icon-a-xianduanshang"></q-icon-stub></span></div>
        <div class="menuItemSection active">
          <div class="group">
            <div class="menuGroup">
              <div class="item">1-1</div>
              <div class="item active">1-2</div>
            </div>
          </div>
        </div>
      </div>
    `);
  });
});
