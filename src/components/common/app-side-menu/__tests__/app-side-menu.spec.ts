import { mount, shallowMount } from '@vue/test-utils';

import AppSideMenu from '../app-side-menu';

describe('AppSideMenu', () => {
  test('render: empty', () => {
    const wrapper = shallowMount<InstanceType<typeof AppSideMenu>>(AppSideMenu);
    expect(wrapper).toMatchSnapshot();
  });

  test('render', () => {
    const wrapper = mount<InstanceType<typeof AppSideMenu>>(AppSideMenu, {
      propsData: {
        activeKey: '1-2',
        items: [
          {
            key: 'a',
            label: 'A',
            icon: 'ICON',
            children: [
              {
                key: '1-1',
                label: '1-1',
              },
              {
                key: '1-2',
                label: '1-2',
              },
            ],
          },
        ],
      },
    });
    expect(wrapper).toMatchSnapshot();
  });
});
