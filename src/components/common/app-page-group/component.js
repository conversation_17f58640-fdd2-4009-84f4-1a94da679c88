import Vue, { defineComponent } from 'vue';

import scrollToTop from '@/utils/scroll-to-top';

const AppPageGroup = defineComponent({
  name: 'AppPageGroup',
  props: {
    ctype: {
      type: String,
      require: true,
    },
    name: {
      type: String,
    },
    groupInfo: {
      type: Object,
      require: true,
    },
    hashKey: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loaded: false,
      sectionList: [],
      initQueue: [],
      currentKey: '', // 在二级路由的情况下需要控制显示
      keyNo: this.$route.params.id,
    };
  },
  watch: {
    'this.$route.path': 'updateKeyNo',
  },
  created() {
    if (this.groupInfo.children?.length) {
      this.sectionList = this.groupInfo.children.map((item) => {
        return {
          key: item.pos,
          componentId: `d-${item.pos}`,
          api: item.api || item.pos,
          hasLoad: false,
          ...item,
        };
      });
    } else {
      let componentId = `d-${this.groupInfo.id}`;
      if (this.groupInfo.component) {
        const userComponentName = `user-${this.groupInfo.id}`;
        Vue.component(userComponentName, this.groupInfo.component);
        componentId = userComponentName;
      }
      this.sectionList = [
        {
          key: this.groupInfo.id,
          componentId,
          api: this.groupInfo.api || this.groupInfo.id,
          hasLoad: false,
          name: this.groupInfo.name,
          count: this.groupInfo.count,
          disable: this.groupInfo.disable,
        },
      ];
    }
    this.sectionList = this.sectionList.filter((item) => {
      return !item.disable && typeof Vue.component(item.componentId) === 'function';
    });
  },
  mounted() {
    this.$nextTick(this.getLoadState);
  },
  methods: {
    async subNavAction({ domId }) {
      const loaded = this.loaded;
      if (!loaded) {
        window.scrollTo(0, 0);
        await this.getLoadState();
      }
      if (this.groupInfo.subRoute) {
        this.subRouteNav(domId);
      } else {
        const scrollFuc = () => {
          const el = document.getElementById(domId);
          if (el) {
            const { top } = el.getBoundingClientRect();
            scrollToTop((document.documentElement.scrollTop || window.scrollY || 0) + top - 42 - 42 - 40);
          }
        };
        if (loaded) {
          scrollFuc();
        } else {
          setTimeout(scrollFuc, 500);
        }
      }
    },
    subRouteNav(key) {
      this.currentKey = key;
      const section = this.sectionList.find((s) => s.key === this.currentKey);
      if (section) {
        section.hasLoad = true;
      }
    },
    getLoadState() {
      let stateResolve;
      if (this.loadStatePromise) {
        return new Promise((resolve) => {
          stateResolve = resolve;
        });
      }
      this.loadStatePromise = new Promise((resolve) => {
        if (this.initQueue?.length) {
          setTimeout(() => {
            if (!this.loaded) {
              resolve();
            }
          }, 3000);
          Promise.all(this.initQueue).then(() => {
            console.log('init success');
            resolve();
          });
        } else {
          resolve();
        }
      }).finally(() => {
        this.loaded = true;
        this.initQueue = [];
        if (stateResolve) {
          stateResolve();
        }
      });
      return this.loadStatePromise;
    },
    handleInitSuccess(promise) {
      this.initQueue.push(promise);
    },
    updateKeyNo() {
      const keyNo = this.$route.params.id;

      if (keyNo) {
        this.keyNo = keyNo;
      }
    },
    getDefaultModule(item) {
      return item.count === 0 && item.hisCount && item.hisCount > 0 ? 'history' : undefined;
    },
  },
});

export default AppPageGroup;
