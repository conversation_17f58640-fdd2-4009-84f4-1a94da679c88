<template>
  <div v-if="groupInfo" class="container">
    <!-- 需要二级显示切换 -->
    <div v-if="groupInfo.subRoute">
      <template v-for="item in sectionList">
        <div class="data-page" v-if="item.hasLoad" v-show="item.key === currentKey" :key="item.key">
          <component :id="item.key" :keyNo="keyNo" :is="item.componentId" :info="item"></component>
        </div>
      </template>
    </div>
    <div v-else-if="groupInfo.count || !groupInfo.disable" class="data-wrap" :class="{ 'is-load': loaded }">
      <div class="loading">
        <q-loading size="fullsize"></q-loading>
      </div>
      <div class="sections">
        <template v-for="(item, key) in sectionList">
          <component
            v-if="hashKey ? hashKey === item.key : true"
            :id="item.key"
            :key="key"
            :keyNo="keyNo"
            :name="name"
            :is="item.componentId"
            :defaultModule="getDefaultModule(item)"
            class="data-section"
            :info="item"
            @initStart="handleInitSuccess"
          ></component>
        </template>
        <p class="statement">
          免责声明：数据来源于公开渠道和第三方提供，企查查尊重并倡导保护知识产权，本产品所引用数据及其他信息仅作参考，不代表企查查赞同或证实其描述。如对该数据服务存在异议，或发现违法及不良信息，请拨打电话400-928-2212或发送邮件至*****************，我们将及时处理。
        </p>
      </div>
    </div>
    <q-no-data v-else></q-no-data>
  </div>
</template>

<script src="./component.js" />
<style lang="less" scoped src="./style.less" />
