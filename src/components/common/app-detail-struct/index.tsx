import { defineComponent, PropType } from 'vue';

import { PageStatus } from '@/shared/constants/page-status.constant';

import styles from './app-detail-struct.module.less';

const AppDetailStruct = defineComponent({
  functional: true,

  props: {
    status: {
      type: Number as PropType<PageStatus>,
      default: PageStatus.Idle,
    },
    title: {
      type: String as PropType<string>,
      required: false,
    },
  },

  render(h, { props, slots }) {
    const { aside, extra, default: defaultSlot } = slots();
    const stages = {
      [PageStatus.Idle]: null,
      [PageStatus.Loading]: <q-loading size="fullsize" />,
      [PageStatus.Loaded]: defaultSlot,
      [PageStatus.Failed]: <span>请求失败，请重试</span>,
    };
    const node = stages[props.status] || stages[PageStatus.Idle];

    return (
      <q-firm-explorer headerHeight={props.title ? '40px' : '0'}>
        {/* Header */}
        {props.title && (
          <q-firm-header slot="header" title={props.title}>
            {extra}
          </q-firm-header>
        )}
        {/* Aside */}
        {aside}
        {/* Content */}
        <div class={styles.container}>{node}</div>
      </q-firm-explorer>
    );
  },
});

export default AppDetailStruct;
