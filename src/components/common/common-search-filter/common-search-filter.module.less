.container {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .main {
    flex: 1;
    display: flex;
    align-items: center;
  }

  .aside {
    &:hover {
      :global {
        .ant-btn {
          border-radius: 2px;
          color: #0069bf;
        }
      }
    }
  }

  :global {
    .ant-input-suffix {
      right: 0;
      padding: 8px;
      background-color: white;
    }
  }

  .dropdown {
    padding: 0 10px;
    text-align: center;
    height: 32px;
    line-height: 32px;
    cursor: pointer;

    .searchIcon {
      color: #128bed;
      margin-right: 5px;
    }

    .trangle {
      color: #666;
      font-size: 16px;
      transform: translateY(1px);
    }

    &:hover {
      .trangle {
        transform: translateY(1px) rotate(180deg);
      }
    }
  }

  .active,
  .dropdown:hover {
    color: #128bed;

    .trangle {
      color: #128bed;
    }
  }
}

.ddOverlay {
  width: 160px;
}

