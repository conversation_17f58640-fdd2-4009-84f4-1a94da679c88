import { Button, Dropdown, Menu } from 'ant-design-vue';
import { debounce, isEmpty, isNil, omitBy } from 'lodash';
import { computed, defineComponent, nextTick, ref, watch } from 'vue';

import QFilter from '@/components/global/q-filter';
import QTinySearch from '@/components/global/q-tiny-search';
import { openPasteDialog } from '@/components/modal/paste-dialog';
import QIcon from '@/components/global/q-icon';

import styles from './common-search-filter.module.less';

const CommonSearchFilter = defineComponent({
  name: 'CommonSearchFilter',
  props: {
    filterConfig: {
      type: Array,
      default: () => [],
    },
    placeholder: {
      type: String,
      default: '请输入要搜索的内容',
    },
    // 输入框长度
    inputWidth: {
      type: String,
      default: '258px',
    },
    showSearch: {
      type: Boolean,
      default: true,
    },
    needReset: {
      type: Boolean,
      default: true,
    },
    defaultValue: {
      type: Object,
      default: () => ({
        filters: undefined,
        keywords: undefined,
      }),
    },
    // 搜索框位置
    searchPosition: {
      type: String,
      default: 'right',
    },
    // select下拉框边框
    checkSelectBorder: {
      type: Boolean,
      default: false,
    },
    // 是否支持批量搜索
    multiSearch: {
      type: Boolean,
      default: false,
    },
    /**
     * Batch placeholder
     */
    batchPlaceholder: {
      type: String,
      default: '您可以输入员工编号或者姓名 \n例\n张三\n1234',
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['change', 'multiClear', 'updateBatchData', 'reset', 'getOptions'],
  setup(props, { emit }) {
    const keywords = ref(props.defaultValue?.keywords || undefined); // 当前关键字状态
    const submittedKeywords = ref(props.defaultValue?.keywords || undefined); // 已发送关键字
    const filters = ref(props.defaultValue?.filters ?? undefined);

    // 是否显示单个搜索框或者批量搜索
    const showSingleSearch = ref(props.showSearch);
    const showMultiSearch = ref(props.multiSearch);

    const filterRef = ref();

    /**
     * Emit change event
     */
    const _change = (details = {}) => {
      const payload = {
        keywords: submittedKeywords.value || undefined, // 关键字为空时, 对外发送 `undefined`, 方便接口请求
        filters: filters.value,
      };
      emit('change', payload, details);
      if (props.multiSearch && !showMultiSearch.value && showSingleSearch.value) {
        nextTick(() => {
          showSingleSearch.value = true;
          showMultiSearch.value = false;
        });
      }
    };
    const change = debounce(_change, 300);

    const handleHover = (group, showAll = false) => {
      emit('getOptions', group, showAll);
    };
    const updateOptions = debounce(handleHover, 300);

    const handleKeywordsSearch = (value) => {
      submittedKeywords.value = value;
      change();
    };
    const handleKeywordsCancel = () => {
      keywords.value = '';
      if (submittedKeywords.value) {
        submittedKeywords.value = '';
        change();
      }
      if (props.multiSearch) {
        showSingleSearch.value = true;
        showMultiSearch.value = true;
      }
    };
    const handleFilterChange = (filterValues, group) => {
      Object.keys(filterValues).forEach((key) => {
        if (filterValues[key] && filterValues[key].length === 0) {
          delete filterValues[key];
        }
      });
      filters.value = isEmpty(filterValues) ? undefined : filterValues;
      change(group);
    };

    // /**
    //  * Debounced filter change
    //  */
    // const handleFilterChange = throttle(_handleFilterChange, 200, { leading: true, trailing: false });

    // 多选的情况下，是否已经有值了
    const multiSearchValue = ref(false);
    const handleClear = () => {
      emit('reset');
      filters.value = undefined;
      keywords.value = undefined;
      submittedKeywords.value = undefined;
      filterRef.value?.resetKeywords?.();
      if (props.multiSearch) {
        multiSearchValue.value = false;
        emit('multiClear');
      }
      change();
    };

    /**
     * 显示重置按钮
     */
    const isFiltersEmpty = computed(() => {
      return isEmpty(omitBy(filters.value, isNil)) && !(keywords.value || multiSearchValue.value);
    });

    const batchData = ref([]);
    const openBatchAdd = async () => {
      const res = await openPasteDialog({
        placeholder: props.batchPlaceholder,
        max: 20 * 1000,
      });
      try {
        multiSearchValue.value = true;
        emit('updateBatchData', res);
      } catch (error) {
        console.error(error);
      }
    };

    watch(
      () => props.defaultValue,
      (val) => {
        keywords.value = val?.keywords;
        submittedKeywords.value = val?.keywords;
        filters.value = val?.filters;
      },
      { deep: true }
    );

    return {
      keywords,
      filters,
      isFiltersEmpty,
      handleKeywordsSearch,
      handleKeywordsCancel,
      handleFilterChange,
      handleClear,
      showSingleSearch,
      showMultiSearch,
      batchData,
      openBatchAdd,
      multiSearchValue,
      updateOptions,
      handleHover,
      filterRef,
    };
  },
  render() {
    const renderSearch = () => {
      if (!this.showSingleSearch && !this.showSearch) {
        return null;
      }
      if (!this.showMultiSearch) {
        return (
          <QTinySearch
            v-model={this.keywords}
            placeholder={this.placeholder}
            modeType={this.multiSearch && this.showSingleSearch ? 'Search' : 'Default'}
            searchPosition={this.searchPosition}
            onSearch={this.handleKeywordsSearch}
            onCancel={this.handleKeywordsCancel}
            inputWidth={this.inputWidth}
          />
        );
      }
      return (
        <Dropdown trigger={['hover']} overlayClassName={styles.ddOverlay}>
          <div class={[styles.dropdown, this.multiSearchValue ? styles.active : '']}>
            <q-icon class={styles.searchIcon} type="icon-chaxun"></q-icon>
            {this.multiSearchValue ? '批量搜索' : '搜索'}
            <q-icon class={styles.trangle} type="icon-a-shixinxia1x1"></q-icon>
          </div>
          <Menu slot="overlay">
            <Menu.Item
              onClick={() => {
                this.keywords = this.keywords || '';
                this.showSingleSearch = true;
                this.showMultiSearch = false;
              }}
            >
              单个搜索
            </Menu.Item>
            <Menu.Item
              onClick={() => {
                this.showMultiSearch = true;
                this.showSingleSearch = false;
                this.openBatchAdd();
              }}
            >
              批量搜索
            </Menu.Item>
          </Menu>
        </Dropdown>
      );
    };

    return (
      <div class={styles.container}>
        <div class={styles.main}>
          <QFilter
            ref={'filterRef'}
            theme="filled"
            groups={this.filterConfig as any}
            value={this.filters}
            showSearch={this.showSearch}
            checkSelectBorder={this.checkSelectBorder}
            onChange={this.handleFilterChange}
            isLoading={this.loading}
            onHover={this.handleHover}
            onAfterChange={(group) => this.updateOptions(group, true)}
          >
            <template slot={this.searchPosition === 'right' ? 'last-addons' : 'first-addons'}>{renderSearch()}</template>
            <template slot="last-extra">
              <div id="reset-filter-btn" class={styles.aside} v-show={!this.isFiltersEmpty && this.needReset}>
                <Button onClick={this.handleClear} type="link">
                  <QIcon type="icon-chexiaozhongzhi" />
                  <span>重置筛选</span>
                </Button>
              </div>
            </template>
          </QFilter>
        </div>
      </div>
    );
  },
});

export default CommonSearchFilter;
