@import '@/styles/token.less';

.container {
  display: flex;
  flex-direction: column;
  gap: 10px;

  > .item {
    display: flex;
    gap: 30px;
    align-items: center;
    min-height: 66px;
    background-color: #f8fbfe;
    border-radius: 2px;
    cursor: pointer;
    padding: 10px 30px;
    transition: background-color 0.15s linear;
    box-shadow: inset 5px 0 #128bed;

    &.hover,
    &:hover {
      background: #e2f1fd;
      color: #128bed;
    }
  }

  // 分隔线
  .divider {
    width: 1px;
    background: #eee;
    height: 100%;
    min-height: 44px;
  }

  // 模型信息
  .riskInfo {
    width: 150px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .title {
      font-size: 16px;
      line-height: 24px;
      font-weight: 700;
    }

    .risk {
      padding: 1px 4px;
      border-radius: 2px;
      line-height: 20px;
      font-size: 12px;
      text-align: center;
      background: #fff;
      border: 0.5px solid #E3E3E3;
    }
  }

  // 开启模型指标数量
  .riskMetric {
    width: 150px;
    color: #666;
  }

  // 风险等级
  .riskStat {
    display: flex;
    align-items: center;
    gap: 10px;

    .riskLevel {
      line-height: 24px;
      gap: 5px;
      display: flex;
      align-items: center;
      min-width: 150px;

      &.disabled {
        opacity: 0.2;
      }

      .title {
        font-size: 14px;
        color: #666;
      }

      .count {
        font-size: 16px;
        font-weight: 700;
        color: #333;
      }
    }
  }
}

