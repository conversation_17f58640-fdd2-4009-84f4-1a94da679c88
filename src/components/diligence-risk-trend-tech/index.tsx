import { defineComponent, computed, ref } from 'vue';

import QIcon from '../global/q-icon';
import { Tooltip } from 'ant-design-vue';
import DiligenceRiskTrendScore from '../diligence-risk-trend-score';
import DiligenceRiskTrendModal from './widgets/diligence-risk-trend-modal';

const DiligenceRiskTrendTech = defineComponent({
  name: 'DiligenceRiskTrendTech',
  props: {
    previousScore: {
      type: Number,
      default: 0,
    },
    score: {
      type: Number,
      default: 0,
    },
    /** 企业KeyNo，用于获取评分趋势图数据 */
    companyId: {
      type: String,
      required: false,
    },
  },
  setup(props) {
    // 弹窗显隐
    const modalVisible = ref(false);
    const setModalVisible = (visible: boolean) => {
      modalVisible.value = visible;
    };
    const handleShowModal = () => {
      modalVisible.value = true;
    };

    // 变化评分
    const scoreChange = computed(() => {
      return props.score - props.previousScore;
    });

    return {
      modalVisible,
      setModalVisible,

      handleShowModal,
      scoreChange,
    };
  },
  render() {
    const { handleShowModal, previousScore, score } = this;

    return (
      <div>
        <div class="flex justify-center gap-8px leading-22px">
          <span>评分趋势</span>
          <DiligenceRiskTrendScore previousScore={previousScore} score={score} description="环比上月评分" />
          {/* 评分趋势图 */}
          <Tooltip title="评分趋势">
            <div
              class="size-22px border-1px border-solid border-#eee color-#128bed flex items-center justify-center p-5px text-12px cursor-pointer rounded-2px bg-white hover:border-[rgba(18,139,237,0.4)] hover:bg-#e2f1fd"
              onClick={handleShowModal}
            >
              <QIcon type="icon-fqushituicon1" />
            </div>
          </Tooltip>
        </div>
        {/* 弹窗 */}
        <DiligenceRiskTrendModal v-model={this.modalVisible} title={'评分趋势'} companyId={this.companyId} />
      </div>
    );
  },
});

export default DiligenceRiskTrendTech;
