import { defineComponent, ref, computed, watch } from 'vue';

import DiligenceRiskTrendScore from '@/components/diligence-risk-trend-score';
import QPlainTable from '@/components/global/q-plain-table';
import QModal from '@/components/global/q-modal';
import QChart from '@/components/global/q-chart';
import { useRequest } from '@/shared/composables/use-request';
import { diligence as diligenceService } from '@/shared/services';
import { pick, reverse } from 'lodash';
import { dateFormat } from '@/utils/format';
import QLoading from '@/components/global/q-loading';
import Empty from '@/shared/components/empty';

const INDICATOR_TYPES = ['quality', 'innovation', 'development', 'scale', 'capability', 'stability'];

const INDICATOR_TYPE_MAP = {
  quality: '技术质量',
  innovation: '运营能力',
  development: '发展能力',
  scale: '技术规模',
  capability: '技术能力',
  stability: '技术稳定性',
};

const getChartOption = (data: any[] = []) => {
  const months = data.map((item) => item.scoreDate);
  const scores = data.map((item) => pick(item, INDICATOR_TYPES.concat(['total'])));

  const series = [
    {
      data: scores.map((scoreData) => ({
        value: scoreData.total,
        scores: scoreData, // 将完整的scores数据附加到每个数据点
      })),
      name: '评分',
      type: 'line',
      label: {
        show: true,
        color: '#7397BA',
        position: 'top',
      },
      lineStyle: {
        color: '#5b8ff9',
        width: 2,
      },
      symbol: 'emptyCircle',
      symbolSize: 6,
      itemStyle: {
        color: '#5b8ff9',
      },
      emphasis: {
        itemStyle: {
          color: '#5b8ff9',
          borderWidth: 2,
        },
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(91, 143, 249, 0.3)' },
            { offset: 1, color: 'rgba(91, 143, 249, 0.05)' },
          ],
        },
      },
    },
  ];

  return {
    series,
    title: null,
    legend: null,
    xAxis: {
      data: months,
      type: 'category',
      boundaryGap: false,
      axisLine: { onZero: true, lineStyle: { color: '#bbb', width: 1 } },
      axisTick: { show: true },
      axisLabel: {
        color: '#999',
        fontSize: 12,
        margin: 8,
        formatter: (value: string) => {
          // 格式化成月/日，只在第一个刻度显示年份
          const date = new Date(value);
          const month = date.getMonth() + 1;
          return `${month}月`;
        },
      },
      splitLine: { show: false },
    },
    yAxis: {
      type: 'value',
      // min: 0,
      // max: 100,
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { color: '#999', fontSize: 12, formatter: '{value}' },
      splitLine: { lineStyle: { color: '#f3f3f3', type: 'solid' } },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.75)',
      borderWidth: 0,
      padding: [8, 12],
      textStyle: {
        color: '#fff',
        fontSize: 14,
        lineHeight: 22,
      },
      position: (point, params, dom, rect, size) => {
        let left = 0;
        const isLeft = !!(point[0] < size.viewSize[0] / 2);
        if (isLeft) {
          left = point[0] + 10;
        } else {
          left = point[0] - size.contentSize[0] - 10;
        }
        return { top: 0, left };
      },
      formatter: (params) => {
        let html = `<div>`;
        html += `<div style="">${params[0].name}</div>`;

        params.forEach((param) => {
          const name = param.seriesName;
          const value = param.value;
          // 从 param.data 中获取完整的 scores 数据
          const scores = param.data?.scores || {};

          html += `<div style="">${name}：${value}</div>`;
          html += `
            <div style="padding: 4px; background: rgba(255, 255, 255, 0.05); color: #999; border-radius: 4px; line-height: 18px;">`;

          // 动态生成各个维度的评分数据
          INDICATOR_TYPES.forEach((type) => {
            const indicatorName = INDICATOR_TYPE_MAP[type];
            const indicatorValue = scores[type] || '-';
            html += `<div>${indicatorName}：${indicatorValue}</div>`;
          });

          html += `</div>`;
        });
        html += `</div>`;
        return html;
      },
    },
    grid: {
      left: 5,
      right: 12,
      top: 22,
      bottom: 5,
      containLabel: true,
    },
    axisPointer: {
      type: 'line',
    },
  };
};

const DiligenceRiskTrendModal = defineComponent({
  name: 'DiligenceRiskTrendModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '评分趋势',
    },
    height: {
      type: String,
      default: '180px',
    },
    /** 企业KeyNo，用于获取评分趋势图数据 */
    companyId: {
      type: String,
      required: false,
    },
  },
  model: {
    prop: 'visible',
    event: 'change',
  },
  emits: ['change'],
  setup(props, { emit }) {
    const { data, execute, isLoading } = useRequest(diligenceService.getTechScoreCardTrend);

    const chartRef = ref();
    const chartOptions = computed(() => {
      const chartData = data.value?.scoreList ?? [];
      const orderedChartData = reverse(chartData);
      return getChartOption(orderedChartData);
    });

    /** 表格数据 */
    const tableData = computed(() => {
      const changedData = data.value?.changedData ?? [];

      // 获取所有唯一的月份，用于构建表头
      const allMonths = new Set<number>();
      changedData.forEach((item) => {
        item.monthScore?.forEach((monthItem) => {
          allMonths.add(monthItem.month);
        });
      });
      const sortedMonths = Array.from(allMonths).sort((a, b) => a - b);

      // 将每个指标的 monthScore 展平处理
      const flattenedData = changedData.map((item) => {
        const flattened = {
          metricName: item.metricName,
          metricId: item.metricId,
          changeScore: item.changeScore,
        };

        // 将 monthScore 中的分数按月份展平到主级别
        item.monthScore?.forEach((monthItem) => {
          flattened[`month-${monthItem.month}`] = monthItem.score;
        });

        // 为缺失的月份填充默认值
        sortedMonths.forEach((month) => {
          if (!(flattened[`month-${month}`] !== undefined)) {
            flattened[`month-${month}`] = '-';
          }
        });

        return flattened;
      });

      return {
        data: flattenedData,
        months: sortedMonths,
      };
    });

    const request = () => {
      return execute({ keyNo: props.companyId, count: 12 });
    };

    watch(
      () => props.visible,
      (isVisible: boolean) => {
        if (isVisible) {
          request();
        }
      }
    );

    const onChangeVisible = () => {
      emit('change', false);
    };

    return {
      chartRef,
      chartOptions,
      onChangeVisible,
      data,
      isLoading,
      tableData,
    };
  },
  render() {
    const { visible, onChangeVisible, title, chartOptions, height, isLoading } = this;

    return (
      <QModal size="large" visible={visible} onCancel={onChangeVisible} footer={false}>
        <div slot="title" class="flex justify-between pr-30px">
          <div class="font-bold">{title}</div>
          <div class="font-normal">
            上次更新：{this.data?.lastUpdateDate ? dateFormat(this.data.lastUpdateDate, { pattern: 'YYYY-MM-DD HH:mm:ss' }) : '-'}
          </div>
        </div>

        <div class="flex items-center justify-center h-220px" v-show={isLoading}>
          <QLoading loading={isLoading}></QLoading>
        </div>

        <div class="flex flex-col gap-20px" v-show={!isLoading}>
          {/* 图表 */}
          <QChart ref="chartRef" height={height} option={chartOptions} />
          {/* 表格 */}
          <div class="flex flex-col gap-8px">
            <div>
              <DiligenceRiskTrendScore previousScore={0} score={this.data?.scoreMonthOverMonth} description={'环比上月评分'} />
            </div>
            {this.data?.changedData?.length > 0 ? (
              <QPlainTable>
                <thead>
                  <tr>
                    <th class="px-10px! py-13px!">Top5 差异指标</th>
                    {this.tableData.months.map((month: number) => (
                      <th key={month} class="px-10px! py-13px!">
                        {month}月
                      </th>
                    ))}
                    <th class="px-10px! py-13px!">指标评分变化</th>
                  </tr>
                </thead>
                <tbody>
                  {this.tableData.data.map((item: any) => (
                    <tr key={item.metricId}>
                      <td class="px-9px! py-10px!">{item.metricName}</td>
                      {this.tableData.months.map((month: number) => (
                        <td key={`${item.metricId}-${month}`} class="px-9px! py-10px!">
                          {item[`month-${month}`]}
                        </td>
                      ))}
                      <td class="px-9px! py-10px!">
                        <DiligenceRiskTrendScore previousScore={0} score={item.changeScore} />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </QPlainTable>
            ) : (
              <div>
                <Empty description="环比上月评分无变化" type="text" iconSize="60" />
              </div>
            )}
          </div>
        </div>
      </QModal>
    );
  },
});

export default DiligenceRiskTrendModal;
