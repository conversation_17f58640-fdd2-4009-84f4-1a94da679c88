.container {
  position: relative;
  padding-bottom: 22px;

  .marker {
    left: 0;
    transform: translateX(-50% - 4px);
    position: absolute;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1px;

    .pointer {
      color: #00AD65;
      display: flex;
      flex-direction: column;
      align-items: center;

      &::before {
        border: 2px solid #fff;
        content: '';
        width: 10px;
        height: 17px;
        background: currentcolor;
        border-radius: 5px;
        display: block;
      }

      &::after {
        margin-top: -2px;
        content: '';
        width: 3px;
        height: 8px;
        border-left: 1px solid #fff;
        border-right: 1px solid #fff;
        border-bottom: 1px solid #fff;
        background: currentcolor;
        display: block;
      }
    }

    .label {
      background: #eee;
      color: #fff;
      display: block;
      line-height: 22px;
      height: 22px;
      padding: 0 10px;
      border-radius: 10px;
    }
  }
}
