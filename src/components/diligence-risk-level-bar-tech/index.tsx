import { defineComponent } from 'vue';

import styles from './diligence-risk-level-bar-tech.module.less';
import { TechRiskLevel, TECH_RISK_COLORS, SCORE_TO_TECH_RISK_MAPPING } from '@/config/risk.config';

// 主题配置
const THEME = {
  colors: {
    highLight: '#333333',
    primary: '#bbb',
    secondary: '#999',
    white: '#fff',
    // 使用统一的技术风险颜色配置
    risk: {
      high: TECH_RISK_COLORS[TechRiskLevel.CRITICAL],
      mediumHigh: '#F04040',
      medium: TECH_RISK_COLORS[TechRiskLevel.NORMAL],
      mediumLow: TECH_RISK_COLORS[TechRiskLevel.MEDIUM],
      low1: TECH_RISK_COLORS[TechRiskLevel.GOOD],
      low2: TECH_RISK_COLORS[TechRiskLevel.EXCELLENT],
    },
  },
  opacity: {
    none: 0,
    gradient: 0.1,
    light: 0.3,
    half: 0.5,
    full: 1,
  },
  styles: {
    fillOpacity: 1,
    mixBlendMode: 'mix-blend-mode:passthrough',
  },
} as const;

// 组件配置
const CONFIG = {
  // SVG视图配置
  svg: {
    width: 336,
    height: 63,
    viewBox: '0 0 336 63',
  },
  // 阴影配置
  shadow: {
    gradientOpacity: THEME.opacity.gradient,
    gradientOffset: '97%',
    rectConfig: [
      { x: 0, width: 65, height: 29, color: THEME.colors.risk.mediumHigh },
      { x: 66, width: 67, height: 29, color: THEME.colors.risk.medium },
      { x: 134, width: 66, height: 29, color: THEME.colors.risk.mediumLow },
      { x: 201, width: 67, height: 29, color: THEME.colors.risk.low1 },
      { x: 269, width: 67, height: 29, color: THEME.colors.risk.low2 },
    ],
    rectCommon: { y: 24, height: 29, rx: 0 },
  },
  // 条形图配置
  bar: {
    colors: [THEME.colors.risk.high, THEME.colors.risk.medium, THEME.colors.risk.mediumLow, THEME.colors.risk.low1, THEME.colors.risk.low2],
    width: 33.6,
    rectCommon: { y: 46, height: 10 },
    // 简化位置配置
    generatePositions: () => {
      const positions: Array<Array<{ x: number; width: number; isRounded?: boolean; roundSide?: 'left' | 'right' }>> = [];
      for (let i = 0; i < 5; i++) {
        const baseX = i * 67;
        positions.push([
          { x: baseX, width: 33.6, isRounded: i === 0, roundSide: i === 0 ? 'left' : undefined },
          { x: baseX + 33.6, width: 33.6, isRounded: i === 4, roundSide: i === 4 ? 'right' : undefined },
        ]);
      }
      return positions;
    },
  },
  // 指示器配置
  indicator: {
    opacity: THEME.opacity.half,
    colors: [THEME.colors.risk.high, THEME.colors.risk.mediumHigh, THEME.colors.risk.mediumLow, THEME.colors.risk.low1],
    positions: [64, 132, 199, 266],
    rectCommon: {
      y: 41,
      width: 3,
      height: 20,
      rx: 1.66,
      strokeWidth: 1,
      strokeColor: THEME.colors.white,
    },
  },
};

const INDICATOR_LABELS = [
  // 0
  {
    paths: [
      {
        d: 'M2.820000013589859,6.495999904632568L2.820000013589859,8.319999904632567L1.584000013589859,8.319999904632567C1.896000013589859,7.539999904632569,2.184000013589859,6.639999904632568,2.448000013589859,5.607999904632568L4.764000013589859,5.607999904632568L4.764000013589859,4.7679999046325685L2.652000013589859,4.7679999046325685C2.760000013589859,4.311999904632568,2.868000013589859,3.819999904632568,2.964000013589859,3.3039999046325685L2.160000013589859,3.1719999046325684C2.052000013589859,3.7239999046325685,1.944000013589859,4.263999904632568,1.836000013589859,4.7679999046325685L0.528000013589859,4.7679999046325685L0.528000013589859,5.607999904632568L1.632000013589859,5.607999904632568C1.3440000135898589,6.7119999046325685,1.032000013589859,7.623999904632568,0.708000013589859,8.343999904632568L0.888000013589859,9.099999904632568L2.820000013589859,9.099999904632568L2.820000013589859,10.899999904632569C2.088000013589859,11.031999904632569,1.3080000135898588,11.151999904632568,0.492000013589859,11.259999904632569L0.600000013589859,12.099999904632568C1.368000013589859,11.991999904632568,2.112000013589859,11.871999904632569,2.820000013589859,11.727999904632568L2.820000013589859,14.187999904632568L3.648000013589859,14.187999904632568L3.648000013589859,11.547999904632569C4.152000013589859,11.427999904632568,4.644000013589859,11.307999904632569,5.112000013589859,11.175999904632569L5.112000013589859,10.359999904632568C4.644000013589859,10.479999904632567,4.152000013589859,10.611999904632569,3.648000013589859,10.731999904632568L3.648000013589859,9.099999904632568L4.764000013589859,9.099999904632568L4.764000013589859,8.319999904632567L3.648000013589859,8.319999904632567L3.648000013589859,6.495999904632568L2.820000013589859,6.495999904632568ZM5.220000013589859,4.7439999046325685L5.220000013589859,5.583999904632568L11.32800001358986,5.583999904632568L11.32800001358986,4.7439999046325685L8.66400001358986,4.7439999046325685C8.47200001358986,4.155999904632568,8.268000013589859,3.6399999046325684,8.052000013589858,3.1840000046325683L7.188000013589859,3.3279999046325686C7.416000013589859,3.7599999046325685,7.620000013589859,4.2279999046325685,7.812000013589859,4.7439999046325685L5.220000013589859,4.7439999046325685ZM6.696000013589859,6.027999904632568C6.204000013589859,7.179999904632568,5.616000013589859,8.163999904632568,4.944000013589859,8.991999904632568L5.628000013589859,9.483999904632569C6.312000013589859,8.607999904632567,6.912000013589859,7.575999904632568,7.428000013589859,6.375999904632568L6.696000013589859,6.027999904632568ZM9.528000013589859,5.979999904632568L8.868000013589858,6.399999904632568C9.672000013589859,7.503999904632568,10.30800001358986,8.499999904632569,10.788000013589858,9.399999904632569L11.48400001358986,8.907999904632568C11.028000013589859,8.079999904632569,10.368000013589858,7.107999904632568,9.528000013589859,5.979999904632568ZM9.18000001358986,8.295999904632568C9.000000013589858,9.291999904632569,8.688000013589859,10.167999904632568,8.23200001358986,10.923999904632568C7.668000013589859,10.15599990463257,7.320000013589859,9.279999904632568,7.176000013589859,8.295999904632568L6.420000013589859,8.523999904632568C6.600000013589859,9.68799990463257,7.032000013589859,10.71999990463257,7.716000013589859,11.631999904632568C6.960000013589859,12.507999904632568,5.928000013589859,13.119999904632568,4.620000013589859,13.47999990463257L5.100000013589859,14.235999904632568C6.420000013589859,13.863999904632568,7.476000013589859,13.215999904632568,8.29200001358986,12.291999904632569C9.012000013589859,13.035999904632568,9.97200001358986,13.671999904632568,11.14800001358986,14.175999904632569L11.592000013589859,13.407999904632568C10.45200001358986,12.915999904632569,9.528000013589859,12.315999904632568,8.820000013589858,11.595999904632569C9.384000013589858,10.743999904632568,9.78000001358986,9.699999904632568,9.996000013589859,8.46399990463257L9.18000001358986,8.295999904632568ZM13.26000001358986,4.527999904632568L13.26000001358986,5.331999904632569L17.62800001358986,5.331999904632569C17.56800001358986,5.679999904632568,17.49600001358986,6.027999904632568,17.424000013589858,6.375999904632568L13.95600001358986,6.375999904632568L13.95600001358986,7.179999904632568L17.19600001358986,7.179999904632568C17.07600001358986,7.527999904632568,16.94400001358986,7.863999904632569,16.78800001358986,8.199999904632568L12.912000013589859,8.199999904632568L12.912000013589859,9.015999904632569L16.368000013589857,9.015999904632569C16.32000001358986,9.099999904632568,16.26000001358986,9.183999904632568,16.212000013589858,9.279999904632568C15.37200001358986,10.551999904632568,14.112000013589858,11.655999904632568,12.432000013589859,12.567999904632568L13.008000013589859,13.311999904632568C14.160000013589858,12.615999904632568,15.12000001358986,11.847999904632568,15.900000013589858,11.019999904632568L18.25200001358986,11.019999904632568L18.25200001358986,12.951999904632569L14.28000001358986,12.951999904632569L14.28000001358986,13.791999904632569L23.18400001358986,13.791999904632569L23.18400001358986,12.951999904632569L19.12800001358986,12.951999904632569L19.12800001358986,11.019999904632568L22.44000001358986,11.019999904632568L22.44000001358986,10.179999904632568L16.60800001358986,10.179999904632568C16.70400001358986,10.047999904632569,16.80000001358986,9.915999904632567,16.89600001358986,9.771999904632569C17.040000013589857,9.531999904632569,17.17200001358986,9.279999904632568,17.31600001358986,9.015999904632569L23.19600001358986,9.015999904632569L23.19600001358986,8.199999904632568L17.68800001358986,8.199999904632568C17.82000001358986,7.875999904632568,17.94000001358986,7.539999904632569,18.06000001358986,7.179999904632568L22.15200001358986,7.179999904632568L22.15200001358986,6.375999904632568L18.28800001358986,6.375999904632568C18.372000013589858,6.039999904632568,18.44400001358986,5.691999904632569,18.51600001358986,5.331999904632569L22.78800001358986,5.331999904632569L22.78800001358986,4.527999904632568L20.41200001358986,4.527999904632568C20.66400001358986,4.119999904632568,20.85600001358986,3.6879999046325684,20.988000013589858,3.2199999046325685L20.08800001358986,3.0999999046325684C19.968000013589858,3.5919999046325684,19.776000013589858,4.071999904632568,19.52400001358986,4.527999904632568L16.74000001358986,4.527999904632568C16.54800001358986,4.011999904632568,16.34400001358986,3.5559999046325683,16.12800001358986,3.1480000046325682L15.240000013589858,3.2919999046325685C15.46800001358986,3.6639999046325684,15.68400001358986,4.071999904632568,15.87600001358986,4.527999904632568L13.26000001358986,4.527999904632568Z',
        fill: THEME.colors.primary,
      },
      {
        d: 'M3.600000023841858,25.263999462127686C2.568000023841858,25.263999462127686,1.8000000238418579,25.707999462127685,1.284000023841858,26.619999462127687C0.8280000238418579,27.387999462127684,0.6000000238418579,28.419999462127684,0.6000000238418579,29.715999462127684C0.6000000238418579,31.011999462127687,0.8280000238418579,32.04399946212769,1.284000023841858,32.81199946212769C1.8000000238418579,33.711999462127686,2.568000023841858,34.16799946212768,3.600000023841858,34.16799946212768C4.6200000238418575,34.16799946212768,5.388000023841858,33.711999462127686,5.916000023841858,32.81199946212769C6.372000023841858,32.04399946212769,6.600000023841858,31.011999462127687,6.600000023841858,29.715999462127684C6.600000023841858,28.419999462127684,6.372000023841858,27.387999462127684,5.916000023841858,26.619999462127687C5.388000023841858,25.707999462127685,4.6200000238418575,25.263999462127686,3.600000023841858,25.263999462127686ZM3.600000023841858,26.103999462127685C4.368000023841858,26.103999462127685,4.920000023841858,26.511999462127687,5.256000023841858,27.339999462127686C5.496000023841858,27.915999462127687,5.6280000238418575,28.707999462127685,5.6280000238418575,29.715999462127684C5.6280000238418575,30.711999462127686,5.496000023841858,31.503999462127688,5.256000023841858,32.09199946212769C4.920000023841858,32.907999462127684,4.368000023841858,33.327999462127686,3.600000023841858,33.327999462127686C2.832000023841858,33.327999462127686,2.2800000238418576,32.907999462127684,1.944000023841858,32.09199946212769C1.704000023841858,31.503999462127688,1.584000023841858,30.711999462127686,1.584000023841858,29.715999462127684C1.584000023841858,28.707999462127685,1.704000023841858,27.915999462127687,1.944000023841858,27.339999462127686C2.2800000238418576,26.511999462127687,2.832000023841858,26.103999462127685,3.600000023841858,26.103999462127685Z',
        fill: THEME.colors.secondary,
      },
    ],
  },
  // 1
  {
    paths: [
      {
        d: 'M89.10000172257423,8.164000085830688L89.10000172257423,9.028000085830689L99.88800172257423,9.028000085830689L99.88800172257423,8.164000085830688L89.10000172257423,8.164000085830688ZM101.05200172257423,8.42800008583069L101.05200172257423,9.220000085830687L101.91600172257424,9.220000085830687C101.86800172257423,10.97200008583069,101.56800172257424,12.424000085830688,100.99200172257423,13.58800008583069L101.66400172257423,14.200000085830688C102.31200172257424,12.916000085830689,102.66000172257424,11.260000085830688,102.70800172257424,9.220000085830687L105.16800172257423,9.220000085830687L105.16800172257423,12.892000085830688C105.16800172257423,13.216000085830688,105.02400172257424,13.384000085830689,104.73600172257423,13.384000085830689C104.42400172257423,13.384000085830689,104.06400172257423,13.360000085830688,103.68000172257423,13.336000085830689L103.90800172257423,14.164000085830688L105.00000172257424,14.164000085830688C105.64800172257424,14.164000085830688,105.98400172257423,13.828000085830688,105.98400172257423,13.180000085830688L105.98400172257423,4.432000085830689L104.29200172257424,4.432000085830689C104.44800172257423,4.072000085830688,104.56800172257424,3.6880000858306885,104.66400172257423,3.2800000858306886L103.76400172257424,3.1600000858306885C103.68000172257423,3.5920000858306884,103.56000172257424,4.024000085830688,103.39200172257424,4.432000085830689L101.92800172257424,4.432000085830689L101.92800172257424,8.42800008583069L101.05200172257423,8.42800008583069ZM105.16800172257423,8.42800008583069L102.72000172257424,8.42800008583069L102.72000172257424,5.236000085830689L105.16800172257423,5.236000085830689L105.16800172257423,8.42800008583069ZM103.88400172257424,5.848000085830689L103.24800172257423,6.052000085830688C103.51200172257424,6.604000085830688,103.72800172257423,7.1680000858306885,103.89600172257423,7.768000085830688L104.58000172257424,7.576000085830689C104.36400172257423,6.928000085830688,104.13600172257424,6.352000085830689,103.88400172257424,5.848000085830689ZM103.82400172257424,9.880000085830687L103.15200172257423,10.096000085830688C103.46400172257424,10.792000085830688,103.72800172257423,11.512000085830689,103.94400172257423,12.268000085830689L104.65200172257423,12.076000085830689C104.41200172257423,11.260000085830688,104.13600172257424,10.528000085830689,103.82400172257424,9.880000085830687ZM107.49600172257423,3.7840000858306886C107.49600172257423,4.720000085830689,107.42400172257423,5.416000085830689,107.30400172257424,5.860000085830689C107.17200172257424,6.292000085830688,106.88400172257423,6.760000085830688,106.42800172257424,7.252000085830688L107.10000172257423,7.852000085830689C107.65200172257423,7.276000085830688,107.98800172257424,6.772000085830689,108.12000172257423,6.352000085830689C108.25200172257424,5.944000085830688,108.32400172257424,5.344000085830689,108.32400172257424,4.564000085830688L109.96800172257423,4.564000085830688L109.96800172257423,6.580000085830688C109.96800172257423,7.2160000858306885,110.28000172257424,7.540000085830688,110.92800172257424,7.540000085830688L111.97200172257423,7.540000085830688L111.97200172257423,6.736000085830689L111.16800172257423,6.736000085830689C110.92800172257424,6.736000085830689,110.80800172257423,6.616000085830688,110.80800172257423,6.388000085830688L110.80800172257423,3.7840000858306886L107.49600172257423,3.7840000858306886ZM106.74000172257423,8.44000008583069L106.74000172257423,9.220000085830687L107.10000172257423,9.220000085830687C107.37600172257423,10.300000085830689,107.91600172257424,11.272000085830689,108.70800172257424,12.13600008583069C108.07200172257424,12.688000085830689,107.30400172257424,13.156000085830689,106.41600172257424,13.516000085830688L106.88400172257423,14.236000085830689C107.84400172257423,13.852000085830689,108.66000172257424,13.360000085830688,109.33200172257423,12.736000085830689C109.94400172257423,13.276000085830688,110.68800172257423,13.768000085830689,111.57600172257423,14.212000085830688L112.02000172257424,13.456000085830688C111.21600172257423,13.060000085830689,110.52000172257424,12.616000085830688,109.92000172257423,12.112000085830688C110.60400172257424,11.308000085830688,111.09600172257423,10.324000085830688,111.38400172257423,9.172000085830689L111.38400172257423,8.44000008583069L106.74000172257423,8.44000008583069ZM107.89200172257424,9.220000085830687L110.54400172257424,9.220000085830687C110.26800172257424,10.096000085830688,109.84800172257424,10.864000085830689,109.29600172257423,11.536000085830688C108.63600172257424,10.852000085830689,108.16800172257423,10.084000085830688,107.89200172257424,9.220000085830687Z',
        fill: THEME.colors.primary,
      },
      {
        d: 'M70.72000654435158,25.264000177383423C69.85600654435157,25.264000177383423,69.14800654435157,25.552000177383423,68.62000554435157,26.128000177383424C68.09200654435158,26.692000177383424,67.81600514435158,27.460000177383424,67.81600514435158,28.42000017738342L68.80000654435158,28.42000017738342C68.81199654435157,27.66400017738342,68.99199654435158,27.088000177383424,69.32800654435158,26.704000177383424C69.64000654435158,26.296000177383423,70.09600654435158,26.104000177383423,70.68399654435157,26.104000177383423C71.23600654435158,26.104000177383423,71.68000654435158,26.247999177383424,71.99199654435158,26.53600017738342C72.30400654435158,26.82400017738342,72.46000654435157,27.220000177383422,72.46000654435157,27.748000177383425C72.46000654435157,28.300000177383424,72.23200654435158,28.816000177383422,71.80000654435158,29.284000177383422C71.57200654435158,29.52400017738342,71.12800654435158,29.872000177383423,70.45600654435158,30.352000177383424C69.54400654435157,30.976000177383423,68.93200654435158,31.492000177383424,68.59600354435157,31.876000177383425C68.01999954435158,32.51200017738342,67.74400654435158,33.220000177383426,67.74400654435158,34.00000017738343L73.45600654435158,34.00000017738343L73.45600654435158,33.12400017738342L68.95600654435158,33.12400017738342C69.11200654435157,32.54800017738342,69.73600654435158,31.88800017738342,70.82800654435158,31.156000177383422C71.71600654435157,30.556000177383424,72.30400654435158,30.08800017738342,72.60399654435157,29.776000177383423C73.15599654435158,29.176000177383422,73.44400654435158,28.504000177383425,73.44400654435158,27.76000017738342C73.44400654435158,27.01600017738342,73.18000654435158,26.404000177383423,72.67600654435158,25.948000177383424C72.16000654435157,25.492001177383422,71.51200654435158,25.264000177383423,70.72000654435158,25.264000177383423ZM77.80000654435158,25.264000177383423C76.76799654435158,25.264000177383423,76.00000654435158,25.708000177383422,75.48400654435157,26.620000177383425C75.02800654435158,27.38800017738342,74.80000654435158,28.42000017738342,74.80000654435158,29.71600017738342C74.80000654435158,31.012000177383424,75.02800654435158,32.044000177383424,75.48400654435157,32.812000177383425C76.00000654435158,33.71200017738342,76.76799654435158,34.16800017738342,77.80000654435158,34.16800017738342C78.82000654435157,34.16800017738342,79.58800654435157,33.71200017738342,80.11600654435158,32.812000177383425C80.57200654435158,32.044000177383424,80.80000654435158,31.012000177383424,80.80000654435158,29.71600017738342C80.80000654435158,28.42000017738342,80.57200654435158,27.38800017738342,80.11600654435158,26.620000177383425C79.58800654435157,25.708000177383422,78.82000654435157,25.264000177383423,77.80000654435158,25.264000177383423ZM77.80000654435158,26.104000177383423C78.56800654435158,26.104000177383423,79.12000654435158,26.512000177383424,79.45600654435158,27.340000177383423C79.69600654435158,27.916000177383424,79.82800654435158,28.708000177383422,79.82800654435158,29.71600017738342C79.82800654435158,30.712000177383423,79.69600654435158,31.504000177383425,79.45600654435158,32.092000177383426C79.12000654435158,32.90800017738342,78.56800654435158,33.32800017738342,77.80000654435158,33.32800017738342C77.03199654435159,33.32800017738342,76.48000654435158,32.90800017738342,76.14399654435158,32.092000177383426C75.90400654435157,31.504000177383425,75.78399654435158,30.712000177383423,75.78399654435158,29.71600017738342C75.78399654435158,28.708000177383422,75.90400654435157,27.916000177383424,76.14399654435158,27.340000177383423C76.48000654435158,26.512000177383424,77.03199654435159,26.104000177383423,77.80000654435158,26.104000177383423Z',
        fill: THEME.colors.secondary,
      },
    ],
  },
  // 2
  {
    paths: [
      {
        d: 'M161.05600012589454,3.1479999724792482L161.05600012589454,5.308000072479248L156.75999012589455,5.308000072479248L156.75999012589455,10.948000072479248L157.62398812589456,10.948000072479248L157.62398812589456,10.192000072479248L161.05600012589454,10.192000072479248L161.05600012589454,14.284000072479248L161.94400012589455,14.284000072479248L161.94400012589455,10.192000072479248L165.38800012589454,10.192000072479248L165.38800012589454,10.948000072479248L166.25199012589454,10.948000072479248L166.25199012589454,5.308000072479248L161.94400012589455,5.308000072479248L161.94400012589455,3.1479999724792482L161.05600012589454,3.1479999724792482ZM157.62398812589456,9.352000072479248L157.62398812589456,6.148000072479248L161.05600012589454,6.148000072479248L161.05600012589454,9.352000072479248L157.62398812589456,9.352000072479248ZM161.94400012589455,9.352000072479248L161.94400012589455,6.148000072479248L165.38800012589454,6.148000072479248L165.38800012589454,9.352000072479248L161.94400012589455,9.352000072479248ZM169.49199012589455,6.736000072479248L169.49199012589455,7.492000072479248L173.06799012589454,7.492000072479248L173.06799012589454,8.656000072479248L168.15999012589455,8.656000072479248L168.15999012589455,9.412000072479248L175.64799012589455,9.412000072479248L175.64799012589455,10.56400007247925L168.86799012589455,10.56400007247925L168.86799012589455,11.320000072479248L175.64799012589455,11.320000072479248L175.64799012589455,13.000000072479247C175.64799012589455,13.288000072479248,175.47999012589455,13.444000072479248,175.14399012589456,13.444000072479248C174.63999012589454,13.444000072479248,174.09999012589455,13.420000072479247,173.52399012589456,13.384000072479248L173.70399012589453,14.200000072479249L175.44399012589454,14.200000072479249C176.15199012589454,14.200000072479249,176.51199012589456,13.864000072479248,176.51199012589456,13.204000072479248L176.51199012589456,11.320000072479248L178.31199012589454,11.320000072479248L178.31199012589454,10.56400007247925L176.51199012589456,10.56400007247925L176.51199012589456,9.412000072479248L178.82799012589456,9.412000072479248L178.82799012589456,8.656000072479248L173.93199012589454,8.656000072479248L173.93199012589454,7.492000072479248L177.45999012589454,7.492000072479248L177.45999012589454,6.736000072479248L173.93199012589454,6.736000072479248L173.93199012589454,5.980000072479248L173.06799012589454,5.980000072479248L173.06799012589454,6.736000072479248L169.49199012589455,6.736000072479248ZM170.89599012589454,11.656000072479248L170.19999012589454,12.088000072479248C170.82399012589454,12.712000072479247,171.32799012589456,13.276000072479247,171.69999012589454,13.792000072479247L172.37199012589454,13.324000072479247C172.03599012589456,12.844000072479249,171.54399012589454,12.280000072479249,170.89599012589454,11.656000072479248ZM169.92399012589453,4.804000072479248L170.40399012589455,4.804000072479248C170.73999012589454,5.272000072479248,171.03999012589455,5.752000072479248,171.27999012589456,6.256000072479248L172.05999012589456,5.9680000724792475C171.86799012589455,5.608000072479248,171.61599012589454,5.224000072479248,171.30399012589456,4.804000072479248L173.33199012589455,4.804000072479248L173.33199012589455,4.0720000724792484L170.25999012589455,4.0720000724792484C170.35599012589455,3.8320000724792482,170.43999012589455,3.580000072479248,170.53599012589456,3.316000072479248L169.71999012589455,3.135999872479248C169.39599012589454,4.1200000724792485,168.87999012589455,5.0320000724792475,168.18399012589455,5.860000072479249L168.92799012589455,6.328000072479249C169.29999012589454,5.872000072479248,169.63599012589455,5.356000072479248,169.92399012589453,4.804000072479248ZM174.79599012589455,4.768000072479248L175.50399012589455,4.768000072479248C175.86399012589453,5.236000072479248,176.16399012589454,5.716000072479249,176.41599012589455,6.220000072479248L177.15999012589455,5.944000072479248C176.97999012589455,5.584000072479248,176.72799012589454,5.188000072479248,176.40399012589455,4.768000072479248L178.57599012589455,4.768000072479248L178.57599012589455,4.036000072479248L175.11999012589456,4.036000072479248C175.20399012589453,3.796000072479248,175.28799012589454,3.544000072479248,175.35999012589454,3.292000072479248L174.54399012589454,3.124000072479248C174.30399012589456,3.964000072479248,173.93199012589454,4.720000072479248,173.40399012589455,5.416000072479248L174.12399012589455,5.872000072479248C174.36399012589453,5.536000072479248,174.59199012589454,5.176000072479248,174.79599012589455,4.768000072479248Z',
        fill: THEME.colors.primary,
      },
      {
        d: 'M138.60800000382423,25.431998462127687L134.28799000382423,31.119999462127687L134.28799000382423,32.05599946212769L138.59599000382423,32.05599946212769L138.59599000382423,33.99999946212769L139.53199000382423,33.99999946212769L139.53199000382423,32.05599946212769L140.92399000382423,32.05599946212769L140.92399000382423,31.251999462127685L139.53199000382423,31.251999462127685L139.53199000382423,25.431998462127687L138.60800000382423,25.431998462127687ZM138.55999000382423,26.715999462127684L138.59599000382423,26.715999462127684L138.59599000382423,31.251999462127685L135.11599300382423,31.251999462127685L138.55999000382423,26.715999462127684ZM144.79999000382423,25.263999462127686C143.76800000382423,25.263999462127686,143.00000000382423,25.707999462127685,142.48399000382423,26.619999462127687C142.02800000382425,27.387999462127684,141.80000000382424,28.419999462127684,141.80000000382424,29.715999462127684C141.80000000382424,31.011999462127687,142.02800000382425,32.04399946212769,142.48399000382423,32.81199946212769C143.00000000382423,33.711999462127686,143.76800000382423,34.16799946212768,144.79999000382423,34.16799946212768C145.81999000382424,34.16799946212768,146.58799000382425,33.711999462127686,147.11599000382424,32.81199946212769C147.57199000382423,32.04399946212769,147.79999000382423,31.011999462127687,147.79999000382423,29.715999462127684C147.79999000382423,28.419999462127684,147.57199000382423,27.387999462127684,147.11599000382424,26.619999462127687C146.58799000382425,25.707999462127685,145.81999000382424,25.263999462127686,144.79999000382423,25.263999462127686ZM144.79999000382423,26.103999462127685C145.56799000382424,26.103999462127685,146.11999000382423,26.511999462127687,146.45599000382424,27.339999462127686C146.69599000382422,27.915999462127687,146.82799000382423,28.707999462127685,146.82799000382423,29.715999462127684C146.82799000382423,30.711999462127686,146.69599000382422,31.503999462127688,146.45599000382424,32.09199946212769C146.11999000382423,32.907999462127684,145.56799000382424,33.327999462127686,144.79999000382423,33.327999462127686C144.03199000382423,33.327999462127686,143.47999000382424,32.907999462127684,143.14399000382423,32.09199946212769C142.90400000382422,31.503999462127688,142.78399000382424,30.711999462127686,142.78399000382424,29.715999462127684C142.78399000382424,28.707999462127685,142.90400000382422,27.915999462127687,143.14399000382423,27.339999462127686C143.47999000382424,26.511999462127687,144.03199000382423,26.103999462127685,144.79999000382423,26.103999462127685Z',
        fill: THEME.colors.secondary,
      },
    ],
  },
  // 3
  {
    paths: [
      {
        d: 'M225.28398829483987,7.287999891281128L231.64398829483986,7.287999891281128L231.64398829483986,8.607999891281128L225.28398829483987,8.607999891281128L225.28398829483987,7.287999891281128ZM231.64398829483986,6.531999891281128L225.28398829483987,6.531999891281128L225.28398829483987,5.247999891281128L231.64398829483986,5.247999891281128L231.64398829483986,6.531999891281128ZM228.45198829483985,3.099999891281128L227.52799829483985,3.243999891281128C227.71999829483985,3.603999891281128,227.89998829483986,4.011999891281128,228.06798829483986,4.455999891281127L224.41999329483986,4.455999891281127L224.41999329483986,13.023999891281129C224.41999329483986,13.191999891281128,224.33599429483985,13.311999891281127,224.17998829483986,13.383999891281128L224.38399829483987,14.163999891281128C226.07599829483985,13.803999891281128,227.47998829483987,13.419999891281128,228.60799829483986,13.035999891281127L228.39199829483985,12.219999891281129C227.37198829483987,12.591999891281128,226.33998829483986,12.891999891281127,225.28398829483987,13.131999891281128L225.28398829483987,9.387999891281128L227.41999829483987,9.387999891281128C228.61998829483986,11.835999891281128,230.68399829483985,13.431999891281128,233.59999829483985,14.163999891281128L234.00798829483986,13.359999891281127C232.66399829483987,13.059999891281128,231.52399829483986,12.567999891281127,230.56398829483987,11.895999891281129C231.47599829483985,11.511999891281128,232.27999829483986,10.97199989128113,232.99999829483986,10.275999891281128L232.38799829483986,9.663999891281128C231.59598829483986,10.455999891281127,230.76799829483986,11.019999891281127,229.89199829483985,11.379999891281129C229.25598829483985,10.815999891281127,228.72798829483986,10.155999891281127,228.30799829483985,9.387999891281128L232.49599829483986,9.387999891281128L232.49599829483986,4.455999891281127L228.95598829483987,4.455999891281127C228.79999829483987,3.951999891281128,228.63199829483986,3.495999891281128,228.45198829483985,3.099999891281128ZM237.05598829483986,3.063999891281128C236.92398829483986,3.8919998912811278,236.77998829483985,4.695999891281128,236.61198829483985,5.487999891281127L235.15998829483985,5.487999891281127L235.15998829483985,6.315999891281128L236.43198829483987,6.315999891281128C236.14398829483986,7.539999891281128,235.81998829483985,8.679999891281128,235.44798829483986,9.735999891281129C236.11998829483986,10.227999891281128,236.74398829483985,10.695999891281128,237.29598829483984,11.139999891281128C236.73198829483985,12.075999891281128,235.99998829483985,12.867999891281128,235.08798829483985,13.527999891281128L235.67598829483987,14.247999891281127C236.59998829483985,13.539999891281129,237.36798829483985,12.687999891281128,237.96798829483987,11.691999891281128C238.56798829483986,12.195999891281128,239.08398829483986,12.651999891281127,239.49198829483987,13.083999891281128L240.07998829483986,12.399999891281128C239.63598829483985,11.967999891281128,239.07198829483985,11.475999891281129,238.37598829483986,10.935999891281128C239.04798829483985,9.531999891281128,239.44398829483987,7.875999891281128,239.55198829483987,5.979999891281128L239.55198829483987,5.487999891281127L237.47598829483985,5.487999891281127C237.61998829483986,4.767999891281128,237.76398829483986,3.975999891281128,237.90798829483987,3.135999891281128L237.05598829483986,3.063999891281128ZM237.67998829483986,10.40799989128113C237.28398829483984,10.119999891281129,236.85198829483986,9.807999891281128,236.39598829483987,9.483999891281128C236.70798829483985,8.643999891281128,237.00798829483986,7.587999891281128,237.29598829483984,6.315999891281128L238.69998829483987,6.315999891281128C238.59198829483987,7.851999891281128,238.25598829483985,9.219999891281127,237.67998829483986,10.40799989128113ZM239.95998829483986,8.235999891281129L239.95998829483986,9.075999891281128L242.67198829483985,9.075999891281128L242.67198829483985,12.651999891281127C242.67198829483985,13.059999891281128,242.52798829483987,13.263999891281127,242.25198829483986,13.263999891281127C241.67598829483987,13.263999891281127,241.13598829483985,13.251999891281129,240.64398829483986,13.251999891281129L240.87198829483987,14.067999891281127L242.59998829483987,14.067999891281127C243.22398829483987,14.067999891281127,243.54798829483985,13.683999891281127,243.54798829483985,12.915999891281128L243.54798829483985,9.075999891281128L245.99598829483986,9.075999891281128L245.99598829483986,8.235999891281129L243.54798829483985,8.235999891281129L243.54798829483985,6.987999891281127C244.26798829483985,6.2679998912811286,244.92798829483985,5.487999891281127,245.50398829483987,4.659999891281128L245.50398829483987,3.819999891281128L240.30798829483984,3.819999891281128L240.30798829483984,4.659999891281128L244.51998829483986,4.659999891281128C243.93198829483987,5.475999891281128,243.31998829483985,6.159999891281128,242.67198829483985,6.723999891281128L242.67198829483985,8.235999891281129L239.95998829483986,8.235999891281129Z',
        fill: THEME.colors.primary,
      },
      {
        d: 'M204.71999513077736,25.26400065422058C203.74799513077735,25.26400065422058,202.97999513077735,25.70800065422058,202.41599613077736,26.608000654220582C201.87600313077735,27.460000654220583,201.61199513077736,28.576000654220582,201.61199513077736,29.94400065422058C201.61199513077736,31.25200065422058,201.85200013077736,32.272000654220584,202.35599813077735,33.00400065422058C202.88399513077735,33.77200065422058,203.66399513077735,34.16800065422058,204.70798513077736,34.16800065422058C205.54799513077737,34.16800065422058,206.23199513077736,33.88000065422058,206.78399513077736,33.32800065422058C207.32400513077735,32.77600065422058,207.60000513077736,32.06800065422058,207.60000513077736,31.22800065422058C207.60000513077736,30.38800065422058,207.34800513077735,29.71600065422058,206.85599513077736,29.18800065422058C206.35200513077737,28.660000654220582,205.69199513077737,28.396000654220583,204.87600513077737,28.396000654220583C204.37198513077735,28.396000654220583,203.92798513077736,28.51600065422058,203.54400513077735,28.768000654220582C203.13599513077736,29.00800065422058,202.83599513077735,29.35600065422058,202.61999513077737,29.800000654220582L202.57200213077735,29.800000654220582C202.55999313077737,29.64400065422058,202.55999313077737,29.53600065422058,202.55999313077737,29.48800065422058C202.55999313077737,28.51600065422058,202.74000513077735,27.71200065422058,203.12398513077736,27.088000654220583C203.50799513077735,26.416000654220582,204.03599513077737,26.09199965422058,204.70798513077736,26.09199965422058C205.70399513077737,26.09199965422058,206.29200513077737,26.57200065422058,206.48398513077737,27.556000654220583L207.45598513077735,27.556000654220583C207.21599513077737,26.020001654220582,206.30399513077737,25.26400065422058,204.71999513077736,25.26400065422058ZM204.68400513077736,29.21200065422058C205.27199513077736,29.21200065422058,205.74000513077735,29.39200065422058,206.10000513077736,29.77600065422058C206.44799513077737,30.13600065422058,206.62800513077735,30.61600065422058,206.62800513077735,31.22800065422058C206.62800513077735,31.84000065422058,206.44799513077737,32.34400065422058,206.08799513077736,32.75200065422058C205.72799513077734,33.13600065422058,205.25998513077735,33.34000065422058,204.68400513077736,33.34000065422058C204.08399513077737,33.34000065422058,203.61599513077735,33.13600065422058,203.25599513077736,32.72800065422058C202.89599513077735,32.34400065422058,202.72799513077737,31.852000654220582,202.72799513077737,31.25200065422058C202.72799513077737,30.65200065422058,202.90800513077735,30.172000654220582,203.27999513077737,29.788000654220582C203.65198513077735,29.40400065422058,204.11999513077737,29.21200065422058,204.68400513077736,29.21200065422058ZM211.79999513077735,25.26400065422058C210.76800513077737,25.26400065422058,209.99999513077736,25.70800065422058,209.48398513077737,26.620000654220583C209.02799513077736,27.38800065422058,208.79999513077735,28.42000065422058,208.79999513077735,29.71600065422058C208.79999513077735,31.012000654220582,209.02799513077736,32.04400065422058,209.48398513077737,32.81200065422058C209.99999513077736,33.71200065422058,210.76800513077737,34.16800065422058,211.79999513077735,34.16800065422058C212.81999513077736,34.16800065422058,213.58799513077736,33.71200065422058,214.11599513077735,32.81200065422058C214.57199513077737,32.04400065422058,214.79999513077735,31.012000654220582,214.79999513077735,29.71600065422058C214.79999513077735,28.42000065422058,214.57199513077737,27.38800065422058,214.11599513077735,26.620000654220583C213.58799513077736,25.70800065422058,212.81999513077736,25.26400065422058,211.79999513077735,25.26400065422058ZM211.79999513077735,26.10400065422058C212.56799513077735,26.10400065422058,213.11999513077737,26.512000654220582,213.45599513077735,27.34000065422058C213.69599513077736,27.916000654220582,213.82799513077737,28.70800065422058,213.82799513077737,29.71600065422058C213.82799513077737,30.71200065422058,213.69599513077736,31.504000654220583,213.45599513077735,32.092000654220584C213.11999513077737,32.90800065422058,212.56799513077735,33.32800065422058,211.79999513077735,33.32800065422058C211.03199513077735,33.32800065422058,210.47999513077735,32.90800065422058,210.14399513077737,32.092000654220584C209.90400513077736,31.504000654220583,209.78399513077736,30.71200065422058,209.78399513077736,29.71600065422058C209.78399513077736,28.70800065422058,209.90400513077736,27.916000654220582,210.14399513077737,27.34000065422058C210.47999513077735,26.512000654220582,211.03199513077735,26.10400065422058,211.79999513077735,26.10400065422058Z',
        fill: THEME.colors.secondary,
      },
    ],
  },
  // 4
  {
    paths: [
      {
        d: 'M314.0759824549961,7.3840001125335695L314.0759824549961,14.20000011253357L314.9400024549961,14.20000011253357L314.9400024549961,6.064000112533569C315.3720024549961,5.284000112533569,315.7439824549961,4.4560001125335695,316.0320124549961,3.592000112533569L315.25201245499613,3.2320001125335693C314.6639924549961,5.080000112533569,313.7279924549961,6.724000112533569,312.4320024549961,8.16400011253357L312.6839864549961,9.028000112533569C313.18798345499613,8.51200011253357,313.6560024549961,7.960000112533569,314.0759824549961,7.3840001125335695ZM315.8039824549961,6.124000112533569L315.8039824549961,6.9640001125335695L318.2279924549961,6.9640001125335695C317.9639824549961,9.94000011253357,317.0280124549961,12.14800011253357,315.4200124549961,13.61200011253357L316.0559924549961,14.16400011253357C317.7839924549961,12.56800011253357,318.7919924549961,10.16800011253357,319.0559924549961,6.9640001125335695L319.8120024549961,6.9640001125335695L319.8120024549961,13.012000112533569C319.8120024549961,13.672000112533569,320.1239924549961,14.00800011253357,320.7600024549961,14.00800011253357L322.24798245499613,14.00800011253357C322.5600024549961,13.996000112533569,322.8120024549961,13.87600011253357,322.9800024549961,13.66000011253357C323.1480024549961,13.396000112533569,323.2800024549961,12.61600011253357,323.3640024549961,11.34400011253357L322.5840024549961,11.092000112533569C322.5480024549961,12.19600011253357,322.4760024549961,12.832000112533569,322.3680024549961,13.012000112533569C322.2720024549961,13.14400011253357,322.1639924549961,13.21600011253357,322.0320124549961,13.21600011253357L320.9880024549961,13.21600011253357C320.74798245499613,13.21600011253357,320.6400124549961,13.09600011253357,320.6400124549961,12.85600011253357L320.6400124549961,6.9640001125335695L323.1360024549961,6.9640001125335695L323.1360024549961,6.124000112533569L319.10400245499613,6.124000112533569C319.1159924549961,5.8840001125335695,319.12798245499613,5.644000112533569,319.12798245499613,5.40400011253357L319.12798245499613,3.2320001125335693L318.2999824549961,3.2320001125335693L318.2999824549961,5.40400011253357C318.2999824549961,5.644000112533569,318.28799245499613,5.8840001125335695,318.2759924549961,6.124000112533569L315.8039824549961,6.124000112533569ZM320.7000124549961,3.4480001125335695L320.1359824549961,3.9880001125335696C320.9280024549961,4.55200011253357,321.5400024549961,5.09200011253357,321.99600245499613,5.608000112533569L322.5840024549961,5.02000011253357C322.0799824549961,4.492000112533569,321.44399245499613,3.9640001125335695,320.7000124549961,3.4480001125335695ZM330.7800024549961,6.3760001125335695C331.6080024549961,7.48000011253357,333.0000024549961,8.380000112533569,334.9560024549961,9.06400011253357L335.3400024549961,8.272000112533568C333.7080024549961,7.816000112533569,332.47200245499613,7.18000011253357,331.6560024549961,6.3760001125335695L335.0160024549961,6.3760001125335695L335.0160024549961,5.572000112533569L330.5040024549961,5.572000112533569L330.5040024549961,4.48000011253357C331.8120024549961,4.3600001125335694,332.9640024549961,4.204000112533569,333.97200245499613,3.9880001125335696L333.5520024549961,3.2560000125335695C331.5000024549961,3.6760001125335693,328.9200024549961,3.8920001125335695,325.8120024549961,3.9040001125335695L326.0640024549961,4.636000112533569C327.3360024549961,4.636000112533569,328.5120024549961,4.60000011253357,329.61600245499613,4.540000112533569L329.61600245499613,5.572000112533569L324.9120024549961,5.572000112533569L324.9120024549961,6.3760001125335695L328.4520024549961,6.3760001125335695C327.7560024549961,7.096000112533569,326.50800245499613,7.756000112533569,324.6960024549961,8.332000112533569L325.1880024549961,9.124000112533569C327.2040024549961,8.356000112533568,328.5840024549961,7.444000112533569,329.3280024549961,6.3760001125335695L329.61600245499613,6.3760001125335695L329.61600245499613,8.476000112533569L330.5040024549961,8.476000112533569L330.5040024549961,6.3760001125335695L330.7800024549961,6.3760001125335695ZM328.2360024549961,9.82000011253357C328.06800245499613,11.50000011253357,326.9400024549961,12.70000011253357,324.8520024549961,13.42000011253357L325.3200024549961,14.17600011253357C327.6240024549961,13.26400011253357,328.8720024549961,11.81200011253357,329.0400024549961,9.82000011253357L331.7160024549961,9.82000011253357L331.20000245499614,11.428000112533569L333.5760024549961,11.428000112533569C333.4560024549961,12.412000112533569,333.2880024549961,13.00000011253357,333.0480024549961,13.18000011253357C332.9280024549961,13.27600011253357,332.7480024549961,13.336000112533569,332.50800245499613,13.336000112533569C332.0280024549961,13.336000112533569,331.4640024549961,13.32400011253357,330.8280024549961,13.31200011253357L331.06800245499613,14.11600011253357C331.9200024549961,14.10400011253357,332.56800245499613,14.08000011253357,333.0120024549961,14.044000112533569C333.3600024549961,13.996000112533569,333.6240024549961,13.864000112533569,333.8040024549961,13.63600011253357C334.11600245499613,13.32400011253357,334.3320024549961,12.448000112533569,334.4280024549961,11.02000011253357L334.4280024549961,10.624000112533569L332.2680024549961,10.624000112533569L332.67600245499614,9.42400011253357L332.67600245499614,9.016000112533568L326.54400245499613,9.016000112533568L326.54400245499613,9.82000011253357L328.2360024549961,9.82000011253357Z',
        fill: THEME.colors.primary,
      },
      {
        d: 'M319.547995140543,25.431998700546266C319.260005140543,25.756000700546263,318.924005140543,26.044000700546263,318.51598514054297,26.307999700546265C318.107997140543,26.547999700546264,317.700008140543,26.715999700546263,317.316005140543,26.823999700546263L317.316005140543,27.807999700546265C318.119991140543,27.567999700546263,318.791985140543,27.183999700546266,319.331995140543,26.631999700546263L319.331995140543,33.99999970054627L320.316005140543,33.99999970054627L320.316005140543,25.431998700546266L319.547995140543,25.431998700546266ZM325.200005140543,25.263999700546265C324.167995140543,25.263999700546265,323.399985140543,25.707999700546264,322.883995140543,26.619999700546266C322.428005140543,27.387999700546263,322.200005140543,28.419999700546263,322.200005140543,29.715999700546263C322.200005140543,31.011999700546266,322.428005140543,32.043999700546266,322.883995140543,32.81199970054627C323.399985140543,33.711999700546265,324.167995140543,34.16799970054626,325.200005140543,34.16799970054626C326.219995140543,34.16799970054626,326.988005140543,33.711999700546265,327.516005140543,32.81199970054627C327.972005140543,32.043999700546266,328.200005140543,31.011999700546266,328.200005140543,29.715999700546263C328.200005140543,28.419999700546263,327.972005140543,27.387999700546263,327.516005140543,26.619999700546266C326.988005140543,25.707999700546264,326.219995140543,25.263999700546265,325.200005140543,25.263999700546265ZM325.200005140543,26.103999700546265C325.96798514054296,26.103999700546265,326.519985140543,26.511999700546266,326.855985140543,27.339999700546265C327.09600514054296,27.915999700546266,327.227995140543,28.707999700546264,327.227995140543,29.715999700546263C327.227995140543,30.711999700546265,327.09600514054296,31.503999700546267,326.855985140543,32.09199970054627C326.519985140543,32.90799970054626,325.96798514054296,33.327999700546265,325.200005140543,33.327999700546265C324.43200514054297,33.327999700546265,323.880005140543,32.90799970054626,323.544005140543,32.09199970054627C323.303985140543,31.503999700546267,323.183985140543,30.711999700546265,323.183985140543,29.715999700546263C323.183985140543,28.707999700546264,323.303985140543,27.915999700546266,323.544005140543,27.339999700546265C323.880005140543,26.511999700546266,324.43200514054297,26.103999700546265,325.200005140543,26.103999700546265ZM332.400005140543,25.263999700546265C331.368005140543,25.263999700546265,330.600005140543,25.707999700546264,330.084005140543,26.619999700546266C329.628005140543,27.387999700546263,329.400005140543,28.419999700546263,329.400005140543,29.715999700546263C329.400005140543,31.011999700546266,329.628005140543,32.043999700546266,330.084005140543,32.81199970054627C330.600005140543,33.711999700546265,331.368005140543,34.16799970054626,332.400005140543,34.16799970054626C333.42000514054297,34.16799970054626,334.188005140543,33.711999700546265,334.71600514054296,32.81199970054627C335.172005140543,32.043999700546266,335.400005140543,31.011999700546266,335.400005140543,29.715999700546263C335.400005140543,28.419999700546263,335.172005140543,27.387999700546263,334.71600514054296,26.619999700546266C334.188005140543,25.707999700546264,333.42000514054297,25.263999700546265,332.400005140543,25.263999700546265ZM332.400005140543,26.103999700546265C333.16800514054296,26.103999700546265,333.720005140543,26.511999700546266,334.056005140543,27.339999700546265C334.296005140543,27.915999700546266,334.428005140543,28.707999700546264,334.428005140543,29.715999700546263C334.428005140543,30.711999700546265,334.296005140543,31.503999700546267,334.056005140543,32.09199970054627C333.720005140543,32.90799970054626,333.16800514054296,33.327999700546265,332.400005140543,33.327999700546265C331.63200514054296,33.327999700546265,331.080005140543,32.90799970054626,330.744005140543,32.09199970054627C330.504005140543,31.503999700546267,330.38400514054297,30.711999700546265,330.38400514054297,29.715999700546263C330.38400514054297,28.707999700546264,330.504005140543,27.915999700546266,330.744005140543,27.339999700546265C331.080005140543,26.511999700546266,331.63200514054296,26.103999700546265,332.400005140543,26.103999700546265Z',
        fill: THEME.colors.secondary,
      },
      {
        d: 'M272.2000095253086,25.26400065422058C271.29997952530863,25.26400065422058,270.6039995253086,25.49200165422058,270.1000015253086,25.95999965422058C269.6319845253086,26.38000065422058,269.4039875253086,26.90800065422058,269.4039875253086,27.53200065422058C269.4039875253086,27.98800065422058,269.5119895253086,28.37200065422058,269.7279925253086,28.684000654220583C269.9559895253086,29.02000065422058,270.2919895253086,29.27200065422058,270.7479795253086,29.428000654220583L270.7479795253086,29.45200065422058C270.2919895253086,29.56000065422058,269.9200095253086,29.812000654220583,269.60799752530863,30.196000654220583C269.2719985253086,30.60400065422058,269.1039995253086,31.08400065422058,269.1039995253086,31.63600065422058C269.1039995253086,32.39200065422058,269.3680075253086,33.00400065422058,269.8959915253086,33.46000065422058C270.4359995253086,33.92800065422058,271.2040095253086,34.16800065422058,272.2000095253086,34.16800065422058C273.1839895253086,34.16800065422058,273.9519895253086,33.92800065422058,274.5039895253086,33.46000065422058C275.0320095253086,33.00400065422058,275.2959895253086,32.39200065422058,275.2959895253086,31.63600065422058C275.2959895253086,31.08400065422058,275.12798952530864,30.60400065422058,274.7919895253086,30.196000654220583C274.4800095253086,29.812000654220583,274.0959995253086,29.56000065422058,273.6519995253086,29.45200065422058L273.6519995253086,29.428000654220583C274.0959995253086,29.27200065422058,274.4319995253086,29.02000065422058,274.6719895253086,28.684000654220583C274.8879995253086,28.37200065422058,274.9959995253086,27.98800065422058,274.9959995253086,27.53200065422058C274.9959995253086,26.90800065422058,274.7560095253086,26.38000065422058,274.29997952530863,25.95999965422058C273.7959895253086,25.49200165422058,273.0880095253086,25.26400065422058,272.2000095253086,25.26400065422058ZM272.2000095253086,26.06800165422058C272.79997952530863,26.06800165422058,273.2799895253086,26.22400165422058,273.62798952530864,26.53600065422058C273.9159795253086,26.800000654220582,274.0719895253086,27.14800065422058,274.0719895253086,27.56800065422058C274.0719895253086,28.00000065422058,273.9399995253086,28.36000065422058,273.67598952530864,28.62400065422058C273.3519895253086,28.93600065422058,272.85997952530863,29.10400065422058,272.2000095253086,29.10400065422058C271.5280095253086,29.10400065422058,271.0360095253086,28.93600065422058,270.7239995253086,28.62400065422058C270.45998952530863,28.36000065422058,270.3279995253086,28.00000065422058,270.3279995253086,27.56800065422058C270.3279995253086,27.14800065422058,270.4719795253086,26.800000654220582,270.7719995253086,26.53600065422058C271.1079995253086,26.22400165422058,271.5880095253086,26.06800165422058,272.2000095253086,26.06800165422058ZM272.2000095253086,29.87200065422058C272.8839995253086,29.87200065422058,273.42400952530863,30.04000065422058,273.8080095253086,30.38800065422058C274.1440095253086,30.70000065422058,274.32399952530864,31.120000654220583,274.32399952530864,31.63600065422058C274.32399952530864,32.14000065422058,274.1440095253086,32.54800065422058,273.8080095253086,32.860000654220585C273.42400952530863,33.19600065422058,272.8959895253086,33.36400065422058,272.2000095253086,33.36400065422058C271.5039895253086,33.36400065422058,270.9639895253086,33.19600065422058,270.6039995253086,32.87200065422058C270.2439795253086,32.56000065422058,270.0759845253086,32.14000065422058,270.0759845253086,31.63600065422058C270.0759845253086,31.120000654220583,270.2439795253086,30.70000065422058,270.6039995253086,30.38800065422058C270.9760095253086,30.04000065422058,271.5039895253086,29.87200065422058,272.2000095253086,29.87200065422058ZM279.3999995253086,25.26400065422058C278.3680095253086,25.26400065422058,277.5999995253086,25.70800065422058,277.0840095253086,26.620000654220583C276.62798952530864,27.38800065422058,276.3999895253086,28.42000065422058,276.3999895253086,29.71600065422058C276.3999895253086,31.012000654220582,276.62798952530864,32.04400065422058,277.0840095253086,32.81200065422058C277.5999995253086,33.71200065422058,278.3680095253086,34.16800065422058,279.3999995253086,34.16800065422058C280.4199995253086,34.16800065422058,281.1879995253086,33.71200065422058,281.71599952530863,32.81200065422058C282.1719995253086,32.04400065422058,282.3999995253086,31.012000654220582,282.3999995253086,29.71600065422058C282.3999995253086,28.42000065422058,282.1719995253086,27.38800065422058,281.71599952530863,26.620000654220583C281.1879995253086,25.70800065422058,280.4199995253086,25.26400065422058,279.3999995253086,25.26400065422058ZM279.3999995253086,26.10400065422058C280.16799952530863,26.10400065422058,280.7199995253086,26.512000654220582,281.0559995253086,27.34000065422058C281.2959995253086,27.916000654220582,281.4279995253086,28.70800065422058,281.4279995253086,29.71600065422058C281.4279995253086,30.71200065422058,281.2959995253086,31.504000654220583,281.0559995253086,32.092000654220584C280.7199995253086,32.90800065422058,280.16799952530863,33.32800065422058,279.3999995253086,33.32800065422058C278.6319795253086,33.32800065422058,278.0799795253086,32.90800065422058,277.7439795253086,32.092000654220584C277.5039895253086,31.504000654220583,277.3839995253086,30.71200065422058,277.3839995253086,29.71600065422058C277.3839995253086,28.70800065422058,277.5039895253086,27.916000654220582,277.7439795253086,27.34000065422058C278.0799795253086,26.512000654220582,278.6319795253086,26.10400065422058,279.3999995253086,26.10400065422058Z',
        fill: THEME.colors.secondary,
      },
    ],
  },
];

// 分数区间配置 - 使用统一配置
const SCORE_RANGES = SCORE_TO_TECH_RISK_MAPPING.map((item, index) => ({
  min: item.min,
  max: item.max,
  step: index,
  color: TECH_RISK_COLORS[item.level],
}));

/**
 * 根据分数获取对应的风险配置
 * @param score 分数 0-100
 * @returns 风险配置 { color: string, step: number | undefined }
 */
const getScoreConfig = (score: number): { color: string; step: number | undefined } => {
  const range = SCORE_RANGES.find((range) => score >= range.min && score <= range.max);

  if (range) {
    return {
      color: range.color,
      step: range.step,
    };
  }

  // 默认配置
  return {
    color: THEME.colors.risk.low2,
    step: undefined,
  };
};

// 通用渲染函数
const RenderHelpers = {
  /**
   * 创建路径元素
   */
  createPath: (path: { d: string; fill: string }) => (
    <path d={path.d} fill={path.fill} fill-opacity={THEME.styles.fillOpacity} style={THEME.styles.mixBlendMode} />
  ),

  /**
   * 创建带透明度的组元素
   */
  createGroupWithOpacity: (opacity: number, children: any = null) => <g style={`opacity:${opacity};`}>{children}</g>,

  /**
   * 创建矩形元素
   */
  createRect: (props: {
    x: number;
    y: number;
    width: number;
    height: number;
    rx?: number;
    fill: string;
    opacity?: number;
    stroke?: string;
    strokeWidth?: number;
  }) => (
    <rect
      x={props.x}
      y={props.y}
      width={props.width}
      height={props.height}
      rx={props.rx || 0}
      fill={props.fill}
      fill-opacity={props.opacity || THEME.styles.fillOpacity}
      stroke={props.stroke}
      stroke-width={props.strokeWidth}
      style={THEME.styles.mixBlendMode}
    />
  ),

  /**
   * 创建线性渐变
   */
  createLinearGradient: (id: string, color: string, opacity: number) => (
    <linearGradient key={id} x1="0.5" y1="0" x2="0.5" y2="1" id={id}>
      <stop offset="0%" stop-color={color} stop-opacity={THEME.opacity.none} />
      <stop offset={CONFIG.shadow.gradientOffset} stop-color={color} stop-opacity={opacity} />
    </linearGradient>
  ),
};

// 阴影组件
const ShadowsComponent = defineComponent({
  functional: true,
  props: {
    step: {
      type: Number,
      required: false,
    },
  },
  render(h, { props }) {
    // 生成渐变定义
    const gradients = CONFIG.shadow.rectConfig.map((rect, index) =>
      RenderHelpers.createLinearGradient(`tech-level-bar-${index}`, rect.color, CONFIG.shadow.gradientOpacity)
    );

    // 生成矩形
    const rectangles = CONFIG.shadow.rectConfig.map((rect, index) => {
      const opacity = props.step === index ? THEME.opacity.full : THEME.opacity.half;
      return RenderHelpers.createGroupWithOpacity(
        opacity,
        <g key={`rectangle-${index}`}>
          {RenderHelpers.createRect({
            x: rect.x,
            y: CONFIG.shadow.rectCommon.y,
            width: rect.width,
            height: rect.height,
            rx: CONFIG.shadow.rectCommon.rx,
            fill: `url(#tech-level-bar-${index})`,
            opacity: THEME.opacity.full,
          })}
        </g>
      );
    });

    return (
      <g>
        <defs>{gradients}</defs>
        {rectangles}
      </g>
    );
  },
});

// 条形图组件
const BarsComponent = defineComponent({
  functional: true,
  props: {
    step: {
      type: Number,
      required: false,
    },
  },
  render(h, { props }) {
    const barPositions = CONFIG.bar.generatePositions();

    // 生成filter定义
    const createBarFilter = (filterId: string) => (
      <filter
        key={filterId}
        id={filterId}
        filterUnits="objectBoundingBox"
        color-interpolation-filters="sRGB"
        x="0"
        y="0"
        width="1"
        height="1"
      >
        <feFlood flood-opacity={THEME.opacity.none} result="BackgroundImageFix" />
        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
        <feColorMatrix in="SourceAlpha" type="matrix" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
        <feOffset dy="0" dx="-2" />
        <feGaussianBlur stdDeviation="0" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0" />
        <feBlend mode="normal" in2="shape" result="effect1_innerShadow" />
      </filter>
    );

    const filters = barPositions.flatMap((positions, rIndex) => positions.map((_, index) => createBarFilter(`svg-bar-${rIndex}-${index}`)));

    // 创建圆角路径
    const createRoundedPath = (position: any) => {
      const { x, width, roundSide } = position;
      const y = CONFIG.bar.rectCommon.y;
      const height = CONFIG.bar.rectCommon.height;

      if (roundSide === 'left') {
        return `M0,${y + 5}C0,${y + 2.23858},2.23858,${y},5,${y}L${width},${y}L${width},${y + height}L5,${y + height}C2.23858,${y + height},0,${y + height - 2.23858},0,${y + 5}Z`;
      }
      const offsetX = 2.3;
      return `M${x},${y}L${x + width - 5 + offsetX},${y}C${x + width - 2.23858 + offsetX},${y},${x + width + offsetX},${y + 2.23858},${x + width + offsetX},${y + 5}C${x + width + offsetX},${y + height - 2.23858},${x + width - 2.23858 + offsetX},${y + height},${x + width - 5 + offsetX},${y + height}L${x},${y + height}L${x},${y}Z`;
    };

    // 生成条形图
    const bars = barPositions.flatMap((positions, rIndex) => {
      return positions.map((position, index) => {
        const filterId = `svg-bar-${rIndex}-${index}`;
        const opacity = props.step === rIndex ? THEME.opacity.full : THEME.opacity.light;

        const barElement = position.isRounded ? (
          <path
            d={createRoundedPath(position)}
            fill={CONFIG.bar.colors[rIndex]}
            fill-opacity={THEME.styles.fillOpacity}
            style={THEME.styles.mixBlendMode}
          />
        ) : (
          RenderHelpers.createRect({
            x: position.x,
            y: CONFIG.bar.rectCommon.y,
            width: position.width,
            height: CONFIG.bar.rectCommon.height,
            fill: CONFIG.bar.colors[rIndex],
            opacity: THEME.styles.fillOpacity,
          })
        );

        return (
          <g key={filterId} style={`opacity:${opacity};`} filter={`url(#${filterId})`}>
            <g>{barElement}</g>
          </g>
        );
      });
    });

    // 生成指示器
    const indicators = CONFIG.indicator.positions.map((xPos, index) => (
      <g key={index}>
        <g>
          {RenderHelpers.createRect({
            x: xPos,
            y: CONFIG.indicator.rectCommon.y,
            width: CONFIG.indicator.rectCommon.width,
            height: CONFIG.indicator.rectCommon.height,
            rx: CONFIG.indicator.rectCommon.rx,
            fill: CONFIG.indicator.colors[index],
            opacity: CONFIG.indicator.opacity,
          })}
        </g>
        <g>
          <rect
            x={xPos + 0.5}
            y={CONFIG.indicator.rectCommon.y + 0.5}
            width={CONFIG.indicator.rectCommon.width - 1}
            height={CONFIG.indicator.rectCommon.height - 1}
            rx={CONFIG.indicator.rectCommon.rx - 0.5}
            fill-opacity={THEME.opacity.none}
            stroke-opacity={THEME.styles.fillOpacity}
            stroke={CONFIG.indicator.rectCommon.strokeColor}
            fill="none"
            stroke-width={CONFIG.indicator.rectCommon.strokeWidth}
            style={THEME.styles.mixBlendMode}
          />
        </g>
      </g>
    ));

    return (
      <g>
        <defs>{filters}</defs>
        {bars}
        {indicators}
      </g>
    );
  },
});

// 指示器标签组件
const IndicatorsComponent = defineComponent({
  functional: true,
  props: {
    step: {
      type: Number,
      required: false,
    },
  },
  render(h, { props }) {
    return (
      <g>
        {INDICATOR_LABELS.map((paths, index: number) => {
          const key = `indicator-level-label-${index}`;
          return RenderHelpers.createGroupWithOpacity(
            THEME.opacity.full,
            <g key={key}>
              {paths.paths.map((path, pathIndex: number) => {
                const pathKey = `indicator-level-label-path-${index}-${pathIndex}`;
                const fill = props.step === index && pathIndex === 0 ? THEME.colors.highLight : path.fill;
                return (
                  <g key={pathKey}>
                    {RenderHelpers.createPath({
                      d: path.d,
                      fill,
                    })}
                  </g>
                );
              })}
            </g>
          );
        })}
      </g>
    );
  },
});

// 分数指针组件
const ScoreMarkerComponent = defineComponent({
  props: {
    score: {
      type: Number,
      required: true,
    },
    color: {
      type: String,
      required: true,
    },
  },
  render() {
    return (
      <div class={styles.marker} style={{ left: `${this.score}%` }}>
        <i class={styles.pointer} style={{ color: this.color }}></i>
        <span class={styles.label} style={{ background: this.color }}>
          {this.score}
        </span>
      </div>
    );
  },
});

const DiligenceRiskLevelBarTech = defineComponent({
  name: 'DiligenceRiskLevelBarTech',
  props: {
    score: {
      type: Number,
      default: -1,
    },
    // TODO: 使用 mapping来动态控制区间范围
  },
  render() {
    const { step, color: scoreColor } = getScoreConfig(this.score);
    const isValidScore = this.score >= 0;

    return (
      <div class={styles.container}>
        {/* 分数指针 */}
        {isValidScore && <ScoreMarkerComponent score={this.score} color={scoreColor} />}

        {/* SVG 标尺 */}
        <svg width={CONFIG.svg.width} height={CONFIG.svg.height} viewBox={CONFIG.svg.viewBox}>
          <g>
            <BarsComponent step={step} />
            <IndicatorsComponent step={step} />
            <ShadowsComponent step={step} />
          </g>
        </svg>
      </div>
    );
  },
});

export default DiligenceRiskLevelBarTech;
