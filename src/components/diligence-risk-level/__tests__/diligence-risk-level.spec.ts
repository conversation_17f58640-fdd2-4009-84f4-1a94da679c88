import { shallowMount } from '@vue/test-utils';

import DiligenceRiskLevel from '..';

describe('DiligenceRiskLevel', () => {
  test('render', () => {
    const wrapper = shallowMount(DiligenceRiskLevel, {
      propsData: {
        level: -1,
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('props: level', () => {
    const wrapper = shallowMount(DiligenceRiskLevel, {
      propsData: {
        level: -1,
      },
    });
    expect(wrapper.text()).toMatch('低风险');
  });

  test('props: mapping', () => {
    const wrapper = shallowMount(DiligenceRiskLevel, {
      propsData: {
        level: 2,
        mapping: {
          2: {
            scheme: ['#00AD65', '#E3E3E3', '#D8D8D8'],
            color: '#00AD65',
            text: '测试风险',
          },
        },
      },
    });
    expect(wrapper.text()).toMatch('测试风险');
  });
});
