import { PropType, defineComponent } from 'vue';

import styles from './diligence-risk-level.module.less';

type RiskLevel = -2 | -1 | 0 | 1 | 2;

const DiligenceRiskLevel = defineComponent({
  functional: true,
  props: {
    level: {
      type: Number,
      default: -2,
    },
    mapping: {
      type: Object as PropType<{ [key in RiskLevel]: { color: string; text: string; scheme: string[] } }>,
      default: () => ({
        '-2': {
          scheme: ['#00AD65', '#E3E3E3', '#D8D8D8'],
          color: '#00AD65',
          text: '低风险',
        },
        '-1': {
          scheme: ['#00AD65', '#E3E3E3', '#D8D8D8'],
          color: '#00AD65',
          text: '低风险',
        },
        '0': {
          scheme: ['#00AD65', '#E3E3E3', '#D8D8D8'],
          color: '#00AD65',
          text: '低风险',
        },
        '1': {
          scheme: ['#E3E3E3', '#FFAA00', '#D8D8D8'],
          color: '#FFAA00',
          text: '中风险',
        },
        '2': {
          scheme: ['#E3E3E3', '#D8D8D8', '#FF6060'],
          color: '#FF6060',
          text: '高风险',
        },
      }),
    },
  },
  render(h, { props, slots }) {
    const { color, text } = props.mapping[`${props.level}`];
    const { default: defaultSlot } = slots();
    const { level } = props;
    const imgUrl = new URL(`./imgs/${level}.svg`, import.meta.url);
    return (
      <div class={styles.container}>
        <img src={imgUrl.href} width={151} />
        <div
          class={styles.text}
          style={{
            color,
          }}
        >
          {defaultSlot || text}
        </div>
        {/* <div class={[styles.result]}>{riskLevelDesc}</div> */}
      </div>
    );
  },
});

export default DiligenceRiskLevel;
