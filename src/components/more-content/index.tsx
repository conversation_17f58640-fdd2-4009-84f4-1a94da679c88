import { Ref, computed, defineComponent, onMounted, onUpdated, ref, set, unref } from 'vue';
import { useMutationObserver, useResizeObserver } from '@vueuse/core';
import { throttle } from 'lodash';

import styles from './styles.module.less';
import QIcon from '../global/q-icon';

const ClampContentToggleMap = ref({});

const MoreContent = defineComponent({
  name: 'MoreContent',
  props: {
    line: {
      type: Number,
      default: 5,
    },
    marginTop: {
      type: Number,
      default: 0,
    },
    // 必须传当前tr的唯一标志
    clampKey: {
      type: String,
      default: new Date().getTime().toString(), // tofix: 未指定clamkey的时候会采用defalut会导致组件的clampKey 重复
    },
    // 是否点击全部地方都能展开
    openAreaBig: {
      type: Boolean,
      default: false,
    },
    defaultText: {
      type: String,
      default: '更多',
    },
  },
  setup(props, { emit }) {
    const domRef = ref() as Ref<HTMLElement>;

    const openRow = (isMiddle: boolean) => {
      const middleOrTop = isMiddle ? 'middle' : 'top';
      const ele = domRef.value?.parentElement?.parentElement as HTMLElement;
      if (ele) {
        (ele.style as CSSStyleDeclaration).verticalAlign = middleOrTop;
      }
    };

    const useLineCamp = (element: Ref<HTMLElement>, { line, key }) => {
      if (!key) {
        key = Math.random().toString(36).slice(2);
      }
      const scrollHeight = ref(0);
      const maxHeight = ref(0);

      // 是否溢出
      const isClamp = computed({
        get: () => {
          if (ClampContentToggleMap.value[key] === undefined) {
            set(ClampContentToggleMap.value, key, true);
          }
          return ClampContentToggleMap.value[key];
        },
        set: (val) => {
          set(ClampContentToggleMap.value, key, val);
        },
      });
      // 是否展示溢出文字
      const isShowClamp = computed(() => unref(scrollHeight) > unref(maxHeight));

      const getMaxHeight = () => {
        if (unref(element)) {
          return line * parseInt(window.getComputedStyle(unref(element)).lineHeight, 10) + props.marginTop * (line - 1);
        }
        return 0;
      };

      const setSize = throttle(() => {
        maxHeight.value = getMaxHeight();
        scrollHeight.value = unref(element)?.scrollHeight;
        openRow(unref(isClamp));
      }, 50);

      onUpdated(() => {
        setSize();
      });

      onMounted(() => {
        setSize();
      });

      useResizeObserver(element, () => {
        setTimeout(() => {
          setSize();
        });
      });

      useMutationObserver(
        element,
        () => {
          setSize();
        },
        {
          childList: true,
        }
      );

      return {
        isShowClamp,
        maxHeight,
        isClamp,
      };
    };

    const { maxHeight, isShowClamp, isClamp } = useLineCamp(domRef, {
      line: props.line,
      key: props.clampKey,
    });
    const rootStyle = computed<Partial<CSSStyleDeclaration>>(() => ({
      overflow: 'hidden',
      height: isClamp.value && isShowClamp.value ? `${maxHeight.value}px` : 'auto',
    }));

    const handleToggle = () => {
      isClamp.value = !isClamp.value;
      emit('change', isClamp.value);
      openRow(isClamp.value);
    };

    return {
      domRef,
      handleToggle,
      isShowClamp,
      maxHeight,
      rootStyle,
      isClamp,
    };
  },
  render() {
    const style = {
      position: 'relative',
      ...this.rootStyle,
    } as Record<string, any>;
    return (
      <div class={styles.root} style={style} ref="domRef">
        <div
          onClick={() => {
            // 是否点击整体 是否显示了折叠 是否已经折叠
            if (this.openAreaBig && this.isShowClamp && this.isClamp) {
              this.handleToggle();
            }
          }}
        >
          {this.$slots.default}
        </div>
        <div
          class={[styles.btn, 'clamp-more-btn']}
          onClick={() => {
            this.handleToggle();
          }}
          v-show={this.isShowClamp && this.isClamp}
        >
          {this.isClamp ? this.defaultText : ''}
          <QIcon type="icon-zhankai" />
        </div>
      </div>
    );
  },
});

export default MoreContent;
