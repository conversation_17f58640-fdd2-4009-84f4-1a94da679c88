import { shallowMount } from '@vue/test-utils';

import AssociationPath from '../index';

describe('AssociationPath', () => {
  let $route;
  beforeEach(() => {
    $route = {
      path: '/',
      hash: '',
      query: {},
      params: {},
    };
  });

  test('render', () => {
    const wrapper = shallowMount(AssociationPath, {
      propsData: {
        paths: [
          [
            {
              KeyNo: '9cce0780ab7644008b73bc2120479d31',
              Name: '小米科技有限责任公司',
              Level: '1',
              isReverse: true,
              underStr: '董事',
            },
            { KeyNo: 'p077472cd738bf6c70d757a42c6acaf4', Name: '林斌', Level: '2', underStr: '历史高管' },
            { KeyNo: '2c2a4a5d83f570b42fc9a5b92a0dbe75', Name: '小米之家科技有限公司', Level: '3' },
          ],
        ],
        showTitle: true,
        reverse: false,
      },
      mocks: {
        $route,
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('props: paths 大于10条截断', () => {
    const paths = new Array(11);
    paths.fill([
      {
        KeyNo: '9cce0780ab7644008b73bc2120479d31',
        Name: '小米科技有限责任公司',
        Level: '1',
        isReverse: true,
        underStr: '董事',
      },
      { KeyNo: 'p077472cd738bf6c70d757a42c6acaf4', Name: '林斌', Level: '2', underStr: '董事' },
      { KeyNo: '3d9a2d02379f9ae6d16db49c3472f286', Name: '天星数科科技有限公司', Level: '3' },
    ]);

    const wrapper = shallowMount(AssociationPath, {
      propsData: {
        paths,
        showTitle: false,
        reverse: false,
      },
      mocks: {
        $route,
      },
    });
    expect(wrapper.text()).toContain('*仅显示TOP10路径');
  });
});
