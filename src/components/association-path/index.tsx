import { defineComponent } from 'vue';

import QEntityLink from '@/components/global/q-entity-link';

import styles from './association-path.module.less';

const contentRenderer = (pathList: any[], options?: { showTitle: boolean; showBackground: boolean; forceShowTitle: boolean }) => {
  const contentNode: any = [];
  const tempNode: any = pathList.map((path, idx, source) => {
    const percent = path.arr[path.arr.length - 1].PercentTotal;
    const renderTitle = () => {
      if (percent) {
        return <span>（占比约 {percent}）</span>;
      }
      return '';
    };
    // 是否显示标题: showTitle 为 true，同时有多条链路时才显示
    const isTitleVisible = options?.forceShowTitle || (options?.showTitle && source.length > 1);
    const isShowBackground = options?.showBackground && source.length > 1;

    return (
      <div class={styles.pathLine} key={`path-line_${idx}`}>
        {/* 显示标题 */}
        <div class={styles.title} v-show={isTitleVisible}>
          路径 {idx + 1}
          {renderTitle()}
        </div>

        {/* 渲染路径 */}
        <div class={{ [styles.pathBlock]: isShowBackground }}>
          {path.arr.map((item, index) => (
            <span class={styles.pathItem} key={`path-item_${idx}_${index}`}>
              <QEntityLink coyObj={item} />
              {index !== path.arr.length - 1 && (
                <span class={[styles.percent, item.isReverse ? styles.left : styles.right]} title={item.underStr}>
                  {item.underStr || ''}
                </span>
              )}
            </span>
          ))}
        </div>
      </div>
    );
  });
  contentNode.push(tempNode);

  if (pathList.length >= 10) {
    contentNode.push(<div class="text-gray">*仅显示TOP10路径</div>);
  }
  return contentNode;
};

const AssociationPath = defineComponent({
  functional: true,
  props: {
    /**
     * 路径数组
     */
    paths: {
      type: Array,
      default: () => [],
    },
    /**
     * 路径方向是否取反
     */
    reverse: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否展示控制链路，占比xx字样
     */
    showTitle: {
      type: Boolean,
      default: false,
    },
    /**
     * 只有一条路径时是否展示标题
     */
    forceShowTitle: {
      type: Boolean,
      default: false,
    },
    showBackground: {
      type: Boolean,
      default: false,
    },
  },

  render(h, { props }) {
    if (!props.paths.length) {
      return <div></div>;
    }

    const pathList = props.paths.map((paths: any) => {
      const result = paths.slice();
      if (props.reverse) {
        result.reverse();
      }
      return {
        arr: result,
      };
    });

    return (
      <div class={styles.container}>
        {contentRenderer(pathList, {
          showTitle: props.showTitle,
          showBackground: props.showBackground,
          forceShowTitle: props.forceShowTitle,
        })}
      </div>
    );
  },
});

export default AssociationPath;
