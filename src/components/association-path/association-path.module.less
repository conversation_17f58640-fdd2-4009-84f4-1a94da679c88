.container {
  .path-line {
    line-height: 28px;

    &:not(:last-child) {
      margin-bottom: 6px;
    }

    .name {
      color: #222;
      font-weight: bold;
    }

    a {
      color: #333 !important;

      &:hover {
        color: #128bed !important;
      }
    }

    .title{
      height: 20px;
      line-height: 20px;
      font-weight: 700;
    }

    .path-item {
      .percent {
        display: inline-block;
        height: 26px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 12px;
        color: #128bed;
        text-align: center;
        position: relative;
        margin: 0 4px;
        padding: 0 8px 10px;

        &:empty {
          min-width: 84px;
          height: 0;
        }

        &::before,
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
        }

        &::before {
          height: 1px;
          display: block;
          width: 100%;
          background: #d6d6d6;
        }

        &:not(:empty)::before {
          transform: translateY(10px);
        }

        &::after {
          height: 0;
          width: 0;
          border: 0 solid transparent;
          border-width: 3px 10px;
        }

        &:not(:empty)::after {
          transform: translateY(7.5px);
        }
      }

      .right {
        padding-right: 14px;

        &::before {
          right: 10px;
        }

        &::after {
          right: -10px;
          border-left-color: #d6d6d6;
        }
      }

      .left {
        padding-left: 14px;

        &::before {
          left: 10px;
        }

        &::after {
          left: -10px;
          border-right-color: #d6d6d6;
        }
      }
    }
  }

  .path-block {
    padding: 10px;
    margin-top: 5px;
    background: #f8f8f8;
    border-radius: 4px;
  }
}
