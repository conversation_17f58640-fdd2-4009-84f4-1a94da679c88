import { mount } from '@vue/test-utils';

import { diligence } from '@/shared/services';

import BatchStopButton from '..';

vi.mock('@/shared/services', () => ({
  diligence: {
    discontinue: vi.fn(),
  },
}));

describe('BatchStopButton', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });
  it('当executionNum大于0时，显示中止', () => {
    const wrapper = mount(BatchStopButton, {
      propsData: {
        consumptionNum: 10,
        executionNum: 1,
        batchId: 1,
        statisticsInfo: {
          successCount: 1,
        },
      },
    });
    expect(wrapper.findComponent({ name: 'APopconfirm' }).exists()).toBe(true);
  });

  it('当executionNum为0时，显示取消', () => {
    const wrapper = mount(BatchStopButton, {
      propsData: {
        consumptionNum: 10,
        executionNum: 0,
        batchId: 1,
        statisticsInfo: {
          successCount: 1,
        },
      },
    });
    expect(wrapper.findComponent({ name: 'APopconfirm' }).exists()).toBe(false);
    expect(wrapper.findComponent({ name: 'AButton' }).exists()).toBe(true);
  });

  it('当点击取消按钮时，调用handleCancel方法', async () => {
    const wrapper = mount(BatchStopButton, {
      propsData: {
        consumptionNum: 10,
        executionNum: 0,
        batchId: 1,
        statisticsInfo: {
          successCount: 1,
        },
      },
    });
    const btn = wrapper.findComponent({ name: 'AButton' });
    btn.vm.$emit('click');
    expect(diligence.discontinue).toHaveBeenCalledWith({ batchId: 1 });
  });

  it('当batchId为空时，handleCancel方法不调用discontinue', async () => {
    const wrapper = mount(BatchStopButton, {
      propsData: {
        consumptionNum: 10,
        executionNum: 0,
        batchId: null,
        statisticsInfo: {
          successCount: 1,
        },
      },
    });
    const btn = wrapper.findComponent({ name: 'AButton' });
    btn.vm.$emit('click');
    expect(diligence.discontinue).not.toHaveBeenCalled();
  });
});
