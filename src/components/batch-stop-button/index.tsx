import { Button, Popconfirm } from 'ant-design-vue';
import { computed, defineComponent } from 'vue';

import { diligence } from '@/shared/services';

const BatchStopButton = defineComponent({
  name: 'BatchStopButton',
  props: {
    consumptionNum: {
      type: Number,
      default: 0,
    },
    executionNum: {
      type: Number,
      default: 0,
    },
    batchId: {
      type: Number,
      required: true,
    },
    statisticsInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const hasBatch = computed(() => props.executionNum > 0);

    const handleCancel = () => {
      if (!props.batchId) {
        return;
      }
      diligence.discontinue({
        batchId: props.batchId,
      });
    };
    return {
      hasBatch,
      handleCancel,
    };
  },
  render() {
    if (this.statisticsInfo.successCount === this.statisticsInfo.recordCount) {
      return null;
    }
    if (this.hasBatch) {
      return (
        <Popconfirm
          placement="bottom"
          title={`已累计消耗${this.consumptionNum}个排查额度，您确认中止任务吗？`}
          onConfirm={() => this.handleCancel()}
        >
          <Button type="link">中止</Button>
        </Popconfirm>
      );
    }
    return (
      <Button type="link" onClick={this.handleCancel}>
        取消
      </Button>
    );
  },
});

export default BatchStopButton;
