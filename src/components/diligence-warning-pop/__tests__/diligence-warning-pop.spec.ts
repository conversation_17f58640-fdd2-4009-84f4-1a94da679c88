import { mount } from '@vue/test-utils';
import { ref } from 'vue';
import { useStorage } from '@vueuse/core';

import { diligence } from '@/shared/services';
import { getRiskLevelStyle } from '@/config/risk.config';
import { isValidCompanyType } from '@/utils/company/company-type';

import DiligenceWarningPop from '..';

vi.mock('@vueuse/core');
vi.mock('@/shared/services');
vi.mock('@/config/risk.config');
vi.mock('@/utils/company/company-type');

const mockUseStorage = useStorage as vi.mockedFunction<typeof useStorage>;
const mockScanRiskDetail = diligence.scanRiskDetail as vi.mockedFunction<typeof diligence.scanRiskDetail>;
const mockGetRiskLevelStyle = getRiskLevelStyle as vi.mockedFunction<typeof getRiskLevelStyle>;
const mockIsValidCompanyType = isValidCompanyType as vi.mockedFunction<typeof isValidCompanyType>;

describe('DiligenceWarningPop', () => {
  // 初始化模拟数据
  const mockRowData = {
    companyId: '123',
    name: '测试公司',
    t_type: 'validType',
    orgModel: { resultSetting: [] },
  };

  beforeEach(() => {
    mockUseStorage.mockReturnValue(ref({}));
    mockGetRiskLevelStyle.mockReturnValue({ color: 'red' });
    mockIsValidCompanyType.mockReturnValue(true);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  test('应该渲染Popover当enableInvestigate为true且有diligenceId', async () => {
    const wrapper = mount(DiligenceWarningPop, {
      propsData: {
        score: 1,
        rowData: mockRowData,
        diligenceId: 456,
      },
    });

    expect(wrapper.findComponent({ name: 'APopover' }).exists()).toBe(true);
  });

  test('应该渲染普通标签当没有diligenceId', () => {
    const wrapper = mount(DiligenceWarningPop, {
      propsData: {
        score: 1,
        rowData: mockRowData,
        diligenceId: undefined,
      },
    });

    expect(wrapper.findComponent({ name: 'APopover' }).exists()).toBe(false);
  });

  test('应该不渲染Popover当企业类型无效', () => {
    mockIsValidCompanyType.mockReturnValue(false);

    const wrapper = mount(DiligenceWarningPop, {
      propsData: {
        score: 1,
        rowData: { ...mockRowData, t_type: 'invalidType' },
        diligenceId: 456,
      },
    });

    expect(wrapper.findComponent({ name: 'APopover' }).exists()).toBe(false);
  });

  test('应该显示加载状态当正在请求数据', async () => {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    mockScanRiskDetail.mockImplementation(() => new Promise(() => {}));

    const wrapper = mount(DiligenceWarningPop, {
      propsData: {
        score: 1,
        rowData: mockRowData,
        diligenceId: 456,
      },
    });

    await wrapper.findComponent({ name: 'APopover' }).trigger('mouseenter');
    expect(mockScanRiskDetail).toHaveBeenCalled();
    expect(wrapper.vm.loading).toBe(true);
  });

  test('应该使用缓存数据当存在缓存', async () => {
    const cachedData = { test: 'cached' };
    mockUseStorage.mockReturnValue(ref({ 456: cachedData }));

    const wrapper = mount(DiligenceWarningPop, {
      propsData: {
        score: 1,
        rowData: mockRowData,
        diligenceId: 456,
      },
    });

    await wrapper.findComponent({ name: 'APopover' }).trigger('mouseenter');
    expect(mockScanRiskDetail).not.toHaveBeenCalled();
  });

  test('应该显示"-"当score无效', () => {
    const wrapper = mount(DiligenceWarningPop, {
      propsData: {
        score: undefined,
        rowData: mockRowData,
        diligenceId: 456,
      },
    });

    expect(wrapper.text()).toContain('-');
  });
});
