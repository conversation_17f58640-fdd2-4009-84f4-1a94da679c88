import { computed, defineComponent, onMounted, PropType, ref } from 'vue';
import _, { cloneDeep } from 'lodash';
import formatDate from '@/utils/format/date';

import bgImg from './images/bg.png';
import styles from './recruitmentAnalysis.module.less';
import { BarConfig, getPercentWithPrecision, PieConfig } from './config';

// FIXME: 缺少 图表，产品说不要了
const RecruitmentAnalysis = defineComponent({
  name: 'RecruitmentAnalysis',
  props: {
    viewData: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
  },
  setup(props) {
    const info = ref<Record<string, any>>({ ...props.viewData, descList: [] });
    const chartList = ref([
      {
        name: '薪资分布',
        option: null as any,
        col: 2,
      },
      {
        name: '学历要求',
        option: null,
        col: 2,
      },
      {
        name: '经验要求',
        option: null,
        col: 2,
      },
    ]);
    const jobDetail = ref(props.viewData.jobDetail);

    // 工资分布
    const drawSalChart = (salgroup) => {
      const salData = { xData: [], yData: [] } as any;
      const tempData = [] as any[];

      _.forEach(salgroup, (item) => {
        tempData.push(item);
      });
      if (tempData.length) {
        const sum = getSum(tempData);
        _.forEach(tempData, (x) => {
          if (x as any) {
            salData.xData.push(x.desc);
            salData.yData.push(getPercent(sum, x.count));
          }
        });
      }

      chartList.value[0].name = '薪资分布（平均薪资:￥' + (jobDetail.value?.Summary?.Salaryavg || '-') + '）';
      chartList.value[0].option = BarConfig({
        xData: salData.xData,
        yData: salData.yData,
        tooltip: {
          formatter: '{b}:</br>占比{c}%',
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            width: 38,
            shadowStyle: {
              color: 'rgba(18, 139, 237, 0.05)',
            },
          },
        },
        gridConfig: {
          right: 30,
          left: 40,
          top: 45,
        },
        xAxisConfig: {
          boundaryGap: false,
        },
        seriesConfig: {
          emphasis: {
            itemStyle: {
              color: '#408FFF',
            },
          },
          barWidth: 10,
          itemStyle: {
            barBorderRadius: [5, 5, 0, 0],
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: '#408FFF',
                },
                {
                  offset: 1,
                  color: 'rgba(64, 143, 255, 0.3)',
                },
              ],
            },
          },
        },
      });
    };
    // 学历要求
    const drawEduChart = (data) => {
      const pieData = [] as any[];
      const nameList = [] as any[];
      const valueList = [] as any[];
      data.sort((a, b) => {
        return b.count - a.count;
      });
      for (let i = 0; i < data.length; i++) {
        const obj = {
          value: data[i].count,
          realName: data[i].desc,
          name: data[i].desc,
        };
        nameList.push(data[i].desc);
        pieData.push(obj);
        valueList.push(data[i].count);
      }
      pieData.forEach((item, index) => {
        nameList[index] = {
          name: item.realName,
          value: getPercentWithPrecision(valueList, index, 2),
        };
      });
      const option = PieConfig(
        nameList,
        pieData,
        {
          center: [80, 145],
          radius: ['40', '60'],
        },
        { width: 138, left: 165 }
      );
      chartList.value[1].option = option;
    };
    // 经验要求
    const drawExpChart = (expgroup) => {
      const expData = { xData: [] as any[], yData: [] as any[] };
      const tempData = [...expgroup];

      if (tempData.length) {
        const sum = getSum(tempData);
        _.forEach(tempData, (x) => {
          if (x) {
            expData.xData.push(x.desc);
            expData.yData.push(getPercent(sum, x.count));
          }
        });
      }

      chartList.value[2].option = BarConfig({
        xData: expData.xData,
        yData: expData.yData,
        tooltip: {
          formatter: '{b}:</br>占比{c}%',
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            width: 38,
            shadowStyle: {
              color: 'rgba(18, 139, 237, 0.05)',
            },
          },
        },
        gridConfig: {
          right: 30,
          left: 40,
          top: 50,
        },
        xAxisConfig: {
          boundaryGap: false,
        },
        seriesConfig: {
          emphasis: {
            itemStyle: {
              color: '#408FFF',
            },
          },
          barWidth: 10,
          itemStyle: {
            barBorderRadius: [5, 5, 0, 0],
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: '#408FFF',
                },
                {
                  offset: 1,
                  color: 'rgba(64, 143, 255, 0.3)',
                },
              ],
            },
          },
        },
      });
    };
    // 处理JD
    const handleJD = () => {
      const descList = [] as any[];
      if (info.value?.descriptionResultList?.length) {
        const arr1 = [] as any[];
        const arr2 = [] as any[];
        const arr3 = [] as any[];
        const arr4 = [] as any[];
        _.forEach(info.value.descriptionResultList, (item) => {
          const { type } = item;
          switch (type) {
            case '岗位职责':
              arr1.push(item);
              break;
            case '岗位要求':
              arr2.push(item);
              break;
            case '工作时间':
              arr3.push(item);
              break;
            case '薪酬福利':
              arr4.push(item);
              break;
            default:
              break;
          }
        });
        if (arr1.length > 0) {
          descList.push({
            key: '岗位职责',
            list: arr1.sort((a, b) => {
              return a?.rank - b?.rank;
            }),
          });
        }
        if (arr2.length > 0) {
          descList.push({
            key: '岗位要求',
            list: arr2.sort((a, b) => {
              return a?.rank - b?.rank;
            }),
          });
        }
        if (arr3.length > 0) {
          descList.push({
            key: '工作时间',
            list: arr3.sort((a, b) => {
              return a?.rank - b?.rank;
            }),
          });
        }
        if (arr4.length > 0) {
          // 1450af37fc3fb7ce29d2eb53ffd9bd04
          descList.push({
            key: '薪酬福利',
            list: arr4.sort((a, b) => {
              return a?.rank - b?.rank;
            }),
          });
        }
        _.forEach(descList, (list) => {
          _.forEach(list.list, (item, index) => {
            item.realDetail = list.list?.length > 1 ? `${index + 1}、${item.detail}` : item.detail;
          });
        });
        info.value.descList = descList;
      }
    };
    const getSum = (array) => {
      let all = 0;
      array.forEach((item) => {
        if (item && item.count) {
          all += item.count;
        }
      });
      return all;
    };
    const getPercent = (sum, count) => {
      const percent = ((count * 100) / sum).toFixed(2);
      return percent;
    };
    const initData = () => {
      if (jobDetail.value?.GroupItems?.length > 0) {
        jobDetail.value.GroupItems.map((item) => {
          if (item.key === 'experience') {
            if (item.items && item.items.length > 0) {
              setTimeout(() => {
                drawExpChart(item.items);
              }, 500);
            }
          }
          if (item.key === 'salary') {
            if (item.items && item.items.length > 0) {
              setTimeout(() => {
                drawSalChart(item.items);
              }, 500);
            }
          }
          if (item.key === 'education') {
            if (item.items && item.items.length > 0) {
              setTimeout(() => {
                drawEduChart(item.items);
              }, 500);
            }
          }
        });
        _.forEach(chartList.value, (item: any) => {
          item.isLoaded = true;
        });
      }
    };
    const showAllInfo = computed(() => {
      if (!jobDetail.value) return false;
      if (!info.value?.descList?.length) {
        return true;
      }
      return jobDetail.value?.Result?.length > 1;
    });

    onMounted(() => {
      handleJD();
      initData();
    });

    return {
      info,
      jobDetail,
      showAllInfo,
    };
  },
  render() {
    const { info, showAllInfo, jobDetail } = this;
    return (
      <div class={styles.container}>
        <section class={[styles.npanel, styles.npanelDefault, styles.jobMain]}>
          <div class={styles.extraInfo}>
            {info.area && (
              <span class={styles.tags}>
                <q-icon type="icon-a-zuobiaoxian" class={styles.icon} />
                {info.area}
              </span>
            )}
            {info.experience && (
              <span class={styles.tags}>
                <q-icon type="icon-experience" class={styles.icon} />
                {info.experience}
              </span>
            )}
            {info.education && (
              <span class={styles.tags}>
                <q-icon type="icon-xuelizhengshu" class={styles.icon} />
                {info.education}
              </span>
            )}
            {info.publishTime && (
              <span class={styles.tags}>
                <q-icon type="icon-shenhe" class={styles.icon} />
                {formatDate(info.publishTime)}发布
              </span>
            )}
            {info.compName && (
              <span class={styles.tags}>
                <q-icon type="icon-enterprise" class={styles.icon} />
                <q-entity-link coy-obj={{ KeyNo: info.compKeyNo, Name: info.compName }} />
                {info.financing && <span class={styles.dotParam}>{info.financing}</span>}
                {info.companyScale && <span class={styles.dotParam}>{info.companyScale}</span>}
                {info.industry && <span class={styles.dotParam}>{info.industry}</span>}
              </span>
            )}
          </div>
        </section>
        {info?.descList?.length ? (
          <div class={styles.zw}>
            {info.descList.map((list, idx) => (
              <div class={styles.zwItem}>
                <div>
                  <span class={styles.zwHead}>{list.key}</span>
                </div>
                <div class={styles.zwPart}>
                  {list.list.map((item) => (
                    <div class={styles.zwPartDetail}>{item.realDetail}</div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : null}
        {showAllInfo && (jobDetail.Summary || jobDetail.Result) ? (
          <section class={[styles.npanel, styles.npanelDefault, styles.recruitMain]}>
            <div class={styles.intro}>
              <img src={bgImg} alt=""></img>
              <div class={styles.introContent}>
                <div class={styles.introItem}>
                  <div class={styles.head}>
                    岗位名称: <span class={styles.info}>{info.positionName}</span>
                  </div>
                  <div class={styles.detail}>
                    {jobDetail?.Summary?.Salaryavg && jobDetail.Summary?.Salaryavg !== '0' && (
                      <span class={styles.detailPart}>
                        该岗位平均薪资: <span class={styles.info}>{jobDetail.Summary.Salaryavg}元/月</span>
                      </span>
                    )}
                    {jobDetail?.Summary?.ComCount && jobDetail.Summary.ComCount !== '0' && (
                      <span class={styles.detailPart}>
                        其他企业拥有相似岗位: <span class={styles.info}>{jobDetail.Summary.ComCount}家</span>
                      </span>
                    )}
                    {info.eduPer && info.eduPer !== '0' && (
                      <span class={styles.detailPart}>
                        大专以上学历要求占比: <span class={styles.info}>{info.eduPer}%</span>
                        <span class={styles.comma}>，</span>
                      </span>
                    )}
                    {info.expPer && info.expPer !== '0' && (
                      <span class={styles.detailPart}>
                        3年以上工作经验要求占比: <span class={styles.info}>{info.expPer}%</span>
                      </span>
                    )}
                  </div>
                </div>
                {jobDetail.Result ? (
                  <div class={styles.introItem}>
                    <div class={styles.head}>其中薪资 Top3 的企业为</div>
                    <div class={styles.detail}>
                      {jobDetail.Result.slice(0, 3).map((item) => (
                        <span class={styles.detailPart}>
                          <q-entity-link coy-obj={{ KeyNo: item.CompanyKeyNo, Name: item.CompanyName }} />
                          {item.Salary && <span class={styles.info}>: {item.Salary}</span>}
                        </span>
                      ))}
                    </div>
                  </div>
                ) : null}
              </div>
            </div>
          </section>
        ) : null}
      </div>
    );
  },
});

export default RecruitmentAnalysis;
