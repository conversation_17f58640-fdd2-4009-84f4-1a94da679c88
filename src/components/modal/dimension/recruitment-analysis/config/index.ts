import { map, reduce } from 'lodash';

// 各种状态的颜色
const statusColor = (status: string) => {
  let color = '';
  if (status.indexOf('存续') !== -1) {
    color = '#61DDAA';
  } else if (status.indexOf('在业') !== -1) {
    color = '#5B8FF9';
  } else if (status.indexOf('注销') !== -1) {
    color = '#F6BD16';
  } else if (status.indexOf('撤销') !== -1) {
    color = '#F6903D';
  } else if (status.indexOf('吊销') !== -1) {
    color = '#F04040';
  } else if (status.indexOf('迁入') !== -1) {
    color = '#65789B';
  } else if (status.indexOf('迁出') !== -1) {
    color = '#898E98';
  } else if (status.indexOf('清算') !== -1) {
    color = '#D67B83';
  } else if (status.indexOf('停业') !== -1) {
    color = '#B96C30';
  } else if (status.indexOf('歇业') !== -1) {
    color = '#CBAF0F';
  } else if (status.indexOf('除名') !== -1) {
    color = '#F04040';
  } else if (status.indexOf('责令关闭') !== -1) {
    color = '#9C0E0E';
  } else {
    color = '#808080';
  }

  return color;
};
export const getPercentWithPrecision = (valueList, idx, precision) => {
  if (!valueList[idx]) {
    return 0;
  }

  const sum = reduce(
    valueList,
    (acc, val) => {
      return acc + (isNaN(val) ? 0 : val);
    },
    0
  );
  if (sum === 0) {
    return 0;
  }

  const digits = Math.pow(10, precision);
  const votesPerQuota = map(valueList, (val) => {
    return ((isNaN(val) ? 0 : val) / sum) * digits * 100;
  });
  const targetSeats = digits * 100;

  const seats = map(votesPerQuota, (votes) => {
    // Assign automatic seats.
    return Math.floor(votes);
  });
  let currentSum = reduce(
    seats,
    (acc, val) => {
      return acc + val;
    },
    0
  );

  const remainder = map(votesPerQuota, (votes, idx) => {
    return votes - seats[idx];
  });

  // Has remainding votes.
  while (currentSum < targetSeats) {
    // Find next largest remainder.
    let max = Number.NEGATIVE_INFINITY;
    let maxId: null | number = null;
    for (let i = 0, len = remainder.length; i < len; ++i) {
      if (remainder[i] > max) {
        max = remainder[i];
        maxId = i;
      }
    }

    // Add a vote to max remainder.
    ++seats[maxId as number];
    remainder[maxId as number] = 0;
    ++currentSum;
  }

  return seats[idx] / digits;
};

const mbstrlen = (str = '') => {
  let len = 0;
  for (let i = 0; i < str.length; i++) {
    if (str.charCodeAt(i) > 127 || str.charCodeAt(i) === 94) {
      len += 2;
      // 中文括号比其他字符稍大
      if ([65288, 65289].includes(str.charCodeAt(i))) {
        len++;
      }
    } else {
      len++;
    }
  }
  return len;
};

// 常用柱状图配置
export const BarConfig = (config) => {
  const {
    xData,
    yData,
    series,
    xAxisConfig = {},
    gridConfig = {},
    tooltip = {},
    seriesLabelConfig = {},
    filterData = [],
    extraOption = {},
    showDataZoomCount = 10,
    isShowDataZoom = false,
    isShowSaveAsImage = false,
    seriesConfig = {},
    yAxisConfig = {},
    normalGridBottom = 10,
    dataZoomConfig = {},
  } = config;
  let showDataZoom = false;
  let startPercent: number = 0;
  let endPercent: number = 0;
  if (isShowDataZoom) {
    if (xData.length <= showDataZoomCount) {
      showDataZoom = false;
    } else {
      showDataZoom = true;
      startPercent = 100 - ((showDataZoomCount / xData.length) as any).toFixed(2) * 100;
      if (startPercent < 0) {
        startPercent = 0;
      }
      if (dataZoomConfig.scrollStart) {
        startPercent = 0;
        endPercent = ((showDataZoomCount / xData.length) as any).toFixed(2) * 100;
      }
    }
  }
  return {
    filterData,
    notMerge: true,
    toolbox: {
      feature: {
        saveAsImage: {
          show: isShowSaveAsImage,
          title: ' ',
          pixelRatio: 2,
        },
      },
    },
    dataZoom: [
      {
        show: isShowDataZoom && showDataZoom,
        type: 'slider',
        zoomLock: true,
        start: startPercent ? startPercent + 2 : 0,
        end: endPercent || 100,
        minValueSpan: 0,
        maxValueSpan: 30,
        backgroundColor: '#fff',
        dataBackground: {
          lineStyle: {
            width: 1,
            color: '#E6E7F5',
          },
          areaStyle: {
            color: '#E6E7F5',
          },
        },
        borderColor: '#F3F3F3',
        height: 15,
        fillerColor: 'rgba(217, 229, 255, 0.8)',
        right: 40,
        left: 40,
        bottom: 10,
        ...dataZoomConfig,
      },
    ],
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.8)',
      formatter(param) {
        return param.name + ':\n' + param.value + '%';
      },
      confine: true,
      ...tooltip,
    },
    grid: {
      top: 50,
      right: 20,
      bottom: isShowDataZoom ? (showDataZoom ? 30 : 10) : normalGridBottom,
      left: 4,
      containLabel: true,
      ...gridConfig,
    },
    color: ['#408FFF'],
    xAxis: {
      type: 'category',
      boundaryGap: true,
      axisLine: {
        lineStyle: {
          color: '#999',
          width: 1,
        },
      },
      axisTick: {
        show: true,
        alignWithLabel: true,
        lineStyle: {
          color: '#999',
        },
      },
      axisLabel: {
        textStyle: {
          color: '#999',
        },
        interval: 0,
        rotate: 45,
      },
      data: xData,
      ...xAxisConfig,
    },
    yAxis: {
      axisLine: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#eee',
          width: 0.5,
        },
      },
      axisTick: {
        show: false,
      },
      ...yAxisConfig,
    },
    series: series || [
      {
        name: '数量',
        type: 'bar',
        barMaxWidth: 15,
        label: {
          show: true,
          position: 'top',
          distance: 5,
          color: '#7397BA',
          formatter: '{c}%',
          ...seriesLabelConfig,
        },
        emphasis: {
          label: {
            distance: 3,
          },
          itemStyle: {
            color: '#408FFF',
            borderColor: '#5b8FF950',
            borderWidth: 4,
          },
        },
        data: yData,
        ...seriesConfig,
      },
    ],
    ...extraOption,
  };
};

// 常用饼图配置
export const PieConfig = (
  nameList,
  pieData,
  seriesPositionConfig: Record<string, any> = {},
  legendPositionConfig = {},
  pieLengendCenter = nameList.length <= 7,
  isStatusPie = false
) => {
  const colorList = [] as any[];
  if (isStatusPie) {
    nameList.forEach((item) => {
      const color = statusColor(item.name);
      colorList.push(color);
      item.color = color;
    });
  }

  const brLength = seriesPositionConfig?.brLength || 5;
  return {
    notMerge: true,
    tooltip: {
      show: false,
      trigger: 'item',
      formatter: '{b} : {c} ({d}%)',
    },
    toolbox: {
      feature: {
        saveAsImage: {
          show: false,
        },
      },
    },
    pieLengendCenter,
    legend: {
      show: false,
      type: 'scroll',
      orient: 'vertical',
      left: 175,
      top: 40,
      bottom: 15,
      data: nameList,
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        padding: [1, 0, 0, 0],
      },
      formatter: (name) => {
        if (mbstrlen(name) > 30) {
          return name.substring(0, 15) + '\n' + name.substring(15);
        }
        return name;
      },
      ...legendPositionConfig,
    },
    series: [
      {
        type: 'pie',
        center: [85, 150],
        radius: ['30%', '50%'],
        avoidLabelOverlap: false,
        data: pieData,
        label: {
          show: false,
          position: 'center',
          formatter: (data) => {
            let name = data?.name;
            const percent = data?.percent;
            if (name.length > brLength) {
              name = name.slice(0, brLength - 1) + '...';
            }
            return `${name}\n${percent}%`;
          },
          rich: {
            b: {
              color: '#666666',
              fontSize: 12,
              fontWeight: 'bold',
            },
          },
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 6,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.3)',
          },
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold',
            lineHeight: 20,
            color: '#333',
          },
        },
        color: colorList,
        ...seriesPositionConfig,
      },
    ],
  };
};
