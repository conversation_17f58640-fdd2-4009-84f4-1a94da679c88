.container {
    margin: -15px 0 0 -15px;

    .npanel {
        background: none;
        border: none;
    }

    .npanel-body {
        padding: 0;
    }

    .job-main {
        padding: 0 15px 15px;
        border: none;
        border-radius: 4px;


        .extra-info {
            padding: 10px;
            background: #FAFAFA;
            border-radius: 4px;
            margin-top: 15px;

            .tags {
                position: relative;
                display: inline-block;
                font-size: 14px;
                height: 22px;
                line-height: 22px;
                color: #333;
                padding-right: 10px;
                margin-right: 10px;

                .icon {
                    margin-right: 6px;
                    color: #BBB;
                }

                .icon-icon_nianxian {
                    font-size: 12px;
                    vertical-align: -1px;
                }


                &:not(:last-child)::after {
                    position: absolute;
                    content: '';
                    width: 1px;
                    height: 12px;
                    background: #E3E3E3;
                    right: 0;
                    top: 50%;
                    transform: translateY(-50%);
                }

                .dot-param {
                    display: inline-block;
                    position: relative;

                    &:not(:first-child) {
                        margin-left: 10px;
                        padding-left: 10px;
                    }

                    &::before {
                        content: '';
                        display: block;
                        width: 2px;
                        height: 2px;
                        border-radius: 50%;
                        background: #BBB;
                        position: absolute;
                        top: 50%;
                        margin-top: -1px;
                        left: -1px;
                    }
                }
            }
        }
    }

    .recruit-main {
        padding: 15px 15px 5px;
        border: none;
        border-radius: 4px;

        .intro {
            background-color: #F2F8FE;
            border: 1px solid #E2F1FC;
            position: relative;
            padding-top: 15px;

            img {
                width: 92px;
                height: 22px;
                margin-left: 15px;
                margin-bottom: 15px;
            }

            .intro-content {
                padding: 0 15px;

                .intro-item {
                    &:not(:nth-last-of-type(1)) {
                        margin-bottom: 15px;
                    }

                    .detail-part {
                        position: relative;
                        margin-right: 30px;
                        padding-left: 9px;
                        display: inline-block;

                        &::before {
                            content: "";
                            display: inline-block;
                            width: 4px;
                            height: 4px;
                            background: #128bed;
                            border-radius: 50%;
                            position: absolute;
                            top: 50%;
                            transform: translateY(-50%);
                            left: 0;
                        }
                    }
                }

                .head {
                    position: relative;
                    padding-left: 9px;
                    font-size: 16px;
                    color: #333;
                    font-weight: normal;
                    line-height: 24px;

                    &::after {
                        content: "";
                        display: block;
                        position: absolute;
                        height: 14px;
                        width: 4px;
                        background-color: #128bed;
                        top: 5px;
                        left: 0;
                    }
                }

                .detail {
                    font-size: 14px;
                    color: #666;
                    margin-top: 5px;

                    >div {
                        display: inline-block;
                    }
                }

                .info {
                    color: #333;
                    font-weight: bold;
                }
            }
        }
    }

    .zw {
        background-color: #fff;
        border-radius: 4px;
        margin-bottom: 15px;
        padding: 0 15px;

        .zw-item {
            margin-bottom: 15px;

            .zw-head {
                font-size: 16px;
                line-height: 1;
                position: relative;
                font-weight: bold;
                color: #333;
                padding: 0 1px 2px;
                background: linear-gradient(to top, rgba(18, 139, 237, 0.2) 40%, transparent 0%);
            }

            .zw-part {
                padding: 10px 10px 0 0;
                line-height: 22px;

                .zw-part-detail:not(:last-child) {
                    margin-bottom: 4px;
                }

                .icon-icon_shijian {
                    margin-right: 5px;
                }
            }
        }

    }

    .detail-table {
        margin-bottom: 0;
    }

    .comma:last-child {
        display: none;
    }


    .job-detail-modal {
        ::v-deep .modal-dialog {
            .modal-body {
                padding: 0 !important;
            }

            .item {
                background: #fff;
            }

            .app-echarts-new.app-echarts {
                .pie-hover {
                    height: 208px !important;
                    overflow-y: auto !important;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;

                    .pie-hover-item {
                        &:hover,
                        &.hoverIndex {
                            background: rgba(18, 139, 237, 0.08) !important;
                        }
                    }
                }
            }
        }
    }
}
