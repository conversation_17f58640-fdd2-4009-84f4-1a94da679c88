import { defineComponent } from 'vue';

import QPlainTable from '@/components/global/q-plain-table';
import { numberToHumanWithUnit } from '@/utils/number-formatter';

const EmployeeStockPlatform = defineComponent({
  name: 'EmployeeStockPlatform',
  props: {
    viewData: {
      type: Object,
      default: () => ({}),
    },
  },
  render() {
    const { details } = this.viewData;
    return (
      <QPlainTable>
        <thead>
          <tr>
            <th>出资方式</th>
            <th>出资额</th>
            <th>出资日期</th>
          </tr>
        </thead>
        <tbody>
          {details.map((d) => {
            const { date, capi, type } = d;
            return (
              <tr>
                <td>{type}</td>
                <td>{capi?.split(',').map((c) => <div>{numberToHumanWithUnit(c)}万元</div>) || '-'}</td>
                <td>{date?.split(',').map((c) => <div>{c}</div>) || '-'}</td>
              </tr>
            );
          })}
          <tr></tr>
        </tbody>
      </QPlainTable>
    );
  },
});

export default EmployeeStockPlatform;
