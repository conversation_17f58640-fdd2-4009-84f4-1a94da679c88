@import '@/styles/token.less';

@color-risk-high: #f04040;
@border-color-risk-high: #ffecec;
@color-risk-medium: #fa0;
@border-color-risk-medium: #fff4e0;
@color-risk-low: #00ad65;
@border-color-risk-low: #f8fffc;

.container {
  width: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  font-size: 14px;
  line-height: 22px;
  color: #333;
  gap: 50px;
}

.riskBlock {
  flex: 1;
}

.riskDetail {
  padding-right: 95px;
}

.riskItem {
  display: flex;
  align-items: flex-start;

  &:not(:last-child) {
    margin-bottom: 15px;
  }
}

.dot {
  position: relative;
  padding-left: 18px;

  &::before {
    content: '';
    position: absolute;
    top: 8.5px;
    left: 0;
    width: 5px;
    height: 5px;
    border-radius: 5px;
    background: #d8d8d8;
  }
}

.levelIcon {
  display: flex;
  align-items: center;
  gap: 2px;

  .failed {
    color: @color-risk-high;
    border-color: @border-color-risk-high;
  }

  .warning {
    color: @color-risk-medium;
    border-color: @border-color-risk-medium;
  }

  .pass {
    color: @color-risk-low;
    border-color: @border-color-risk-low;
  }
}

.label {
  flex-shrink: 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
  margin-right: 10px;
}

.content {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.link {
  white-space: nowrap;
  border-radius: 2px;
  background: #FFF;
  border: 1px solid #D8D8D8;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: normal;
  line-height: 18px;
  color: #333;
}

.link:hover {
  border-color: #128BED;
  color: #333;
  background: #F2F8FE;

  .linkNum {
    color: #128BED;
  }
}

.linkNum {
  color: #999;
  margin-left: 0.5em;
}

.riskNum {
  width: 50px;
  text-align: right;
}

.noHit {
  color: #999;
}

.riskScore {
  font-family: D-Din, sans-serif;
  font-size: 26px;
  font-weight: bold;
  line-height: 29px;
  text-align: center;
  color: #666;
}

.highRisk {
  .link {
    color: @color-risk-high;
    border-color: @color-risk-high;
    font-size: 14px;
  }

  .linkNum {
    color: @color-risk-high;
  }

  .link:hover {
    background-color: #fffafa;

    .linkNum {
      color: @color-risk-high;
    }
  };
}
