import { defineComponent, PropType } from 'vue';
import { isArray, sortBy } from 'lodash';

import { getRiskLevelTextColor, RiskResultLevelLabelMapping } from '@/config/risk.config';

import styles from './diligence-waring-block.module.less';
import DiligenceRiskLevel from '../diligence-risk-level';

const DiligenceWarningBlock = defineComponent({
  name: 'DiligenceWarningBlockV2',
  props: {
    /**
     * 是否显示图表
     */
    chart: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否显示企查分详情
     */
    creditRateDetail: {
      type: Boolean,
      default: true,
    },
    riskLevel: {
      type: Number,
    },
    riskInfo: {
      type: Object,
      default: () => ({}),
    },
    scrollToView: {
      type: Function,
    },
    levelGroup: {
      type: Array as PropType<number[]>,
      default: () => [2, 1, 0],
    },
    isCountSort: {
      type: Boolean,
      required: false,
    },
  },
  setup(props) {
    const getSortedData = (data: Record<string, any>[]) => {
      if (!isArray(data) || !data?.length) {
        return [];
      }
      const flatData = data.flatMap((item) => sortBy(item.scoreDetails, (v) => -v.score));
      if (props.isCountSort) {
        return sortBy(flatData, (item) => -item.totalHits);
      }
      return flatData;
    };
    return {
      getSortedData,
    };
  },
  render() {
    const { riskLevel, riskInfo } = this;
    const levelText = riskInfo.riskName;
    const levelGroup = riskInfo.details?.levelGroup || {};
    return (
      <section class={styles.container}>
        {/* 图表 */}
        {this.chart && (
          <DiligenceRiskLevel level={riskLevel}>
            <div class={styles.riskScore}>{riskInfo.score}</div>
            <div style={{ color: getRiskLevelTextColor(riskLevel) }} domPropsInnerHTML={levelText}></div>
          </DiligenceRiskLevel>
        )}

        <div class={styles.riskBlock}>
          {/* 风险等级明细 */}
          <ul class={styles.riskDetail}>
            {[2, 1, 0].map((riskLevelCode) => {
              const highRisk = riskLevelCode === 2;
              const data = this.getSortedData(levelGroup[riskLevelCode]);
              const curLevelObj = RiskResultLevelLabelMapping[riskLevelCode];
              return (
                <li
                  class={{
                    [styles.dot]: true,
                    [styles.riskItem]: true,
                    [styles.noHit]: data?.length <= 0,
                    [styles.highRisk]: highRisk,
                  }}
                  key={riskLevelCode}
                >
                  <div class={styles.label}>
                    <span class={[styles.safe, styles.levelIcon]}>
                      <img src={curLevelObj.icon} class={styles.icon} width="22" height="22" />
                      {curLevelObj.label}
                    </span>
                    <i class={{ [styles.safe]: data.length === 0, [styles.riskNum]: true }}>{`${data.length}项：`}</i>
                  </div>
                  {data?.length > 0 ? (
                    <div class={styles.content}>
                      {data.map((item, idx) => (
                        <a
                          onClick={(event) => {
                            this.scrollToView?.(item.metricsId, item, event);
                          }}
                          class={[
                            this.scrollToView ? styles.anchorHover : styles.anchor,
                            styles.link,
                            idx === data.length - 1 ? styles.noDivider : '',
                          ]}
                          href={`#${item.key}`}
                          key={item.metricsId}
                        >
                          {item.name}
                          <span class={styles.linkNum}>{item.totalHits}</span>
                        </a>
                      ))}
                    </div>
                  ) : (
                    <div class={[styles.content, styles.safe]}>未检测到相关项</div>
                  )}
                </li>
              );
            })}
          </ul>
        </div>
        <div>{this.$slots.extra}</div>
      </section>
    );
  },
});
export default DiligenceWarningBlock;
