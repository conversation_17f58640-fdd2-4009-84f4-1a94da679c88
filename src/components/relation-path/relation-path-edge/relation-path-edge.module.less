.edge {
  white-space: nowrap;
  position: relative;
  padding: 0 15px;
  max-width: 100%;
  overflow: hidden;

  .description {
    // max-width: calc(100% - 30px);
    font-size: 12px;
    line-height: 18px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
    text-align: center;
    transform: translateY(-3px);
    display: inline-block;
  }

  .line {
    position: absolute;
    height: 1px;
    background-color: #d6d6d6;
    transform: translateY(2px);
    top: 50%;
    left: 0;
    right: 0;

    &::before {
      content: "";
      position: absolute;
      transform: translateY(-50%);
      top: 50%;
      height: 0;
      width: 0;
      border: 0 solid rgba(0, 0, 0, 0);
    }

    &.left {
      left: 2px;
      right: 0;

      &::before {
        border-right-color: #d6d6d6;
        border-width: 3px 10px;
        left: -12px;
      }
    }

    &.right {
      left: 0;
      right: 2px;

      &::before {
        border-left-color: #d6d6d6;
        border-width: 3px 10px;
        right: -12px;
      }
    }
  }
}
