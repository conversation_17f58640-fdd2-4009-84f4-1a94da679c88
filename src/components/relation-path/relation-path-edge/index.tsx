import { defineComponent, PropType } from 'vue';
import { PathEdge } from '../core';
import styles from './relation-path-edge.module.less';

export const RelationPathEdge = defineComponent({
  name: 'RelationPathEdge',
  props: {
    data: {
      type: Object as PropType<PathEdge>,
      default: () => new PathEdge(),
    },
  },
  render() {
    const lineClassList = {
      [styles.line]: true,
      [styles.left]: this.data.direction === -1,
      [styles.right]: this.data.direction === 1,
    };

    return (
      <div class={styles.edge}>
        <div class={styles.description}>{this.data.description}</div>
        <div class={lineClassList} />
      </div>
    );
  },
});
