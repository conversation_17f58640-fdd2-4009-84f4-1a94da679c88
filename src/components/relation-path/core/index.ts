export class Path {
  nodes: Array<PathNode | PathEdge> = [];
  /**
   * 如果首尾节点相同，则认为是循环指向
   */
  isCircular() {
    if (this.nodes.length < 2) {
      return false;
    }
    return this.nodes[0].id === this.nodes[this.nodes.length - 1].id;
  }
}

export class PathEdge {
  id?: string;
  direction?: number;
  description?: string;
  startId?: string;
  endId?: string;
}

export class PathNode {
  id?: string;
  name?: string;
  org?: number;
}

export abstract class PathConverter {
  abstract convertNode(data: unknown): PathNode;
  abstract convertEdge(data: unknown): PathEdge;
  abstract isNodeLike(data: unknown): boolean;
  abstract isEdgeLike(data: unknown): boolean;
}
