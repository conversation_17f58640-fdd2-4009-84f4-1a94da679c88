import { defineComponent, PropType } from 'vue';
import { PathEdge } from '../core';
import styles from './relation-path-circular-edge.module.less';

export const RelationPathCircularEdge = defineComponent({
  name: 'RelationPathCircularEdge',
  props: {
    data: {
      type: Object as PropType<PathEdge>,
      default: () => new PathEdge(),
    },
  },
  render() {
    return (
      <div class={styles.edge}>
        <div class={styles.description}>{this.data.description}</div>
        <div class={styles.line}>
          <div
            class={{
              [styles.arrow]: this.data.direction === -1,
              [styles.left]: true,
            }}
          />
          <div
            class={{
              [styles.arrow]: this.data.direction === 1,
              [styles.right]: true,
            }}
          />
        </div>
      </div>
    );
  },
});
