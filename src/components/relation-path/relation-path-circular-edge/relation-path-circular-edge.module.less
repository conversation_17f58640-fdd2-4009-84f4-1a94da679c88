@full-height: 18px;

.edge {
  margin-left: 72px;
  margin-right: 72px;
  white-space: nowrap;
  position: relative;

  .description {
    font-size: 12px;
    line-height: @full-height;
    min-height: @full-height;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
    text-align: center;
  }

  .line {
    position: absolute;
    height: 1px;
    background-color: #d6d6d6;
    bottom: 0;
    left: 0;
    right: 0;

    .left,
    .right {
      position: absolute;
      bottom: 0;
      height: @full-height;
      width: 1px;
      background-color: #d6d6d6;
    }

    .left {
      left: 0;
    }

    .right {
      right: 0;
    }
  }

  .arrow {
    &::before {
      position: absolute;
      content: '';
      width: 0;
      height: 0;
      border: 0 solid rgba(0, 0, 0, 0);
      border-bottom-color: #d6d6d6;
      border-width: 9px 4px;
      // top: -12px; // 出头
      top: -10px; // 出头
    }

    &.left {
      left: 0;
    }

    &.right {
      right: 0;
    }

    &.left::before {
      left: -3px; // 居中
    }

    &.right::before {
      right: -2px; // 居中
    }
  }
}
