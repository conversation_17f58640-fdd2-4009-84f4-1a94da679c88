import { computed, defineComponent, PropType } from 'vue';
import { Path, PathEdge, PathNode } from '../core';
import { RelationPathEdge } from '../relation-path-edge';
import { RelationPathNode } from '../relation-path-node';
import { RelationPathCircularEdge } from '../relation-path-circular-edge';
import styles from './relation-path.module.less';

const RelationPath = defineComponent({
  name: 'RelationPath',
  props: {
    path: {
      type: Object as PropType<Path>,
      default: () => new Path(),
    },
  },
  setup(props) {
    const isEdge = (item: PathNode | PathEdge) => {
      return item instanceof PathEdge;
    };

    const isNode = (item: PathNode | PathEdge) => {
      return item instanceof PathNode;
    };

    const isCircular = computed(() => props.path.isCircular());

    return {
      isEdge,
      isNode,
      isCircular,
    };
  },
  render() {
    // 循环依赖节点处理: 移除重复节点
    const paths = this.isCircular ? this.path.nodes.slice(0, this.path.nodes.length - 2) : this.path.nodes;
    // 循环依赖边信息
    const circularEdge = this.isCircular ? this.path.nodes[this.path.nodes.length - 2] : undefined;

    return (
      <div class={styles.container}>
        {/* 正常路径 */}
        <div class={styles.nodes}>
          {paths.map((part) => {
            if (this.isEdge(part)) {
              return <RelationPathEdge key={part.id} data={part} />;
            } else if (this.isNode(part)) {
              return <RelationPathNode key={part.id} data={part} />;
            }
            return null;
          })}
        </div>
        {/* 依赖路径 */}
        {circularEdge ? (
          <RelationPathCircularEdge
            data={{
              id: 'circular-edge',
              description: (circularEdge as PathEdge)?.description,
              direction: -1,
            }}
          />
        ) : null}
      </div>
    );
  },
});

export default RelationPath;
