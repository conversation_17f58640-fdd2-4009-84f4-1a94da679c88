import { defineComponent, PropType } from 'vue';
import { PathNode } from '../core';
import styles from './relation-path-node.module.less';
import QEntityLink from '@/components/global/q-entity-link';

export const RelationPathNode = defineComponent({
  name: 'RelationPathNode',
  props: {
    data: {
      type: Object as PropType<PathNode>,
      default: () => new PathNode(),
    },
  },
  render() {
    return (
      <div class={styles.node}>
        <QEntityLink
          coyObj={{
            KeyNo: this.data.id,
            Name: this.data.name,
            Org: this.data.org,
          }}
        />
      </div>
    );
  },
});
