@import '@/styles/token.less';

.container {
  .divider {
    color: #eee;
    margin: 0 10px;
  }

  .reason {
    display: flex;
    align-items: center;
    gap: 30px;

    .risk-credit-rate {
      margin-bottom: 10px;
    }

    .risk-block{
      flex: 1
    }

    .risk-detail {
      li + li {
        margin-top: 10px;
      }

      li {
        border-width: 1px;
        border-style: solid;
        border-radius: 4px;
        display: flex;

        .icon {
          font-size: 18px;
          margin-right: 4px;
          position: relative;
          margin-top: -2px;
        }

        .label {
          line-height: 22px;
          padding: 10px 15px;
          display: flex;
          align-items: center;
          border-right-width: 1px;
          border-right-style: solid;
        }

        .content {
          padding: 7.5px 15px;
          line-height: 27px;
          flex: 1;
          display: flex;
          align-items: center;
          gap: 5px;
          flex-wrap: wrap;

          &.safe {
            color: #999;
          }
        }

        .anchorHover {
          color: #333;

          &:hover {
            color: @qcc-color-blue-500;
          }
        }

        .anchor {
          color: #333;
          cursor: auto;

          &:hover {
            color:#333;
          }
        }

        &.failed {
          background: radial-gradient(100% 100% at 0% 0%, #FFF6F4 0%, #FFF 52%);
          border-color: #ffecec;

          .label {
            color: #F04040;
            border-color: #ffecec;
          }
        }

        &.warning {
          background: radial-gradient(100% 100% at 0% 0%, #FFFDF4 0%, #FFF 52%);
          border-color: #fff4e0;

          .label {
            color: #fa0;
            border-color: #fff4e0;
          }
        }

        &.pass {
          background: radial-gradient(100% 100% at 0% 0%, rgba(224, 245, 236, 0.5) 0%, #FFF 52%);
          border-color: #e0f5ec;

          .label {
            color: #00ad65;
            border-color: #E0F5EC;
          }
        }
      }

    }

  }
}

.link {
  white-space: nowrap;
  border-radius: 2px;
  background: #EEE;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: normal;
  line-height: 18px;
  color: #333;
}

.link:hover {
  color: #333;
  background: #F2F8FE;

  .linkNum {
    color: #128BED;
  }
}

.linkNum {
  color: #999;
  margin-left: 0.5em;
}