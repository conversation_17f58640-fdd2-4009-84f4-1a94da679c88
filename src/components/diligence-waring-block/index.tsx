import { computed, defineComponent } from 'vue';
import { isArray, isEmpty, sortBy } from 'lodash';

import { RiskResultLevelLabelMapping } from '@/config/risk.config';

import styles from './diligence-waring-block.module.less';
import DiligenceRiskLevel from '../diligence-risk-level';

const levelMap = {
  0: 'pass',
  1: 'warning',
  2: 'failed',
};

const DiligenceWarningBlock = defineComponent({
  name: 'DiligenceWarningBlock',
  props: {
    /**
     * 是否显示图表
     */
    chart: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否显示企查分详情
     */
    creditRateDetail: {
      type: Boolean,
      default: true,
    },
    riskLevel: {
      type: [String, Number],
      required: false,
    },
    riskInfo: {
      type: Object,
      default: () => ({}),
    },
    scrollToView: {
      type: Function,
    },
    isCountSort: {
      type: Boolean,
      required: false,
    },
  },
  setup(props) {
    const hasCreditRate = computed(() => {
      return !isEmpty(props.riskInfo?.details?.creditRateResult);
    });
    const getSortedData = (data: Record<string, any>[]) => {
      if (!isArray(data) || !data?.length) {
        return [];
      }
      const flatData = data.flatMap((item) => sortBy(item.scoreDetails, (v) => -v.score));
      if (props.isCountSort) {
        return sortBy(flatData, (item) => -item.totalHits);
      }
      return flatData;
    };
    return {
      hasCreditRate,
      getSortedData,
    };
  },
  render() {
    const { riskLevel, riskInfo } = this;
    const level = riskLevel ? levelMap[riskLevel] : undefined;
    const levelGroup = riskInfo.details?.levelGroup || {};
    return (
      <section class={styles.container}>
        <main class={styles.reason}>
          {/* 图表 */}
          {this.chart ? <DiligenceRiskLevel level={level} /> : null}

          <div class={styles.riskBlock}>
            {/* 风险等级明细 */}
            <ul class={styles.riskDetail}>
              {[2, 1, 0].map((riskLevelCode) => {
                const data = this.getSortedData(levelGroup[riskLevelCode]);
                const currentLevel = levelMap[riskLevelCode];
                const curLevelObj = RiskResultLevelLabelMapping[riskLevelCode];
                return (
                  <li class={styles[currentLevel]} key={riskLevelCode}>
                    <div class={styles.label}>
                      <img src={curLevelObj.icon} class={styles.icon} width="22" height="22" />
                      <span>{curLevelObj.label}</span>
                    </div>
                    {data?.length > 0 ? (
                      <div class={styles.content}>
                        {data.map((item) => (
                          <a
                            onClick={(event) => {
                              event.preventDefault();
                              event.stopPropagation();
                              this.scrollToView?.(item.metricsId.toString(), item, event);
                            }}
                            class={[this.scrollToView ? styles.anchorHover : styles.anchor, styles.link]}
                            href={`#${item.metricsId}`}
                            key={item.metricsId}
                          >
                            {item.name}
                            <span class={styles.linkNum}>{item.totalHits}</span>
                          </a>
                        ))}
                      </div>
                    ) : (
                      <div class={[styles.content, styles.safe]}>未检测到相关项</div>
                    )}
                  </li>
                );
              })}
            </ul>
          </div>
        </main>
      </section>
    );
  },
});
export default DiligenceWarningBlock;
