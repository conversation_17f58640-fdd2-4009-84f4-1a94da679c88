import { DatePicker, Form, Input, Select, TreeSelect } from 'ant-design-vue';
import { defineComponent, PropType } from 'vue';
import type { WrappedFormUtils } from 'ant-design-vue/types/form/form';

import AutoComplete from '@/components/global/q-auto-complete';

import type { FormItemType } from './types/form-item.type';
import styles from './form.module.less';

const FormController = defineComponent({
  functional: true,
  props: {
    form: {
      type: Object as PropType<WrappedFormUtils>,
      required: true,
    },
    formItem: {
      type: Object as PropType<FormItemType>,
      required: true,
    },
    // 增加 flex 布局支持
    formLayoutType: {
      type: String as PropType<'ant-row' | 'ant-row-flex'>,
      default: 'ant-row',
    },
  },
  render(h, { props }) {
    const { formItem, form } = props;
    const { type, name, label, rules, hidden, customRender, attrs, ...itemProps } = formItem;
    // TODO: 拆分为 decoratorOptions 与 v-data
    const decorateController = form.getFieldDecorator(name, { rules, initialValue: attrs?.defaultValue });
    let controller;

    switch (type) {
      case 'input':
        controller = decorateController(<Input {...{ attrs, props: itemProps }} onChange={attrs?.onChange || null} />);
        break;
      case 'select':
        controller = decorateController(<Select dropdownClassName={styles.select} {...{ attrs, props: itemProps }} />);
        break;
      case 'tree-select':
        controller = decorateController(<TreeSelect {...{ attrs, props: itemProps }} />);
        break;
      case 'text':
        controller = decorateController(<Input.TextArea {...{ attrs, props: itemProps }} />);
        break;
      case 'date':
        controller = decorateController(
          <DatePicker style={{ width: '100%' }} {...{ attrs, props: itemProps }} dropdownClassName={styles.select} />
        );
        break;
      case 'auto-complete': {
        controller = decorateController(
          <AutoComplete
            {...{ attrs, props: itemProps }}
            showSuffix={itemProps.dataSource?.length > 0 && itemProps.showsSuffix}
            remote={(keywords) => {
              if (!String(keywords).trim()) {
                return itemProps.dataSource;
              }
              return itemProps.dataSource.filter((item) => {
                return item.includes(keywords);
              });
            }}
            onChange={attrs?.onChange || null}
          ></AutoComplete>
        );
        break;
      }
      default:
        break;
    }
    if (!controller) {
      return <div>不支持当前控件类型. {JSON.stringify(formItem.type)}</div>;
    }
    return (
      <Form.Item label={label} v-show={!hidden} class={[props.formLayoutType, styles.formStyle]}>
        {controller}
      </Form.Item>
    );
  },
});

export default FormController;
