import { defineComponent, PropType } from 'vue';
import { Form, Input, Select } from 'ant-design-vue';
import type { WrappedFormUtils } from 'ant-design-vue/types/form/form';

import type { FormItemType } from './types/form-item.type';
import styles from './form.module.less';

const FormBuilder = defineComponent({
  functional: true,
  props: {
    form: {
      type: Object as PropType<WrappedFormUtils>,
      required: true,
    },
    config: {
      type: Array as PropType<FormItemType[]>,
      required: true,
    },
    layout: {
      type: String,
      default: 'horizontal',
    },
    itemLayout: {
      type: Object,
      default: () => ({
        labelCol: { span: 4 },
        wrapperCol: { span: 16 },
      }),
    },
  },
  render(h, { props, parent }) {
    const { config, form, layout, itemLayout } = props;
    return (
      <Form form={form} layout={layout} colon={false}>
        {config.map((formItem) => {
          const { type, name, label, rules, customRender, ...itemProps } = formItem;

          switch (type) {
            case 'input':
              return (
                <Form.Item
                  key={name}
                  label={label}
                  labelCol={itemLayout.labelCol}
                  wrapperCol={itemLayout.wrapperCol}
                  class={styles.formStyle}
                >
                  {form.getFieldDecorator(name, {
                    rules,
                  })(<Input {...{ props: itemProps }} />)}
                </Form.Item>
              );

            case 'select':
              return (
                <Form.Item
                  key={name}
                  label={label}
                  labelCol={itemLayout.labelCol}
                  wrapperCol={itemLayout.wrapperCol}
                  class={styles.formStyle}
                >
                  {form.getFieldDecorator(name, {
                    rules,
                  })(<Select dropdownClassName={styles.select} {...{ props: itemProps }} />)}
                </Form.Item>
              );

            case 'custom':
              return typeof customRender === 'string' ? parent.$scopedSlots[customRender]?.({}) : customRender?.(formItem);

            // TODO
            // case 'date-range':
            // case 'number':
            // case 'number-range':
            default:
              return null;
          }
        })}
      </Form>
    );
  },
});

export default FormBuilder;
