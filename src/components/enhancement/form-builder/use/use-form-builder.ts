import { Form } from 'ant-design-vue';
import { PropType } from 'vue';
import type { IformCreateOption } from 'ant-design-vue/types/form/form';

import type { FormItemType } from '../types/form-item.type';
import FormBuilder from '../form-builder';

export const useFormBuilder = (options: IformCreateOption) => {
  const FormInstance = Form.create(options)(FormBuilder);
  return [FormInstance] as const;
};
