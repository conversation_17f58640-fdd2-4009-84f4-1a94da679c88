import type { ValidationRule } from 'ant-design-vue/types/form/form';

export type FormItemType = {
  type: 'input' | 'search' | 'select' | 'multiple-select' | 'text' | 'suggestion' | 'custom' | 'date' | 'tree-select' | 'auto-complete';
  name: string;
  label?: string;
  placeholder?: string;
  options?: any[];
  rules?: ValidationRule[];
  customRender?: string | ((props: any) => Vue);
  attrs?: Record<string, any>;
  hidden?: boolean;
  [key: string]: any;
};
