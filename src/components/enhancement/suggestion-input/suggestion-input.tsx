import _, { get } from 'lodash';
import { computed, defineComponent, nextTick, PropType, ref } from 'vue';
import { AutoComplete, Input, message, Select, Tooltip } from 'ant-design-vue';

import Icon from '@/shared/components/icon';
import { getHTMLText, sleep } from '@/utils';
import CompanyLogo from '@/components/company-logo';
import { validateCompanyTypeWithWarning, validateCompanyWithCountInfo } from '@/utils/company/company-type';

import styles from './suggestion-input.module.less';

type SelectOption = Record<string | number, any>;

const isExist =
  (field: string) =>
  (value: string, options: any[] = []) => {
    if (value.trim()) {
      const option = _.find(options, { [field]: value });
      return option;
    }
    return null;
  };
const isExistById = isExist('id');
const isExistByValue = isExist('value');

const useHistory = (storageKey) => {
  const storageRead = () => {
    let records: unknown[] = [];
    const localData = localStorage.getItem(storageKey);
    if (localData) {
      try {
        const data = JSON.parse(localData);
        if (!Array.isArray(data)) {
          throw new Error('保存结构必须为数组');
        }
        records = data;
      } catch (err) {
        records = [];
      }
    }
    return records;
  };

  const storageWrite = (records: unknown[]) => {
    const localData = localStorage.setItem(storageKey, JSON.stringify(records));
    return localData;
  };

  const storageReset = () => {
    localStorage.removeItem(storageKey);
  };

  const HISTORY_LIMIT = 5;
  const history = ref<SelectOption[]>(storageRead().slice(0, HISTORY_LIMIT) as SelectOption[]);

  const add = (record) => {
    const existed = isExistById(record.id, history.value);
    if (existed) {
      return;
    }

    if (history.value.length > HISTORY_LIMIT) {
      history.value.pop();
    }
    history.value.unshift({
      ...record,
      label: record.value, // 前值 label 包含 `em` 标签, 在历史记录模式中不适用
    });

    storageWrite(history.value);
  };

  const clear = () => {
    storageReset();
    history.value = [];
  };

  return [history, add, clear] as const;
};

const SuggestionInput = defineComponent({
  name: 'SuggestionInput',
  props: {
    value: {
      type: String,
      required: false,
    },
    /**
     * 占位符文本
     */
    placeholder: {
      type: String,
      required: false,
    },
    size: {
      type: String,
      default: 'default',
    },
    enterButton: {
      type: String,
      default: '',
      required: false,
    },
    suffixIcon: {
      type: String,
      default: '',
      required: false,
    },
    /**
     * 后置占位符文本
     */
    suffixPlaceholder: {
      type: String,
      default: '批量排查',
    },
    clearIcon: {
      type: String,
      default: '',
      required: false,
    },
    remote: {
      type: Function as PropType<(keywords: string) => Promise<any>>,
    },
    autoFocus: {
      type: Boolean,
      default: true,
    },
    storageKey: {
      type: String,
      default: '__SEARCH_HISTORY__',
      required: false,
    },
    hasBulkSearch: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['change', 'clear', 'input', 'search', 'select', 'suffixClick'],
  model: {
    prop: 'value',
    event: 'input',
  },
  setup(props, { emit }) {
    // 搜索历史
    const [history, addHistoryRecord, clearHistoryRecord] = useHistory(props.storageKey);
    const showHistory = computed(() => !props.value);
    const activeOption = ref();
    // 搜索选项
    const options = ref<SelectOption[]>([]);

    const handleSelect = (companyId: string, option: SelectOption) => {
      const currentOptions = showHistory.value ? history.value : options.value;
      activeOption.value = currentOptions.find((item) => item.id === companyId);
      const existed = isExistById(companyId, currentOptions);
      if (existed) {
        addHistoryRecord(existed);
      }
      if (existed?.IsHide && props.hasBulkSearch) {
        activeOption.value = null;
        message.warning('涉嫌非法社会组织不支持风险排查');
        return;
      }
      emit('select', existed?.value, existed);
    };

    const handleChange = async (value: string, option) => {
      const label = getHTMLText(get(option, 'data.attrs.option.value', '')) || value;
      emit('input', label);
    };

    const handleClearHistory = () => {
      clearHistoryRecord();
    };

    const _handleAutoComplete = async (keywords: string) => {
      if (props.remote && keywords?.trim().length >= 2) {
        try {
          const result = await props.remote(keywords);
          options.value = result;
        } catch (err) {
          options.value = [];
        }
      } else {
        options.value = [];
      }
    };
    const handleAutoComplete = _.debounce(_handleAutoComplete, 250);

    /**
     * 回车事件
     * @param keywords
     * @param event
     */
    const emitSearch = async (keywords: string, event: InputEvent) => {
      event.stopPropagation(); // 如果关键字为空，则不触发 auto-complete 组件的 search 事件
      await nextTick();
      await sleep(10);

      // 从下拉列表选选择的，判断如果是非法组织或者是境外公司，直接去搜索页
      if (activeOption.value) {
        if (!validateCompanyTypeWithWarning(activeOption.value.type) || validateCompanyWithCountInfo(activeOption.value)) {
          activeOption.value = null;
          emit('search');
          return;
        }
        handleSelect(activeOption.value.id, activeOption.value);
        activeOption.value = null;
        return;
      }
      // 如果搜索内容存在于结果，直接进入详情结果 如果是非法组织的关键词，直接去搜索页
      const matched = isExistByValue(keywords, options.value);
      if (matched && !matched.IsHide) {
        handleSelect(matched.id, matched);
      } else {
        emit('search');
      }
    };

    const selectOptions = computed(() => {
      return showHistory.value ? history.value : options.value;
    });

    return {
      options,
      handleAutoComplete,
      handleSelect,
      handleChange,
      emitSearch,
      history,
      showHistory,
      handleClearHistory,
      activeOption,

      selectOptions,
    };
  },
  methods: {
    suffixClick(e) {
      e.stopPropagation();
      this.$emit('suffixClick');
    },
    clearClick(e) {
      e.stopPropagation();
      this.$emit('clear');
    },
  },
  render() {
    const props = {
      placeholder: this.placeholder,
      value: this.value,
      size: this.size,
      enterButton: this.enterButton,
      dropdownClassName: this.showHistory ? `${styles.dropdown} ${styles.history}` : styles.dropdown,
      optionLabelProp: 'title', // 指定value会有问题，label是不生效的，单纯解决input会回填keyNo的问题
      autoFocus: this.autoFocus,
      defaultActiveFirstOption: false,
      getPopupContainer: (triggerNode) => {
        return triggerNode.parentNode;
      },
    };
    const on = {
      change: this.handleChange,
      select: this.handleSelect,
      search: this.handleAutoComplete,
      focus: () => {
        this.activeOption = null;
      },
    };
    return (
      <AutoComplete
        class={`${this.selectOptions.length ? '' : 'ant-select-empty'}`}
        // allowClear
        {...{ props, on }}
      >
        <template slot="dataSource">
          {this.selectOptions.map((option) => (
            <Select.Option class={styles.option} key={option.id} title={option.value} value={option.id} option={option}>
              <Tooltip title={getHTMLText(option.label)} mouseEnterDelay={getHTMLText(option.label).length > 48 ? 0 : 9999}>
                <div class={styles.optionText}>
                  <Icon v-show={this.showHistory} type="icon-shenhe" class={styles.icon} />
                  {!this.showHistory ? (
                    <CompanyLogo
                      hoverable={false}
                      id={option.id}
                      name={option.ShortName}
                      src={option.LogoUrl || option.imageUrl}
                      size={'22px'}
                      class={styles.avatar}
                    />
                  ) : null}

                  <span domPropsInnerHTML={option.label} />
                </div>
              </Tooltip>
              <div v-show={option.reason} class={styles.optionExtra}>
                <span class={styles.reason}>{option.reason}</span>
              </div>
            </Select.Option>
          ))}
          {this.showHistory && this.history.length > 0 ? (
            <Select.Option key="actions" disabled class={styles.action}>
              <div class={styles.item} onClick={this.handleClearHistory}>
                <Icon class={styles.icon} type="icon-a-shanchuxian" />
                <span>删除历史</span>
              </div>
            </Select.Option>
          ) : null}
        </template>

        <Input.Search maxLength={100} size={props.size} enterButton={props.enterButton} onSearch={this.emitSearch}>
          <Icon
            ref="clear"
            type={this.clearIcon}
            slot="suffix"
            class={styles.clearIcon}
            v-show={this.value}
            onClick={(e) => this.clearClick(e)}
          />
          {/* 批量查询图标 */}
          {this.hasBulkSearch ? (
            <Tooltip placement="bottom" slot="suffix">
              <template slot="title">
                <span>{this.suffixPlaceholder}</span>
              </template>
              <Icon ref="suffix" type={this.suffixIcon} class={styles.suffixIcon} onClick={(e) => this.suffixClick(e)} />
            </Tooltip>
          ) : null}
        </Input.Search>
      </AutoComplete>
    );
  },
});

export default SuggestionInput;
