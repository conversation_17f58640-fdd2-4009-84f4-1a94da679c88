// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`SuggestionInput > render 1`] = `<aselect-stub size="default" transitionname="slide-up" choicetransitionname="zoom" placeholder="" dropdownclassname="dropdown" autofocus="true" getpopupcontainer="[Function]" value="VALUE" mode="SECRET_COMBOBOX_MODE_DO_NOT_USE" optionlabelprop="title" getinputelement="[Function]" class="ant-select-show-search ant-select-auto-complete ant-select-empty"></aselect-stub>`;
