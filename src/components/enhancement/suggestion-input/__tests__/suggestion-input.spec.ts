import { shallowMount, mount } from '@vue/test-utils';

import SuggestionInput from '..';

const ICONOSST = {
  clearIcon: 'clear',
  suffixIcon: 'suffix',
};

describe('SuggestionInput', () => {
  test('render', () => {
    const wrapper = shallowMount(SuggestionInput, {
      propsData: {
        value: 'VALUE',
        ...ICONOSST,
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('props: value', () => {
    const wrapper = mount(SuggestionInput, {
      propsData: {
        value: 'VALUE',
        ...ICONOSST,
      },
    });

    const input = wrapper.findComponent({ name: 'AInput' });
    expect(input.exists()).toBe(true);
    expect(input.vm.$props.value).toBe('VALUE');
  });

  test('props: hasBulkSearch', () => {
    const wrapper = mount(SuggestionInput, {
      propsData: {
        hasBulkSearch: false,
        ...ICONOSST,
      },
    });
    expect(wrapper.text()).not.toContain('批量排查');
  });

  test('events: clear', async () => {
    const wrapper = mount(SuggestionInput, {
      propsData: {
        value: 'VALUE',
        ...ICONOSST,
      },
    });
    const clearButton = wrapper.findComponent({ ref: 'clear' });
    expect(clearButton.exists()).toBe(true);
    // 点击清空按钮
    await clearButton.trigger('click');
    // 期望触发 clear 事件
    expect(wrapper.emitted('clear')).toBeTruthy();
  });

  test('events: suffixClick', async () => {
    const wrapper = mount(SuggestionInput, {
      propsData: {
        value: 'VALUE',
        hasBulkSearch: true,
        ...ICONOSST,
      },
    });
    const suffixClearButton = wrapper.findComponent({ ref: 'suffix' });
    expect(suffixClearButton.exists()).toBe(true);
    // 点击清空按钮
    await suffixClearButton.trigger('click');
    // 期望触发 suffixClear 事件
    expect(wrapper.emitted('suffixClick')).toBeTruthy();
  });

  test('internal: history', async () => {
    const wrapper = mount(SuggestionInput, {
      propsData: {
        value: 'VALUE',
        storageKey: '__SEARCH_HISTORY__',
        hasBulkSearch: true,
        ...ICONOSST,
      },
    });
    const input = wrapper.findComponent({ name: 'AInput' });
    expect(input.exists()).toBe(true);
  });
});
