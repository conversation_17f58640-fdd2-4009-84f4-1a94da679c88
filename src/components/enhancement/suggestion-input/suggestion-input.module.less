@import '@/styles/token.less';

.dropdown {
  text-align: initial;

  :global(.ant-select-dropdown) {
    &-content {
      // NOTE: 无法覆盖 `SelectTrigger` 的 `BUILT_IN_PLACEMENTS` (dom-align)
      // 通过覆盖 CSS 的间接方案
      transform: translateZ(0) translateY(-4px) !important;
      padding: 15px;
      background: #fff;
      border-top: 1px solid #eee;
    }

    &-menu {
      max-height: unset;
      // background: red;
      background: #f8fbfe;
      padding-top: 0;
      padding-bottom: 0;
    }

    &-menu {
      &-item {
        &-active {
          background: #f2f8fe;
          color: #128bed;

          em {
            color: #128bed;
          }

          .reason {
            background: @qcc-color-blue-300;
            color: @qcc-color-blue-500;
          }
        }
      }
    }
  }

  &.history {
    :global(.ant-select-dropdown) {
      &-content {
        padding-bottom: 0;
      }
    }
  }
}

.option {
  font-size: 14px;
  line-height: 22px;
  padding: 10px;
  display: flex;
  justify-content: space-between;

  .optionText {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  em {
    font-weight: normal;
    color: @qcc-color-red-500;
  }

  &:hover {
    background: #f8fbfe !important;
    color: @qcc-color-blue-500;

    em {
      color: @qcc-color-blue-500;
      font-weight: normal;
    }

    .reason {
      background: @qcc-color-blue-300;
      color: @qcc-color-blue-500;
    }
  }

  .icon {
    color: @qcc-color-black-200;
    margin-right: 5px;
    font-size: 16px;
    transform: translateY(2px);
  }

  .avatar {
    margin-right: 5px;
    border: 1px solid #eee;
    border-radius: 4px;
    display: inline-block;
  }

  .reason {
    font-weight: normal;
    background-color: #f6f6f6;
    color: #999;
    white-space: nowrap;
    // vertical-align: bottom;
    font-size: 12px;
    line-height: 18px;
    padding: 2px 6px;
    border-radius: 2px;
  }
}

.action {
  cursor: default !important;
  background: #fff;
  padding: 15px 0;
  gap: 20px;
  display: flex;
  align-items: center;

  &:global(.ant-select-dropdown-menu-item:hover) {
    background-color: #FFF !important;
    color: #999 !important;
  }

  .item {
    display: flex;
    align-items: center;
    line-height: 22px;
    cursor: pointer;
    color: #999;

    &:hover {
      color: #128bed;

      svg {
        color: inherit;
      }
    }

    svg {
      margin-right: 5px;
      color: #bbb;
    }
  }
}

.suffixIcon {
  font-size: 16px;
  color: #bbb !important;
  margin-left: 12px;

  &:hover {
    color: #128bed;
  }
}

.clearIcon {
  font-size: 14px;
  color: #d8d8d8;

  &:hover {
    color: #bbb;
  }

  :global {
    .ant-select-selection__clear {
      right: 165px;
      margin-right: 10px;
    }

    .ant-btn > i,
    .ant-btn > span {
      font-weight: bold;
    }
  }
}
