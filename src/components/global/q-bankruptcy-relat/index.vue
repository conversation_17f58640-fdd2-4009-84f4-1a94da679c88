<template>
  <div id="app-bankruptcy-relat" v-if="noSearchId">
    <div class="tcaption" style="color: #333">关联公告</div>
    <table class="ntable ntable-odd">
      <tr>
        <th class="tx">序号</th>
        <th>公告标题</th>
        <th>公告类型</th>
        <th>公开日期</th>
      </tr>
      <tr v-for="(item, index) in relatedList" :key="index">
        <td class="tx">{{ index + 1 }}</td>
        <td>
          <q-link :href="`/embed/bankruptcydetail?id=${item.Id}&title=${item.Title}`">
            <span v-html="item.Title || '-'"></span>
          </q-link>
        </td>
        <td width="15%" align="center" class="q-text-center" v-html="item.CaseType || '-'"></td>
        <td width="20%" align="center" class="q-text-center">
          {{ item.RiskDate | dateformat('YYYY-MM-DD') }}
        </td>
      </tr>
    </table>
  </div>
</template>
<script>
export default {
  name: 'qBankruptcyRelat',
  props: {
    searchParams: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      casesList: {},
      noSearchId: false,
      relatedList: [],
    };
  },
  mounted() {
    // this.getBankruptcyDetail()
    this.noNeedRequest();
  },
  methods: {
    noNeedRequest() {
      if (this.searchParams.RelatedAnnouncementList && this.searchParams.RelatedAnnouncementList.length > 0) {
        this.relatedList = this.searchParams.RelatedAnnouncementList;
        this.noSearchId = true;
      } else {
        this.noSearchId = false;
      }
    },
    getBankruptcyDetail() {
      if (this.searchParams.RelatedAnnouncementList && this.searchParams.RelatedAnnouncementList.length !== 0) {
        this.$service.dimensionDetail
          .announcementDetail({
            id: this.searchParams.RelatedAnnouncementList[0].Id,
          })
          .then((res) => {
            if (res.Status === 200) {
              this.casesList = res.Result;
              this.noSearchId = true;
            } else {
              this.noSearchId = false;
            }
          });
      }
    },
  },
};
</script>
<style lang="less" src="./style.less" scoped></style>
