.container {
  // display: inline-flex;

  .copy {
    cursor: pointer;
    position: relative;
  }

  .copyInput {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    z-index: -10;
    width: 0;
    height: 0;
  }

  .baseCopy {
    color: #128bed;
    display: inline-block;
    line-height: 22px;
    font-size: 14px;
    border-radius: 2px;
    width: 56px;
    padding: 0 5px;

    div {
      display: inline;
    }

    .icon {
      color: #128bed;
      font-size: 14px;
      margin-left: 0;
      margin-right: 4px;
    }

    &:hover {
      color: #0069bf;

      .icon {
        color: #0069bf;
      }

      :global {
        .copyContent {
          background-color: #e5f2fd;
        }
      }
    }
  }
}

// 复制按钮的显示隐藏
.containerHover {
  position: relative;

  .copyButtonItem {
    position: absolute;
    top: 0;
    transform: translateY(0);
    display: none;
    vertical-align: top;
    border-radius: 2px;
    // padding-left: 0px;
    padding-left: 5px;

    .content {
      background: #fff;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .1);
    }

    &.transparent {
      .content {
        background: unset;
        box-shadow: unset;
      }
    }
  }

  &:hover {
    .copyButtonItem {
      // opacity: 1;
      display: inline-block;
    }

    :global {
      .copyContent {
        background-color: #e5f2fd;
      }
    }
  }
}
