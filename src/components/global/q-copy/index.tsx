import { Tooltip, message } from 'ant-design-vue';
import { defineComponent } from 'vue';

import { copyToClipboard } from '@/utils';

import styles from './q-copy.module.less';

const QCopy = defineComponent({
  name: 'QCopy',

  props: {
    copyValue: {
      type: String,
      default: '',
    },
    text: {
      type: String,
      default: '复制',
    },
    visible: {
      type: String,
      default: 'hover',
    },
    copyButtonStyle: {
      type: Object,
      default: () => ({}),
    },
    /** 是否背景透明 */
    transparent: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      leftWidth: 0,
    };
  },

  created() {
    this.$nextTick(() => {
      this.leftWidth = (this.$refs.contain as any).offsetWidth;
    });
  },

  methods: {
    onCopy(copyValue) {
      const that = this as any;
      try {
        const text = copyValue || that.copyValue;
        copyToClipboard(text);
        message.success('复制成功');
      } catch (error) {
        message.error('复制失败');
      }
    },
  },

  render() {
    const that = this as any;
    return (
      <div
        class={{
          [styles.container]: true,
          [styles.containerHover]: this.visible === 'hover',
        }}
      >
        <Tooltip title={this.$attrs.tooltip}>
          <div ref="contain">{this.$slots.contain}</div>
        </Tooltip>
        {this.$slots.btn ? (
          this.$slots.btn
        ) : (
          <span
            class={{
              [styles.copy]: true,
              [styles.copyButtonItem]: true,
              [styles.transparent]: this.transparent,
              'copy-btn-item': true,
            }}
            style={{ ...this.copyButtonStyle, left: `${this.leftWidth}px` }}
          >
            <div class={styles.content}>
              {this.$slots.default || (
                <div
                  onClick={(e) => {
                    e.stopPropagation();
                    that.onCopy();
                  }}
                  class={{
                    [styles.baseCopy]: true,
                    'copy-btn': true,
                  }}
                >
                  <q-icon class={[styles.icon]} type="icon-gongyingshangchouqu" />
                  <div>{this.text}</div>
                </div>
              )}
            </div>
          </span>
        )}
      </div>
    );
  },
});

export default QCopy;
