import { shallowMount } from '@vue/test-utils';
import { message } from 'ant-design-vue';

import { copyToClipboard } from '@/utils';

import QCopy from '..';

vi.mock('@/utils', () => ({
  copyToClipboard: vi.fn(),
}));

vi.mock('ant-design-vue', () => ({
  Tooltip: vi.fn(),
  message: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe('QCopy', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('应该在复制成功时显示成功消息', async () => {
    const wrapper = shallowMount(QCopy, {
      propsData: {
        copyValue: '测试文本',
      },
    });
    wrapper.vm.$refs.contain = { offsetWidth: 100 } as any;

    vi.mocked<any>(copyToClipboard).mockImplementation(() => true);

    const btn = wrapper.find('.copy-btn');
    await btn.trigger('click');

    expect(copyToClipboard).toHaveBeenCalledWith('测试文本');
    expect(message.success).toHaveBeenCalledWith('复制成功');
  });

  it('应该在复制失败时显示错误消息', async () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => ({}));
    const wrapper = shallowMount(QCopy, {
      propsData: {
        copyValue: '测试文本',
      },
    });

    vi.mocked<any>(copyToClipboard).mockImplementation(() => {
      throw new Error('复制失败');
    });

    const btn = wrapper.find('.copy-btn');
    await btn.trigger('click');

    expect(copyToClipboard).toHaveBeenCalledWith('测试文本');
    expect(message.error).toHaveBeenCalledWith('复制失败');
    consoleSpy.mockRestore();
  });

  it('btn插槽', async () => {
    const wrapper = shallowMount(QCopy, {
      propsData: {
        copyValue: '测试文本',
      },
      slots: {
        default: '<span class="custom-slot">自定义内容</span>',
        btn: '<button class="custom-btn">自定义按钮</button>',
      },
    });
    wrapper.vm.$refs.contain = { offsetWidth: 100 } as any;
    expect(wrapper.find('.custom-btn').exists()).toBeTruthy();
  });

  it('默认插槽', async () => {
    const wrapper = shallowMount(QCopy, {
      propsData: {
        copyValue: '测试文本',
      },
      slots: {
        default: '<span class="custom-slot">自定义内容</span>',
      },
    });
    wrapper.vm.$refs.contain = { offsetWidth: 100 } as any;
    expect(wrapper.find('.custom-slot').exists()).toBeTruthy();
  });
});
