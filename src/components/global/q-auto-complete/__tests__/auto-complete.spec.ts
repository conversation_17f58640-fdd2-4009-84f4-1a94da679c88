import { shallowMount } from '@vue/test-utils';
import { AutoComplete as AAutoComplete, Select } from 'ant-design-vue';

import { flushPromises } from '@/test-utils/flush-promises';

import AutoComplete from '..';

describe('AutoComplete', () => {
  test('render', () => {
    const wrapper = shallowMount<InstanceType<typeof AutoComplete>>(AutoComplete);
    expect(wrapper).toMatchSnapshot();
  });

  test('props: showSuffix', () => {
    const wrapper = shallowMount<InstanceType<typeof AutoComplete>>(AutoComplete, {
      propsData: {
        showSuffix: true,
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('props: value', () => {
    const wrapper = shallowMount<InstanceType<typeof AutoComplete>>(AutoComplete, {
      propsData: {
        value: 'VALUE',
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('props: remote', async () => {
    const mockRequest = vi.fn().mockResolvedValue(['OPTION_A', 'OPTION_B']);

    const wrapper = shallowMount<InstanceType<typeof AutoComplete>>(AutoComplete, {
      propsData: {
        value: 'VALUE',
        remote: mockRequest,
      },
    });
    wrapper.findComponent(AAutoComplete).vm.$emit('focus');
    expect(mockRequest).toHaveBeenCalled();
    await flushPromises();
    expect(wrapper.findAllComponents(Select.Option)).toHaveLength(2);
  });

  test('props: search', async () => {
    const wrapper = shallowMount<InstanceType<typeof AutoComplete>>(AutoComplete, {
      propsData: {
        value: 'VALUE',
        remote: vi.fn().mockRejectedValue([]),
      },
    });
    wrapper.findComponent(AAutoComplete).vm.$emit('search', 'SEARCH_KEYWORD');
    expect(wrapper.emitted('change')).toEqual([['SEARCH_KEYWORD']]);
  });

  test('events: change', async () => {
    const wrapper = shallowMount<InstanceType<typeof AutoComplete>>(AutoComplete, {
      propsData: {
        value: 'VALUE',
      },
    });
    wrapper.findComponent(AAutoComplete).vm.$emit('select', 'SELECT_EVENT');
    expect(wrapper.emitted('change')).toEqual([['SELECT_EVENT']]);
  });

  test('events: blur', async () => {
    const wrapper = shallowMount<InstanceType<typeof AutoComplete>>(AutoComplete, {
      propsData: {
        value: 'VALUE',
      },
    });
    wrapper.findComponent(AAutoComplete).vm.$emit('blur');
    expect(wrapper.emitted('blur').at(0)).toEqual(['VALUE']);
  });
});
