// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`AutoComplete > props: showSuffix 1`] = `<aselect-stub transitionname="slide-up" choicetransitionname="zoom" allowclear="true" placeholder="" defaultactivefirstoption="true" mode="SECRET_COMBOBOX_MODE_DO_NOT_USE" optionlabelprop="label" getinputelement="[Function]" class="ant-select-show-search ant-select-auto-complete container"></aselect-stub>`;

exports[`AutoComplete > props: value 1`] = `<aselect-stub transitionname="slide-up" choicetransitionname="zoom" allowclear="true" placeholder="" defaultactivefirstoption="true" value="VALUE" mode="SECRET_COMBOBOX_MODE_DO_NOT_USE" optionlabelprop="label" getinputelement="[Function]" class="ant-select-show-search ant-select-auto-complete container"></aselect-stub>`;

exports[`AutoComplete > render 1`] = `<aselect-stub transitionname="slide-up" choicetransitionname="zoom" allowclear="true" placeholder="" defaultactivefirstoption="true" mode="SECRET_COMBOBOX_MODE_DO_NOT_USE" optionlabelprop="label" getinputelement="[Function]" class="ant-select-show-search ant-select-auto-complete container"></aselect-stub>`;
