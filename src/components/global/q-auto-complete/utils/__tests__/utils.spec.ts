import { htmlDecode, nameToHtml } from '..';

describe('htmlDecode', () => {
  test('should decode HTML entities to their respective characters', () => {
    const input = 'Hello, &lt;strong&gt;world&lt;/strong&gt;!';
    const expectedOutput = 'Hello, <strong>world</strong>!';
    const output = htmlDecode(input);
    expect(output).toBe(expectedOutput);
  });

  test('should return the same string if no HTML entities are present', () => {
    const input = 'Hello, world!';
    const expectedOutput = 'Hello, world!';
    const output = htmlDecode(input);
    expect(output).toBe(expectedOutput);
  });

  test('should handle empty strings', () => {
    const input = '';
    const expectedOutput = '';
    const output = htmlDecode(input);
    expect(output).toBe(expectedOutput);
  });
});

describe('nameToHtml', () => {
  const DEPARTMENT_SPLIT_CHARACTER = '|';
  const DEPARTMENT_JOIN_CHARACTER = ', ';

  test('should replace department split character with join character', () => {
    const name = '<PERSON>|<PERSON>';
    const expectedResult = '<PERSON>|<PERSON>';
    const result = nameToHtml(name);
    expect(result).toBe(expectedResult);
  });

  test('should return the same name if no split character found', () => {
    const name = 'John Doe';
    const expectedResult = 'John Doe';
    const result = nameToHtml(name);
    expect(result).toBe(expectedResult);
  });

  test('should return the same name if input is not a string', () => {
    const name = 12345;
    const expectedResult = 12345;
    const result = nameToHtml(name as any);
    expect(result).toBe(expectedResult);
  });

  test('should handle HTML encoded characters', () => {
    const name = 'John &lt;Doe&gt;|Jane &amp;Smith';
    const expectedResult = 'John <Doe>|Jane &Smith';
    const result = nameToHtml(name);
    expect(result).toBe(expectedResult);
  });

  test('should handle empty string', () => {
    const name = '';
    const expectedResult = '';
    const result = nameToHtml(name);
    expect(result).toBe(expectedResult);
  });
});
