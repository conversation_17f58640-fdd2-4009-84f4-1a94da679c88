// 分割客户和部门的特殊字符
export const DEPARTMENT_SPLIT_CHARACTER = '☢';
export const DEPARTMENT_JOIN_CHARACTER = '-';

export const htmlDecode = (input) => {
  const doc = new DOMParser().parseFromString(input, 'text/html');
  return doc.documentElement.textContent;
};

export const nameToHtml = (name = '') => {
  try {
    if (typeof name === 'string') {
      return htmlDecode(name?.replace(DEPARTMENT_SPLIT_CHARACTER, DEPARTMENT_JOIN_CHARACTER));
    }
    return name;
  } catch (e) {
    return name;
  }
};
