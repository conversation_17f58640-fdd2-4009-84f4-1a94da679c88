.container{
  .name {
    em {
      color: #1677fe;
    }
  }
  
  .arrow{
    position: absolute;
    top: 50%;
    right: 0;
    transition: transform 0.3s;
    font-size: 12px;
    transform: scaleX(1.34) translateY(-5px) !important;
    color: rgba(0, 0, 0, 0.25);
  }

  &:hover{
    .arrow{
      color: #128BED;
    }
  }
}

.valued{
  :global{
    .ant-select-selection__clear{
      right: 30px;
    }
  }
}

:global{
  .ant-select-focused{
      .coustomArrow{
        color: #128BED;
        transition: transform 0.3s;
        transform:  scaleX(1.34) rotate(180deg) translateY(5px) !important
      }
  }
}

