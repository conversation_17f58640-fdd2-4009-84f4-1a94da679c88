import _ from 'lodash';
import { AutoComplete as AAutoComplete, Icon, Input, Select } from 'ant-design-vue';
import { defineComponent } from 'vue';

import { nameToHtml } from './utils';
import styles from './auto-complete.module.less';

const AutoComplete = defineComponent({
  name: 'AutoComplete',

  props: {
    value: {
      type: String,
      default: undefined,
    },
    placeholder: {
      type: String,
      default: '请输入或选择...',
    },
    showSuffix: {
      type: Boolean,
      default: false,
    },
    allowClear: {
      type: Boolean,
      default: true,
    },
    remote: {
      type: Function,
      default: undefined,
    },
  },

  data() {
    return {
      options: [],
    };
  },

  created() {
    (this as any).request = _.debounce((this as any).request, 300, {
      leading: true,
      trailing: true,
    }) as any;
  },

  methods: {
    async request(keywords?: string): Promise<any> {
      // `remote` 与 `dataSource` 两者只取其一
      try {
        this.options = await (this.remote as any)(keywords || ' ');
      } catch (err) {
        this.options = [];
      }
    },

    handleSearch(keywords: string) {
      (this as any).request(keywords);
      (this as any).handleChange(keywords);
    },

    handleFocus() {
      // 修复输入框有值时， 下拉选项不出现的问题
      if (!this.options.length) {
        (this as any).request();
      }
    },
    handleChange(keywords: string) {
      this.$emit('change', keywords);
    },
    handleBlur() {
      this.$emit('blur', this.value);
    },
  },

  render() {
    const autoCompleteData = {
      attrs: this.$attrs,
      props: {
        ...this.$props,
        optionLabelProp: 'label',
        // dataSource: this.options,
      },
      on: {
        search: (this as any).handleSearch,
        select: (this as any).handleChange,
        blur: (this as any).handleBlur,
        focus: (this as any).handleFocus,
      },
    };
    return (
      <AAutoComplete {...autoCompleteData} class={[styles.container, this.showSuffix && this.value ? styles.valued : '']}>
        <template slot="dataSource">
          {this.options.map((label) => {
            const name = nameToHtml(label);
            return (
              <Select.Option key={label} label={name}>
                <span class={styles.name} domPropsInnerHTML={name}></span>
              </Select.Option>
            );
          })}
        </template>
        <Input autoComplete="new-password">
          {this.showSuffix ? <Icon slot="suffix" type="down" class={[styles.arrow, 'coustomArrow']} /> : null}
        </Input>
      </AAutoComplete>
    );
  },
});
export default AutoComplete;
