/**
 * FIXME: 老旧组件改造
 */
import { VNode, CreateElement, defineComponent, PropType } from 'vue';
import _ from 'lodash';

import DropdownButton from '@/components/dropdown-button';

import Checkbox from './components/filter-checkbox';
import Radio from './components/filter-radio';
import { Group, GroupsGroup, BasicGroupItem, BreakLineGroup } from './interface';
import styles from './q-filter.module.less';

type RefArgs = {
  label: string;
  active: boolean;
  visible: boolean;
  placeholder?: string;
  nodes?: Array<any>;
};

const getDefaultRowHeight = (type: string) => {
  const defaultHeight = 20;
  const map = {
    button: 24,
    'button-multiple': 20,
  };

  return map[type] || defaultHeight;
};

const DEFAULT_BLOCK_TYPES = ['groups', 'button', 'button-multiple', 'checkbox'];

const QFilter = defineComponent({
  name: 'QFilter',
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    labelWidth: {
      type: String,
      default: 'auto',
    },
    groups: {
      type: Array as PropType<Group[]>,
      required: true,
    },
    value: {
      type: Object,
    },
    getPopupContainer: {
      type: Function,
    },
    renderLabel: {
      type: Function as PropType<(group: Group, args: RefArgs) => string>,
    },
    renderExtra: {
      type: Function as PropType<(h: CreateElement, group: Exclude<Group, BreakLineGroup>) => VNode>,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    // select下拉框边框
    checkSelectBorder: {
      type: Boolean,
      default: false,
    },
    // 是否有search，有的话需要align-center
    showSearch: {
      type: Boolean,
      default: true,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    theme: {
      type: String as PropType<'filled' | 'outlined'>,
      default: 'outlined',
    },
  },
  data() {
    return {
      collapseState: {},
    };
  },
  methods: {
    // common-search-filter点击重置筛选时清空所有筛选项中的搜索框
    resetKeywords() {
      const refs = this.$refs as any;
      if (refs.select) {
        refs.select.forEach((el) => {
          el.clearSearchInput?.();
        });
      }
    },
    callChange(group: BasicGroupItem, value: any) {
      const nextValue = { ...this.value };
      if (value === undefined) {
        _.unset(nextValue, group.field);
      } else {
        _.set(nextValue, group.field, value);
      }

      if (
        [nextValue, this.value].every((v) => v === undefined || (_.isPlainObject(v) && _.isEmpty(v))) || // 值没变，都是空
        _.isEqual(this.value, nextValue)
      ) {
        return;
      }
      this.$emit('change', nextValue, group);
      if (!value?.length) {
        this.$emit('afterChange', group);
      }

      setTimeout(() => {
        const refs = this.$refs as any;
        let nodes: any = [];
        if (refs.select) {
          nodes = [...nodes, ...refs.select];
        }
        if (refs.cascader) {
          nodes = [...nodes, ...refs.cascader];
        }

        nodes.forEach((node) => {
          const align = _.get(node, 'forcePopupAlign');

          if (_.isFunction(align)) {
            align();
          }
        });
      }, 0);
    },
    detectCollapsed() {
      this.groups.forEach((group) => {
        if (group.type === 'breakLine') {
          return;
        }
        const rowHeight = group.rowHeight || getDefaultRowHeight(group.type);

        const key = group.field;

        if (group.autoCollapsed && !_.has(this.collapseState, key)) {
          const ref = this.$refs[key] as HTMLElement;

          if (ref && ref.offsetHeight > rowHeight + 5) {
            this.$set(this.collapseState, key, true);
          }
        }
      });
    },
    toggleRowCollapsed(key: string) {
      this.collapseState[key] = !this.collapseState[key];
    },
    getGroupLayout(group?: Group) {
      if (!group || group.type === 'breakLine') {
        return '';
      }
      return group.layout || (DEFAULT_BLOCK_TYPES.includes(group.type) ? 'block' : 'inline');
    },
    renderLabelRef(group: BasicGroupItem, args: RefArgs) {
      const { label, active, visible, placeholder } = args;
      let upLabel = this.renderLabel ? this.renderLabel(group, args) : null;
      const prefix = group.placeholder ? `${group.placeholder} ` : '';
      const renderLabelContent = () => {
        if (group.type === 'single' && group.meta?.showValue) {
          return <span>{(active ? `${prefix}${label}` : placeholder) || group.label || '请选择'}</span>;
        }
        if (group.type === 'single') {
          return <span>{label || upLabel || (active ? `${prefix}` : placeholder) || group.label || '请选择'}</span>;
        }
        if (group.type === 'cascader') {
          // 为了解决重新对cascader选项赋值后，回显内容不对的问题
          if (
            group.field === 'region' &&
            this.value?.[group.field] &&
            this.value?.[group.field].length > 1 &&
            args?.nodes?.[0]?.path !== this.value?.[group.field]
          ) {
            if (args?.nodes?.[0]) {
              upLabel =
                this.value?.[group.field].length === 2
                  ? `${args?.nodes[0].children[0].label}`
                  : `${args?.nodes[0].children[0].children[0].label}`;
            }
          }
          return <span>{upLabel || (active ? `${label}` : placeholder) || group.label || '请选择'}</span>;
        }
        return <span>{upLabel || (active ? `${prefix}${label}` : placeholder) || group.label || '请选择'}</span>;
      };
      return (
        <div
          class={{
            [styles.reference]: true,
            [styles.active]: active,
            [styles.open]: visible,
            [styles.withTip]: !!group.tip,
          }}
          role="button"
        >
          {renderLabelContent()}
          <q-icon class={styles.icon} type="icon-a-shixinxia1x1" />
          {group.tip ? (
            <a-tooltip placement="bottom" getPopupContainer={this.getPopupContainer}>
              <a-icon type="info-circle" class={styles.tipIcon} />
              <div slot="title" class={styles.tip} domPropsInnerHTML={group.tip}></div>
            </a-tooltip>
          ) : null}
        </div>
      );
    },
    renderTypeBasic(group: BasicGroupItem) {
      const holder = group?.placeholder ?? '不限';
      switch (group.type) {
        case 'cascader':
        case 'cascader-multiple':
          return (
            <q-cascader
              // disabled
              ref="cascader"
              refInFor
              class={styles.select}
              value={_.get(this.value, group.field)}
              multiple={group.type === 'cascader-multiple'}
              options={group.options}
              loading={this.isLoading}
              expandTrigger={'hover'}
              scopedSlots={{
                reference: (e) => (this as any).renderLabelRef(group, { ...e, placeholder: holder }),
              }}
              getPopupContainer={this.getPopupContainer}
              {...{
                props: group.meta,
              }}
              onVisibleChange={(val) => {
                if (val) {
                  this.$emit('hover', group);
                }
              }}
              onChange={(e) => (this as any).callChange(group, e)}
            />
          );
        case 'button':
        case 'button-multiple':
          return (
            <q-button-select
              // disabled
              value={_.get(this.value, group.field)}
              multiple={group.type === 'button-multiple'}
              options={group.options}
              extra={group.extra}
              custom={group.custom}
              getPopupContainer={this.getPopupContainer}
              onChange={(e) => (this as any).callChange(group, e)}
              customRender={group.customRender}
              {...{
                props: group.meta,
              }}
            />
          );
        case 'single':
        case 'multiple':
          return (
            <q-select
              // disabled
              ref="select"
              refInFor
              class={[
                styles.select,
                this.checkSelectBorder ? styles.selectBorder : '',
                this.checkSelectBorder && _.get(this.value, group.field) ? styles.selectBorderValued : '',
              ]}
              value={_.get(this.value, group.field)}
              multiple={group.type === 'multiple'}
              multi-column={group.props?.multiColumn}
              options={group.options}
              loading={this.isLoading}
              renderReferenceLabel={(value, option) => {
                if (option?.custom) {
                  return value;
                }
                if (!value) {
                  return '';
                }
                if (typeof value === 'number') {
                  return ` (${String(value).trim()})`;
                }
                return value;
              }}
              scopedSlots={{
                reference: (e) => (this as any).renderLabelRef(group, { ...e, placeholder: holder }),
              }}
              custom={group.custom}
              getPopupContainer={this.getPopupContainer}
              onChange={(e) => (this as any).callChange(group, e)}
              {...{
                props: group.meta,
              }}
              onVisibleChange={async (visible) => {
                if (visible) {
                  this.$emit('hover', group);
                }
              }}
            />
          );
        case 'checkbox': {
          return (
            <Checkbox
              // disabled
              {...{
                props: group.meta,
              }}
              onChange={(e) => (this as any).callChange(group, e)}
              values={_.get(this.value, group.field)}
              options={group.options}
            ></Checkbox>
          );
        }
        case 'radio':
          return (
            <Radio
              // disabled
              class={[styles.radio]}
              value={_.get(this.value, group.field)}
              options={group.options}
              onChange={(e) => (this as any).callChange(group, e)}
            />
          );
        default:
          return null;
      }
    },
    renderTypeGroups(group: GroupsGroup) {
      return group.children.map((child) =>
        (this as any).renderTypeBasic({
          ...child,
          placeholder: child.placeholder || child.label,
        })
      );
    },
    renderGroupsItem(group: GroupsGroup, showLabel = true) {
      return group.children.map((child) => {
        return (
          <div class={styles.foldGroupItem}>
            {showLabel ? <div class={styles.label}>{child.label}</div> : null}
            {(this as any).renderTypeBasic({
              ...child,
              placeholder: child.placeholder || child.label,
            })}
          </div>
        );
      });
    },
    renderGroup(group: Group, index: number, arr): VNode {
      if (group.type === 'breakLine') {
        return <br key={`br_${index}`} />;
      }
      if (group.type === 'fold-groups') {
        const isSelected = group.children.some((child) => {
          const val = _.get(this.value, child.field);
          return !_.isNil(val);
        });
        return (
          <div
            class={{
              [styles.dropdown]: true,
              [styles[this.theme]]: true,
            }}
          >
            <DropdownButton
              class={{ active: isSelected }}
              text={group.label}
              onVisibleChange={(val) => {
                if (val) {
                  this.$emit('hover', group);
                }
              }}
            >
              <template slot="overlay">
                <div class={styles.foldGroupWrap}>{(this as any).renderGroupsItem(group)}</div>
              </template>
            </DropdownButton>
          </div>
        );
      }
      const { label, field, type } = group;
      const layout = (this as any).getGroupLayout(group);
      const extra = this.renderExtra ? this.renderExtra(this.$createElement, group) : null;
      const collapsedEnabled = _.has(this.collapseState, field);
      const collapsed = this.collapseState[field];
      const rowHeight = group.rowHeight || getDefaultRowHeight(group.type);
      const isLastGroup = index === arr.length - 1;
      return (
        <div
          ref={field}
          key={field}
          class={{
            [styles.group]: true,
            [styles.inline]: layout === 'inline',
            [styles.block]: layout === 'block',
            [`q-filter-group--${field}`]: true,
            'q-filter-group': true,
            [styles.lastGroup]: isLastGroup,
            [styles[this.theme]]: true,
          }}
          style={group.meta?.style}
        >
          {label ? (
            <div
              class={[styles.label, 'q-filter-label']}
              style={{
                width: group.labelWidth || this.labelWidth,
                paddingTop: `${(rowHeight - 20) / 2}px`,
              }}
            >
              {label}
            </div>
          ) : null}
          <div
            class={{
              [styles.wrap]: true,
              [styles.wrapGroups]: type === 'groups',
              [styles.collapsed]: collapsed,
              [`q-filter-wrap--${field}`]: true,
              [styles.disabled]: this.disabled,
            }}
            style={
              collapsed
                ? {
                    height: `${rowHeight}px`,
                  }
                : undefined
            }
          >
            {isLastGroup && this.$slots['first-addons']
              ? this.$createElement('div', { style: { display: 'inline-block' } }, this.$slots['first-addons'])
              : null}
            {type === 'groups'
              ? (this as any).renderTypeGroups(group as GroupsGroup)
              : (this as any).renderTypeBasic(group as BasicGroupItem)}
            {isLastGroup && this.$slots['last-addons']
              ? this.$createElement('div', { style: { display: 'inline-block' } }, this.$slots['last-addons'])
              : null}
          </div>
          {collapsedEnabled ? (
            <div class={styles.collapse} onClick={() => (this as any).toggleRowCollapsed(field)}>
              <a-icon type={collapsed ? 'down' : 'up'} />
              更多
            </div>
          ) : null}
          {isLastGroup && this.$slots['last-extra']
            ? this.$createElement('div', { style: { display: 'inline-block' } }, this.$slots['last-extra'])
            : null}
          {extra ? <div class={styles.extra}>{extra}</div> : null}
        </div>
      );
    },
  },
  render() {
    return (
      <div
        class={{
          [styles.root]: true,
          [styles.offset]:
            (this as any).getGroupLayout(this.groups[0]) === 'inline' || DEFAULT_BLOCK_TYPES.includes(_.get(this.groups, [0, 'type'])),
          'q-filter__root': true,
        }}
      >
        {this.groups.map((this as any).renderGroup)}
      </div>
    );
  },
  mounted() {
    (this as any).detectCollapsed();
  },
});

export default QFilter;
