import { shallowMount } from '@vue/test-utils';
import { Radio } from 'ant-design-vue';

import FilterRadio from '../radio';

describe('FilterRadio', () => {
  let source;
  let wrapper;
  let innerComponent;

  beforeEach(() => {
    source = {
      value: 1,
      options: [
        {
          value: 1,
          label: '1',
        },
        {
          value: 2,
          label: '2',
        },
      ],
    };

    wrapper = shallowMount(FilterRadio, {
      propsData: {
        value: source.value,
        options: source.options,
      },
    });

    innerComponent = wrapper.findComponent(Radio.Group);
  });

  test('props: values', () => {
    // Props
    const innerProps = innerComponent.props();
    expect(innerProps.options).toEqual(
      source.options.map(({ value, label }) => ({
        value: JSON.stringify(value),
        label,
      }))
    );
    expect(innerProps.value).toEqual(JSON.stringify(source.value));
  });

  test('events: change', () => {
    // Event
    innerComponent.vm.$emit('change', {
      target: {
        value: JSON.stringify(2),
      },
    });
    expect(wrapper.emitted('change')).toStrictEqual([[2]]);
  });
});
