import _ from 'lodash';
import { Radio } from 'ant-design-vue';
import { defineComponent, PropType } from 'vue';

import { SelectOption } from '@/interfaces/data.interface';

const FilterRadio = defineComponent({
  name: 'FilterRadio',
  props: {
    options: {
      type: Array as PropType<SelectOption[]>,
      required: true,
    },
    value: {},
  },
  computed: {
    parsedOptions(): SelectOption[] {
      return this.options.map((o) => ({
        ...o,
        value: _.isNil(o.value) ? null : JSON.stringify(o.value),
      }));
    },
    parsedValue(): any {
      const { value } = this;

      return _.isNil(value) ? null : JSON.stringify(value);
    },
  },
  methods: {
    handleChange(e) {
      const value = e.target.value;
      this.$emit('change', _.isNil(value) ? undefined : JSON.parse(value));
    },
  },
  render() {
    return <Radio.Group {...{ props: this.$attrs }} value={this.parsedValue} options={this.parsedOptions} onChange={this.handleChange} />;
  },
});

export default FilterRadio;
