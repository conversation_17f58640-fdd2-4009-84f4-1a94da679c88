import { shallowMount } from '@vue/test-utils';
import { Checkbox } from 'ant-design-vue';

import FilterCheckbox from '../checkbox';

describe('FilterCheckbox', () => {
  test('props: values', () => {
    const wrapper = shallowMount(FilterCheckbox, {
      propsData: {
        values: [1, 2],
        options: [
          {
            value: 1,
            label: '1',
          },
          {
            value: 2,
            label: '2',
          },
        ],
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('props: values - nested', () => {
    const wrapper = shallowMount(FilterCheckbox, {
      propsData: {
        values: [1, 2, 21],
        options: [
          {
            value: 1,
            label: '1',
          },
          {
            value: 2,
            label: '2',
            children: [
              {
                value: 21,
                label: '21,',
              },
            ],
          },
        ],
      },
    });

    expect(wrapper).toMatchSnapshot();
  });

  test('props: values - empty', () => {
    const wrapper = shallowMount(FilterCheckbox, {
      propsData: {},
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('events: change', () => {
    const wrapper = shallowMount(FilterCheckbox, {
      propsData: {
        values: [1, 2, 21],
        options: [
          {
            value: 1,
            label: '1',
          },
          {
            value: 2,
            label: '2',
            children: [
              {
                value: 21,
                label: '21,',
              },
            ],
          },
        ],
      },
    });
    const checkboxList = wrapper.findAllComponents(Checkbox);

    const SwitchAll = checkboxList.at(0);
    SwitchAll.vm.$emit('change');

    expect(wrapper.emitted('change')).toEqual([[[]]]);
  });
});
