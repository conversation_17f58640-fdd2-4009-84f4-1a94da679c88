// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`FilterCheckbox > props: values - empty 1`] = `
<div class="container"><label class="ant-checkbox-wrapper ant-checkbox-wrapper-checked item" style="width: 145px; display: none;">
    <checkbox-stub prefixcls="ant-checkbox" type="checkbox" checked="true" class=""></checkbox-stub><span>不限</span>
  </label></div>
`;

exports[`FilterCheckbox > props: values - nested 1`] = `
<div class="container"><label class="ant-checkbox-wrapper ant-checkbox-wrapper-checked item" style="width: 145px; display: none;">
    <checkbox-stub prefixcls="ant-checkbox" type="checkbox" checked="true" class=""></checkbox-stub><span>不限</span>
  </label>
  <div><label class="ant-checkbox-wrapper">
      <checkbox-stub prefixcls="ant-checkbox" type="checkbox" class=""></checkbox-stub><span>1</span>
    </label></div>
  <div>
    <anonymous-stub prefixcls="ant-dropdown" transitionname="slide-up" overlayclassname="" overlaystyle="[object Object]" placement="bottomLeft" trigger="click" showaction="" hideaction="" mouseenterdelay="0.15" mouseleavedelay="0.1"><label class="ant-checkbox-wrapper ant-dropdown-trigger">
        <checkbox-stub prefixcls="ant-checkbox" type="checkbox" class=""></checkbox-stub><span><span>2</span>
        <q-icon-stub type="icon-a-shixinxia1x"></q-icon-stub>
        </span>
      </label><template>
        <menu-stub prefixcls="ant-menu" focusable="true" visible="true" defaultselectedkeys="" defaultopenkeys="" mode="vertical" triggersubmenuaction="hover" submenuopendelay="0.1" submenuclosedelay="0.1" level="1" inlineindent="24" theme="light" opentransitionname="zoom-big" isrootmenu="true" builtinplacements="[object Object]" expandicon="[object Object]" class="ant-menu-light dropContent">
          <atooltip-stub trigger="hover" placement="right" transitionname="zoom-big-fast" overlaystyle="[object Object]" overlayclassname="undefined-inline-collapsed-tooltip" mouseenterdelay="0.1" mouseleavedelay="0.1" autoadjustoverflow="true" align="[object Object]" class="dropItem">
            <proxy_connect_menuitem-stub inlineindent="24" level="1" mode="vertical" manualref="[Function]"><label class="ant-checkbox-wrapper">
                <checkbox-stub prefixcls="ant-checkbox" type="checkbox" class=""></checkbox-stub><span>21,<span style="color: #666; display: none;"></span></span>
              </label></proxy_connect_menuitem-stub>
          </atooltip-stub>
        </menu-stub>
      </template></anonymous-stub>
  </div>
</div>
`;

exports[`FilterCheckbox > props: values 1`] = `
<div class="container"><label class="ant-checkbox-wrapper ant-checkbox-wrapper-checked item" style="width: 145px; display: none;">
    <checkbox-stub prefixcls="ant-checkbox" type="checkbox" checked="true" class=""></checkbox-stub><span>不限</span>
  </label>
  <div><label class="ant-checkbox-wrapper">
      <checkbox-stub prefixcls="ant-checkbox" type="checkbox" class=""></checkbox-stub><span>1</span>
    </label></div>
  <div><label class="ant-checkbox-wrapper">
      <checkbox-stub prefixcls="ant-checkbox" type="checkbox" class=""></checkbox-stub><span>2</span>
    </label></div>
</div>
`;
