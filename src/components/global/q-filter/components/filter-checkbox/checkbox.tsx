import { PropType, computed, defineComponent, ref, watch } from 'vue';
import { cloneDeep, isEqual } from 'lodash';
import { Checkbox, Dropdown, Menu } from 'ant-design-vue';

import Icon from '@/shared/components/icon';

import styles from './filter-checkbox.module.less';

const FilterCheckbox = defineComponent({
  name: 'FilterCheckbox',
  props: {
    options: {
      type: Array as PropType<Array<any>>,
      default: () => [],
    },
    values: {
      type: Array as PropType<Array<any>>,
      required: false,
    },
    defaultButton: {
      type: <PERSON>olean,
    },
  },
  setup(props, { emit }) {
    const innerOptions = ref<any[]>([]);

    const checkedAll = computed(() => {
      return innerOptions.value.filter((x) => x.checked || x.indeterminate === true).length === 0;
    });

    const reset = (item) => {
      item.indeterminate = false;
      item.checked = false;
      item.children?.forEach((x) => {
        x.checked = false;
      });
    };

    const switchAll = () => {
      innerOptions.value.forEach((item) => {
        reset(item);
      });
      emit(
        'change',
        innerOptions.value.filter((x) => x.checked)
      );
    };

    const resetData = () => {
      // 筛选值
      if (!props.values) {
        switchAll(); // 重置
        return;
      }
      const options = cloneDeep(props.options);
      const t = cloneDeep(props.values);
      t.forEach((v) => {
        const rt = options.find((x) => x.label === v.label);
        if (rt) {
          let fullChildren = rt.children;
          // 子筛选项， 看现有设计最多两层，不用递归
          if (rt.children) {
            fullChildren = cloneDeep(rt.children);
            fullChildren.forEach((c) => {
              c.checked = v.children.findIndex((cc) => cc.label === c.label) > -1;
            });
          } else {
            rt.checked = true;
          }
          options.splice(
            options.findIndex((x) => x.label === v.label),
            1,
            {
              ...v,
              judge: true,
              showSubMenu: false,
              children: fullChildren,
            }
          );
        }
      });
      // init other
      options.forEach((item) => {
        // judge 已经判断
        if (item.judge) return;
        item.checked = false;
        item.showSubMenu = false;
        item.indeterminate = false;
        if (item.children && item.children.length) {
          item.children.forEach((x) => {
            x.checked = false;
          });
        }
      });
      innerOptions.value = options;
    };

    watch(
      () => props.values,
      () => {
        resetData();
      },
      { deep: true, immediate: true }
    );

    watch(
      () => props.options,
      (newVal, oldVal) => {
        if (newVal && isEqual(newVal, oldVal)) {
          return;
        }
        const cloneVal = cloneDeep(newVal);
        const valArr = (props.values || []).map((item) => item.value);
        cloneVal.forEach((item) => {
          if (!item.judge) {
            item.checked = false;
            item.showSubMenu = false;
            item.indeterminate = false;
            if (item.children && item.children.length) {
              item.children.forEach((x) => {
                x.checked = false;
              });
            } else {
              item.checked = valArr.includes(item.value);
            }
          }
        });
        innerOptions.value = cloneVal;
      },
      { deep: true, immediate: true }
    );

    const getCheckedTargetFromAllItems = () => {
      const checkedTarget = innerOptions.value.filter((x) => x.checked || x.indeterminate);
      const checkboxDropTarget = checkedTarget.filter((x) => x.children && x.children.length);
      checkboxDropTarget.forEach((x) => {
        x.children = x.children.filter((y) => y.checked);
      });
      return checkedTarget;
    };

    const toggleChildren = (item) => {
      item.checked = !item.checked;
      item.indeterminate = false;
      if (item.children?.length) {
        item.children.forEach((x) => {
          x.checked = item.checked;
        });
      }
      emit('change', getCheckedTargetFromAllItems());
    };

    const handleMenuClick = (item, sItem) => {
      sItem.checked = !sItem.checked;
      const selectedItems = item.children.filter((x) => x.checked);
      item.indeterminate = !!selectedItems.length && !!(selectedItems.length < item.children.length);
      item.checked = selectedItems.length === item.children.length;

      emit('change', getCheckedTargetFromAllItems());
    };

    // 单选组件-非下拉直接发射事件
    const onChange = (e, item) => {
      item.checked = e.target.checked;
      emit('change', getCheckedTargetFromAllItems());
    };

    return {
      checkedAll,
      innerOptions,
      resetData,
      getCheckedTargetFromAllItems,
      toggleChildren,
      reset,
      handleMenuClick,
      onChange,
      switchAll,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <Checkbox class="item" checked={this.checkedAll} onChange={this.switchAll} style={{ width: '145px' }} v-show={this.defaultButton}>
          不限
        </Checkbox>

        {this.innerOptions.map((item, index) => {
          if (!item.children || !item.children.length) {
            return (
              <div key={index}>
                <Checkbox checked={item.checked} onChange={(ev) => this.onChange(ev, item)} style={{ width: `${item.width}px` }}>
                  {item.label}
                </Checkbox>
              </div>
            );
          }

          return (
            <div key={index}>
              <Dropdown value={item.showSubMenu} trigger={['click']}>
                <Checkbox onClick_stop={() => this.toggleChildren(item)} checked={item.checked} indeterminate={item.indeterminate}>
                  <span>{item.label}</span>
                  {item.showSubMenu ? <Icon class="caret" type="icon-a-shixinshang1x" /> : <Icon class="caret" type="icon-a-shixinxia1x" />}
                </Checkbox>

                <Menu slot="overlay" class={styles.dropContent}>
                  {item.children.map((sItem, sIndex) => (
                    <Menu.Item class={styles.dropItem} key={`${index}-${sIndex}`}>
                      <Checkbox onClick={() => this.handleMenuClick(item, sItem)} checked={sItem.checked}>
                        {sItem.label}
                        <span style="color: #666" v-show={sItem.count}>
                          {sItem.count}
                        </span>
                      </Checkbox>
                    </Menu.Item>
                  ))}
                </Menu>
              </Dropdown>
            </div>
          );
        })}
      </div>
    );
  },
});

export default FilterCheckbox;
