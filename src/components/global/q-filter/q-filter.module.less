@import '@/styles/token.less';

.root {
  width: 100%;
  color: #333;
  line-height: 24px;
  font-size: 14px;
  padding: 15px 0;

  &.offset {
    margin-top: -15px;
  }
}

.label {
  width: 76px !important;
  display: inline-block;
  color: #999;
  white-space: pre;
  font-size: 14px;
}

.group {
  margin-top: 12px;
  line-height: 22px;

  &.lastGroup {
    line-height: 22px;

    .label {
      align-self: center;
    }
  }
}

.group.inline {
  display: inline-flex;
  margin-right: 30px;
}

.group.block {
  display: flex;

  .wrap {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    // gap: 10px 0;

    & > .select,
    & > .checkbox,
    & > .radio {
      display: inline-flex;
      align-items: center;
      flex: none;
    }
  }

  .extra,
  .collapse {
    white-space: nowrap;
    flex: none;
  }
}

// Group theme
.group.filled {
  background: transparent;
}

.group.outlined {
  .selectBorder {
    border: 1px solid #d9d9d9;
    border-radius: 2px;

    &:hover {
      border: 1px solid #128bed;
    }
  }
}
// Dropdown theme
.dropdown.filled {
  :global {
    .ant-btn {
      background: transparent;
      border: none;
    }
  }
}

.dropdown {
  :global {
    .ant-btn {
      padding: 0 10px;
    }

    .ant-btn:active, .ant-btn.active {
      color: @qcc-color-blue-500;
    }
  }
}

.wrap.collapsed {
  overflow: hidden;
}

.wrap.wrapGroups {
  & > * {
    font-size: 14px;
  }
}

.wrap.disabled {
  position: relative;
  cursor: not-allowed;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.7);
  }
}

.reference {
  position: relative;
  padding: 0 10px;
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;

  &:hover {
    color: @primary-color;

    .icon {
      color: inherit;
    }
  }

  &.active {
    color: @primary-color;

    .icon {
      color: inherit;
    }
  }

  &.open .icon {
    transform: rotate(180deg);
  }

  &.withTip {
    padding-right: 16px + 18px;

    .icon {
      right: 18px;
    }
  }

  .icon {
    font-size: 16px;
    transform-origin: center;
    color: #666;
    // color: #bbb;
  }

  .tipIcon {
    color: #d6d6d6;
    font-size: 12px;
    position: absolute;
    right: (18px - 12px) / 2;
    top: (20px - 12px) / 2;
  }
}

.collapse {
  color: #999;
  padding-top: 1px;
  padding-left: 5px;
  cursor: pointer;

  &:hover {
    color: @primary-color;
  }

  i {
    font-size: 12px;
    margin-right: 2px;
  }
}

.checkbox,
.radio {
  line-height: 20px;
  color: #333;
}

.checkbox :global(.ant-checkbox-wrapper) {
  color: #333;
}

.selectBorder {
  height: 32px;
  line-height: 32px;
}

.foldGroupWrap {
  max-height: 400px;
  overflow-y: auto;
  width: 500px;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  margin: 0;
  text-align: left;
  list-style-type: none;
  background-color: #fff;
  background-clip: padding-box;
  outline: none;

  .foldGroupItem + .foldGroupItem {
    margin-top: 10px;
    border-top: 1px solid #f0f0f0;
    padding-top: 10px;
  }

  .foldGroupItem .label{
    margin-bottom: 5px;
  }
}
