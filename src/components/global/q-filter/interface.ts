import { SelectOption, CascaderOption } from '@/interfaces/data.interface';
import { CustomConfig } from '@/components/global/q-select/interface';

type BasicGroup = {
  field: string;
  label?: string;
  labelWidth?: string;
  layout?: 'inline' | 'block';
  // flatten [[1,2],[3]] => [1,2,3]
  // array 1 => [1]
  // split '1,2' => ['1','2'] 注意 split 转换后必然是 string[]
  // split ['1,2','2,3','4'] => ['1','2','3','4'] 注意 split 转换后必然是 string[]
  transform?: 'flatten' | 'array' | 'split';
  autoCollapsed?: boolean;
  rowHeight?: number;
  placeholder?: string;
  meta?: Record<string, any>;
  customRender?: any;
};

export type SelectGroup = BasicGroup & {
  type: 'single' | 'multiple' | 'radio' | 'checkbox';
  options: SelectOption[];
  custom?: CustomConfig;
  tip?: string;
  [key: string]: any;
};

export type CascaderGroup = BasicGroup & {
  type: 'cascader' | 'cascader-multiple';
  options: CascaderOption[];
  tip?: string;
};

export type ButtonGroup = BasicGroup & {
  type: 'button' | 'button-multiple';
  options: SelectOption[];
  extra?: {
    title: string;
    options: SelectOption[];
  };
  custom?: CustomConfig;
  tip?: string;
};

export type BreakLineGroup = {
  type: 'breakLine';
};

export type BasicGroupItem = SelectGroup | CascaderGroup | ButtonGroup;

export type GroupsGroup = BasicGroup & {
  type: 'groups' | 'fold-groups';
  children: Array<SelectGroup | CascaderGroup>;
};

export type Group = SelectGroup | CascaderGroup | GroupsGroup | ButtonGroup | BreakLineGroup;
