import { shallowMount } from '@vue/test-utils';

import QFilter from '@/components/global/q-filter/q-filter';
import DropdownButton from '@/components/dropdown-button';
import Checkbox from '@/components/global/q-filter/components/filter-checkbox';
import Radio from '@/components/global/q-filter/components/filter-radio';

describe('QFilter', () => {
  const groups: any[] = [
    {
      field: 'testField1',
      type: 'single',
      label: 'Test Label 1',
      options: [{ label: 'Option 1', value: '1' }],
    },
    {
      field: 'testField2',
      type: 'checkbox',
      label: 'Test Label 2',
      options: [
        { label: 'Option 1', value: '1' },
        { label: 'Option 2', value: '2' },
      ],
    },
    {
      type: 'breakLine',
    },
    {
      field: 'testField3',
      type: 'fold-groups',
      label: 'Fold Groups Label',
      children: [
        {
          field: 'testChildField1',
          type: 'single',
          label: 'Test Child Label 1',
          options: [{ label: 'Option 1', value: '1' }],
        },
        {
          field: 'testChildField2',
          type: 'radio',
          label: 'Test Child Label 2',
          options: [
            { label: 'Option 1', value: '1' },
            { label: 'Option 2', value: '2' },
          ],
        },
      ],
    },
  ];

  it('renders correctly with single group', () => {
    const wrapper = shallowMount(QFilter, {
      propsData: {
        groups: [groups[0]],
        value: {},
      },
    });
    expect(wrapper.find('.q-filter-group').exists()).toBe(true);
    expect(wrapper.find('.q-filter-group .q-filter-label').text()).toBe('Test Label 1');
  });

  it('renders correctly with checkbox group', () => {
    const wrapper = shallowMount(QFilter, {
      propsData: {
        groups: [groups[1]],
        value: {},
      },
    });
    expect(wrapper.find('.q-filter-group').exists()).toBe(true);
    expect(wrapper.find(Checkbox).exists()).toBe(true);
  });

  it('renders break line when type is breakLine', () => {
    const wrapper = shallowMount(QFilter, {
      propsData: {
        groups: [groups[2]],
        value: {},
      },
    });
    expect(wrapper.find('br').exists()).toBe(true);
  });

  it('renders fold groups correctly', () => {
    const wrapper = shallowMount(QFilter, {
      propsData: {
        groups: [groups[3]],
        value: {},
      },
    });
    expect(wrapper.find(DropdownButton).exists()).toBe(true);
    expect(wrapper.find(Radio).exists()).toBe(true);
  });

  it('emits hover event when dropdown button is hovered', () => {
    const wrapper = shallowMount(QFilter, {
      propsData: {
        groups: [groups[3]],
        value: {},
      },
    });
    const hoverSpy = vi.spyOn(wrapper.vm, '$emit');
    wrapper.find(DropdownButton).vm.$emit('visibleChange', true);
    expect(hoverSpy).toHaveBeenCalledWith('hover', groups[3]);
  });

  it('renders with inline layout when group layout is inline', () => {
    const wrapper = shallowMount(QFilter, {
      propsData: {
        groups: [{ ...groups[0], layout: 'inline' }],
        value: {},
      },
    });
    expect(wrapper.find('.q-filter-group').exists()).toBe(true);
  });

  it('renders with custom label when renderLabel function is provided', () => {
    const renderLabel = vi.fn(() => 'Custom Label');
    const wrapper = shallowMount(QFilter, {
      propsData: {
        groups: [groups[0]],
        value: {},
        renderLabel,
      },
    });
    expect(wrapper.find('.q-filter-group .q-filter-label').text()).toBe('Test Label 1');
  });

  it('renders with custom extra content when renderExtra function is provided', () => {
    const renderExtra = vi.fn((h) => h('div', 'Custom Extra'));
    const wrapper = shallowMount(QFilter, {
      propsData: {
        groups: [groups[0]],
        value: {},
        renderExtra,
      },
    });
    expect(renderExtra).toHaveBeenCalledWith(expect.any(Function), groups[0]);
  });

  it('renders with loading state when isLoading prop is true', () => {
    const wrapper = shallowMount(QFilter, {
      propsData: {
        groups: [groups[0]],
        value: {},
        isLoading: true,
      },
    });
    expect(wrapper.find('.q-filter-group').find({ name: 'q-select' }).exists()).toBe(false);
  });
});
