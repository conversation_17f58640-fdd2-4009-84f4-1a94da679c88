import _ from 'lodash';
import { defineComponent } from 'vue';

import * as companyUtil from '@/utils/firm';

import styles from './q-entity-avatar.module.less';

export const ENTITY_AVATAR_THEME = ['#F08A4B', '#13C261', '#368F8B', '#128BED', '#7874EE', '#F04040', '#0AB4BA', '#5294D7', '#C371D8'];

export interface IEntityAvatar {
  src?: string;
  name?: string;
  size?: number;
}

const QEntityAvatar = defineComponent({
  name: 'QEntityAvatar',

  props: {
    src: {
      type: String,
      required: false,
    },
    name: {
      type: String,
      default: '企查查',
    },
    size: {
      type: Number,
      default: 20,
    },
    keyNo: {
      type: String,
      default: '',
    },

    hasImage: {
      type: Boolean,
      default: false,
    },

    radius: {
      type: Number,
      default: 2,
    },

    bordered: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      loadError: false,
      backgroundColor: _.get(ENTITY_AVATAR_THEME, _.random(ENTITY_AVATAR_THEME.length - 1)),
    };
  },

  computed: {
    imgUrl(): string {
      if (this.src) {
        return this.src;
      }
      if (this.keyNo) {
        return companyUtil.getLogoByKeyNo(this.keyNo, this.hasImage);
      }
      return '';
    },
    avatarStyle(): string {
      const { size, radius } = this;
      return `width: ${size}px; height: ${size}px; border-radius: ${radius}px; overflow: hidden;`;
    },
    autoStyle(): string {
      const fontSize = Math.round(this.size / 2);
      return `${this.avatarStyle} background: ${this.backgroundColor}; font-size: ${fontSize}px;`;
    },
    shortName(): string {
      return this.name ? this.name.substring(0, 1) : '企';
    },
  },

  methods: {
    handleImageLoadErr() {
      this.loadError = true;
    },
    generateAvatar() {
      return (
        <div class={[styles.auto, this.bordered && styles.border]} style={this.autoStyle}>
          {this.shortName}
        </div>
      );
    },
  },

  render() {
    return this.imgUrl && !this.loadError ? (
      <img
        class={[styles.container, this.bordered && styles.border]}
        style={this.avatarStyle}
        src={this.imgUrl}
        alt={this.name}
        width={this.size}
        height={this.size}
        onError={(this as any).handleImageLoadErr}
      />
    ) : (
      (this as any).generateAvatar()
    );
  },
});

export default QEntityAvatar;
