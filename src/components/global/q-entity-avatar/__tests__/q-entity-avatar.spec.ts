import { shallowMount } from '@vue/test-utils';

import QEntityAvatar from '..';

vi.mock('lodash', async () => {
  const original = await vi.importActual('lodash');
  return {
    ...original,
    random: vi.fn().mockReturnValue(0), // 保证 random 总是保持一致的输出
  };
});

describe('QEntityAvatar', () => {
  test('render', () => {
    const wrapper = shallowMount<InstanceType<typeof QEntityAvatar>>(QEntityAvatar, {
      propsData: {
        name: '企查查',
        keyNo: 'f625a5b661058ba5082ca508f99ffe1b',
        hasImage: true,
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('props: src', () => {
    const wrapper = shallowMount<InstanceType<typeof QEntityAvatar>>(QEntityAvatar, {
      propsData: {
        name: '企查查',
        src: 'SRC.jpg',
      },
    });
    expect(wrapper).toMatchSnapshot();
  });
});
