.filter-item {
  padding-right: 50px;
  position: relative;

  .title {
    font-size: 14px;
    color: #999;
    margin-right: 8px;
    display: inline-block;
    float: left;
  }

  .filter-container {
    float: right;
    width: calc(100% - 80px);
    overflow: hidden;

    .filter-wrapper {
      width: 100%;
    }
  }

  .title-main {
    .filter-span {
      margin-left: 9px;
      margin-bottom: 10px;
      padding: 1px 8px;
      display: inline-block;
      line-height: 21px;
      cursor: pointer;

      &.selected {
        background: #128bed;
        border-radius: 2px;
        color: #fff;

        .text-gray {
          color: #fff;
        }
      }
    }

    .text-gray {
      color: #999;
    }
  }

  .expander {
    position: absolute;
    right: 0;
    top: 0;
    padding: 1px 0;
    font-size: 14px;
    line-height: 21px;
    color: #979797;
    cursor: pointer;

    >.text {
      margin-right: 3px;
    }
  }
}
