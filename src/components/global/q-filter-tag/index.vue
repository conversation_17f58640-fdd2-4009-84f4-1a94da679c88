<template>
  <div class="clearfix filter-item">
    <div class="title">{{ title }}</div>
    <div class="filter-container" ref="container">
      <div class="filter-wrapper" ref="wrapper">
        <div class="title-main" v-show="dataSource.length > 0">
          <span
            v-for="item in dataSource"
            class="filter-span"
            :class="[item.code === val && 'selected']"
            :key="item.name"
            @click="selectedItem(item)"
          >
            {{ item.name }}
            <span v-if="item.count" class="text-gray">({{ item.count }})</span>
          </span>
          <span v-if="showDatePicker" style="margin-left: 9px">
            <a-range-picker size="small" v-model="momentDate" @change="onChange" separator="-">
              <a-icon slot="suffixIcon" type="calendar" />
            </a-range-picker>
          </span>
        </div>
      </div>
    </div>
    <div class="expander" v-if="!hideExpander && type === 'nodeCode'" @click="toggle">
      <span class="text">{{ expand ? '收起' : '更多' }}</span
      ><a-icon :type="expand ? 'up' : 'down'" :style="{ fontSize: '10px', color: '#999' }" />
    </div>
  </div>
</template>

<script src="./component.js"></script>

<style lang="less" src="./style.less"></style>
