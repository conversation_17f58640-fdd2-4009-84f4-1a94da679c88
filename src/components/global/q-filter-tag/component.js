import moment from 'moment';

export default {
  name: 'q-filter-tag',

  props: {
    options: {
      type: Array,
      default: () => [],
    },
    title: String,
    type: String,
    val: [String, Number],
    visualHeight: {
      type: Number,
      default: 33,
    },
    duration: {
      type: Number,
      default: 200,
    },
    level: Number,
  },

  data() {
    return {
      dataSource: {},
      showDatePicker: false,
      momentDate: [],
      expand: false,
      hideExpander: true,
    };
  },

  watch: {
    options: {
      handler(val) {
        this.dataSource = this.type === 'nodeCode' ? val.filter((item) => item.count !== 0) : val;
      },
      immediate: true,
    },
    visualHeight() {
      this.calc();
    },
  },

  mounted() {
    this.$nextTick(() => {
      this.$container = $(this.$refs.container);
      this.$wrapper = $(this.$refs.wrapper);
      const height = this.$wrapper.height();
      if (height <= this.visualHeight) {
        this.hideExpander = true;
        this.$container.css({ height });
      } else {
        this.hideExpander = false;
        this.$container.css({ height: this.visualHeight });
      }
      this.calc();
    });
  },

  updated() {
    this.calc(false);
  },

  methods: {
    calc(animate) {
      const height = this.$wrapper.height();
      if (this.expand) {
        if (animate) {
          this.$container.animate({ height }, this.duration);
        } else {
          this.$container.css({ height });
        }
        this.hideExpander = !(height > this.visualHeight);
      } else if (height > this.visualHeight) {
        this.hideExpander = false;
        if (animate) {
          this.$container.animate({ height: this.visualHeight }, this.duration);
        } else {
          this.$container.css({ height: this.visualHeight });
        }
      } else {
        this.$container.css({ height });
        this.hideExpander = true;
      }
    },

    toggle() {
      this.expand = !this.expand;
      this.calc();
    },

    selectedItem({ code, val }) {
      if (this.type === 'groupType') {
        this.$emit('cb', {
          level: this.level,
          code,
          type: this.type,
        });
      } else {
        if (this.type === 'date') {
          this.showDatePicker = false;
        }
        if (code === 'custom') {
          this.momentDate = [];
          this.showDate();
        }
        this.$emit('cb', {
          level: this.level,
          code,
          type: this.type,
          ...(this.type === 'date' ? { val } : {}),
        });
      }
    },

    onChange(date) {
      const startDate = date[0]?.format('YYYY/MM/DD');
      const endDate = date[1]?.format('YYYY/MM/DD');
      this.$emit('cb', {
        level: this.level,
        val: {
          startDate,
          endDate,
        },
        code: 'custom',
      });
    },

    showDate() {
      this.showDatePicker = !this.showDatePicker;
    },

    disabledDate(date) {
      return date && date > moment().endOf('day');
    },
  },
};
