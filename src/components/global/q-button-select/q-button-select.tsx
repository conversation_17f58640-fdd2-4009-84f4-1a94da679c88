import mixins from 'vue-typed-mixins';
import { VNode } from 'vue';
import _ from 'lodash';

import QSelect from '../q-select';
import selectStyles from '../q-select/q-select.module.less';
import styles from './q-button-select.module.less';
import { CLEAR_VALUE } from '../constants';

// 这个组件和 select 逻辑完全一致，只有 UI 上有区别
// 多了个额外的render，提供自定义渲染
export default mixins(QSelect).extend({
  name: 'QButtonSelect',
  props: {
    customRender: {
      type: Function,
      default: (a, b) => {
        return null;
      },
    },
    maxLength: {
      type: Number,
      default: 0,
    },
    defaultButtonLabel: {
      type: String,
      default: '全部',
    },
    defaultButton: {
      type: Boolean,
      default: true,
    },
    // 0的时候不能点击
    zeroDisable: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    isActive(option: { value: any }) {
      if (this.multiple) {
        return !!(this.value || []).find((v) => _.isEqual(v, option.value));
      }

      return _.isEqual(option.value, this.value);
    },
    renderButton({
      className,
      key,
      label,
      active,
      count,
      tip,
      onClick,
    }: {
      className?: string;
      key: string;
      label: string | Array<string | VNode>;
      active: boolean;
      count?: number;
      tip?: string;
      onClick: () => void;
    }) {
      const isTruncate = this.maxLength && label?.length > this.maxLength;
      const tooltip = isTruncate ? label : '';
      const showLabel = isTruncate ? `${label.slice(0, this.maxLength)}...` : label;

      return (
        <a-tooltip title={tooltip}>
          <div
            role="button"
            class={[
              {
                [styles.btn]: true,
                [styles.disable]: this.zeroDisable && count === 0 && !active,
                [styles.active]: active,
                'q-button-select__btn': true,
              },
              className,
            ]}
            key={key}
            onClick={() => {
              if (this.zeroDisable && count === 0) {
                return;
              }
              onClick();
            }}
          >
            {showLabel}
            {_.isNumber(count) ? <span>({count})</span> : null}
            {tip ? (
              <a-tooltip getPopupContainer={this.getPopupContainer}>
                <q-icon type="icon-a-shuomingxian" class={styles.tipIcon} />
                <div slot="title" class={styles.tip}>
                  {tip}
                </div>
              </a-tooltip>
            ) : null}
          </div>
        </a-tooltip>
      );
    },
    renderCustom() {
      if (!this.custom) {
        return null;
      }
      const node = this.renderCustomNode();

      if (!node) {
        return null;
      }

      const checked = !_.isNil(this.customSource);
      const label = checked ? this.customParser.toLabel(this.customSource) : '自定义';

      return (
        <a-popover
          v-model={this.customVisible}
          trigger={['click']}
          placement="bottom"
          overlayClassName={selectStyles.customOverlay}
          getPopupContainer={this.getPopupContainer}
        >
          {this.renderButton({
            className: styles.custom,
            key: 'custom',
            label: [label, <q-icon key="arrow" type={this.customVisible ? 'icon-a-shixinshang1x' : 'icon-a-shixinxia1x'} />],
            active: checked,
            onClick: _.noop,
          })}
          <div slot="content">{node}</div>
        </a-popover>
      );
    },
  },
  render() {
    const render = this.customRender(this.$createElement, this.options);
    const getValueEmptyStatus = () => {
      if (this.multiple) {
        return !this.value?.length;
      }
      return !this.value;
    };
    const isValueEmpty = getValueEmptyStatus();
    return (
      <div class={[styles.root, 'q-button-select__root']}>
        {this.defaultButton
          ? this.renderButton({
              key: 'null',
              active: isValueEmpty,
              label: this.defaultButtonLabel,
              onClick: () => {
                this.callChange(CLEAR_VALUE);
              },
            })
          : null}

        {this.options.map((option) => {
          return this.renderButton({
            key: option.label,
            active: this.isActive(option),
            label: option.label,
            count: option.count,
            tip: option.tip,
            onClick: () => {
              this.callChange(option.value, option);
              this.handelSubmit();
            },
          });
        })}
        {this.renderCustom()}
        {render}
      </div>
    );
  },
});
