@import '@/styles/token.less';

.root {
  color: #333;
  font-size: 14px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.btn {
  position: relative;
  display: inline-block;
  transition: 0.2s;
  border-radius: 2px;
  height: inherit;
  padding: 3px 10px;
  outline: none;
  cursor: pointer;
  background: #F7F7F7;
  font-size: 12px;
  line-height: 18px;

  >span{
    color: #999;
  }

  &:hover:not(.custom) {
    color: #333;
    background-color: #E2F1FD;

    >span{
      color: #999;
    }
  }

  &.active {
    color: #fff;
    background-color: @primary-color;

    >span{
      color: #fff;
    }

    small,
    .tipIcon {
      color: #fff;
    }
  }

  small {
    font-size: 12px;
    color: #999;
    margin-left: 2px;
    transition: inherit;
  }
}

.tipIcon {
  margin-left: 5px;
  color: #d6d6d6;
  transition: inherit;
}

.tip {
  white-space: pre-line;
}

.disable{
  cursor: not-allowed;
  color: #666 !important;

  &:hover{
    >span{
      color: #666 !important;
    }

    color: #666 !important;
    background-color: #fff !important;
  }
}
