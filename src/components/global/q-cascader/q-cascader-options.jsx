import { Menu, Checkbox } from 'ant-design-vue';
import _ from 'lodash';

import shareStyles from '../../../styles/share/select.module.less';
import styles from './cascader.module.less';

const stopPropagation = (e) => e.stopPropagation();

export default {
  name: 'KzzMultipleCascaderOptions',
  props: {
    dataSource: {
      type: Array,
      required: true,
    },
    options: {
      type: Array,
      required: true,
    },
    value: {
      type: Array,
    },
    menuStyle: {
      type: Object,
    },
    multiple: {
      type: Boolean,
      required: true,
    },
    // 是否只允许选择叶节点
    leafOnly: {
      type: Boolean,
      required: true,
    },
    trigger: {
      type: String,
      required: true,
    },
    disabledValues: {
      type: Array,
    },
    getNodeByPath: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      menus: [],
    };
  },
  watch: {
    options: 'resetMenus',
    value: 'syncMenus',
  },
  methods: {
    enterMenuItem(subMenu, index) {
      if (this.menus.length > index + 1) {
        this.menus = this.menus.slice(0, index + 1);
      }
      if (subMenu && Array.isArray(subMenu) && subMenu.length) {
        this.menus.push(subMenu);
      }

      this.$emit('menuChange');
    },
    syncMenus() {
      const nextMenus = [];
      if (this.dataSource.length) {
        nextMenus.push(this.dataSource);
        if (this.menus.length > 1) {
          let children = this.dataSource;
          _.some(this.menus.slice(1), (menus) => {
            const prevNode = _.get(_.first(menus), 'parent');
            if (!prevNode) {
              return true;
            }
            const currentNode = _.find(children, (c) => c.key === prevNode.key);
            if (!currentNode || !currentNode.children.length) {
              return true;
            }
            nextMenus.push(currentNode.children);
            children = currentNode.children;

            return false;
          });
        }
      }
      this.menus = nextMenus;
    },
    resetMenus() {
      this.menus = [];
      if (this.dataSource.length) {
        this.menus.push(this.dataSource);
      }

      // 单选时定位到已选值
      if (!this.multiple && this.value) {
        let node = this.getNodeByPath(this.value);
        if (!node) {
          return;
        }

        const keys = [];

        while (node) {
          keys.push(node.key);
          node = node.parent;
        }

        if (!keys.length) {
          return;
        }

        keys.reverse();

        let menu = this.dataSource;
        keys.forEach((key) => {
          if (!menu) {
            return;
          }
          const n = _.find(menu, (m) => m.key === key);

          menu = _.get(n, 'children');
          if (Array.isArray(menu) && menu.length) {
            this.menus.push(menu);
          }
        });
      }
    },
    isDisabledNode(node) {
      const { disabledValues } = this;
      if (!Array.isArray(disabledValues)) {
        return false;
      }

      return _.some(disabledValues, (value) => {
        let parent = node;
        while (parent) {
          if (_.isEqual(parent.path, value)) {
            return true;
          }

          parent = parent.parent;
        }

        return false;
      });
    },
    handleMenuClick({ key }, index) {
      const node = _.find(_.get(this.menus, [index], []), (v) => v.key === key);
      const isLeaf = !_.get(node, 'children.length');

      if (!node || this.isDisabledNode(node)) {
        return;
      }

      if (this.trigger === 'click') {
        this.enterMenuItem(node.children, index);
      }

      // if (!isLeaf && this.leafOnly) {
      //   return;
      // }
      this.$emit('change', node);
    },
    renderOption(node, index) {
      const isLeaf = !node.children.length;
      // 多选并且只选叶结点是，增加一个选中图标，其他情况都是文本高亮
      const showCheckedIcon = this.multiple && isLeaf && this.leafOnly && node.checked;
      const events = {};
      const disabled = this.isDisabledNode(node);
      const shouldHighlight = !node.parent || !node.parent.checked;
      let label;

      if (this.trigger === 'hover') {
        Object.assign(events, {
          mouseenter: () => {
            this.$_debounceEnterMenuItem(node.children, index);
          },
          mouseleave: () => {
            this.$_debounceEnterMenuItem.cancel();
          },
        });
      }

      if (this.multiple && !this.leafOnly) {
        label = (
          <Checkbox
            key="checkbox"
            checked={node.checked}
            indeterminate={node.indeterminate}
            nativeOnClick={stopPropagation}
            disabled={disabled}
            onChange={() => this.handleMenuClick({ key: node.key }, index)}
          >
            {node.label}
          </Checkbox>
        );
      } else {
        label = <div key="label">{node.label}</div>;
      }
      return (
        <Menu.Item
          key={node.key}
          class={{
            [shareStyles.menuItem]: true,
            [shareStyles.menuItemWithChildren]: !isLeaf,
            [shareStyles.menuItemChecked]: (node.checked && shouldHighlight) || node.indeterminate,
            [shareStyles.menuItemDisabled]: disabled,
            [shareStyles.menuItemPath]: !isLeaf && _.get(this.menus, index + 1) === node.children,
            [styles.menuItemWithCheckedIcon]: this.multiple && this.leafOnly, // 可能会出现选中图标
          }}
          {...{ nativeOn: events }}
        >
          {label}
          {!isLeaf && <q-icon class={[shareStyles.icon, shareStyles.arrowIcon]} type="icon-a-shixinyou1x" key="arrow-right" />}
          {showCheckedIcon && <q-icon class={styles.icon} type="icon-gouhao" key="checked" />}
        </Menu.Item>
      );
    },
    getMenuWrapperWidth() {
      this.$nextTick(() => {
        const width = (this.$refs.menuWrapper && this.$refs.menuWrapper.clientWidth) || 0;
        this.$emit('getMenuWrapperWidth', width);
      });
    },
    // 是否展开
    isExpand(node) {
      if (!node?.children) {
        return false;
      }
      console.log(this.menus, node.children);
      return this.menus?.includes(node.parent);
    },
  },
  render() {
    const style = this.menuStyle || {};

    return (
      <div class={styles.menus}>
        {this.menus.map((dataSource, index) => (
          <div class={styles.menuWrapper} ref="menuWrapper">
            <Menu
              key={dataSource.map((o) => o.key).join('|')}
              class={styles.menu}
              prefixCls="ant-dropdown-menu"
              ref="menus"
              refInFor
              selectedKeys={[]}
              style={style}
              onClick={(v) => this.handleMenuClick(v, index)}
            >
              {dataSource.map((o) => this.renderOption(o, index))}
            </Menu>
          </div>
        ))}
      </div>
    );
  },
  created() {
    this.$_debounceEnterMenuItem = _.debounce(this.enterMenuItem, 200);
  },
  mounted() {
    this.resetMenus();
  },
};
