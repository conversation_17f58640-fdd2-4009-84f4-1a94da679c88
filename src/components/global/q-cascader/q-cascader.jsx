import { Dropdown, Input, Empty, Spin } from 'ant-design-vue';
import _ from 'lodash';
import { defineComponent } from 'vue';

import Tree from '@/utils/tree';

import Options from './q-cascader-options';
import SelectTrigger from '../q-select-trigger';
import styles from './cascader.module.less';

const clearSpace = (str) => {
  return str ? _.trim(str) : str;
};

export default defineComponent({
  name: 'QCascader',
  inheritAttrs: false,
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    getPopupContainer: {
      type: Function,
    },
    options: {
      required: true,
      type: Array,
    },
    value: {
      type: Array,
    },
    placeholder: {
      type: String,
    },
    menuStyle: {
      type: Object,
      default: () => ({ minWidth: '150px', maxWidth: 'auto' }),
    },
    minOverlayWidthMatchTrigger: {
      type: Boolean,
      default: undefined,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    // 是否只允许选择叶节点
    leafOnly: {
      type: <PERSON>olean,
      default() {
        // 根据是否多选，默认值不同
        let multiple = _.get(this || {}, '$options.propsData.multiple');

        if (_.isNil(multiple)) {
          // 没传参，multiple 默认为 true
          multiple = true;
        }
        return !multiple;
      },
    },
    allowClear: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    getLabelText: {
      type: Function,
    },
    // click 或 hover 展开子级
    menuTrigger: {
      type: String,
      default: 'hover',
    },
    disabledValues: {
      type: Array,
    },
    showSearch: {
      type: Boolean,
      default: false,
    },
    // 在菜单发生变化是检测位置
    detectMenu: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      treeJS: [],
      keyword: undefined,
      menuWrapperWidth: 0,
    };
  },
  watch: {
    options: {
      handler(options) {
        this.$_tree = new Tree(options);
        this.updateTreeFromValue();
      },
      immediate: true,
    },
    value: 'updateTreeFromValue',
  },
  beforeCreate() {
    this.$_tree = null;
  },
  mounted() {
    this.updateTreeFromValue();
  },
  computed: {
    convertedOptions() {
      return this.rebuildData(this.keyword, this.options);
    },
  },
  methods: {
    handleClickClearBtn() {
      this.$emit('change', this.multiple ? [] : undefined);
      this.updateVisible(false);
    },
    updateTreeFromValue() {
      let { value } = this;
      if (!this.$_tree) {
        return;
      }

      if (!this.multiple) {
        value = !_.get(value, 'length') ? undefined : [value];
      }

      this.$_tree.setCheckedPaths(value);
      this.treeJS = this.rebuildData(this.keyword, this.$_tree.toJS());
    },
    updateVisible(visible) {
      // 值为空后重置菜单

      this.$emit('visibleChange', visible);
      this.visible = visible;
      if (visible && this.$refs.options) {
        this.$refs.options.resetMenus();
      }
      this.$nextTick(() => {
        if (this.$refs.options && visible && this.showSearch) {
          this.$refs.options.getMenuWrapperWidth();
        }
      });
    },
    toggleChecked({ path }) {
      if (!this.$_tree) {
        return;
      }
      const node = this.$_tree.getNodeByPath(path);

      if (!node) {
        return;
      }

      if (!this.multiple) {
        if (this.leafOnly || !_.get(node, 'children.length')) {
          this.$nextTick(() => {
            this.updateVisible(false);
          });
        }
        this.$_tree.reset();
      }
      // undefined 时不处理，例如单选时选则了全部
      if (node.value !== undefined) {
        node.checked = !node.checked;
      }

      if (this.multiple) {
        this.$emit('change', this.$_tree.getCheckedPaths());
      } else {
        this.$emit('change', node.checked ? node.path : undefined);
      }
    },
    renderLabel(nodes) {
      const { reference } = this.$scopedSlots;
      const { getLabelText } = this;

      let text;

      if (_.isFunction(getLabelText)) {
        text = getLabelText(nodes);
      } else if (this.multiple) {
        if (nodes.length) {
          let count = 0;
          // 支持二级选项统计个数
          nodes.forEach((item) => {
            if (item.children && item.children.length) {
              count += item.children.length;
            } else {
              count += 1;
            }
          });
          text = `${count}`;
        }
      } else if (nodes.length) {
        text = _.get(
          nodes.filter((node) => {
            const { parent } = node;

            if (!parent) {
              return true;
            }
            // 合并子级
            return !_.get(node, 'parent.checked');
          }),
          '[0].label',
          ''
        );
      }

      if (reference && _.isFunction(reference)) {
        return reference({
          label: text,
          active: nodes.length > 0,
          disabled: this.disabled,
          visible: this.visible,
          nodes,
        });
      }

      return (
        <SelectTrigger
          visible={this.visible}
          placeholder={this.placeholder}
          label={text}
          class={styles.trigger}
          allowClear={this.allowClear}
          width={this.width}
          onClear={this.handleClickClearBtn}
        />
      );
    },
    onKeywordChange(e) {
      this.keyword = e.target.value ? clearSpace(e.target.value) : e.target.value;
      this.updateTreeFromValue();
    },
    rebuildData(value, arr) {
      if (!value) {
        return arr;
      }
      const newArr = [];
      arr.forEach((item) => {
        if (item.label.indexOf(value) > -1) {
          newArr.push(item);
        } else if (item.children && item.children.length > 0) {
          const children = this.rebuildData(value, item.children);
          const obj = {
            ...item,
            children,
          };
          if (children && children.length > 0) {
            newArr.push(obj);
          }
        }
      });
      return newArr;
    },
    forcePopupAlign() {
      if (!this.visible) {
        return;
      }
      this.$nextTick(() => {
        const ref = _.get(this.$refs.dropdown, 'popupRef.$refs.alignInstance');
        if (ref && ref.forceAlign) {
          ref.forceAlign();
        }
      });
    },
    handleMenuChange() {
      if (this.detectMenu) {
        this.forcePopupAlign();
      }
    },
    onGetMenuWrapperWidth(width) {
      this.menuWrapperWidth = width || 189;
    },
    getNodeByPath(path) {
      if (!this.$_tree) {
        return null;
      }

      return this.$_tree.getNodeByPath(path);
    },
  },
  render(h) {
    if (!this.$_tree) {
      return null;
    }
    const nodes = this.$_tree.getMergedCheckedNodes();

    return (
      <Dropdown
        ref="dropdown"
        // trigger={['click']}
        trigger={['hover']}
        visible={this.visible}
        minOverlayWidthMatchTrigger={
          _.isNil(this.minOverlayWidthMatchTrigger)
            ? !_.has(this.menuStyle, 'width') && !_.has(this.menuStyle, 'minWidth')
            : this.minOverlayWidthMatchTrigger
        }
        {...{
          attrs: this.$attrs,
        }}
        getPopupContainer={this.getPopupContainer}
        disabled={this.disabled}
        onVisibleChange={this.updateVisible}
        overlayClassName={styles.overlay}
      >
        {this.renderLabel(nodes)}
        <div slot="overlay">
          <div v-show={this.showSearch || this.$scopedSlots.extra} class={styles.extra}>
            {this.showSearch ? (
              <div class={styles.content}>
                <Input
                  placeholder="请输入搜索关键字"
                  style={{
                    maxWidth: `${this.menuWrapperWidth}px`,
                  }}
                  onChange={this.onKeywordChange}
                  allowClear={true}
                  scopedSlots={{
                    suffix: () => (!this.keyword ? <q-icon type="icon-sousuo" style={{ color: 'rgba(0, 0, 0, 0.65)' }} /> : ''),
                  }}
                />
              </div>
            ) : null}

            {this.$scopedSlots.extra ? <div class={styles.content}>{this.$scopedSlots.extra(h, this)}</div> : null}
          </div>

          {this.loading ? (
            <div class={styles.extra}>
              <Spin spinning={this.loading} size="small">
                <div style={{ width: '160px', height: '140px' }}></div>
              </Spin>
            </div>
          ) : (
            <Options
              ref="options"
              multiple={this.multiple}
              options={this.convertedOptions}
              dataSource={this.treeJS}
              value={this.value}
              menuStyle={this.menuStyle}
              leafOnly={this.leafOnly}
              trigger={this.menuTrigger}
              disabledValues={this.disabledValues}
              getNodeByPath={this.getNodeByPath}
              onMenuChange={this.handleMenuChange}
              onChange={this.toggleChecked}
              onGetMenuWrapperWidth={this.onGetMenuWrapperWidth}
            />
          )}
        </div>
      </Dropdown>
    );
  },
});
