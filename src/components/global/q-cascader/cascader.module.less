.menus {
  display: flex;
  min-width: inherit;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  background-color: #fff;
}

.menuWrapper {
  position: relative;
  min-width: inherit;
  overflow: hidden;
  // 效果更好的分隔阴影
  & + &::after {
    content: '';
    position: absolute;
    left: -8px;
    top: 0;
    bottom: 0;
    width: 8px;
    background-color: transparent;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.15);
  }
}

.menu {
  min-width: inherit;
  max-height: 300px;
  overflow: auto;
  overscroll-behavior: contain;
  box-shadow: none;
  border-radius: 0;
  background-color: transparent;

  :global {
    .ant-checkbox-wrapper {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.trigger {
  color: #666;
  cursor: pointer;
}

.menuItemWithCheckedIcon {
  & > :first-child {
    padding-right: 30px;
  }
}

.extra {
  max-width: 180px;
  color: #666;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  background-color: #fff;
  padding: 5px 10px;

  .content {
    padding: 5px 0;
  }
}

.overlay {
  :global {
    .ant-dropdown-menu-item-active {
      .ant-checkbox-inner {
        border-color: #128bed !important;
      }
    }
  }
}
