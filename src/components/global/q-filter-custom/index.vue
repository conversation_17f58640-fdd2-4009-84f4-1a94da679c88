<template>
  <div>
    <div v-if="propertyOptions.length > 0" class="item">
      <q-filter-tag :val="objectType" :options="propertyOptions" type="objectType" title="关联对象" @cb="callback" :level="10">
      </q-filter-tag>
    </div>
    <div v-if="personOptions.length > 0" class="item">
      <q-filter-tag :val="roleType" :options="personOptions" type="roleType" :title="title" @cb="callback" :level="11"> </q-filter-tag>
    </div>
    <div v-if="shareholdingOptions.length > 0" class="item">
      <q-filter-tag :val="stockPercent" :options="shareholdingOptions" type="stockPercent" title="持股比例" @cb="callback" :level="12">
      </q-filter-tag>
    </div>
  </div>
</template>

<script src="./component.js"></script>

<style lang="less" src="./style.less" scoped></style>
