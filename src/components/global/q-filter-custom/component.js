import QFilterTag from '../q-filter-tag';

export default {
  name: 'q-filter-custom',

  props: ['dataSource'],

  components: {
    QFilterTag,
  },

  data() {
    return {
      objectType: 'all',
      roleType: '',
      stockPercent: '',
      data: {},
      isAll: 0,
    };
  },

  computed: {
    propertyOptions() {
      return this.dataSource || [];
    },
    personOptions() {
      const list = this.propertyOptions.find((item) => item.code === this.objectType);
      return list.children || [];
    },
    shareholdingOptions() {
      if (this.personOptions.length > 0) {
        const list = this.personOptions.find((item) => item.code === this.roleType);
        return list.children || [];
      }
      return [];
    },
    title() {
      if (this.objectType === '2') {
        return '人员类型';
      }
      if (this.objectType === 'related') {
        return '关联方';
      }
      if (this.$route.params.id.startsWith('p')) {
        return '关联企业';
      }
      return '企业类型';
    },
  },

  methods: {
    callback({ level, code, type }) {
      let params = {};
      // 如果点击的是当前按钮
      if (this[type] === code) return;
      // 如果选择的是爷爷级条件 则清空子孙类条件
      if (type === 'objectType') {
        this.roleType = '';
        this.stockPercent = '';
        this.isAll = 0;
        delete this.data.roleType;
        delete this.data.stockPercent;
      }
      // 如果选择的是第子类条件 则清空孙类条件
      if (type === 'roleType') {
        this.stockPercent = '';
      }
      // 如果选择的是全部关联对象
      if (code === 'all') {
        this.isAll = 1;
        this.data = {};
      } else if (code === '') {
        // 如果选择的全部 则过滤掉这个条件
        delete this.data[type];
      } else {
        // 否则将值赋值给参数
        this.data[type] = code;
      }
      // 赋值给当前选中的值
      this[type] = code;
      // 通知父组件更新接口
      params = {
        filters: [this.data],
        isAll: this.isAll,
      };

      if (code === 'all') {
        params.customerKeyNos = '';
      }

      // 如果选择的是关联对象 - 我添加的关联方
      if (code === 'related') {
        params = {
          customerKeyNos: '',
        };
      }
      // 如果当前选择的是我添加的关联方 并且 当前点击的是关联方
      if (this.objectType === 'related' && type === 'roleType') {
        params = {
          customerKeyNos: code,
        };
      }
      this.$emit('callback', { ...params, level });
    },
  },
};
