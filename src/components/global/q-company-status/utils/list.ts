import _ from 'lodash/fp';

export const pageList = (idx, size, list) => {
  const lCursor = (idx - 1) * size;
  const rCursor = lCursor + size;

  return _.slice(lCursor, rCursor, list);
};

export const pickArrayAndConcat = _.compose(_.flatten, _.filter(_.prop('length')), _.filter(Boolean), _.pick);

export const findItemByPredictAndParseProp =
  (key, predict = _.propEq('Key', key), prop = 'Value') =>
  (list = []) => {
    const data = _.find(predict, list);

    return data ? JSON.parse(data[prop]) : undefined;
  };

export const getRandomItem = _.compose(_.head, _.shuffle);
