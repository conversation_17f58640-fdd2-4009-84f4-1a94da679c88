import { mount, shallowMount } from '@vue/test-utils';

import CompanyStatus from '..';

describe('CompanyStatus', () => {
  test('render', () => {
    const wrapper = shallowMount(CompanyStatus, {
      propsData: {},
    });
    expect(wrapper).toMatchSnapshot();
  });

  test.each([
    ['清算', 'danger'],
    ['注销', 'warning'],
    ['在业', 'success'],
  ])('props: status is %s', (status) => {
    const wrapper = mount(CompanyStatus, {
      propsData: {
        status,
        isShowHover: true,
      },
      mocks: {
        $service: {
          sns: {
            getGlossaryInfo: vi.fn().mockResolvedValue([]),
          },
        },
      },
    });
    expect(wrapper.text()).toBe(status);
    expect(wrapper).toMatchSnapshot();
  });
});
