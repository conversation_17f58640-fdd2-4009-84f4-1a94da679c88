export enum CompanyStatus {
  Danger = 'danger',
  Warning = 'warning',
  Primary = 'primary',
  Success = 'success',
  Default = 'shade',
}

export const successStatusTxt = [
  '在业',
  '存续',
  '筹建',
  '新申请用户',
  '已成立事先报批',
  '成立事先报批中',
  '成立中',
  '名称核准发起中',
  '名称核准通过',
  '已成立',
  '正常',
  '仍注册',
  '接管',
  '核准设立',
  '核准认许',
  '核准许可登记',
  '核准许可',
  '核准报备',
  '核准许可报备',
  '核准登记',
  '有效',
  '核准設立',
  '核准認許',
  '核准許可登記',
  '核准許可',
  '核准報備',
  '核准許可報備',
  '核准登記',
  '核準設立',
  '核準認許',
  '核準許可登記',
  '核準許可',
  '核準報備',
  '核準許可報備',
  '核準登記',
  'ACTIVE',
  'CONVERTED',
  'INCORPORATED',
  'MERGED',
  'OTHERS',
  'PERPETUAL',
  'REDEEMED',
  'UNKNOWN',
  'AMALGAMATED',
  'IN BUSINESS',
  'RESERVED',
  'CONVERSION',
  'RE-INSTATEMENT',
  '存续（在营、开业、在册）',
  '迁出',
  '迁入',
];

export const dangerStatusTxt = [
  '清算',
  '撤销',
  '责令关闭',
  '吊销',
  '已撤销',
  '终止破产',
  '涂销破产',
  '清理完结',
  '清理',
  '破产清算完结',
  '破产程序终结',
  '破产',
  '废止清算完结',
  '废止许可完结',
  '废止许可',
  '废止认许',
  '废止认许完结',
  '废止登记完结',
  '废止登记',
  '废止',
  '撤销完结',
  '撤销无需清算',
  '撤销许可',
  '撤销',
  '撤销认许',
  '撤销认许完结',
  '撤回认许',
  '撤回认许完结',
  '无效',
  '終止破產',
  '涂銷破產',
  '清理完結',
  '破產清算完結',
  '破產程序終結',
  '破產',
  '廢止清算完結',
  '廢止許可完結',
  '廢止許可',
  '廢止認許完結',
  '廢止登記完結',
  '廢止登記',
  '廢止',
  '撤銷完結',
  '撤銷無需清算',
  '撤銷許可',
  '撤銷',
  '撤銷認許',
  '撤銷認許完結',
  '撤回認許',
  '撤回認許完結',
  'ABANDONED',
  'CANCELED',
  'CANCELLED',
  'DELINQUENT',
  'DISSOLVED',
  'EXPIRED',
  'FORFEITED',
  'INACTIVE',
  'REMOVED',
  'SUSPENDED',
  'TERMINATED',
  'WITHDRAWN',
  'REVOKED',
  'LIQUIDATION',
  'STRIKE OFF',
  'STRIKING OFF',
  'DEFUNCT',
  'NOT AVAILabel',
  'DORMANT',
  'CAPTURED',
  'DEREGISTRATION',
  'DUPLICATE',
  'DEREGISTERED',
  'NO STATUS',
  'ARCHIVE',
  '撤銷',
  '廢止',
  '除名',
];

export const warningStatusTxt = [
  '注销',
  '停业',
  '歇业',
  '已告解散',
  '已终止注册',
  '已終止註冊',
  '停業',
  '名称核准不通过',
  '注销中',
  '已终止营业地点',
  '不再是独立的实体',
  '休止活动',
  '重整',
  '解散',
  '解散清算完结',
  '设立但已解散',
  '合并解散',
  '分割解散',
  '撤销设立',
  '撤销登记完结',
  '撤销登记',
  '撤回登记',
  '撤回登记完结',
  '解散清算完結',
  '設立但已解散',
  '合併解散',
  '撤銷設立',
  '撤銷登記完結',
  '撤銷登記',
  '撤回登記完結',
  '撤回登記',
  '撤銷設立',
  '撤銷登記完结',
  '撤銷登記',
  '撤回登記完结',
  '撤回登记',
  '設立但已解散',
  '合並解散',
  '解散清算完結',
  '经营异常',
  '裁判文书',
  '严重违法',
  '失信被执行人',
  '税收违法',
  '行政处罚',
  '开庭公告',
];

export const getCompanyStatusColor = (status) => {
  let result = CompanyStatus.Default;
  if (dangerStatusTxt.includes(status)) {
    result = CompanyStatus.Danger;
  } else if (warningStatusTxt.includes(status)) {
    result = CompanyStatus.Warning;
  } else if (successStatusTxt.includes(status)) {
    result = CompanyStatus.Success;
  }
  return result;
};

export const companyStatusTipsMapper = {
  在业: '企业依法存在并继续正常营业。',
  存续: '企业在通过改制重组后，以集团公司或母公司的形式存在的未上市企业，被称为存续企业。',
  筹建: '公司尚未成立。',
  迁入: '企业住所迁移',
  迁出: '企业住所迁移',
  停业: '企业依法停止经营活动。',
  清算: '企业按章程规定解散以及由于破产或其他原因宣布终止经营后，对企业的财产、债权、债务进行全面清查，并进行收取债权，清偿债务和分配剩余财产的经济活动。',
  撤销: '公司被有关部门强制撤回已生效文件、证书并注销营业执照。',
  注销: '企业已依法注销营业执照，失去法人资格。',
  吊销: '吊销营业执照，是工商局根法对违法的企业法人作出的一种行政处罚。被吊销营业执照后，企业应当依法进行清算，清算程序结束并办理工商注销登记后，该企业法人才归于消灭。',
  歇业: '因自然灾害、事故灾难、公共卫生事件、社会安全事件等原因造成经营困难的，市场主体可以自主决定在一定时期内歇业。',
};

export const statusInfoMap = {
  在业: '43',
  存续: '42',
  // 筹建: '51',
  迁入: '45',
  迁出: '46',
  停业: '47',
  清算: '44',
  撤销: '48',
  注销: '50',
  吊销: '49',
};

export const statusCodeMap = {
  10: '在业', // 正常
  20: '存续',
  30: '筹建',
  40: '清算',
  50: '迁入',
  60: '迁出',
  70: '停业',
  75: '歇业',
  80: '撤销',
  85: '责令关闭',
  87: '除名',
  90: '吊销',
  92: '仍注册',
  93: '其他',
  94: '已告解散',
  95: '已终止营业地点',
  96: '不再是独立的实体',
  97: '休止活动',
  99: '注销',
  100: '废止',
  101: '废止清算完结',
  102: '废止许可',
  103: '废止许可完结',
  104: '废止认许',
  105: '废止认许完结',
  106: '接管',
  107: '撤回认许',
  108: '撤回认许完结',
  110: '撤销设立',
  111: '撤销完结',
  112: '撤销无需清算',
  113: '撤销许可',
  114: '撤销认许',
  115: '撤销认许完结',
  116: '核准报备',
  117: '核准设立',
  118: '设立但已解散',
  119: '核准许可报备',
  120: '核准许可登记',
  121: '核准认许',
  122: '清理',
  123: '清理完结',
  124: '破产',
  125: '破产清算完结',
  126: '破产程序终结',
  127: '解散',
  128: '解散清算完结',
  129: '重整',
  130: '合并解散',
  131: '终止破产',
  132: '涂销破产',
  133: '核准许可',
  134: '核准登记',
  135: '分割解散',
  136: '废止登记完结',
  137: '废止登记',
  138: '撤销登记完结',
  139: '撤销登记',
  140: '撤回登记完结',
  141: '撤回登记',
  991: '设立',
  992: '设立',
  993: '设立',
  994: '设立',
  995: '设立',
  996: '设立失败',
  997: '设立',
  998: '存续',
  9910: '注销',
  9911: '注销',
  9912: '注销',
};

export const statusCodeList = Object.keys(statusCodeMap)
  .map((k) => ({
    label: statusCodeMap[k],
    value: k,
  }))
  .concat([{ label: '其他', value: '0' }]);
