// 定制化组件：公司状态
import { defineComponent, PropType } from 'vue';

import { getCompanyStatusColor, companyStatusTipsMapper, statusInfoMap } from './config';
import QGlossaryInfo from '../q-glossary-info';
import QTag from '../q-tag';

const CompanyStatus = defineComponent({
  functional: true,
  props: {
    /**
     * 状态
     */
    status: {
      type: String as PropType<string>,
      default: '',
    },
    /**
     * 是否描边样式
     */
    ghost: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    /**
     * 标签样式，QTag组件样式均支持
     */
    type: {
      type: String,
    },
    /**
     * 提示信息显示位置
     */
    placement: {
      type: String,
      default: 'bottom',
    },
    isShowHover: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
  },
  render(h, { props, data, listeners: on }) {
    const defaultTagType = getCompanyStatusColor(props.status);
    const statusInfoId = statusInfoMap[props.status];

    const type = props.type || defaultTagType;
    let tips = companyStatusTipsMapper[props.status];
    tips = props.isShowHover ? tips : '';

    const styles = { fontSize: '14px !important', ...(data.style as any) };

    if (!props.status) {
      return null;
    }

    if (statusInfoId && props.isShowHover) {
      return (
        <QGlossaryInfo infoId={+statusInfoId} style="font-size: 0;">
          <QTag style={styles} slot="trigger" type={type} ghost={props.ghost} size="companyIcon" class={data.class}>
            {props.status}
          </QTag>
        </QGlossaryInfo>
      );
    }
    return (
      <QTag
        style={styles}
        type={type}
        hoverText={tips}
        ghost={props.ghost}
        placement={props.placement}
        size="companyIcon"
        class={data.class}
        {...{ on }}
      >
        {props.status}
      </QTag>
    );
  },
});

export default CompanyStatus;
