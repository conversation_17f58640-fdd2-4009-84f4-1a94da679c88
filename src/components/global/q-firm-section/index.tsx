import { Tooltip } from 'ant-design-vue';
import { isNil } from 'lodash';
import { defineComponent, PropType } from 'vue';

import styles from './q-firm-section.module.less';

type QFirmSectionType = 'root' | 'sub';

const renderTitle = (h, { props, slots }) => {
  const { title } = slots();
  if (!title && !props.title) {
    return null;
  }

  if (title) {
    return <div class={styles.title}>{title}</div>;
  }
  return (
    <div class={styles.title}>
      <span>{props.title}</span>
      <span
        v-show={!isNil(props.count)}
        class={{
          [styles.count]: true,
          [styles.danger]: !!props.danger,
        }}
      >
        {props.count}
      </span>
      {props.tooltip && (
        <Tooltip title={props.tooltip} placement="bottom">
          <q-icon class={styles.icon} type="icon-a-shuomingxian" />
        </Tooltip>
      )}
      <span v-show={props.desc} class={styles.desc}>
        {props.desc}
      </span>
    </div>
  );
};

const QFirmSection = defineComponent({
  functional: true,

  props: {
    /**
     * Section类型: 'root' | 'sub'
     */
    type: {
      type: String as PropType<QFirmSectionType>,
      default: 'sub',
    },
    title: {
      type: String as PropType<string>,
      required: false,
    },
    headerBorder: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    headerClick: {
      type: Function,
      required: false,
    },
    headStyle: {
      type: Object,
      default: () => ({}),
    },
    bodyStyle: {
      type: Object,
      default: () => ({}),
    },
    tooltip: {
      type: String as PropType<string>,
      required: false,
    },
    withLogo: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    count: {
      // section标题支持带count
      type: [Number, String] as PropType<number | string>,
      required: false,
    },
    danger: {
      type: Boolean as PropType<boolean>,
      required: false,
    },
    desc: {
      type: String as PropType<string>,
      required: false,
    },
    highlight: {
      type: Boolean,
      default: false,
    },
  },

  render(h, { slots, props, data }) {
    const { title, toolbar, chart, default: defaultNode } = slots();

    return (
      <div
        // 透传(支持 ref)
        {...data}
        style={data.style}
        class={{
          'q-section': true,
          [styles.container]: true,
          [styles[props.type]]: true,
          [styles.highlight]: props.highlight,
        }}
      >
        {chart && <div class={styles.chart}>{chart}</div>}
        <div
          class={['q-section-header', styles.header, props.headerBorder ? styles.border : '']}
          style={props.headStyle}
          v-show={title || props.title || toolbar || props.withLogo}
          onClick={() => props.headerClick && props.headerClick()}
        >
          {renderTitle(h, { props, slots })}
          <div class={styles.toolbar}>{toolbar}</div>
        </div>
        <div class={[styles.body, 'q-firm-body']} style={props.bodyStyle}>
          {defaultNode}
        </div>
      </div>
    );
  },
});

export default QFirmSection;
