@import '@/styles/token.less';

.container {
  // padding: 16px;
  padding: 20px;
  background-color: #fff;

  &:not(:last-child) {
    margin-bottom: 8px;
  }

  .container {
    padding: 0;
    margin-top: 16px;

    &:first-child {
      margin-top: -8px;
    }
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 0;
    margin-bottom: 4px;

    &.border{
      height: 44px;
      border-bottom: 1px solid #eee;
    }
  }

  .icon {
    position: relative;
    top: -1px;
    font-size: 14px;
    color: @single-icon-color;
    margin-left: 4px;
  }

  .toolbar {
    display: flex;

    > *:not(:first-child) {
      margin-left: 8px;
    }
  }

  .title {
    min-height: 28px;
    display: flex;
    align-items: center;
  }

  .count {
    color: @primary-color;
    margin-left: 4px;
  }

  .danger {
    color: @danger-color;
  }

  &.root {
    .title {
      font-size: 16px;
      color: #333;
      display: flex;
      align-items: center;
      font-weight: bold;

      .desc {
        font-weight: normal;
        color: #999;
        font-size: 13px;
        position: relative;
        margin-left: 12px;
      }
    }
  }

  &.highlight {
    > .header {
      .count {
        color: #F04040;
      }
    }
  }

  &.sub {
    .title {
      font-size: 13px;
      // min-height: 28px;
    }
  }

  .body {
    line-height: 20px;
    padding-top: 8px;
  }
}
