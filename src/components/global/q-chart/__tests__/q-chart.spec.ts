import { shallowMount } from '@vue/test-utils';

vi.mock('echarts', () => ({
  init: vi.fn().mockReturnValue({
    resize: vi.fn(),
    on: vi.fn(),
    setOption: vi.fn(),
  }),
}));

import QChart from '..';

describe('QChart', () => {
  test('render', () => {
    // 不需要去测依赖库或组件的具体实现
    const wrapper = shallowMount<InstanceType<typeof QChart>>(QChart, {
      propsData: {
        width: '300px',
        height: '300px',
      },
    });
    expect(wrapper).toMatchSnapshot();
  });
});
