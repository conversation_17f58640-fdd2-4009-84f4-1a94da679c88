import { template, templateSettings } from 'lodash';
import { defineComponent } from 'vue';
import { Button } from 'ant-design-vue';

templateSettings.interpolate = /{{([\s\S]+?)}}/g;

const CountDown = defineComponent({
  props: {
    title: {
      type: String,
      required: true,
    },
    value: {
      type: Number,
      default: 30,
    },
    format: {
      type: String,
      default: '{{count}}秒后重发',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      count: this.value,
    };
  },

  methods: {
    countDown() {
      // Reset
      if (this.$_timer) {
        clearInterval(this.$_timer);
      }
      // Immediately count down
      this.count -= 1;
      this.$_timer = setInterval(() => {
        this.count -= 1;
        if (this.count === 0) {
          clearInterval(this.$_timer);
          this.count = this.value;
        }
      }, 1000);
    },

    async onClick() {
      this.$emit('click', this.countDown);
    },

    onReset() {
      if (this.$_timer) {
        clearInterval(this.$_timer);
        this.count = this.value;
      }
    },
  },

  computed: {
    isDisabled() {
      return this.count !== this.value;
    },

    label() {
      const formated = template(this.format)({
        count: this.count,
      });
      return this.isDisabled ? formated : this.title;
    },
  },

  render() {
    return (
      <Button disabled={this.disabled || this.isDisabled} type="link" onClick={this.onClick}>
        {this.label}
      </Button>
    );
  },
});

export default CountDown;
