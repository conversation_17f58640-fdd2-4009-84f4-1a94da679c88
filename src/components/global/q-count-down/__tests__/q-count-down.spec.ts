import { shallowMount } from '@vue/test-utils';
import { Button } from 'ant-design-vue';

import QCountDown from '..';

describe('QCountDown', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });
  afterEach(() => {
    vi.useRealTimers();
  });

  test('render', () => {
    const wrapper = shallowMount<InstanceType<typeof QCountDown>>(QCountDown, {
      propsData: {
        title: 'TITLE',
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('events: click', async () => {
    const mockCallback = vi.fn();
    const onClick = (callback) => {
      mockCallback();
      callback(); // 直接执行回调
    };

    const wrapper = shallowMount<InstanceType<typeof QCountDown>>(QCountDown, {
      propsData: {
        title: 'TITLE',
      },
      listeners: {
        click: onClick,
      },
    });

    expect(wrapper).toMatchSnapshot();

    // Button
    const button = wrapper.findComponent(Button);
    button.vm.$emit('click'); // button click
    expect(mockCallback).toHaveBeenCalled();

    await vi.advanceTimersByTimeAsync(1000 * 1); // 向前推移 1s
    expect(wrapper.vm.isDisabled).toBe(true);
    expect(wrapper).toMatchSnapshot();

    await vi.advanceTimersByTimeAsync(1000 * 29); // 向前推移 29s
    expect(wrapper.vm.isDisabled).toBe(false);
    expect(wrapper).toMatchSnapshot();
  });
});
