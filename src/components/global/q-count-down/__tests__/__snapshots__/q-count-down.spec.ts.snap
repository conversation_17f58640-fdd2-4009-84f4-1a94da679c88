// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`QCountDown > events: click 1`] = `<button type="button" class="ant-btn ant-btn-link"><span>TITLE</span></button>`;

exports[`QCountDown > events: click 2`] = `<button type="button" class="ant-btn ant-btn-link" disabled="disabled"><span>28秒后重发</span></button>`;

exports[`QCountDown > events: click 3`] = `<button type="button" class="ant-btn ant-btn-link"><span>TITLE</span></button>`;

exports[`QCountDown > render 1`] = `<button type="button" class="ant-btn ant-btn-link"><span>TITLE</span></button>`;
