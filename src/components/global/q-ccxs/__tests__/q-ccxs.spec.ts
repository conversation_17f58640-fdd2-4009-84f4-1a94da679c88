import { shallowMount } from '@vue/test-utils';
import { company } from '@/shared/services';

import QCCXS from '..';

// Mock 模块
vi.mock('@/shared/services', () => ({
  company: {
    getDetail: vi.fn(),
  },
}));

describe('QCCXS.vue', () => {
  let originalWindowOpen: typeof window.open;
  let originalDimensionInstance: any;

  beforeEach(() => {
    originalWindowOpen = window.open;
    originalDimensionInstance = (window as any).__DIMENSION_INSTANCE__;
    window.open = vi.fn(); // Vitest 的 mock 函数
    (window as any).__DIMENSION_INSTANCE__ = undefined;
  });

  afterEach(() => {
    window.open = originalWindowOpen;
    (window as any).__DIMENSION_INSTANCE__ = originalDimensionInstance;
    vi.clearAllMocks(); // 清理所有 mock
  });

  it('TC01: 使用传入的 ccxsCount', async () => {
    const wrapper = shallowMount(QCCXS, {
      propsData: {
        ccxsCount: 5,
        keyNo: 'C123',
      },
    });
    expect(wrapper.vm.count).toBe(5);
    expect(vi.mocked(company.getDetail)).not.toHaveBeenCalled();
  });

  it('TC02: isPerson 正确识别 P 开头 keyNo', async () => {
    const wrapper = shallowMount(QCCXS, {
      propsData: {
        keyNo: 'P123',
      },
    });
    expect(wrapper.vm.isPerson).toBe(true);
  });

  it('TC03: 加载远程 clue count 成功', async () => {
    vi.mocked(company.getDetail).mockResolvedValue({
      data: {
        CountInfo: {
          PropertyClueCount: 2,
          PropertyClueRelatedCount: 3,
        },
      },
    });

    const wrapper = shallowMount(QCCXS, {
      propsData: {
        keyNo: 'C123',
      },
    });

    await wrapper.vm.$nextTick();

    expect(wrapper.vm.count).toBe(5);
  });

  it('TC04: 远程数据无 countInfo 时不更新 count', async () => {
    vi.mocked(company.getDetail).mockResolvedValue({
      data: {},
    });

    const wrapper = shallowMount(QCCXS, {
      propsData: {
        keyNo: 'C123',
      },
    });

    await wrapper.vm.$nextTick();

    expect(wrapper.vm.count).toBe(0);
  });

  it('TC05: hide 方法中调用 __DIMENSION_INSTANCE__.onHide', async () => {
    (window as any).__DIMENSION_INSTANCE__ = {
      onHide: vi.fn(),
    };

    const wrapper = shallowMount(QCCXS, {
      propsData: {
        keyNo: 'C123',
        ccxsCount: 2,
      },
    });

    await wrapper.find('div').trigger('click');

    expect((window as any).__DIMENSION_INSTANCE__.onHide).toHaveBeenCalled();
    expect(window.open).toHaveBeenCalledWith('/embed/risk-assests-clue?keyNo=C123');
    expect(wrapper.emitted('visibleChange')).toBeTruthy();
  });

  it('TC06: render 返回 null 当 count 为 0', () => {
    const wrapper = shallowMount(QCCXS, {
      propsData: {
        ccxsCount: 0,
      },
    });
    expect(wrapper.text()).toBe('');
  });
});
