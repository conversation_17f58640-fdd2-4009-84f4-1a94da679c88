import { computed, defineComponent, onMounted, ref } from 'vue';

import { company } from '@/shared/services';

const QCCXS = defineComponent({
  name: 'QCCXS',
  props: {
    ccxsCount: {
      type: [String, Number],
      default: '',
    },
    keyNo: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const count = ref(props.ccxsCount || 0);
    const isPerson = computed(() => props.keyNo.includes('P'));

    const getClueCount = (item) => {
      count.value = Number(item.PropertyClueCount) + Number(item.PropertyClueRelatedCount);
    };
    const loadClueCount = async () => {
      const params = isPerson.value
        ? {
            keyNo: props.keyNo,
            personId: props.keyNo,
          }
        : {
            keyNo: props.keyNo,
          };
      const res = await company.getDetail(params);
      const data = res?.data;
      if (data?.CountInfo) {
        getClueCount(data.CountInfo);
      }
    };
    const closeDialog = () => {
      emit('visibleChange');
    };
    const hide = () => {
      if ((window as any).__DIMENSION_INSTANCE__) {
        (window as any).__DIMENSION_INSTANCE__.onHide();
      }
      window.open(`/embed/risk-assests-clue?keyNo=${props.keyNo}`);
      closeDialog();
    };

    onMounted(() => {
      if (!props.ccxsCount && props.keyNo) {
        loadClueCount();
      }
    });

    return {
      count,
      isPerson,
      hide,
    };
  },
  render() {
    if (!this?.count) {
      return null;
    }
    return (
      <div onClick={this.hide}>
        <span style="cursor: pointer" class="text-warning">
          财产线索 {this.count} &gt;{' '}
        </span>
      </div>
    );
  },
});

export default QCCXS;
