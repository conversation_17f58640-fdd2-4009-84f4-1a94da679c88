import { mount, shallowMount } from '@vue/test-utils';

import QCard from '..';

describe('Card', () => {
  test('render', () => {
    const wrapper = shallowMount(QCard);
    expect(wrapper).toMatchSnapshot();
  });

  test('props: title', () => {
    const wrapper = mount(QCard, {
      propsData: {
        title: 'TestTitle',
      },
    });
    expect(wrapper.text()).toMatch(/TestTitle/);
  });

  test('slot: extra', () => {
    let wrapper = mount(QCard, {
      slots: {
        extra: 'TestExtra',
      },
    });
    expect(wrapper.text()).not.toMatch(/TestExtra/);

    wrapper = mount(QCard, {
      slots: {
        title: 'TestTitle',
        extra: 'TestExtra',
      },
    });

    expect(wrapper.text()).toMatch(/TestExtra/);
  });

  test('slots: body', () => {
    const wrapper = mount(QCard, {
      slots: {
        body: 'TestBody',
      },
    });
    expect(wrapper.text()).toMatch(/TestBody/);
  });
});
