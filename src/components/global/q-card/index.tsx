import { defineComponent } from 'vue';

import styles from './q-card.module.less';

const QCard = defineComponent({
  name: 'QCard',

  props: {
    rootStyle: {
      type: Object,
    },
    bodyStyle: {
      type: Object,
    },
    bodyClass: {
      type: [String, Object, Array],
    },
    headerStyle: {
      type: Object,
    },
    headerSticky: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
    },
    titleBorder: {
      type: Boolean,
      default: true,
    },
    mode: {
      type: String, // list
    },
  },

  render() {
    const { rootStyle, bodyStyle, headerStyle, bodyClass } = this;
    const { extra, body: bodySlot, default: defaultSlot } = this.$slots;
    const title = this.$slots.title || this.title;
    const body = defaultSlot || bodySlot;
    return (
      <div
        class={{
          [styles.root]: true,
          [styles.list]: this.mode === 'list',
        }}
        style={rootStyle}
      >
        {title && (
          <div class={[styles.header, this.headerSticky ? styles.headerSticky : null]} style={headerStyle}>
            <div
              class={{
                [styles.wrapper]: true,
                [styles.border]: this.titleBorder,
              }}
            >
              <div
                class={{
                  [styles.title]: true,
                }}
              >
                {title}
              </div>
              {extra && <div class={styles.extra}>{extra}</div>}
            </div>
          </div>
        )}

        {body && (
          <div class={[styles.body, bodyClass]} style={bodyStyle}>
            {body}
          </div>
        )}
      </div>
    );
  },
});

export default QCard;
