@import '@/styles/token.less';

.root {
  background-color: #fff;
  border-radius: 4px;

  & + & {
    // border-top: 10px solid #f5f5f5;
    margin-top: 10px;
  }

  &.list {
    padding: 0;

    .body {
      padding: 0;
    }
  }
}

.header {
  position: relative;
  padding: 0 15px;
  color: #333;

  &.headerSticky {
    position: sticky;
    top: 0;
    background-color: #fff;
    border-radius: 4px 4px 0 0;
    z-index: 1;
  }

  .wrapper {
    padding: 9px 0;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &.border {
      box-shadow: inset 0 -1px 0 0 @qcc-color-gray-500;
      // background-color: #fff;
      // border-bottom: 1px solid #eee;
    }
  }

  .title {
    display: flex;
    align-items: center;
    font-size: 15px;
    color: #333;
    font-weight: bold;
    height: 100%;

    > i,
    > em {
      margin: 0 5px; // 高亮总数边距
    }

    i {
      color: @qcc-color-black-200;
    }

    em {
      color: @qcc-color-red-600;
      font-weight: inherit;
    }
  }

  > .extra {
    display: flex;
    align-items: center;
    position: absolute;
    top: 0;
    right: 15px;
    color: #999;
    height: 100%;
    line-height: 1; // IE 样式问题
  }
}

.body {
  padding: 15px 15px 0;
}
