import { shallowMount } from '@vue/test-utils';

import QSimpleEntityLink from '../simple';

describe('QSimpleEntityLink', () => {
  test('render: text', () => {
    const wrapper = shallowMount(QSimpleEntityLink, {
      propsData: {
        value: {
          // KeyNo: '9cce0780ab7644008b73bc2120479d31',
          Name: '小米科技有限责任公司',
        },
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('render: as link', () => {
    const wrapper = shallowMount(QSimpleEntityLink, {
      propsData: {
        value: {
          KeyNo: '9cce0780ab7644008b73bc2120479d31',
          Name: '小米科技有限责任公司',
        },
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('render: as person', () => {
    const wrapper = shallowMount(QSimpleEntityLink, {
      propsData: {
        value: {
          KeyNo: 'p888',
          Name: '张三',
        },
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('render: as org', () => {
    const wrapper = shallowMount(QSimpleEntityLink, {
      propsData: {
        value: {
          Org: -2,
          KeyNo: '666',
          CompanyKeyNo: '888',
          Name: '测试组织机构',
        },
      },
    });
    expect(wrapper).toMatchSnapshot();
  });
});
