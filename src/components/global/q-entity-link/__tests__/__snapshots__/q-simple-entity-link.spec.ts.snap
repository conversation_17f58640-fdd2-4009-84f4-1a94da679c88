// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`QSimpleEntityLink > render: as link 1`] = `<a href="/embed/companyDetail?keyNo=9cce0780ab7644008b73bc2120479d31&amp;title=小米科技有限责任公司" target="_blank">小米科技有限责任公司</a>`;

exports[`QSimpleEntityLink > render: as org 1`] = `<a href="/embed/beneficaryDetail?personId=888_%E6%B5%8B%E8%AF%95%E7%BB%84%E7%BB%87%E6%9C%BA%E6%9E%84.html&amp;title=测试组织机构" target="_blank">测试组织机构</a>`;

exports[`QSimpleEntityLink > render: as person 1`] = `<a href="/embed/beneficaryDetail?personId=p888&amp;title=张三" target="_blank">张三</a>`;

exports[`QSimpleEntityLink > render: text 1`] = `<span>小米科技有限责任公司</span>`;
