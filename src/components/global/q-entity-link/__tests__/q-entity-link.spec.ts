import { shallowMount } from '@vue/test-utils';

import QEntityLink from '..';

describe('QEntityLink', () => {
  let $route;
  beforeEach(() => {
    $route = {
      path: '/',
      hash: '',
      query: {},
      params: {},
    };
  });

  test('render: coyObj', () => {
    const wrapper = shallowMount(QEntityLink, {
      propsData: {
        coyObj: {
          KeyNo: '9cce0780ab7644008b73bc2120479d31',
          Name: '小米科技有限责任公司',
        },
      },
      mocks: {
        $route,
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('render: coyObj', () => {
    const wrapper = shallowMount(QEntityLink, {
      propsData: {
        coyArr: [
          {
            KeyNo: '9cce0780ab7644008b73bc2120479d31',
            Name: '小米科技有限责任公司',
          },
        ],
      },
      mocks: {
        $route,
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('render: empty', () => {
    const wrapper = shallowMount(QEntityLink, {
      propsData: {},
      mocks: {
        $route,
      },
    });
    expect(wrapper).toMatchSnapshot();
  });
});
