import _ from 'lodash';
import { defineComponent, PropType } from 'vue';

import QLink from '../q-link';
import styles from './q-entity-link.module.less';
import { getHTMLText } from '@/utils';

// export enum EntityLinkType {
//   Simu = 6, // 私募
//   Fund = -1, // 基金
//   People = 2, // 个人
//   People1 = -2, // 个人1
//   Company = 0, // 公司
//   Investor = 13, // 投资人
//   Investor1 = -3, // 投资人1
//   Group = 20, // 集团
//   CompanyGroup = 21, // 企业族群
//   Product = 99, // 产品
// }

interface IEntity {
  Org: number;
  KeyNo: string;
  Name: string;

  CompanyKeyNo?: string;
}

const QEntityLink = defineComponent({
  name: 'QEntityLink',
  props: {
    /**
     * 传对象
     * @values {KeyNo:'9cce0780ab7644008b73bc2120479d31',Name:'小米科技有限责任公司'}
     */
    coyObj: {
      type: Object as PropType<Partial<IEntity>>,
      required: false,
    },
    /**
     * 传数组，也可以传对象，不知道类型就用这个
     * @values [{KeyNo:'9cce0780ab7644008b73bc2120479d31',Name:'小米科技有限责任公司'}]
     */
    coyArr: {
      type: Array as PropType<Partial<IEntity>[]>,
      required: false,
      default: () => [] as PropType<Partial<IEntity>[]>,
      validator(value) {
        return Array.isArray(value);
      },
    },
    /**
     * 从数组里找名字等于coyArrFname的一个元素
     */
    coyArrFname: {
      type: String,
      required: false,
    },
    // innerA: { // 在a标签里面
    //   type: Boolean,
    //   default: false
    // },
    moreGov: {
      type: Boolean,
      default: false,
    },
    coyMaxlength: {
      type: Number,
      default: 10,
    },
    /**
     * 分隔符
     * @values ,、 <br>、空字符
     */
    sep: {
      type: String,
      default: '，',
    },
    ellipsis: {
      type: Boolean,
      default: true,
    },
    /**
     * 省略符号
     * @values ... 等
     */
    ellipsisText: {
      type: String,
    },
    block: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    handledCoyArr() {
      // 数据格式兼容
      let coyArr: Partial<IEntity>[] = [];
      if (_.isArray(this.coyArr)) {
        coyArr = this.coyArr;
      } else if (_.isObject(this.coyArr)) {
        coyArr = [this.coyArr];
      }
      if (coyArr.length) {
        let newCoyArr = coyArr
          .filter((item) => item)
          .map((item) => ({
            ...item,
            KeyNo: _.get(item, 'keyno', item.KeyNo),
            Name: _.get(item, 'name', item.Name),
          }));
        if (this.coyMaxlength) {
          newCoyArr = newCoyArr.splice(0, this.coyMaxlength);
        }
        if (this.coyArrFname) {
          const noTagsStr = this.coyArrFname.replace(/<[^>]+>/g, '');
          const newCoyObj = newCoyArr.find((item) => {
            if (item.Name === noTagsStr) {
              Object.assign(item, {
                Name: this.coyArrFname,
              });
              return true;
            }
            return false;
          });
          if (newCoyObj) {
            newCoyArr = [newCoyObj];
          } else {
            newCoyArr = [];
          }
        }
        return newCoyArr;
      }
      return coyArr;
    },
  },

  methods: {
    createCoy(h, obj: Partial<IEntity>, linkDisplay: string) {
      const nameNode = obj.Name ? <span class={styles.name} domPropsInnerHTML={obj.Name}></span> : <span>-</span>;

      if (obj.KeyNo) {
        let orgPlink = 'firm';
        if (obj.Org === 99) {
          orgPlink = 'product';
        } else if (obj.Org === 13 || obj.Org === -3) {
          orgPlink = 'investor';
        } else if (obj.Org === 6) {
          orgPlink = 'simu';
        } else if (obj.Org === 18) {
          orgPlink = 'product';
        } else if (obj.KeyNo.startsWith('p')) {
          orgPlink = 'pl';
        }
        let url = '';
        switch (orgPlink) {
          case 'firm':
            url = `/embed/companyDetail?keyNo=${obj.KeyNo}&title=${getHTMLText(obj.Name)}`;
            break;
          case 'pl':
            url = `/embed/beneficaryDetail?personId=${obj.KeyNo}&title=${getHTMLText(obj.Name)}`;
            break;
          case 'investor':
            url = `/embed/investAgency?investId=${obj.KeyNo}&title=${getHTMLText(obj.Name)}`;
            break;
          case 'product':
            url = `/embed/product-info?id=${obj.KeyNo}&title=${getHTMLText(obj.Name)}`;
            break;
          default:
            break;
        }
        return obj.KeyNo === this?.$route?.params?.id ? (
          <QLink
            onClick={() => {
              if (url) window.open(url);
            }}
            display={linkDisplay}
            ellipsis={linkDisplay !== 'inline'}
          >
            {nameNode}
          </QLink>
        ) : (
          <QLink href={url} arrow={false} display={linkDisplay} ellipsis={linkDisplay !== 'inline'}>
            {nameNode}
          </QLink>
        );
      }
      if ((obj.Org === -2 || !obj.Org) && obj.CompanyKeyNo && obj.Name) {
        return (
          <QLink
            href={`/embed/beneficaryDetail?personId=${obj.CompanyKeyNo}_${encodeURIComponent(obj.Name)}.html&title=${getHTMLText(obj.Name)}`}
            display={linkDisplay}
            ellipsis={linkDisplay !== 'inline'}
          >
            {nameNode}
          </QLink>
        );
      }
      if (this.moreGov && obj.Name) {
        return nameNode;
      }
      return nameNode;
    },
  },

  render(h) {
    if (this.handledCoyArr?.length) {
      const eles: any[] = [];
      this.handledCoyArr.forEach((v, k) => {
        eles.push(this.createCoy(h, v, this.block ? 'block' : 'inline'));
        if (k !== this.handledCoyArr.length - 1) {
          eles.push(<span domPropsInnerHTML={this.sep}></span>);
        }
      });
      if (this.ellipsisText && this.coyArr.length > this.handledCoyArr.length) {
        eles.push(<span>{this.ellipsisText}</span>);
      }
      return (
        <span
        // onClick={(e) => {
        //   e.stopPropagation();
        // }}
        >
          {eles}
        </span>
      );
    }
    if (this.coyObj) {
      return (
        <span
          // onClick={(e) => {
          //   e.stopPropagation();
          // }}
          class={{
            [styles.container]: true,
            [styles.block]: this.block,
          }}
        >
          {this.$slots.avatar ? <i class={styles.avatar}>{this.$slots.avatar}</i> : null}
          <span class={{ [styles.content]: true, [styles.ellipsis]: this.ellipsis }}>
            {this.createCoy(h, this.coyObj, this.block ? 'block' : 'inline')}
            <span v-show={this.$slots.default} class={styles.extra}>
              {this.$slots.default}
            </span>
          </span>
        </span>
      );
    }
    return <span>-</span>;
  },
});

export default QEntityLink;
