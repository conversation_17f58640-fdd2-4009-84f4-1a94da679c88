import { defineComponent } from 'vue';

const QSimpleEntityLink = defineComponent({
  name: 'QSimpleEntityLink',
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
  },
  setup() {
    const getURL = (value) => {
      if (!value.KeyNo) return '';
      if (value.KeyNo[0] === 'p') {
        return `/embed/beneficaryDetail?personId=${value.KeyNo}&title=${value.Name}`;
      }
      if ((value.Org === -2 || !value.Org) && value.CompanyKeyNo && value.Name) {
        return `/embed/beneficaryDetail?personId=${value.CompanyKeyNo}_${encodeURIComponent(value.Name)}.html&title=${value.Name}`;
      }
      return `/embed/companyDetail?keyNo=${value.KeyNo}&title=${value.Name}`;
    };
    return { getURL };
  },
  render() {
    const href = this.getURL(this.value);
    if (!href) {
      return <span>{this.value.Name}</span>;
    }
    return (
      <a href={href} target="_blank">
        {this.value.Name}
      </a>
    );
  },
});

export default QSimpleEntityLink;
