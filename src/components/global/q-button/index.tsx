import _ from 'lodash';
import { Button } from 'ant-design-vue';
import { defineComponent, PropType } from 'vue';

import styles from './q-button.module.less';

type Size = 'large' | 'default' | 'small';
const buttonProps: {
  size: { type: PropType<string> };
} = (Button as any).props;

const handleSize = (size: Size) => {
  const map = {
    large: 'large',
    default: 'default',
    small: 'small',
  };

  if (!_.has(map, size)) {
    return map.default;
  }

  return map[size];
};

const QButton = defineComponent({
  name: 'QButton',
  props: {
    ...buttonProps,
    size: {
      type: String as PropType<Size>,
      default: 'default' as Size,
    },
  },
  computed: {
    inheritedProps(): Record<string, any> {
      const fields = _.difference(Object.keys(buttonProps), ['size']);

      return _.pick(this.$props, fields);
    },
  },
  methods: {
    handleClick() {
      this.$emit('click');
    },
  },
  render() {
    return (
      <span class={[styles.qButtonWrapper, this.size && styles[`${this.size}`]]}>
        <Button
          {...{ props: { ...this.inheritedProps } }}
          size={handleSize(this.size)}
          onClick={() => {
            this.handleClick();
          }}
        >
          {this.$slots.default}
        </Button>
      </span>
    );
  },
});

export default QButton;
