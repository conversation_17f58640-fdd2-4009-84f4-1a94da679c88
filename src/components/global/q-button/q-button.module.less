.qButtonWrapper {
  position: relative;
  display: inline-block;
  line-height: 1;

  :global {
    .ant-btn {
      height: 26px;
      padding: 3px 6px;
      font-size: 12px;
      line-height: 1;
      box-shadow: none !important;
      text-shadow: none !important;

      &:hover {
        > .anticon {
          color: #3cabfa !important;
        }
      }

      &:focus {
        > .anticon {
          color: #3cabfa !important;
        }
      }

      &.ant-btn-primary {
        > .anticon {
          color: #fff !important;
        }

        &:hover {
          > .anticon {
            color: #fff !important;
          }
        }
      }

      > .anticon {
        height: 16px;
        font-size: 16px;
        position: relative;
        top: 1px;
        color: #999;
      }

      > span {
        display: inline-block;
        vertical-align: top;
        line-height: 1.5;
      }

      > .anticon + span {
        margin-left: 3px;
      }
    }

    .ant-btn-two-chinese-chars > *:not(.anticon) {
      margin-right: 0;
      letter-spacing: normal;
    }
  }

  &.small {
    :global {
      .ant-btn-sm {
        height: 24px;
        padding: 2px 6px;
        font-size: 12px;
        line-height: 1;
      }
    }
  }

  &.large {
    :global {
      .ant-btn-lg {
        height: 40px;
        padding: 7px 15px;
        font-size: 16px;

        > .anticon {
          height: 16px;
          position: relative;
          top: 4px;
        }
      }
    }
  }
}

.qButtonWrapper + .qButtonWrapper {
  margin-left: 8px;
}
