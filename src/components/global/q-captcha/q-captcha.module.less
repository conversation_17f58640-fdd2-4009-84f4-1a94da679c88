@height: 40px;

@size: {
  sm: 22px;
  md: 32px;
  lg: 40px;
};

.container {
  height: 100%;
  // each(@size, {
  //   &.@{key} {
  //     height: @value;
  //   }
  // });
  :global {
    // 滑动验证码相关样式
    #aliyunCaptcha-sliding-wrapper {
      #aliyunCaptcha-sliding-body {
        // 滑轨背景色
        #aliyunCaptcha-sliding-text-box {
          background-color: #d4d4d4;

          // 滑轨文字颜色
          .aliyunCaptcha-sliding-text {
            background-image: -webkit-gradient(linear, left top, right top, color-stop(0, rgb(77, 77, 77)), color-stop(0.4, rgb(77, 77, 77)), color-stop(0.5, #fff), color-stop(0.6, rgb(77, 77, 77)), color-stop(1, rgb(77, 77, 77)));
            -webkit-text-fill-color: rgba(0,0,0,0);
          }

          // 验证通过
          &.verified {
            background-color: #00ad65;
            color: #fff;
          }
        }

        // 滑块样式
        #aliyunCaptcha-sliding-slider {
          border-radius: 2px;
          border: 1px solid #d8d8d8;
          box-shadow: none;
          color: #999;
        }
      }
    }
    // 验证成功
    #aliyunCaptcha-captcha-success {
      font-size: 16px;
      color: #fff;
    }
  }
}
