import loadjs from 'loadjs';
import { PropType, defineComponent, onMounted, ref } from 'vue';
import styles from './q-captcha.module.less';

type NoCaptchaSizeProp = 'lg' | 'md' | 'sm';

const NoCaptcha = defineComponent({
  name: 'QNoCaptcha',
  props: {
    appUrl: {
      type: String,
      default: '//o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js',
    },
    sceneId: {
      type: String,
      required: true,
    },
    size: {
      type: String as PropType<NoCaptchaSizeProp>,
      default: 'lg',
    },
    containerId: {
      type: String,
      default: 'nc-container',
    },
  },

  emits: ['success', 'error'],
  expose: ['reset'],
  setup(props, { emit }) {
    const failed = ref(false);
    const ncControllerRef = ref<HTMLElement | undefined>();

    const onSuccess = (result) => {
      emit('success', result);
    };
    const onFail = (error) => {
      emit('error', error);
    };
    const getInstance = (instance) => {
      // ncController.value = instance;
    };

    const activeCaptcha = async () => {
      // Reset
      failed.value = false;
      const d = new Date();
      const ts = `${d.getFullYear()}${d.getMonth() + 1}${d.getDate()}`;
      const appUrl = `${props.appUrl}?t=${ts}`;

      try {
        // 加载 JS 文件
        await loadjs(appUrl, { returnPromise: true });

        window.initAliyunCaptcha({
          SceneId: props.sceneId, // 场景ID。根据步骤二新建验证场景后，您可以在验证码场景列表，获取该场景的场景ID
          mode: 'embed', // 验证码模式。popup表示要集成的验证码模式为弹出式。无需修改
          element: '#captcha-element', // 页面上预留的渲染验证码的元素，与原代码中预留的页面元素保持一致。
          button: '#captcha-button', // 触发验证码弹窗的元素。
          success: onSuccess,
          fail: onFail,
          getInstance, // 绑定验证码实例函数
          slideStyle: {
            width: ncControllerRef.value?.clientWidth,
            height: ncControllerRef.value?.clientHeight,
          }, // 滑块验证码样式，支持自定义宽度和高度，单位为px。其中，width最小值为320 px
          language: 'cn', // 验证码语言类型
        });
      } catch (error) {
        failed.value = true;
        console.error(error);
      }
    };

    const reset = () => {
      activeCaptcha();
    };

    onMounted(() => {
      activeCaptcha();
    });

    return {
      reset,
      failed,
      ncControllerRef,
      activeCaptcha,
    };
  },

  render() {
    return (
      <div
        ref="ncControllerRef"
        id={this.containerId}
        class={{
          [styles.container]: true,
          [styles[this.size]]: true,
        }}
      >
        <div id="captcha-element"></div>
        <div v-show={false} id="captcha-button"></div>
        <div
          v-show={this.failed}
          id="refresh-captcha"
          style={{
            width: '100%',
            height: '100%',
            textAlign: 'center',
            backgroundColor: '#fff1f0',
            color: '#ef9f06',
            lineHeight: '40px',
            cursor: 'pointer',
          }}
          onClick={this.reset}
        >
          非常抱歉，控件加载失败，请点击重试
        </div>
      </div>
    );
  },
});

export default NoCaptcha;
