import loadjs from 'loadjs';
import { shallowMount } from '@vue/test-utils';

import { flushPromises } from '@/test-utils/flush-promises';

import QCaptcha from '..';

vi.mock('loadjs');

describe('QCaptcha', () => {
  beforeEach(() => {
    Object.defineProperty(window, 'initAliyunCaptcha', {
      value: (config) => {},
      writable: true,
    });
  });
  afterEach(() => {
    vi.resetAllMocks();
    vi.useRealTimers();
  });

  test('render', () => {
    vi.useFakeTimers({
      now: new Date('2024-01-01T00:00:00.000Z'),
    });
    const mockLoadjs = vi.mocked<() => Promise<boolean>>(loadjs).mockImplementation(() => Promise.resolve(true));

    const wrapper = shallowMount<InstanceType<typeof QCaptcha>>(QCaptcha, {
      propsData: {
        appUrl: '//test',
        sceneId: 'SCENE',
      },
    });
    expect(mockLoadjs).toHaveBeenCalledWith('//test?t=202411', { returnPromise: true });
    expect(wrapper).toMatchSnapshot();
  });

  test('render: failed', async () => {
    vi.mocked<() => Promise<unknown>>(loadjs).mockImplementation(() => Promise.reject());

    const wrapper = shallowMount<InstanceType<typeof QCaptcha>>(QCaptcha, {
      propsData: {
        appUrl: '//test',
        sceneId: 'SCENE',
      },
    });
    await flushPromises();
    expect(wrapper.vm.failed).toBe(true);
    expect(wrapper).toMatchSnapshot();
  });
});
