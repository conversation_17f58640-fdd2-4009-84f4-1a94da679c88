// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`QCaptcha > render 1`] = `
<div id="nc-container" class="container lg">
  <div id="captcha-element"></div>
  <div id="captcha-button" style="display: none;"></div>
  <div id="refresh-captcha" style="width: 100%; height: 100%; text-align: center; background-color: #fff1f0; color: #ef9f06; line-height: 40px; cursor: pointer; display: none;">非常抱歉，控件加载失败，请点击重试</div>
</div>
`;

exports[`QCaptcha > render: failed 1`] = `
<div id="nc-container" class="container lg">
  <div id="captcha-element"></div>
  <div id="captcha-button" style="display: none;"></div>
  <div id="refresh-captcha" style="width: 100%; height: 100%; text-align: center; background-color: #fff1f0; color: #ef9f06; line-height: 40px; cursor: pointer;">非常抱歉，控件加载失败，请点击重试</div>
</div>
`;
