.container {
  .titleWrapper {
    float: left;

    tr {
      &:hover {
        cursor: pointer;
      }
    }

    td {
      height: 46px;

      &.active {
        color: #128bed;
        background: #f2f9fc;
      }
    }
  }

  .content {
    margin-left: 10px;
    border: solid 1px #e4eef6;
    position: relative;
    overflow: hidden;

    .name {
      display: flex;
      color: #333;
      font-size: 14px;
      position: absolute;
      top: 11px;
      left: 15px;
    }

    .iconInfo {
      margin-left: 4px;
    }
  }
}
