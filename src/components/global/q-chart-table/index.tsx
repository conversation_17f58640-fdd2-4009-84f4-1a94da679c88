import { Popover, Icon } from 'ant-design-vue';
import { isFunction } from 'lodash';
import { defineComponent, PropType } from 'vue';

import { Current } from './types/q-chart-table.entity';
import QPlainTable from '../q-plain-table';
import QNoData from '../q-no-data';
import styles from './q-chart-table.module.less';

const TD_HEIGHT = 46;

const QChartTable = defineComponent({
  name: 'QChartTable',
  props: {
    dataSource: {
      type: Array as PropType<any[]>,
      required: true,
    },
    titleWidth: {
      type: String,
      default: '180px',
    },
    height: {
      type: String,
      default: '250px',
    },
  },
  data(): { current: Partial<Current | null> } {
    return {
      current: null,
    };
  },
  watch: {
    dataSource(v) {
      this.current = v[0];
    },
  },
  mounted() {
    this.current = this.dataSource[0];
  },
  methods: {
    genTitle() {
      return (
        <slot name="table">
          <div
            class={styles.titleWrapper}
            style={{
              width: this.titleWidth,
              marginLeft: `-${this.titleWidth}`,
              height: this.height,
            }}
          >
            <QPlainTable>
              {this.dataSource.map((item) => (
                <tr key={item.key} onMouseenter={() => (this as any).onHover(item)}>
                  <td
                    class={{
                      [styles.active]: this.current === item,
                    }}
                  >
                    {item.name}
                  </td>
                </tr>
              ))}
            </QPlainTable>
          </div>
        </slot>
      );
    },
    genContentWrapper() {
      if (!this.current) {
        return null;
      }
      return (
        <div
          class={styles.content}
          style={{
            height: this.height || `${this.dataSource.length * TD_HEIGHT + 1}px`,
          }}
        >
          {(this as any).genContentTitle()}
          {(this as any).genContent()}
        </div>
      );
    },
    genContentTitle() {
      const { current } = this;
      return (current as any).tips ? (
        <Popover placement="bottom">
          <template slot="content">
            <div domPropsInnerHTML={(this.current as any).tips} />
          </template>
          {(current as any).title ? (
            <div class={styles.name}>
              <span domPropsInnerHTML={(current as any).title} />
              <a class={styles.iconInfo}>
                <Icon class={styles.icon} type="info-circle" />
              </a>
            </div>
          ) : (
            <div class={styles.name}>{(current as any).name}</div>
          )}
        </Popover>
      ) : (
        <div>
          {(current as any).title ? (
            <div class={styles.name} domPropsInnerHTML={(current as any).title} />
          ) : (
            <div class={styles.name}>{(current as any).name}</div>
          )}
        </div>
      );
    },
    genContent() {
      const current: Current = this.current as any;
      if (current.customRender) {
        return isFunction(current.customRender) ? current.customRender() : current.customRender;
      }
      if (current.option) {
        return <div>图表</div>;
      }

      return <QNoData old absolute padding={10} />;
    },
    onHover(item) {
      if (this.current !== item) {
        this.$emit('hoverChanged', item);
      }
      this.current = item;
    },
  },
  render() {
    return (
      <div
        class={{
          [styles.container]: true,
        }}
        style={{
          paddingLeft: this.titleWidth,
        }}
      >
        {[
          // 标题
          (this as any).genTitle(),
          // 内容区域
          (this as any).genContentWrapper(),
        ]}
      </div>
    );
  },
});

export default QChartTable;
