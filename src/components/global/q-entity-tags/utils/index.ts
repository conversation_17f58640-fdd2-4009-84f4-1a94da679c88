import { message } from 'ant-design-vue';

// 获取跳转链接
export const getJumpLink = (keyNo, fp) => {
  let result = '';
  if (keyNo) {
    result = `/${keyNo[0] === 'p' ? 'pl' : 'firm'}/${keyNo}?fp=${fp}`;
  }
  return result;
};

export const getListTag = (keyNo, name) => {
  let link;
  let hoverText;
  if (name.includes('上市公司') || name.includes('A股') || name.includes('新三板')) {
    link = `/firm/${keyNo}?fp=realtime`;
  } else if (name.includes('港股')) {
    link = `/firm/${keyNo}?mtlist=h&fp=realtime`;
  } else if (name.includes('中概股')) {
    hoverText = '在国外上市的中国注册的公司，或虽在国外注册但业务和关系在大陆的公司的股票。';
  } else if (name.includes('新四板')) {
    hoverText =
      '即“区域性股权交易市场”，是为特定区域内的企业提供股权、债券转让和融资服务的私募市场，是公司规范治理、进入资本市场的孵化器，也为股份公司股权转让提供交易场所。';
  }
  return { link, hoverText };
};

// 格式化标签
export const formatTags = ({ inst, source, info }) => {
  const { keyNo, companyKeyNo } = info;
  const result: any = [];

  source.forEach((tag) => {
    let name;
    let hoverText;
    let tagClass;
    let click;
    let link;
    let navPos;
    if (tag === '大股东') {
      name = tag;
      tagClass = 'danger';
      hoverText =
        '大股东是指股票占比较大的股东，它表示该股东与其余的股东相比较，它的占比最大。控股股东一定是大股东，但大股东却并不一定是控股股东。';
    } else if (tag === '执行事务合伙人') {
      name = tag;
      tagClass = 'danger';
    } else if (tag === '实际控制人') {
      name = tag;
      tagClass = 'warning';
      navPos = 'kzrtupu';
    } else if (tag === '最终受益人') {
      name = tag;
      tagClass = 'primary';
      navPos = 'syrlist';
    } else if (tag === '有股权出质') {
      name = tag;
      tagClass = 'danger';
      if (info.stock) {
        link = getJumpLink(keyNo, 'pledgelist');
      } else {
        click = () => {
          inst.$modal.showDimension('gdPledges', {
            keyNo: companyKeyNo,
            personId: keyNo,
          });
        };
      }
    } else if (tag === '有股权冻结') {
      name = tag;
      tagClass = 'danger';
      link = getJumpLink(keyNo, 'assistancelist');
    } else if (tag === '有股权质押') {
      name = tag;
      tagClass = 'danger';
      link = getJumpLink(keyNo, 'spledgelist');
    } else if (tag === '有失信') {
      name = tag;
      tagClass = 'danger';
      link = getJumpLink(keyNo, 'shixinlist');
    } else if (tag === '有被执行') {
      name = tag;
      tagClass = 'danger';
      link = getJumpLink(keyNo, 'zhixinglist');
    } else if (tag === '有限制高消费') {
      name = tag;
      tagClass = 'danger';
      link = `/pl/${keyNo}?fp=sumptuarylist`;
    } else if (tag.includes('私募基金')) {
      name = tag;
      tagClass = 'pl';
    } else if (tag === '企业名存在变更') {
      name = tag;
      tagClass = 'primary';
    } else if (tag === '香港') {
      name = tag;
      tagClass = 'pl';
    } else if (/(上市公司|A股|新三板|新四板|中概股|港股)/.test(tag)) {
      name = tag;
      tagClass = 'list';
    }
    if (name) {
      result.push({
        name,
        hoverText,
        tagClass,
        click,
        link,
        navPos,
      });
    }
  });

  return result;
};

//
export const formatTagsByTagConfig = ({ inst, info, tagConfig }) => {
  const { keyNo, name: companyName, companyKeyNo } = info;
  const result: any = [];

  Object.keys(tagConfig).forEach((key) => {
    let name;
    let tagClass;
    let hoverText;
    let click;
    let link;
    let type;
    let navPos;
    if (key === 'IsBP') {
      type = 1;
      name = '大股东';
      tagClass = 'danger';
      hoverText =
        '大股东是指股票占比较大的股东，它表示该股东与其余的股东相比较，它的占比最大。控股股东一定是大股东，但大股东却并不一定是控股股东。';
    } else if (key === 'IsAC') {
      type = 2;
      name = '实际控制人';
      tagClass = 'warning';
      navPos = 'kzrtupu';
    } else if (key === 'IsB') {
      type = 3;
      name = '最终受益人';
      tagClass = 'primary';
      navPos = 'syrlist';
    } else if (key === 'IsSRP') {
      type = 6;
      name = '有股权出质';
      tagClass = 'danger';
      if (info.stock) {
        link = getJumpLink(keyNo, 'pledgelist');
      } else {
        click = () => {
          inst.$modal.showDimension('gdPledges', {
            keyNo: companyKeyNo,
            personId: keyNo,
          });
        };
      }
    } else if (key === 'IsSRF') {
      type = 7;
      name = '有股权冻结';
      tagClass = 'danger';
      link = getJumpLink(keyNo, 'assistancelist');
    } else if (key === 'IsP') {
      type = 5;
      name = '有股权质押';
      tagClass = 'danger';
      link = getJumpLink(keyNo, 'spledgelist');
    } else if (key === 'IsNTM') {
      type = 8;
      name = '有失信';
      tagClass = 'danger';
      link = getJumpLink(keyNo, 'shixinlist');
    } else if (key === 'IsEM') {
      type = 9;
      name = '有被执行';
      tagClass = 'danger';
      link = getJumpLink(keyNo, 'zhixinglist');
    } else if (key === 'IsLH') {
      type = 10;
      name = '有限制高消费';
      tagClass = 'danger';
      link = `/pl/${keyNo}?fp=sumptuarylist`;
    } else if (key === 'SI') {
      type = 11;
      name = tagConfig[key];
      tagClass = 'list';
      if (name.includes('。')) {
        const nameArr = name.split('。');
        const listObj0 = getListTag(keyNo, nameArr[0]);
        result.push({
          name: nameArr[0],
          tagClass,
          hoverText: listObj0.hoverText,
          link: listObj0.link,
        });
        name = nameArr[1];
      }
      const listObj = getListTag(keyNo, name);
      link = listObj.link;
      hoverText = listObj.hoverText;
    } else if (key === 'IsFM') {
      type = 13;
      name = '私募基金管理人';
      tagClass = 'warning';
      // link = `/company_simu?companykey=${keyNo}&companyname=${encodeURIComponent(companyName)}&id=${keyNo}`;
    } else if (key === 'IsFD') {
      type = 14;
      name = '私募基金';
      tagClass = 'pl';
      let fDObj: any = {};
      try {
        fDObj = JSON.parse(tagConfig[key]);
      } catch (err) {
        console.log(err);
      }

      if (fDObj.Id2) {
        link = `/investor/${fDObj.Id2}?fundId=${keyNo}`;
      } else if (fDObj.ProductId2) {
        click = () => {
          message.success('弹框privatefund');
          // uiService.showViewModal('privatefund', { id: fDObj.ProductId2 });
        };
      } else {
        link = `/company_simu?companykey=${fDObj.Id}&id=${fDObj.Id}&modalkeyno=${fDObj.ProductId}`;
      }
    } else if (key === 'Area') {
      type = 4;
      name = tagConfig[key];
      tagClass = 'pl';
      hoverText = `国籍（或地区）/注册地：${name}`;
    } else if (key === 'IsCoyNameChange') {
      type = 12;
      name = '企业名存在变更';
      tagClass = 'primary';
      link = `/firm/${keyNo}`;
    }
    if (name) {
      result.push({
        type,
        name,
        tagClass,
        hoverText,
        click,
        link,
        navPos,
      });
    }
  });
  const sortRule = [4, 11, 1, 2, 3, 8, 9, 10, 6, 5, 7, 12];
  result.sort((a: any, b: any) => {
    return sortRule.indexOf(a.type) - sortRule.indexOf(b.type);
  });

  return result;
};
