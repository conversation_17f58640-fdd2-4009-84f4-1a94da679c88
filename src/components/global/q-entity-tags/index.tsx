import { isEmpty } from 'lodash';
import { defineComponent, PropType } from 'vue';

import { formatTags, formatTagsByTagConfig } from './utils';
import QTag from '../q-tag';
import { Info } from './types/q-entity-tags.entity';
import styles from './q-entity-tags.module.less';

const TAG_BOTTOM = 5;
const DISABLED_TAG_TYPE = [13, 14];

const QEntityTags = defineComponent({
  name: 'QEntityTags',

  components: {
    QTag,
  },

  props: {
    dataSource: {
      type: Array,
      default: () => [],
    },
    // 标签配置，有了这个，dataSource就没用
    tagConfig: {
      type: Object,
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      default: () => {},
    },
    // 公司信息：{keyNo,name,companyKeyNo,stock}
    info: {
      type: Object as PropType<Partial<Info>>,
    },
    // 不能点击
    noClick: Boolean,
  },

  data() {
    return {
      list: [],
    };
  },

  mounted() {
    const that = this as any;
    const tagConfig = that.tagConfig;
    const props = {
      inst: this,
      source: that.dataSource,
      info: that.info ?? {},
    };
    if (!isEmpty(tagConfig)) {
      that.list = formatTagsByTagConfig({ ...props, tagConfig });
    } else {
      that.list = formatTags(props);
    }
  },

  methods: {
    genTag(tag, index) {
      const that = this as any;
      const contentNode: any = [];
      contentNode.push(
        <q-tag
          key={`tag_${index}`}
          type={tag.tagClass}
          hoverText={tag.hoverText}
          disabled={that.noClick || DISABLED_TAG_TYPE.includes(tag.type)}
          style={{
            marginBottom: `${TAG_BOTTOM}px`,
          }}
          onClick={() => that.onClick(tag)}
        >
          <span domPropsInnerHTML={tag.name} />
        </q-tag>
      );
      return contentNode;
    },
    onClick(tag) {
      const that = this as any;
      const keyNo = that.info.keyNo;
      if (that.noClick) {
        return;
      }
      if (tag.link) {
        that.$tabs.open(tag.link, tag.name);
      } else if (tag.click) {
        tag.click();
      }
    },
  },

  render() {
    const that = this as any;
    const { list } = that;

    if (!list.length) {
      return <div></div>;
    }

    return (
      <div
        class={styles.container}
        style={{
          marginBottom: `${-1 * TAG_BOTTOM}px`,
        }}
      >
        {list.map(that.genTag)}
      </div>
    );
  },
});

export default QEntityTags;
