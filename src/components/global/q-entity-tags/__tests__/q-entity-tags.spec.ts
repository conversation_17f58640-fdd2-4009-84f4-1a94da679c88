import { shallowMount } from '@vue/test-utils';

import QEntityTags from '..';

describe('QEntityTags', () => {
  test('render', () => {
    const wrapper = shallowMount<InstanceType<typeof QEntityTags>>(QEntityTags, {
      propsData: {
        tagConfig: {
          IsBP: true,
          SI: '上市公司。A股',
          IsFM: true,
          Area: '江苏苏州',
          IsCoynameChange: true,
        },
        info: {
          keyNo: 'p3483c261f318de061fb3ded0508bf90',
          name: '乐视',
        },
      },
    });
    expect(wrapper).toMatchSnapshot();
  });
});
