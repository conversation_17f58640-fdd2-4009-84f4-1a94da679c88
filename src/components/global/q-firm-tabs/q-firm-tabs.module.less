@import '@/styles/token.less';

.container {
  display: flex;
  color: #999;
}

.tab {
  font-size: 13px;
  font-weight: normal;
  border-bottom: 2px solid transparent;
  line-height: 26px;
  cursor: pointer;
  transition: all 0.2s;

  &.highlight {
    .count {
      color: #F04040;
    }
  }

  &:not(:last-child) {
    margin-right: 16px;
  }

  &.active {
    color: @text-color;
    font-weight: bold;
    border-color: @primary-color;

    &.extra {
      color: @primary-color;
      font-weight: normal;
      border-color: transparent;
    }
  }

  &.disable {
    cursor: default;
  }

  .count {
    color: @primary-color;
    margin-left: 4px;
  }

  .tooltip {
    color: #d8d8d8;
    margin-left: 4px;
    font-size: 14px;
  }

  .arrow {
    color: inherit !important;
  }

}

.notAllowed {
  color:#bbb;
  cursor: not-allowed;
}
