import { mount } from '@vue/test-utils';
import { Drawer } from 'ant-design-vue';

import QDrawer from '..';

describe('QDrawer', () => {
  it('renders with default props', async () => {
    const wrapper = mount(QDrawer, {
      propsData: {
        visible: true,
      },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent(Drawer).exists()).toBe(true);
    expect(wrapper.find('.ant-drawer-close').exists()).toBe(true);
  });

  it('renders with custom title', async () => {
    const wrapper = mount(QDrawer, {
      propsData: {
        visible: true,
        title: 'Custom Title',
      },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.find('h3').text()).toBe('Custom Title');
  });

  it('renders without title', () => {
    const wrapper = mount(QDrawer, {
      propsData: {
        visible: true,
        title: '',
      },
    });
    expect(wrapper.find('h3').exists()).toBe(false);
  });

  it('emits close event when close button is clicked', async () => {
    const wrapper = mount(QDrawer, {
      propsData: {
        visible: true,
        title: 'Custom Title',
      },
    });
    await wrapper.vm.$nextTick();
    await wrapper.find('.ant-drawer-close').trigger('click');
    expect(wrapper.emitted('visibleChange')).toBeTruthy();
    expect(wrapper.emitted('visibleChange')[0]).toEqual([false]);
    expect(wrapper.emitted('close')).toBeTruthy();
  });

  it('renders with inherited props', () => {
    const wrapper = mount(QDrawer, {
      propsData: {
        visible: true,
        width: 500,
      },
    });
    const drawer = wrapper.findComponent(Drawer);
    expect(drawer.props('width')).toBe(500);
  });

  it('does not render when visible is false', async () => {
    const wrapper = mount(QDrawer, {
      propsData: {
        visible: false,
      },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.html()).toBe('');
  });
});
