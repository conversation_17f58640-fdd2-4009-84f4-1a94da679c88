import _ from 'lodash';
import { Drawer } from 'ant-design-vue';
import { defineComponent, PropType } from 'vue';

import iconLogo from '@/assets/images/module-logo.png';

import styles from './q-drawer.module.less';

const drawerProps: {
  width: { type: PropType<string | number> };
} = (Drawer as any).props;

const QDrawer = defineComponent({
  name: 'QDrawer',
  model: {
    prop: 'visible',
    event: 'visibleChange',
  },
  props: {
    ...drawerProps,
    visible: {
      type: Boolean,
    },
    title: {
      type: String,
    },
    showLogo: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    inheritedProps(): Record<string, any> {
      const fields = _.difference(Object.keys(drawerProps), ['visible', 'width', 'title']);
      return _.pick(this.$props, fields);
    },
  },
  methods: {
    closeDrawer() {
      this.$emit('visibleChange', false);
      this.$emit('close');
    },
  },
  render() {
    const title = this.$slots.title || this.title;
    const showTitle = !!title;
    return (
      <Drawer
        {...{ props: { ...this.inheritedProps } }}
        visible={this.visible}
        width={this.width}
        closable={false}
        onClose={this.closeDrawer}
      >
        {showTitle && (
          <div slot="title" class={styles.title}>
            <div class={styles.left}>{title ? <h3>{title}</h3> : null}</div>
            {/* <div class={styles.right}>
              <img src={iconLogo} width="92" height="26" alt="企查查" />
            </div> */}
          </div>
        )}
        <button aria-label="Close" class="ant-drawer-close" onClick={this.closeDrawer}>
          <span class="ant-drawer-close-x">
            <q-icon type="icon-tanchuangguanbi" />
          </span>
        </button>
        {this.$slots.default}
      </Drawer>
    );
  },
});

export default QDrawer;
