// NOTE: 避免全局样式污染

:global {
//   .ant-drawer-header {
//     margin: 0 16px;
//     padding: 16px 0;
//     border-bottom: 1px solid #eee;

    .ant-drawer-close {
//       width: 20px;
      height: 54px;
//       font-size: 20px;
    }
//   }

//   .ant-drawer-body {
//     height: calc(100% - 59px);
//     overflow-y: auto;
//     padding: 16px;
//   }
}

.title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 36px;

  .left {
    & > * {
      display: inline-block;
    }

    h3 {
      font-size: 16px;
      margin: 0 8px 0 0;
      line-height: 24px;
      color: #333;
      font-weight: bold;
    }
  }

  .right img {
    display: block;
  }
}
