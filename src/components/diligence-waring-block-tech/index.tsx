import { computed, defineComponent, onMounted, ref } from 'vue';

import DiligenceRiskLevelTech from '../diligence-risk-level-tech';
import DiligenceRiskLevelBarTech from '../diligence-risk-level-bar-tech';
import DiligenceRiskTrendTech from '../diligence-risk-trend-tech';
import DiligenceRiskTechRadarChart from '../diligence-risk-tech-radar-chart';
import { TechRiskLevel, getTechRiskColor } from '@/config/risk.config';
import { diligence as diligenceService } from '@/shared/services';

// 常量定义
const CONSTANTS = {
  COLORS: {
    DEFAULT_TEXT: '#666',
    GRAY: '#999',
  },
  FONT_SIZES: {
    SCORE_DEFAULT: '24px',
    SCORE_NR: '22px',
  },
  TEXT: {
    NR: 'NR',
    COMPANY_NAME: '该企业',
    NON_TECH_COMPANY_NOTE: '非科技型企业无法排名',
    RADAR_CHART_WIDTH: '245px',
    RADAR_CHART_HEIGHT: '191px',
    RADAR_CHART_CONTAINER_WIDTH: '245px',
  },
} as const;

const INDICATOR_TYPES = ['quality', 'innovation', 'development', 'scale', 'capability', 'stability'];

const INDICATOR_TYPE_MAP = {
  quality: '技术质量',
  innovation: '运营能力',
  development: '发展能力',
  scale: '技术规模',
  capability: '技术能力',
  stability: '技术稳定性',
};

/**
 * 指标策略配置 - 使用策略模式管理不同指标的取值范围
 */
interface IndicatorStrategy {
  min: number;
  max: number;
}
/**
 * 指标策略配置
 */
const INDICATOR_STRATEGIES: Record<string, IndicatorStrategy> = {
  [INDICATOR_TYPE_MAP.quality]: { min: 0, max: 41 },
  [INDICATOR_TYPE_MAP.innovation]: { min: -35, max: 15 },
  [INDICATOR_TYPE_MAP.development]: { min: -5, max: 22 },
  [INDICATOR_TYPE_MAP.scale]: { min: 0, max: 10 },
  [INDICATOR_TYPE_MAP.capability]: { min: 0, max: 6 },
  [INDICATOR_TYPE_MAP.stability]: { min: -5, max: 17 },
};

/**
 * 按照指标类型顺序处理数据
 * @param types INDICATOR_TYPES 数组
 * @param dataList 包含 name 和 value 的数据列表，其中 name 是中文名称（INDICATOR_TYPE_MAP 的 value）
 * @returns 按照 INDICATOR_TYPES 顺序排列的数据数组
 */
const processDataByTypesOfIndicator = (types: string[], dataList: { name: string; value: number }[]) => {
  const result: number[] = [];

  // 按照 INDICATOR_TYPES 的顺序遍历
  types.forEach((typeKey) => {
    // 获取对应的中文名称
    const targetName = INDICATOR_TYPE_MAP[typeKey];

    // 在 dataList 中查找匹配的数据项
    const foundItem = dataList.find((item) => item.name === targetName);

    // 如果找到了对应的数据项，添加其 value；否则添加 0
    result.push(foundItem ? foundItem.value : 0);
  });

  return result;
};

/**
 * 排名类型映射
 */
const RANKING_TYPE_MAP = [
  {
    key: 'national' as const,
    text: '全国',
    scheme: ['#65789B'],
    dataName: '全国行业平均',
  },
  {
    key: 'province' as const,
    text: '全省',
    scheme: ['#A651F1'],
    dataName: '全省行业平均',
  },
];

/**
 * 创建雷达图数据项
 * @param rankingType 排名类型配置
 * @param rankingData 排名数据
 * @returns 处理后的数据项，如果数据无效则返回null
 */
const createRadarDataItem = (rankingType: (typeof RANKING_TYPE_MAP)[number], rankingData: { name: string; score: number }[]) => {
  if (!Array.isArray(rankingData) || rankingData.length === 0) {
    return null;
  }

  const processedData = processDataByTypesOfIndicator(
    INDICATOR_TYPES,
    rankingData.map(({ name, score }) => ({ name, value: score }))
  );

  return {
    name: rankingType.dataName,
    value: processedData,
  };
};

/**
 * 科技型企业评分排名
 */
const useTechRanking = () => {
  const data = ref<{
    /** 是否为科技型企业 */
    isTechCompany: boolean;
    /** 国际二级行业 */
    industryL2: string | undefined;
    /** qcc二级行业 */
    qccIndustryB: string | undefined;
    /** 环比评分 */
    scoreTrend: number | undefined;
    /** 上月评分 */
    previousScore: number | undefined;
    /** 排名信息 */
    rankingText: {
      province: string | undefined;
      national: string | undefined;
    };
    /** 排名数据 */
    rankingData: {
      national: { score: number; name: string; key: string }[];
      province: { score: number; name: string; key: string }[];
    };
    Area: {
      City: undefined;
      Province: undefined;
      County: undefined;
    };
  }>({
    isTechCompany: false,
    industryL2: undefined,
    scoreTrend: undefined,
    previousScore: undefined,
    qccIndustryB: undefined,
    rankingText: {
      province: undefined,
      national: undefined,
    },
    rankingData: {
      national: [],
      province: [],
    },
    Area: {
      City: undefined,
      Province: undefined,
      County: undefined,
    },
  });

  const reset = () => {
    data.value = {
      isTechCompany: false,
      industryL2: undefined,
      qccIndustryB: undefined,
      scoreTrend: undefined,
      previousScore: undefined,
      rankingText: {
        province: undefined,
        national: undefined,
      },
      rankingData: {
        national: [],
        province: [],
      },
      Area: {
        City: undefined,
        Province: undefined,
        County: undefined,
      },
    };
  };

  /**
   * 获取科技型企业评分排名
   * @param keyNo 企业keyNo
   * @param currentScore 当前评分
   */
  const execute = async (keyNo: string) => {
    try {
      const {
        isTechCompany,
        previousTotalScore,
        formattedProvinceRankPercentage,
        formattedNationalRankPercentage,
        industryL2,
        qccIndustryB,
        provinceAVGGroup,
        nationalAVGGroup,
        Area,
      } = await diligenceService.getTechScoreCard(keyNo);

      // 行业信息：无论是否为科技企业都需要显示qcc行业信息
      data.value.industryL2 = industryL2;
      data.value.qccIndustryB = qccIndustryB;
      data.value.Area = Area;
      // 非科技型企业，直接返回
      if (!isTechCompany) {
        return;
      }

      data.value.isTechCompany = isTechCompany;
      data.value.previousScore = previousTotalScore;
      data.value.rankingText = {
        province: formattedProvinceRankPercentage,
        national: formattedNationalRankPercentage,
      };
      data.value.rankingData = {
        national: nationalAVGGroup,
        province: provinceAVGGroup,
      };
    } catch (error) {
      console.error(error);
      reset();
    }
  };

  return {
    data,
    execute,
  };
};

/**
 * 获取风险等级对应的文本颜色
 * @param level 风险等级
 * @returns 对应的颜色值
 */
const getLevelTextColor = (level?: TechRiskLevel) => {
  if (level === undefined) {
    return CONSTANTS.COLORS.DEFAULT_TEXT;
  }
  return getTechRiskColor(level) || CONSTANTS.COLORS.DEFAULT_TEXT;
};

const DiligenceWarningBlockTech = defineComponent({
  name: 'DiligenceWarningBlockTech',
  props: {
    /**
     * 是否显示图表
     */
    chart: {
      type: Boolean,
      default: true,
    },
    riskInfo: {
      type: Object,
      default: () => ({}),
    },
    scrollToView: {
      type: Function,
    },
    theme: {
      type: Object,
      default: () => ({
        primary: '#128bed',
      }),
    },
  },
  setup(props) {
    /** 排名数据 */
    const { data: techRankingData, execute } = useTechRanking();
    /** 图表图例 */
    const chartLegends = computed(() => {
      const result: { scheme: string[]; text: string }[] = [];
      if (techRankingData.value.isTechCompany) {
        result.push({
          scheme: [props.theme.primary],
          text: CONSTANTS.TEXT.COMPANY_NAME,
        });
        RANKING_TYPE_MAP.forEach(({ key, text, scheme }) => {
          if (techRankingData.value.rankingText[key]) {
            result.push({
              scheme,
              text: `${text}行业平均`,
            });
          }
        });
      }
      return result;
    });

    /** 计算雷达图维度数据 */
    const radarChartData = computed(() => {
      if (!Array.isArray(props.riskInfo.details.groupMetricScores) || props.riskInfo.details.groupMetricScores.length === 0) {
        return {
          indicator: [],
          data: [],
        };
      }

      // 公司维度
      const indicator = INDICATOR_TYPES.map((type) => {
        const groupMetric = props.riskInfo.details.groupMetricScores.find(
          (item) => item.groupDefinition.groupName === INDICATOR_TYPE_MAP[type]
        );
        return {
          name: INDICATOR_TYPE_MAP[type],
          value: groupMetric ? groupMetric.score : undefined,
          ...INDICATOR_STRATEGIES[INDICATOR_TYPE_MAP[type]],
        };
      });

      const data: { name: string; value: number[] }[] = [];

      // 使用统一的数据处理逻辑处理排名数据
      RANKING_TYPE_MAP.forEach((rankingType) => {
        const rankingData = techRankingData.value?.rankingData?.[rankingType.key];
        const dataItem = createRadarDataItem(rankingType, rankingData);
        if (dataItem) {
          data.push(dataItem);
        }
      });

      // 按照 INDICATOR_TYPES 顺序处理数据
      const companyData = processDataByTypesOfIndicator(
        INDICATOR_TYPES,
        props.riskInfo.details.groupMetricScores.map(({ groupDefinition, score }) => ({ name: groupDefinition.groupName, value: score }))
      );

      data.push({
        name: CONSTANTS.TEXT.COMPANY_NAME,
        value: companyData,
      });

      return {
        indicator,
        data,
      };
    });

    const highlightText = (text: string, primaryColor: string) => {
      return (
        <strong class="relative after:absolute after:left-0 after:right-0 after:top-[calc(100%-4px)] after:w-full after:h-6px after:bg-blue/20 after:content-['']">
          {text}
        </strong>
      );
    };

    const getScoreTextTheme = (score: number) => {
      if (score < 0) {
        return {
          text: CONSTANTS.TEXT.NR,
          style: {
            fontSize: CONSTANTS.FONT_SIZES.SCORE_NR,
          },
        };
      }
      return {
        text: score,
        style: {
          fontSize: CONSTANTS.FONT_SIZES.SCORE_DEFAULT,
        },
      };
    };

    onMounted(() => {
      execute(props.riskInfo.companyId);
    });

    return {
      chartLegends,
      radarChartData,

      techRankingData,

      highlightText,
      getScoreTextTheme,
    };
  },
  render() {
    const { riskInfo, highlightText, getScoreTextTheme } = this;

    const textTheme = getScoreTextTheme(riskInfo.score);

    return (
      <section class="flex items-start justify-between gap-40px w-100%">
        {/* 图表 */}
        {this.chart ? (
          <div class="flex flex-col gap-8px">
            {/* 评分环形卡 */}
            <DiligenceRiskLevelTech level={riskInfo.riskLevel}>
              <div class="text-24px font-700 leading-29px text-center color-#666 font-[D-Din,sans-serif]">{textTheme.text}</div>
              <div style={{ color: getLevelTextColor(riskInfo.riskLevel), ...textTheme.style }} domPropsInnerHTML={riskInfo.riskName}></div>
            </DiligenceRiskLevelTech>

            {/* 评分趋势变化 (仅在科技型企业时显示) */}
            <div class="min-h-22px">
              {this.techRankingData.isTechCompany ? (
                <DiligenceRiskTrendTech
                  previousScore={this.techRankingData.previousScore}
                  score={riskInfo.score}
                  companyId={riskInfo.companyId}
                />
              ) : null}
            </div>

            {/* 评分条形卡 */}
            <DiligenceRiskLevelBarTech level={riskInfo.riskLevel} score={riskInfo.score}></DiligenceRiskLevelBarTech>
          </div>
        ) : null}

        <div class="flex-1 flex flex-col gap-4px bg-white/50">
          {/* Header */}
          <div class="min-h-49px flex items-center px-20px justify-between">
            <div class="text-18px font-500 color-#333">科创健康性概览</div>
            <div>
              <ul class="flex gap-22px">
                {this.chartLegends.map(({ text, scheme }, index) => {
                  const key = `radar-chart-legend-${index}`;
                  return (
                    <li class="inline-flex items-center gap-4px" key={key}>
                      <i
                        class={`rounded-6px h-4px w-12px`}
                        style={{
                          background: scheme[0],
                        }}
                      ></i>
                      <span>{text}</span>
                    </li>
                  );
                })}
              </ul>
            </div>
          </div>

          <div class="flex justify-between gap-20px px-30px py-4px items-center">
            <div class="flex-1 flex flex-col gap-12px lh-20px">
              {/* 行业信息 */}
              <div v-show={this.techRankingData.qccIndustryB}>
                <span v-show={this.techRankingData?.Area?.Province}> 该企业所属{this.techRankingData.Area.Province}，</span>{' '}
                企查查二级行业是 {highlightText(`「${this.techRankingData.qccIndustryB}」`, this.theme.primary)}
              </div>

              {/* 排名信息 */}
              <div v-show={this.techRankingData.isTechCompany} class="flex flex-col gap-12px lh-20px">
                {RANKING_TYPE_MAP.map(({ key, text }, index) => {
                  const value = this.techRankingData.rankingText[key];
                  return (
                    <div key={index}>
                      {highlightText(text, this.theme.primary)}
                      同行业的科技型企业中排名{' '}
                      <strong class="font-700" style={{ color: this.theme.primary }}>
                        前{value}
                      </strong>
                    </div>
                  );
                })}
              </div>

              {/* 无排名 */}
              <div v-show={!this.techRankingData.isTechCompany} style={{ color: CONSTANTS.COLORS.GRAY }}>
                {CONSTANTS.TEXT.NON_TECH_COMPANY_NOTE}
              </div>
            </div>

            {/* 雷达图 */}
            <div style={{ width: CONSTANTS.TEXT.RADAR_CHART_CONTAINER_WIDTH }}>
              <DiligenceRiskTechRadarChart
                indicator={this.radarChartData.indicator}
                data={this.radarChartData.data}
                width={CONSTANTS.TEXT.RADAR_CHART_WIDTH}
                height={CONSTANTS.TEXT.RADAR_CHART_HEIGHT}
              />
            </div>
          </div>
        </div>
      </section>
    );
  },
});
export default DiligenceWarningBlockTech;
