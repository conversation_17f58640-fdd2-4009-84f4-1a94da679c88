/* eslint-disable consistent-return */
/* eslint-disable no-underscore-dangle */
const _settings = {
  default: {
    note: '默认',
    types: {
      default: {
        title: '',
        style: 'primary',
      },
    },
  },

  0: {
    note: '默认人标签',
    types: {
      default: {
        title: '',
        style: 'danger',
      },
    },
  },

  903: {
    note: '登记状态',
    types: {
      default: {
        title: '',
        style: 'success',
      },
      在业: {
        title: '企业依法存在并继续正常营业。',
        style: 'success',
      },
      续存: {
        title: '企业在通过改制重组后，以集团公司或母公司的形式存在的未上市企业，被称为存续企业。',
        style: 'success',
      },
      吊销: {
        title:
          '吊销营业执照，是工商局根法对违法的企业法人作出的一种行政处罚。被吊销营业执照后，企业应当依法进行清算，清算程序结束并办理工商注销登记后，该企业法人才归于消灭。',
        style: 'danger',
      },
      注销: {
        title: '企业已依法注销营业执照，失去法人资格。',
        style: 'danger',
      },
      停业: {
        title: '企业依法停止经营活动。',
        style: 'danger',
      },
      清算: {
        title:
          '企业按章程规定解散以及由于破产或其他原因宣布终止经营后，对企业的财产、债权、债务进行全面清查，并进行收取债权，清偿债务和分配剩余财产的经济活动。',
        style: 'danger',
      },
      迁入: {
        title: '企业住所迁移。',
        style: 'success',
      },
      迁出: {
        title: '企业住所迁移。',
        style: 'success',
      },
      撤销: {
        title: '公司被有关部门强制撤回已生效文件、证书并注销营业执照。',
        style: 'danger',
      },
      筹建: {
        title: '公司尚未成立。',
        style: 'success',
      },
    },
  },
  112: {
    note: '香港企业',
    types: {
      default: {
        title: '香港企业',
        style: 'pl',
      },
    },
  },
  115: {
    note: '台湾企业',
    types: {
      default: {
        title: '台湾企业',
        style: 'pl',
      },
    },
  },
  114: {
    note: '律所',
    types: {
      default: {
        title: '律所',
        style: 'pl',
      },
    },
  },
  203: {
    note: '基金会',
    types: {
      default: {
        title: '',
        style: 'pl',
      },
    },
  },
  200: {
    note: '社会组织',
    types: {
      default: {
        title: '',
        style: 'pl',
      },
    },
  },
  111: {
    note: '事业单位',
    types: {
      default: {
        title: '',
        style: 'pl',
      },
    },
  },
  109: {
    note: '学校',
    types: {
      default: {
        title: '',
        style: 'pl',
      },
    },
  },
  110: {
    note: '医院',
    types: {
      default: {
        title: '',
        style: 'pl',
      },
    },
  },
  402: {
    note: '海外企业注册地',
    types: {
      default: {
        title: '',
        style: 'pl',
      },
    },
  },
  99: {
    note: '曾用名',
    types: {
      default: {
        title: '',
        style: 'warning',
      },
    },
  },
  3: {
    note: '融资',
    types: {
      default: {
        title: '',
        style: 'list',
      },
    },
  },
  2: {
    note: 'A股',
    types: {
      default: {
        title: '',
        style: 'list',
      },
    },
  },
  6: {
    note: '大陆公司港股',
    types: {
      default: {
        title: '',
        style: 'list',
      },
    },
  },
  401: {
    note: '香港公司港股',
    types: {
      default: {
        title: '',
        style: 'list',
      },
    },
  },
  1: {
    note: '新三板',
    types: {
      default: {
        title: '',
        style: 'list',
      },
    },
  },
  301: {
    note: '新四板',
    types: {
      default: {
        title: '',
        style: 'list',
      },
    },
  },
  7: {
    note: '中概股',
    types: {
      default: {
        title: '',
        style: 'list',
      },
    },
  },
  501: {
    note: '科创板',
    types: {
      default: {
        title: '',
        style: 'list',
      },
    },
  },
  502: {
    note: '科创板状态',
    types: {
      default: {
        title: '',
        style: 'list',
      },
    },
  },
  108: {
    note: '高新技术企业',
    types: {
      default: {
        title: '',
        style: 'primary',
      },
    },
  },
  404: {
    note: '建筑企业',
    types: {
      default: {
        title: '',
        style: 'primary',
      },
    },
  },
  208: {
    note: '投资机构',
    types: {
      default: {
        title: '投资机构',
        style: 'warning',
      },
    },
  },
  207: {
    note: '私募基金',
    types: {
      default: {
        title: '',
        style: 'warning',
      },
    },
  },
  904: {
    note: '失信被执行人',
    types: {
      default: {
        title: '',
        style: 'danger',
      },
    },
  },
  902: {
    note: '严重违法',
    types: {
      default: {
        title: '',
        style: 'danger',
      },
    },
  },
  901: {
    note: '经营异常',
    types: {
      default: {
        title: '',
        style: 'danger',
      },
    },
  },
  302: {
    note: '非正常户',
    types: {
      default: {
        title: '',
        style: 'danger',
      },
    },
  },
  905: {
    note: '发票抬头',
    types: {
      default: {
        title: '',
        style: 'primary',
      },
    },
  },
};
const getType = (type, text) => {
  let tag1 = _settings[type];
  if (!tag1) {
    tag1 = _settings.default;
  }

  let tag2 = tag1?.types[text];
  if (!tag2) {
    tag2 = tag1.types.default;
  }

  if (tag2) {
    return tag2.style;
  }
};

export default {
  getType,
};
