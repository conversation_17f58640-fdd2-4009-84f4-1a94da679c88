import UAParser from 'ua-parser-js';

class DeviceInfo {
  constructor(ua) {
    this.parser = new UAParser(ua || undefined);
    this.result = this.parser.getResult();
  }

  isMacOS() {
    return this.result.os.name === 'Mac OS';
  }

  isIE() {
    return this.result.browser.name === 'IE';
  }

  isSafari() {
    return this.result.browser.name === 'Safari';
  }

  isChrome() {
    return (
      this.result.browser.name === 'Chrome' ||
      this.result.browser.name === 'Chrome Headless' ||
      this.result.browser.name === 'Chrome WebView' ||
      this.result.browser.name === 'Chromium'
    );
  }

  isFireFox() {
    return this.result.browser.name === 'Firefox';
  }

  isMobile() {
    const deviceAgent = this.result.ua.toLowerCase();
    return deviceAgent.match(/(iphone|ipod|android)/);
  }

  isPad() {
    const deviceAgent = this.result.ua.toLowerCase();
    return deviceAgent.match(/(ipad|pad)/);
  }
}

const deviceInfo = new DeviceInfo();

export { deviceInfo, DeviceInfo };

export default {
  deviceInfo,
  DeviceInfo,
};
