const getOperTypeName = (operType) => {
  operType = +operType;
  let name = '';
  switch (operType) {
    case 1:
      name = '法定代表人';
      break;
    case 2:
      name = '执行事务合伙人';
      break;
    case 3:
      name = '负责人';
      break;
    case 4:
      name = '经营者';
      break;
    case 5:
      name = '投资人';
      break;
    case 6:
      name = '董事长';
      break;
    case 7:
      name = '理事长';
      break;
    case 8:
      name = '代表人';
      break;
    default:
      name = '法定代表人';
  }
  return name;
};

export default { getOperTypeName };
