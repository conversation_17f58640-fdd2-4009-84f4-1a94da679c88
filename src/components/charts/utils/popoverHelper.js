/* eslint-disable func-names */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable no-underscore-dangle */

import Vue from 'vue';
import _ from 'lodash';
import resizeDetector from 'element-resize-detector';

import { appRouter as router } from '@/router/app.router';

const _settings = {
  fadeDuration: 400,
};

const _getRightPositionForPopover = ({ targetPosition, targetSize, popoverSize, isLeft }) => {
  const windowSize = {
    width: $(window).width(),
    height: $(window).height(),
  };

  let left;
  let top;
  let right;
  let bottom;

  const y = targetPosition.top - popoverSize.height;
  if (y < 0) {
    top = targetPosition.top + targetSize.height;
  } else {
    top = y;
  }

  const min = targetPosition.left - popoverSize.width;
  if (min < 0) {
    left = targetPosition.left + targetSize.width;
  } else {
    left = targetPosition.left - popoverSize.width;
  }

  return { left, top, right, bottom };
};

const showPopover = ({ component, data, targetPosition, targetSize, container, identity, isLeft, containerSize }) => {
  const Constructor = Vue.extend(component);
  const popover = new Constructor({
    router,
    data() {
      return data || {};
    },
  });
  const instance = popover.$mount();
  const $instance = $(instance.$el);
  $instance.attr('id', identity).addClass('detail-popover');

  $instance
    .css({
      zIndex: 999,
      position: 'absolute',
    })
    .hide();

  $(container).append($instance);
  setTimeout(() => {
    const position = _getRightPositionForPopover({
      targetPosition,
      targetSize,
      containerSize,
      popoverSize: {
        width: $instance.outerWidth(),
        height: $instance.outerHeight(),
      },
      isLeft,
    });

    $instance.css(position).fadeIn(400);

    $instance.mouseover(() => {
      $instance.stop(true).fadeIn(0);
    });

    $instance.mouseout(() => {
      $instance.fadeOut(400, function () {
        $instance.remove();
      });
    });
  }, 400);
};

const showDetailCard = ({ component, data, container, identity, size }) => {
  $('.detail-card').remove();
  const Constructor = Vue.extend(component);
  const detail = new Constructor({
    data() {
      return data || {};
    },
  });
  const instance = detail.$mount();
  const $instance = $(instance.$el);
  $instance.attr('id', identity).addClass('detail-card');
  $instance
    .css({
      zIndex: 999,
      position: 'fixed',
    })
    .hide();

  $(container).append(instance.$el);
  let position = {};
  setTimeout(() => {
    if (size) {
      const $window = $(window);
      const windowSize = {
        width: $window.width(),
        height: $window.height(),
      };
      let left;
      let top;
      let right;
      let bottom;

      const margin = 55;
      const y = size.top - $instance.outerHeight() - margin;
      if (y < 0) {
        top = size.top + size.height;
      } else {
        bottom = windowSize.height - size.top;
      }
      const min = size.left - $instance.outerWidth();
      if (min < 0) {
        left = size.left + size.width;
      } else {
        right = windowSize.width - size.left;
      }
      position = { left, top, right, bottom };
    } else {
      let instanceTop = 130;
      const containerHeight = $(container).height();
      if (containerHeight > 700) {
        instanceTop = 200;
      }
      position = {
        left: 10,
        top: instanceTop,
      };
    }
    $instance.css(position).fadeIn(_settings.fadeDuration);
  }, 500);

  $instance.mouseover(() => {
    $instance.stop(true).fadeIn(0);
  });

  $instance.mouseout(() => {
    $instance.fadeOut(_settings.fadeDuration, function () {
      $instance.remove();
    });
  });
};

const hideDetailCard = () => {
  $('.detail-card').remove();
};

const showSearchDialog = ({ component, data, container }) => {
  $('.search-dialg').remove();

  const Constructor = Vue.extend(component);
  const searchDialog = new Constructor({
    data() {
      return data || {};
    },
  });

  const instance = searchDialog.$mount();
  const $instance = $(instance.$el);

  $instance
    .css({
      zIndex: 999,
      position: 'fixed',
    })
    .hide();

  const $masker = $('<div></div>');
  $masker
    .css({
      position: 'fixed',
      left: 0,
      top: 0,
      right: 0,
      bottom: 0,
      zIndex: 999,
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
    })
    .addClass('search-dialg')
    .hide()
    .appendTo(container);

  $instance.close = () => {
    $masker.fadeOut(_settings.fadeDuration, function () {
      $masker.remove();
    });
  };

  $masker.click(function (sender) {
    if (sender.target === $masker[0]) {
      $instance.close();
    }
  });

  $masker.append($instance).fadeIn(_settings.fadeDuration);

  const $window = $(window);
  const windowSize = {
    width: $window.width(),
    height: $window.height(),
  };

  $instance
    .css({
      left: windowSize.width / 2 - $instance.outerWidth() / 2,
      top: windowSize.height / 2 - $instance.outerHeight() / 2,
    })
    .fadeIn(_settings.fadeDuration);

  const detector = resizeDetector();
  detector.listenTo(
    container,
    _.debounce(() => {
      const $window = $(window);
      const windowSize = {
        width: $window.width(),
        height: $window.height(),
      };
      $instance.css({
        left: windowSize.width / 2 - $instance.outerWidth() / 2,
        top: windowSize.height / 2 - $instance.outerHeight() / 2,
      });
    }, 100)
  );

  return {
    instance,
    $instance,
  };
};

export default {
  showPopover,
  hideDetailCard,
  showDetailCard,
  showSearchDialog,
};
