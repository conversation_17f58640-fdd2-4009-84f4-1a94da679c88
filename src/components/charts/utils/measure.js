/* eslint-disable no-underscore-dangle */
import utils from './utils';

class Measure {
  constructor() {
    const $container = $('<div></div>').css({
      width: 0,
      height: 0,
      top: -200,
      position: 'fixed',
      zIndex: -1,
      overflow: 'hidden',
    });

    $('body').append($container);

    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', 0);
    svg.setAttribute('height', 0);
    $container.append(svg);
    this.svg = svg;

    this._cache = [];
  }

  // 单行文本
  getTotal(text, fontSize) {
    const ele = utils.createText(text, fontSize);
    this.svg.appendChild(ele);
    const $ele = $(ele);
    const svgRect = $ele[0].getBBox();
    $ele.remove();
    const size = {
      width: svgRect.width,
      height: svgRect.height,
    };
    return size;
  }
}

export default new Measure();
