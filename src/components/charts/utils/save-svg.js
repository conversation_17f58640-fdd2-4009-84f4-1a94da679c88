/* eslint-disable no-param-reassign */
/**
 * Created by <PERSON> on - 2019/07/10.
 */

import canvg from 'canvg';
import { saveSvgAsPng } from 'save-svg-as-png';

import { deviceInfo } from './device-info';
// import eventBus from '../common/event-bus'

/**
 * Save svg to png
 * @param {SVGElement} svg - SVG DOM Element
 * @param {string} fileName
 * @param {object} option
 * @param {number} option.left
 * @param {number} option.top
 * @param {number} option.width
 * @param {number} option.height
 * @param {number} option.scale
 * @returns {Promise}
 */
const saveAsImage = (svg, fileName, option) => {
  // IE use canvg
  if (deviceInfo.isIE()) {
    option.canvg = canvg;
  }
  if (option.chartType === 'equity_chart') {
    if (option.width * option.scale > 59000 || option.height * option.scale > 59900) {
      // eventBus.triggerToastr({ message: '由于浏览器大小限制，暂无法为您保存该图片，请筛选隐藏部分节点后重试。' }, 'error')
      return new Promise((resolve, reject) => {
        reject(new Error('errorMsg'));
      });
    }
    if ((option.width * option.scale > 50000 || option.height * option.scale > 50000) && deviceInfo.isIE()) {
      option.scale = 0.5;
    } else if ((option.width * option.scale > 20000 || option.height * option.scale > 20000) && deviceInfo.isIE()) {
      option.scale = 0.8;
    }
  } else if (!option.canvg) {
    // 这个条件是实际测试下来的值，不一定100%准确
    // {top: -2278.5078125, left: -2401.40625, width: 8037.3125, height: 7864.515625, scale: 2}
    if (option.width * option.scale > 15600 || option.height * option.scale > 15600) {
      option.canvg = canvg;
      // eventBus.triggerToastr({ message: '图片尺寸较大，保存可能需要消耗一些时间' }, 'info')
    }
  }
  return saveSvgAsPng(svg, fileName, option);
};

export default {
  saveAsImage,
};
