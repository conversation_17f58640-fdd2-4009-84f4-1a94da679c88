/* eslint-disable @typescript-eslint/no-this-alias */
/* eslint-disable @typescript-eslint/no-shadow */
import _ from 'lodash';
import moment from 'moment';
import fileSaver from 'file-saver';

import waterMarkLogo from './images/shuiying6.png';

const SVG_NS = 'http://www.w3.org/2000/svg';

// 排列单文本
const createText = (text, fontSize = 12) => {
  const ele = document.createElementNS(SVG_NS, 'text');
  ele.setAttribute('stroke-width', 0);
  ele.setAttribute('style', 'font-family:"微软雅黑", Microsoft YaHei", Arial;');
  ele.setAttribute('font-size', fontSize);
  ele.setAttribute('text-anchor', 'middle');
  ele.setAttribute('dominant-baseline', 'middle');

  const tspan = document.createElementNS(SVG_NS, 'tspan');
  tspan.textContent = text;
  ele.appendChild(tspan);
  return ele;
};

const getWaterMarkLogo = () => {
  return $(`<g id="V2.3" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
  <g id="画板" transform="translate(-159.000000, -66.000000)" fill="#F5FAFF" fill-rule="nonzero">
      <g id="logo水印" transform="translate(159.000000, 66.000000)">
          <g id="logo_svg" transform="translate(89.931057, 69.845014) rotate(-30.000000) translate(-89.931057, -69.845014) translate(3.431057, 39.845014)">
              <path d="M76.3163203,43.2213439 C79.9148568,43.2213439 82.8319667,46.1962988 82.8319667,49.866237 C82.8319667,51.6278462 82.1594447,53.2291769 81.0623227,54.4183115 L82.6532687,56.5089512 L79.9567705,56.5089512 L79.3696205,55.7373393 C78.4587956,56.2310063 77.4197353,56.5110092 76.3163203,56.5110092 C72.7179026,56.5110092 69.8006745,53.5360543 69.8006745,49.866237 C69.8006745,46.1962988 72.7179026,43.2213439 76.3163203,43.2213439 Z M93.7433625,46.1532028 C95.4467516,46.1532028 96.9542253,47.0087066 97.8752618,48.3214395 L95.9102972,49.7307752 C95.424073,49.0485027 94.6356652,48.6045899 93.7452628,48.6045899 C92.2677097,48.6045899 91.0698994,49.8256832 91.0698994,51.332106 C91.0698994,52.8385287 92.2677097,54.0596221 93.7452628,54.0596221 C94.6361398,54.0596221 95.4247852,53.615225 95.91101,52.9324683 L97.875974,54.341804 C96.9549382,55.6550211 95.4472262,56.5110092 93.7433625,56.5110092 C90.9389334,56.5110092 88.6654928,54.1922996 88.6654928,51.332106 C88.6654928,48.4719123 90.9389334,46.1532028 93.7433625,46.1532028 Z M148.307093,46.1532028 C151.111522,46.1532028 153.384962,48.4719123 153.384962,51.332106 C153.384962,54.1922996 151.111522,56.5110092 148.307093,56.5110092 C145.502782,56.5110092 143.229341,54.1922996 143.229341,51.332106 C143.229341,48.4719123 145.502782,46.1532028 148.307093,46.1532028 Z M133.263085,46.1532028 C134.966593,46.1532028 136.473947,47.0088277 137.395103,48.3215606 L135.4299,49.7310173 C134.94332,49.0486238 134.154318,48.6045899 133.263085,48.6045899 C131.784582,48.6045899 130.585941,49.8256832 130.585941,51.332106 C130.585941,52.8385287 131.784582,54.0596221 133.263085,54.0596221 C134.154674,54.0596221 134.944032,53.6151039 135.430613,52.9321051 L137.395815,54.3415619 C136.474779,55.6550211 134.966948,56.5110092 133.263085,56.5110092 C130.458655,56.5110092 128.185215,54.1922996 128.185215,51.332106 C128.185215,48.4719123 130.458655,46.1532028 133.263085,46.1532028 Z M120.554023,52.8415551 C121.546895,52.8415551 122.351689,53.6629211 122.351689,54.6762821 C122.351689,55.6896432 121.546895,56.5110092 120.554023,56.5110092 C119.561152,56.5110092 118.756239,55.6896432 118.756239,54.6762821 C118.756239,53.6629211 119.561152,52.8415551 120.554023,52.8415551 Z M108.78927,46.1532028 C110.492778,46.1532028 112.000371,47.0083435 112.921882,48.32035 L110.954424,49.7313805 C110.467843,49.0487448 109.678723,48.6045899 108.787371,48.6045899 C107.308868,48.6045899 106.110227,49.8256832 106.110227,51.332106 C106.110227,52.8385287 107.308868,54.0596221 108.787371,54.0596221 C109.679198,54.0596221 110.468674,53.6149828 110.955136,52.931742 L112.922713,54.3428935 C112.001202,55.6555053 110.493253,56.5110092 108.78927,56.5110092 C105.983772,56.5110092 103.709501,54.1922996 103.709501,51.332106 C103.709501,48.4719123 105.983772,46.1532028 108.78927,46.1532028 Z M169.666438,46.1676085 C170.261425,46.1676085 171.057076,46.3315185 171.514567,46.6589754 C171.971582,46.9866744 172.389533,47.4135184 172.587823,47.9395074 C172.786231,48.4658595 172.885375,49.3195474 172.885375,50.5008134 L172.880626,56.5089512 L170.364014,56.5089512 L170.364014,51.6520574 C170.364014,50.5460884 170.27508,49.7800451 170.03832,49.3772912 C169.801204,48.9746585 169.445827,48.773221 168.972188,48.773221 C168.608618,48.773221 168.553049,48.8968194 168.266658,49.143774 C167.980029,49.3909706 167.767966,49.7264172 167.630351,50.1504769 C167.49238,50.5748997 167.397866,51.2570511 167.397866,52.2018944 L167.397866,56.5089512 L164.881134,56.5089512 L164.881134,51.8353364 C164.881134,50.965548 164.786858,50.359904 164.676789,49.9572713 C164.566602,49.5545174 164.401322,49.2564772 164.180948,49.0632716 C163.960454,48.8699449 163.690567,48.773221 163.371048,48.773221 C163.018283,48.773221 162.878649,48.8968194 162.592139,49.143774 C162.30551,49.3909706 162.090716,49.7373122 161.947639,50.1827989 C161.804206,50.6285276 161.732727,51.3184267 161.732727,52.252738 L161.73522,56.5089512 L159.218489,56.5089512 L159.218489,46.5202451 L161.73522,46.5202451 L161.732727,47.5529751 C162.096297,47.091388 162.498456,46.7450463 162.939323,46.5139501 C163.379834,46.2832171 163.864872,46.1676085 164.393841,46.1676085 C164.911649,46.1676085 165.382676,46.3182023 165.80692,46.6186637 C166.231046,46.9194882 166.575618,47.3596484 166.840043,47.9395074 C167.170605,47.3596484 167.580957,46.9194882 168.071337,46.6186637 C168.561599,46.3182023 169.093299,46.1676085 169.666438,46.1676085 Z M148.308992,48.6045899 C146.829421,48.6045899 145.629948,49.8256832 145.629948,51.332106 C145.629948,52.8385287 146.829421,54.0596221 148.308992,54.0596221 C149.788564,54.0596221 150.987917,52.8385287 150.987917,51.332106 C150.987917,49.8256832 149.788564,48.6045899 148.308992,48.6045899 Z M76.2254872,45.6538462 C73.9927729,45.6538462 72.1828779,47.4982577 72.1828779,49.773508 C72.1828779,52.0487584 73.9927729,53.8931699 76.2254872,53.8931699 C76.7606315,53.8931699 77.2709592,53.7862773 77.7384235,53.5937981 L75.912024,51.1938598 L78.6085221,51.1938598 L79.4339758,52.278523 C79.9566517,51.584266 80.2679784,50.7161723 80.2679784,49.773508 C80.2679784,47.4982577 78.4580834,45.6538462 76.2254872,45.6538462 Z" id="Qcc.com"></path>
              <path d="M171.779711,8.58031549 L171.779711,8.63651953 L165.309379,8.63651953 L172.346957,14.6718536 L172.317007,14.7062189 C172.451724,14.897889 172.531365,15.1304649 172.531365,15.3817755 C172.531365,16.0361586 171.995415,16.5664937 171.334281,16.5664937 C171.142134,16.5664937 170.96109,16.5205994 170.800126,16.4408939 L170.751556,16.4965436 L161.515885,8.63651953 L159.034112,8.63651953 L159.034112,13.7244781 L164.245778,13.7244781 C166.621213,13.7244781 168.642077,15.6895118 168.642077,18.0405462 L168.642077,24.2865067 C168.642077,26.7077129 166.621213,28.6377162 164.245778,28.6377162 L151.872474,28.6377162 C149.497038,28.6377162 147.511732,26.7077129 147.511732,24.2865067 L147.511732,18.0405462 C147.511732,15.6895118 149.497038,13.7244781 151.872474,13.7244781 L156.623231,13.7244781 L156.623231,8.63651953 L154.779708,8.63651953 C151.872474,12.5664761 149.461593,14.0403069 143.930913,16.4965436 L143.927099,16.4882294 C143.794851,16.5380037 143.651945,16.5664937 143.501973,16.5664937 C142.840841,16.5664937 142.304889,16.0361586 142.304889,15.3817755 C142.304889,14.9218339 142.570172,14.5240825 142.956937,14.3277565 L142.938204,14.2859638 C147.440728,12.3559606 149.213472,11.5137874 151.73069,8.63651953 L143.434559,8.63651953 L143.434559,8.58153493 C142.804946,8.54672613 142.304889,8.03191077 142.304889,7.40014236 C142.304889,6.74575929 142.840841,6.21531325 143.501973,6.21531325 C143.60001,6.21531325 143.694906,6.2282834 143.786101,6.25034376 L156.623231,6.25034376 L156.623231,4.21513815 L156.684925,4.21513815 C156.781952,3.65731068 157.271128,3.23262129 157.861594,3.23262129 C158.521717,3.23262129 159.056883,3.76306732 159.056883,4.41733953 C159.056883,4.49560387 159.048694,4.57198367 159.034112,4.64603549 L159.034112,6.25034376 L171.404164,6.25034376 C171.495359,6.2282834 171.590255,6.21531325 171.688292,6.21531325 C172.349425,6.21531325 172.885375,6.74575929 172.885375,7.40014236 C172.885375,8.02392915 172.397995,8.53397765 171.779711,8.58031549 Z M151.872474,16.1105429 C150.773427,16.1105429 149.922501,16.9878574 149.922501,18.0405462 L149.922501,19.5142661 C149.922501,19.5142661 156.012687,19.5142661 160.741572,19.5142661 L163.22469,19.5142661 L163.22469,19.57047 C163.779261,19.6738987 164.199115,20.1550139 164.199115,20.7340148 C164.199115,21.3130158 163.779261,21.7941309 163.22469,21.8975596 L163.22469,21.9004418 L163.205397,21.9004418 C163.139216,21.9116383 163.071577,21.918733 163.002144,21.918733 C162.93271,21.918733 162.864959,21.9116383 162.798779,21.9004418 L160.730242,21.9004418 C156.002928,21.9004418 149.922501,21.9004418 149.922501,21.9004418 L149.922501,24.2865067 C149.922501,25.3743368 150.773427,26.2515404 151.872474,26.2515404 L164.245778,26.2515404 C165.253967,26.2515404 166.052733,25.5132393 166.178028,24.5516743 L166.148078,24.5516743 L166.148078,21.9004418 L166.148078,19.5142661 L166.148078,17.8829089 L166.188796,17.8829089 C166.108818,16.9017223 165.289412,16.1105429 164.245778,16.1105429 L151.872474,16.1105429 Z M135.446672,33.146452 C135.369051,33.146452 135.293223,33.1384704 135.219639,33.1245026 L135.219639,33.1642998 L107.282227,33.1642998 L107.282227,33.1454543 C107.275496,33.1455652 107.268878,33.146452 107.262149,33.146452 C106.602025,33.146452 106.066747,32.616782 106.066747,31.9633966 C106.066747,31.3100113 106.602025,30.7803412 107.262149,30.7803412 C107.268878,30.7803412 107.275496,30.7812281 107.282227,30.781339 L107.282227,30.778235 L135.219639,30.778235 L135.219639,30.8022908 C135.293223,30.7883229 135.369051,30.7803412 135.446672,30.7803412 C136.107805,30.7803412 136.643756,31.3100113 136.643756,31.9633966 C136.643756,32.616782 136.107805,33.146452 135.446672,33.146452 Z M135.538764,8.58031549 L135.538764,8.63651953 L129.068432,8.63651953 L136.10601,14.6718536 L136.075724,14.7065515 C136.210329,14.8982216 136.289746,15.1306866 136.289746,15.3817755 C136.289746,16.0361586 135.753795,16.5664937 135.092662,16.5664937 C134.900626,16.5664937 134.719919,16.5207102 134.559067,16.4411156 L134.510609,16.4965436 L125.274938,8.63651953 L122.793166,8.63651953 L122.793166,13.7244781 L128.004831,13.7244781 C130.380267,13.7244781 132.401131,15.6895118 132.401131,18.0405462 L132.401131,24.2865067 C132.401131,26.7077129 130.380267,28.6377162 128.004831,28.6377162 L115.631527,28.6377162 C113.256204,28.6377162 111.270786,26.7077129 111.270786,24.2865067 L111.270786,18.0405462 C111.270786,15.6895118 113.256204,13.7244781 115.631527,13.7244781 L120.382285,13.7244781 L120.382285,8.63651953 L118.538762,8.63651953 C115.631527,12.5664761 113.220646,14.0403069 107.689967,16.4965436 L107.686265,16.4883402 C107.55424,16.5381146 107.411672,16.5664937 107.262149,16.5664937 C106.602025,16.5664937 106.066747,16.0361586 106.066747,15.3817755 C106.066747,14.922499 106.330909,14.5251911 106.716327,14.3285325 L106.697257,14.2859638 C111.199894,12.3559606 112.972525,11.5137874 115.489743,8.63651953 L107.193612,8.63651953 L107.193612,8.58142409 C106.565569,8.54606099 106.066747,8.03157823 106.066747,7.40014236 C106.066747,6.74575929 106.602025,6.21531325 107.262149,6.21531325 C107.360073,6.21531325 107.454745,6.2282834 107.545827,6.25034376 L120.382285,6.25034376 L120.382285,4.21513815 L120.443305,4.21513815 C120.540445,3.65731068 121.030406,3.23262129 121.621657,3.23262129 C122.282789,3.23262129 122.81874,3.76306732 122.81874,4.41733953 C122.81874,4.50092497 122.809767,4.58240414 122.793166,4.66111191 L122.793166,6.25034376 L135.162544,6.25034376 C135.253851,6.2282834 135.348635,6.21531325 135.446672,6.21531325 C136.107805,6.21531325 136.643756,6.74575929 136.643756,7.40014236 C136.643756,8.02370746 136.156711,8.53364511 135.538764,8.58031549 Z M115.631527,16.1105429 C114.53248,16.1105429 113.681555,16.9878574 113.681555,18.0405462 L113.681555,19.5142661 C113.681555,19.5142661 119.77174,19.5142661 124.500625,19.5142661 L126.98307,19.5142661 L126.98307,19.57047 C127.537754,19.6738987 127.957608,20.1550139 127.957608,20.7340148 C127.957608,21.3130158 127.537754,21.7941309 126.98307,21.8975596 L126.98307,21.9004418 L126.963889,21.9004418 C126.897597,21.9116383 126.829958,21.918733 126.760524,21.918733 C126.69109,21.918733 126.623452,21.9116383 126.557159,21.9004418 L124.489296,21.9004418 C119.761982,21.9004418 113.681555,21.9004418 113.681555,21.9004418 L113.681555,24.2865067 C113.681555,25.3743368 114.53248,26.2515404 115.631527,26.2515404 L128.004831,26.2515404 C129.01302,26.2515404 129.811787,25.5132393 129.937081,24.5516743 L129.906571,24.5516743 L129.906571,21.9004418 L129.906571,19.5142661 L129.906571,17.8829089 L129.94785,17.8829089 C129.867872,16.9017223 129.048466,16.1105429 128.004831,16.1105429 L115.631527,16.1105429 Z M99.2716819,33.146452 C99.2049403,33.146452 99.1397696,33.1396899 99.0759443,33.1292694 L71.1934954,33.1292694 C71.1296707,33.1396899 71.0646118,33.146452 70.9977582,33.146452 C70.3366252,33.146452 69.8006745,32.616782 69.8006745,31.9633966 C69.8006745,31.3688758 70.2447575,30.878338 70.8227722,30.79442 L70.8227722,30.7430936 L74.7403271,30.7430936 L74.7403271,20.1284084 L74.7929355,20.1284084 C74.9111628,19.5971864 75.388897,19.1993241 75.9613034,19.1993241 C76.5335974,19.1993241 77.011444,19.5971864 77.1296713,20.1284084 L77.1866538,20.1284084 L77.1866538,30.7430936 L83.6745965,30.7430936 L83.6745965,9.44344046 L83.7407768,9.44344046 C83.8513772,8.90224136 84.3344957,8.49484551 84.913856,8.49484551 C85.5749896,8.49484551 86.1109399,9.02529157 86.1109399,9.67956375 C86.1109399,9.76292744 86.1020787,9.84418492 86.0854774,9.92267096 L86.0854774,19.3037505 L93.8416198,19.3037505 C93.9067904,19.2928866 93.9734196,19.2859026 94.0417321,19.2859026 C94.1101556,19.2859026 94.176673,19.2928866 94.2419561,19.3037505 L94.3107168,19.3037505 L94.3107168,19.3170532 C94.8421807,19.4379971 95.2388153,19.9080266 95.2388153,20.4707318 C95.2388153,21.0334369 94.8421807,21.5034664 94.3107168,21.6244103 L94.3107168,21.6899262 L86.0854774,21.6899262 L86.0854774,30.7430936 L99.1502011,30.7430936 L99.1502011,30.7864383 C99.1901341,30.7824475 99.2307394,30.7803412 99.2716819,30.7803412 C99.9328148,30.7803412 100.468766,31.3100113 100.468766,31.9633966 C100.468766,32.616782 99.9328148,33.146452 99.2716819,33.146452 Z M99.2716819,15.3402045 C99.0186254,15.3402045 98.7841885,15.2619402 98.5908066,15.1292455 L98.5475092,15.1631674 L92.7330382,8.00475104 C91.5985458,6.56617255 90.5703908,5.68885803 88.3014055,5.68885803 L81.9019658,5.68885803 C79.6684269,5.68885803 78.6048261,6.56617255 77.4702213,8.00475104 L72.052385,14.7158634 C71.9844097,14.840909 71.8938882,14.9515433 71.787102,15.0445515 L71.6913085,15.1631674 L71.6624808,15.1405528 C71.4723517,15.2664852 71.2437481,15.3402045 70.9977582,15.3402045 C70.3366252,15.3402045 69.8006745,14.8097585 69.8006745,14.1553754 C69.8006745,14.0092672 69.8287171,13.8699211 69.8776234,13.7405522 L69.8123402,13.6893367 L75.5912526,6.53103119 C77.151208,4.60113881 78.7111634,3.30279314 81.9019658,3.30279314 L88.3014055,3.30279314 C91.5276543,3.30279314 93.0521632,4.60113881 94.6121187,6.53103119 L100.173421,13.3777205 C100.192938,13.4000025 100.211446,13.4230605 100.229394,13.4466729 L100.426477,13.6893367 L100.384974,13.7219284 C100.43848,13.8562859 100.468766,14.0022832 100.468766,14.1553754 C100.468766,14.8097585 99.9328148,15.3402045 99.2716819,15.3402045 Z M143.501973,30.7803412 C143.509152,30.7803412 143.515994,30.781339 143.523174,30.7814498 L143.523174,30.778235 L171.460586,30.778235 L171.460586,30.8024016 C171.534394,30.7883229 171.610333,30.7803412 171.688292,30.7803412 C172.349425,30.7803412 172.885375,31.3100113 172.885375,31.9633966 C172.885375,32.616782 172.349425,33.146452 171.688292,33.146452 C171.610333,33.146452 171.534394,33.1384704 171.460586,33.1243917 L171.460586,33.1642998 L143.523174,33.1642998 L143.523174,33.1453435 C143.515994,33.1454543 143.509152,33.146452 143.501973,33.146452 C142.840841,33.146452 142.304889,32.616782 142.304889,31.9633966 C142.304889,31.3100113 142.840841,30.7803412 143.501973,30.7803412 Z" id="企查查"></path>
              <path d="M49.0312087,0 C55.0136391,0 59.8633571,4.84971804 59.8633571,10.8321484 L59.8633571,49.0312087 C59.8633571,55.0136391 55.0136391,59.8633571 49.0312087,59.8633571 L10.8321484,59.8633571 C4.84971804,59.8633571 0,55.0136391 0,49.0312087 L0,10.8321484 C0,4.84971804 4.84971804,0 10.8321484,0 L49.0312087,0 Z M29.9316786,6.58496929 C17.0376471,6.58496929 6.58496929,17.0376471 6.58496929,29.9316786 C6.58496929,42.8257101 17.0376471,53.2783879 29.9316786,53.2783879 C34.7529385,53.2783879 39.2328686,51.8169808 42.952656,49.3129793 L42.952656,49.3129793 L46.8935197,53.254029 L50.0495787,50.09797 L46.4162698,46.4642856 L46.4162698,46.4642856 L46.420512,46.4600545 L43.2644251,43.3040177 C39.8487845,46.7096084 35.1361072,48.8150464 29.9316786,48.8150464 C19.5026825,48.8150464 11.0483108,40.3606747 11.0483108,29.9316786 C11.0483108,19.5026825 19.5026825,11.0483108 29.9316786,11.0483108 C40.3606747,11.0483108 48.8150464,19.5026825 48.8150464,29.9316786 C48.8150464,33.5389193 47.8035914,36.9099221 46.0488177,39.7765504 L46.0488177,39.7765504 L49.2774281,43.0053142 C51.8031428,39.2751811 53.2783879,34.7757108 53.2783879,29.9316786 C53.2783879,17.0376471 42.8257101,6.58496929 29.9316786,6.58496929 Z M29.9316786,15.6260969 C22.030924,15.6260969 15.6260969,22.030924 15.6260969,29.9316786 C15.6260969,37.8324332 22.030924,44.2372602 29.9316786,44.2372602 C33.8720368,44.2372602 37.4403078,42.6441653 40.0275359,40.0669307 L40.0275359,40.0669307 L36.6284519,36.6683284 C34.911089,38.375589 32.5445875,39.4305848 29.9316786,39.4305848 C24.6855775,39.4305848 20.4327724,35.1777796 20.4327724,29.9316786 C20.4327724,24.6855775 24.6855775,20.4327724 29.9316786,20.4327724 C32.5641463,20.4327724 34.9465038,21.5036215 36.6669083,23.233477 L36.6669083,23.233477 L40.0656061,19.8344916 C37.4753681,17.2348875 33.8914,15.6260969 29.9316786,15.6260969 Z M29.9316786,24.2094459 C26.7713767,24.2094459 24.2094459,26.7713767 24.2094459,29.9316786 C24.2094459,33.0919804 26.7713767,35.6539112 29.9316786,35.6539112 C31.5015301,35.6539112 32.9237326,35.0217507 33.9576092,33.9981063 L33.9576092,33.9981063 L31.5298606,31.57023 C31.1173561,31.9726341 30.5534718,32.2205717 29.9316786,32.2205717 C28.6675578,32.2205717 27.6427855,31.1957993 27.6427855,29.9316786 C27.6427855,28.6675578 28.6675578,27.6427855 29.9316786,27.6427855 C30.5732856,27.6427855 31.1532343,27.9067761 31.5688305,28.3320632 L31.5688305,28.3320632 L33.9959849,25.9036065 C32.9590683,24.8574209 31.5210448,24.2094459 29.9316786,24.2094459 Z" id="logo"></path>
          </g>
      </g>
  </g>
</g>`);
};

const downloadImage = (imgdata, companyName, title) => {
  const img = new Image();
  img.src = imgdata;
  img.crossOrigin = 'Anonymous';
  const shuiying = new Image();
  shuiying.src = waterMarkLogo;
  shuiying.crossOrigin = 'Anonymous';
  const loading = (img) => {
    return new Promise((resolve, reject) => {
      img.onload = function () {
        resolve(img);
      };
    });
  };
  Promise.all([loading(img), loading(shuiying)]).then((res) => {
    shuiying.width = 240;
    shuiying.height = 200;
    const canvas = document.createElement('canvas'); // 准备空画布
    const minSize = { w: 840, height: 600 };
    canvas.width = _.max([minSize.w, img.width + 100]);
    canvas.height = _.max([minSize.h, img.height + 200]);
    const context = canvas.getContext('2d'); // 取得画布的2d绘图上下文
    context.fillStyle = '#fff';
    context.fillRect(0, 0, canvas.width, canvas.height);
    for (let i = 0; i < canvas.width + shuiying.width; i += shuiying.width) {
      for (let j = 0; j < canvas.height + shuiying.height; j += shuiying.height) {
        context.drawImage(shuiying, i, j, shuiying.width, shuiying.height);
      }
    }
    // 画图谱
    context.drawImage(img, (canvas.width - img.width) / 2, (canvas.height - img.height) / 2);
    // let marker = `${title}由企查查基于公开信息利用大数据分析引擎独家生成。`
    const marker = '以上数据是企查查大数据分析引擎基于公开信息挖掘的成果，仅供参考。该成果不构成任何明示或暗示的观点或保证';
    context.font = '16px 微软雅黑';
    context.fillStyle = '#aaaaaa';
    context.fillText(marker, canvas.width / 2 - context.measureText(marker).width / 2, canvas.height - 30);
    // 在文件名中无法使用：
    const fileName = `${companyName}-${title}-${moment().format('YYYY-MM-DD')}.png`;
    if (!HTMLCanvasElement.prototype.toBlob) {
      Object.defineProperty(HTMLCanvasElement.prototype, 'toBlob', {
        value(callback, type, quality) {
          const canvas = this;
          setTimeout(function () {
            const binStr = atob(canvas.toDataURL(type, quality).split(',')[1]);
            const len = binStr.length;
            const arr = new Uint8Array(len);

            for (let i = 0; i < len; i++) {
              arr[i] = binStr.charCodeAt(i);
            }

            callback(new Blob([arr], { type: type || 'image/png' }));
          });
        },
      });
    }
    setTimeout(() => {
      try {
        canvas.toBlob(function (blob) {
          fileSaver.saveAs(blob, fileName);
        });
      } catch (err) {
        console.log(err);
      }
    });
    // Canvas2Image.saveAsImage(canvas, canvas.width, canvas.height, 'png', fileName)
  });
};

export default {
  createText,
  getWaterMarkLogo,
  downloadImage,
};
