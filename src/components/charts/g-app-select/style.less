
.single-filter {
  margin-bottom: 10px;
  position: relative;

  .value {
    position: relative;
    width: 135px;
    height: 32px;
    line-height: 32px;
    color: #666;
    border: 1px solid #d6d6d6;
    background-color: #fff;
    margin-bottom: 10px;
    text-align: center;
    cursor: pointer;
    border-radius: 2px;

    .down-icon {
      transform: rotate(0);
      transition: transform .2s;
      color: #d6d6d6;
      position: absolute;
      right: 6px;
      top: 11px;
    }

    .rotate {
      transform: rotate(180deg);
      transition: transform .2s;
    }

    &:hover {
      border: 1px solid #007add;
    }
  }

  .option {
    position: absolute;
    left: 0;
    top: 34px;
    min-width: 135px;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    z-index: 1;

    div {
      display: block;
      padding: 5px 12px;
      overflow: hidden;
      color: rgba(0, 0, 0, .65);
      font-weight: normal;
      font-size: 14px;
      line-height: 22px;
      white-space: nowrap;
      text-overflow: ellipsis;
      cursor: pointer;

      .tips {
        color: #999;
        font-size: 12px;
      }

      &.active {
        background-color: #fafafa;
      }

      &:hover {
        background-color: #e6f7ff;
      }
    }
  }
}
