/* eslint-disable @typescript-eslint/no-empty-function */
export default {
  name: 'g-app-select',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    chosen: {
      type: Object,
      default: () => {},
    },
    type: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      showSelect: false,
    };
  },
  methods: {
    chooseType(item) {
      this.showSelect = false;
      this.$emit('choose', { type: this.type, item });
    },
    handleSelect() {
      if (!this.showSelect) {
        this.$emit('closeSelect');
      }
      this.showSelect = !this.showSelect;
    },
  },
};
