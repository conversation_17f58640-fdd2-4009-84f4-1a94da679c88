<template>
  <div class="single-filter">
    <div class="value rule-btn" @click="handleSelect">
      {{ chosen.value }}
      <a-icon type="down" :class="['down-icon', showSelect && 'rotate']" style="font-size: 12px" />
    </div>
    <transition name="fade">
      <div class="option" v-if="showSelect">
        <div :class="[item.key === chosen.key && 'active']" @click="chooseType(item)" v-for="item in list" :key="item.key">
          {{ item.value }}<span class="tips" v-if="+item.key === 25 && type === 'gdPercent'">(超过25%的为最终受益人)</span>
        </div>
      </div>
    </transition>
  </div>
</template>
<style lang="less" scoped src="./style.less"></style>
<script src="./component.js"></script>
