/* eslint-disable consistent-return */
/* eslint-disable no-unused-expressions */
/* eslint-disable func-names */
/* eslint-disable no-underscore-dangle */
// import appRelationFilter from './components/filter'
// import appRelationLegend from './components/legend'
// import appRelationSearch from './components/search'
/* eslint-disable no-param-reassign */
import resizeDetector from 'element-resize-detector';
import _ from 'lodash';

import dataLoader from './data';
import { actionTypes, gUiToolbox, gUiToolboxAction } from '../g-ui-toolbox';
import gUiFooter from '../g-ui-footer';
import gUiLoading from '../g-ui-loading';
import gUiNoData from '../g-ui-no-data';
import appPerson from '../g-app-person';
import appCompany from '../g-app-company';
import relationAppLegend from './components/legend';
import relationAppFilter from './components/filter';
import Chart from './chart';
import popoverHelper from '../utils/popoverHelper';

const CONTAINER_SELECTOR = '#relationChart';
export default {
  name: 'g-relation',
  data() {
    return {
      isLoading: false,
      noData: false,
      data: {},
      isInit: false,
      isFilterOpened: false,
      isShowText: true,
      isFullScreen: false,
      isSaving: false,
      scale: 1,
      isPerson: false,
    };
  },
  components: {
    [gUiToolbox.name]: gUiToolbox,
    [gUiToolboxAction.name]: gUiToolboxAction,
    [gUiFooter.name]: gUiFooter,
    [gUiLoading.name]: gUiLoading,
    [gUiNoData.name]: gUiNoData,
    [relationAppLegend.name]: relationAppLegend,
    [relationAppFilter.name]: relationAppFilter,
  },
  props: {
    actionTypes: {
      type: Object,
      default: () => actionTypes,
    },
    containerName: {
      type: String,
      default: 'relationChart',
    },
    keyNo: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
    iframe: {
      type: Boolean,
      default: false,
    },
  },
  mounted() {
    this.isPerson = this.keyNo && this.keyNo[0] === 'p';
    this.$nextTick(() => {
      document.addEventListener('fullscreenchange', () => {
        this.isFullScreen = document.fullscreenElement;
      });
      document.addEventListener('webkitfullscreenchange', () => {
        this.isFullScreen = document.webkitFullscreenElement;
      });
      document.addEventListener('mozfullscreenchange', () => {
        this.isFullScreen = document.mozFullScreenElement;
      });
      document.addEventListener('MSFullscreenChange', () => {
        this.isFullScreen = document.msFullscreenElement;
      });
      this.onRefresh();
      this.attachResizeDetector();
    });
  },
  methods: {
    cleanup() {
      if (this.chart) {
        this.chart.cleanup();
      }
    },
    onFilter() {
      this.isFilterOpened = true;
      this.$refs.filter.setPanel(this.data.nodes || []);
    },
    showText() {
      this.isShowText = !this.isShowText;
      if (this.chart) {
        this.chart.toggleText(this.isShowText);
      }
    },
    onZoomIn() {
      if (this.chart) {
        this.chart.zoomInOrOut({ isIn: true });
      }
    },
    onZoomOut() {
      if (this.chart) {
        this.chart.zoomInOrOut({ isIn: false });
      }
    },
    onRefresh() {
      this.cleanup();
      this.isLoading = true;
      this.noData = false;
      if (this.$refs.filter) {
        this.$refs.filter.resetFilter();
        this.$refs.filter.resetSearch();
        this.$refs.filter.resetCheck();
      }
      this.chart = new Chart(CONTAINER_SELECTOR, this.iframe);
      const params = { keyNo: this.keyNo };
      if (this.keyNo[0] === 'p') {
        params.startLabel = 'Person';
      }
      dataLoader
        .loadGraph(params)
        .then((res) => {
          if (_.isEmpty(res)) {
            this.isLoading = false;
            this.noData = true;
            if (this.iframe) {
              this.$emit('iframeLoaded');
              this.$emit('noData');
            }
            return;
          }
          if ($(window.parent.document).find('#muhouIframeTool') && this.iframe) {
            $(window.parent.document).find('#muhouIframeTool').show();
          }
          this.name =
            res._rootNode &&
            res._rootNode.data &&
            res._rootNode.data.obj &&
            res._rootNode.data.obj.properties &&
            res._rootNode.data.obj.properties.name;
          this.data = res;
          this.chart.init(this.data);
          this.chart.domUpdate();
          if (this.iframe) {
            this.chart.highLight([this.data._rootNode.id]);
          }
        })
        .catch((err) => {
          this.isLoading = false;
          this.noData = true;
          console.log(err);
        });
      this.chart.on('hideLoading', () => {
        this.isLoading = false;
        this.isInit = true;
        if (this.iframe) {
          this.$emit('iframeLoaded');
        }
      });
      this.chart.on('onNodeMouseover', ({ data, position, size }) => {
        if (this.iframe) {
          return;
        }
        data.delayTimer = setTimeout(() => {
          const $instance = $(`#detail-popover-${data.nodeId}`);
          if ($instance.length > 0) {
            $instance.stop(true).fadeIn(0);
            return;
          }
          popoverHelper.showPopover({
            component: data.type === 'Person' ? appPerson : appCompany,
            data: {
              hasKeyNo: true,
              id: data.keyNo,
              keyNo: data.keyNo,
              name: data.name,
              eid: this.companyKeyNo,
              ename: this.companyName,
              rsTags: data.rsTags,
              org: data.org,
              type: 1,
            },
            container: $(CONTAINER_SELECTOR),
            identity: `detail-popover-${data.nodeId}`,
            targetPosition: position,
            targetSize: { width: size.width * this.scale, height: size.height * this.scale },
          });
        }, 400);
      });
      this.chart.on('onNodeMouseout', ({ data }) => {
        if (this.iframe) {
          return;
        }
        if (data && data.delayTimer) {
          clearTimeout(data.delayTimer);
          delete data.delayTimer;
        }

        $(`#detail-popover-${data.nodeId}`).fadeOut(400, function () {
          $(this).remove();
        });
      });
      this.chart.on('clickCy', () => {
        if (this.isFilterOpened) {
          this.isFilterOpened = false;
        } else {
          this.chart && this.chart.cancelHighLight();
          this.$refs.filter.resetFilter();
          this.$refs.filter.resetSearch();
        }
      });
      this.chart.on('onZoom', (k) => {
        this.scale = k;
      });
    },
    onFullScreen() {
      const element = $(`.${this.containerName}`)[0];
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
    },
    onExitFullScreen() {
      if (this.isFullScreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      }
    },
    onSave() {
      this.chart && this.chart.saveImg(this.name);
    },
    searchFocus(ids) {
      this.chart && this.chart.highLight(ids, this.isFilterOpened);
    },
    searchCancelFocus() {
      this.chart && this.chart.cancelHighLight();
    },
    changeFilter(filter) {
      const afterFilterLevels = _.filter(this.data.nodes, (node) => {
        return +node.layout.level <= +filter.level + 1;
      });
      const afterFilterStatus = this.filterNodesByStatus(afterFilterLevels, filter.status);
      const afterFilterRange = this.filterNodesByRange(afterFilterStatus, filter.range);
      const afterFilterInvest = this.filterNodesByInvest(afterFilterRange, filter.invest);
      const filterNodes = [];
      _.forEach(afterFilterInvest, (node) => {
        if (this.isParentExit(node, afterFilterInvest, this.data.links)) {
          filterNodes.push(node);
        }
      });
      const filterNodesIds = _.map(filterNodes, 'id');
      this.chart && this.chart.highLightFilter(filterNodesIds, this.isFilterOpened);
      // 父结点不存在则删除
    },
    filterNodesByStatus(nodes, status) {
      if (status === 'all') {
        return nodes;
      }
      const afterFilterNodes = [];
      _.forEach(nodes, (node) => {
        if ((node.data.obj.labels[0] === 'Company' && node.data.obj.properties.status === status) || node.id === this.data._rootNode.id) {
          afterFilterNodes.push(node);
        }
      });
      return afterFilterNodes;
    },
    filterNodesByRange(nodes, range) {
      if (!+range) {
        return nodes;
      }
      if (+range > 0) {
        let filterNodes = [];
        let links = this.filterLinksByNodes(nodes, this.data.links);
        links = _.filter(links, (link) => +range <= +link.data.obj.properties.stockPercent);
        links.forEach((link) => {
          filterNodes.push(link.sourceNode);
          filterNodes.push(link.targetNode);
        });
        filterNodes = _.uniqBy(filterNodes, 'id');
        return filterNodes;
      }
    },
    filterLinksByNodes(nodes, links) {
      const selLinks = [];
      const nodesIds = _.map(nodes, 'id');
      _.forEach(links, (link) => {
        if (nodesIds.includes(link.sourceNode.nodeId) && nodesIds.includes(link.targetNode.nodeId)) {
          selLinks.push(link);
        }
      });
      return selLinks;
    },
    filterNodesByInvest(nodes, invest) {
      if (invest === 'all') {
        return nodes;
      }
      let filterNodes = [];
      if (invest === 'direct') {
        filterNodes = this.getInvestNodes(this.data._rootNode.nodeId, this.data.links);
      } else if (invest === 'stockholder') {
        let nextNodes = [];
        const stockholderNodes = this.getCompanyStockholder(this.data._rootNode.nodeId, this.data.links);
        for (let i = 0; i < stockholderNodes.length; i++) {
          nextNodes = nextNodes.concat(this.getInvestNodes(stockholderNodes[i].nodeId, this.data.links));
        }
        filterNodes = stockholderNodes.concat(nextNodes);
      } else if (invest === 'legal') {
        let nextNodes = [];
        const stockholderNodes = this.getPersonStockholder(this.data._rootNode.nodeId, this.data.links);
        for (let index = 0; index < stockholderNodes.length; index++) {
          nextNodes = nextNodes.concat(this.getInvestNodes(stockholderNodes[index].nodeId, this.data.links));
        }
        filterNodes = stockholderNodes.concat(nextNodes);
      }
      filterNodes.push(this.data._rootNode);
      filterNodes = _.uniqBy(filterNodes, 'id');
      const nodesIds = _.map(nodes, 'id');
      const filterNodes2 = _.filter(filterNodes, (node) => nodesIds.includes(node.id));
      return filterNodes2;
    },
    // 获取直接投资节点
    getInvestNodes(nodeId, links) {
      const filterNodes = [];
      _.forEach(links, (link) => {
        if (link.sourceNode.nodeId === nodeId && link.data.obj.type === 'INVEST') {
          filterNodes.push(link.targetNode);
        }
      });
      return filterNodes;
    },
    getCompanyStockholder(nodeId, links) {
      const filterNodes = [];
      _.forEach(links, (link) => {
        if (link.targetNode.nodeId === nodeId && link.data.obj.type === 'INVEST') {
          filterNodes.push(link.sourceNode);
        }
      });
      return filterNodes;
    },
    getPersonStockholder(nodeId, links) {
      const filterNodes = [];
      _.forEach(links, (link) => {
        if (link.targetNode.nodeId === nodeId && link.data.obj.type === 'INVEST' && link.sourceNode.data.obj.labels[0] === 'Person') {
          filterNodes.push(link.sourceNode);
        }
      });
      return filterNodes;
    },
    isParentExit(node, nodes, links) {
      let isExist = false;
      const parentLevel = node.layout.level - 1;
      if (parentLevel < 2) {
        return true;
      }
      const nodesIds = _.map(nodes, 'id');
      for (let i = 0; i < links.length; i++) {
        const link = links[i];
        if (
          link.sourceNode.nodeId === node.nodeId &&
          link.targetNode.layout.level === parentLevel &&
          nodesIds.includes(link.targetNode.nodeId)
        ) {
          isExist = true;
          break;
        }
        if (
          link.targetNode.nodeId === node.nodeId &&
          link.sourceNode.layout.level === parentLevel &&
          nodesIds.includes(link.sourceNode.nodeId)
        ) {
          isExist = true;
          break;
        }
      }
      return isExist;
    },
    onMouseenter() {
      if (this.iframe && this.isInit) {
        this.chart.delayZoom();
      }
    },
    attachResizeDetector() {
      this.resizeDetector = resizeDetector();
      this.resizeDetector.listenTo(
        $(`.${this.containerName}`)[0],
        _.debounce(() => {
          this.chart && this.chart.sizeChange();
        }, 200)
      );
    },
  },
};
