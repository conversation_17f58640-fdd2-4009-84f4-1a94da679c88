/* eslint-disable consistent-return */
/* eslint-disable class-methods-use-this */
/* eslint-disable no-underscore-dangle */
/* eslint-disable func-names */
/* eslint-disable no-param-reassign */

import { EventEmitter } from 'eventemitter3';
import _ from 'lodash';
import * as d3 from 'd3';

import * as companyUtil from '@/utils/firm';

import utils from '../../utils/utils';
import settings from '../settings';
// import logoHelper from '../../utils/popoverHelper';

const cytoscape = window.cytoscape;

export default class Chart extends EventEmitter {
  constructor(selector, iframe) {
    super();
    this.selector = selector;
    this.$selector = $(selector);
    this.iframe = iframe;
    this.elements = {
      nodes: [],
      edges: [],
    };
    this.showText = false;
  }

  init(data) {
    this.data = data;

    const width = this.$selector.width();
    const height = this.$selector.height();
    this.size = {
      width,
      height,
    };
  }

  domUpdate() {
    this.getD3Position(this.data);
    for (let i = 0, n = Math.ceil(Math.log(this.simulation.alphaMin()) / Math.log(1 - this.simulation.alphaDecay())); i < n; ++i) {
      this.simulation.tick();
    }
    this.drawChart(this.transformData(this.data));
  }

  getD3Position(graph) {
    this.getLayoutNode(graph);
    graph.layoutLinks = this.filterLinks(graph);
    let strength = -600;
    let distanceMax = 330;
    let distance = 130;
    let colideRadius = 35;
    const distanceMin = 400;
    const nodeLength = graph.nodes.length;
    if (nodeLength < 50) {
      strength = -800;
      distanceMax = 400;
    } else if (nodeLength < 100) {
      strength = -800;
      distanceMax = 350;
      distance = 130;
      colideRadius = 35;
    } else if (nodeLength < 150) {
      strength = -900;
      distanceMax = 450;
    } else if (nodeLength < 200) {
      strength = -1000;
      distanceMax = 500;
    } else if (nodeLength >= 200) {
      strength = -1600;
      distanceMax = 500;
      distance = 100;
      colideRadius = 35;
    }

    this.simulation = d3
      .forceSimulation(graph.nodes)
      .force('charge', d3.forceManyBody().strength(strength).distanceMax(distanceMax).distanceMin(distanceMin))
      .force('link', d3.forceLink(graph.layoutLinks).distance(distance))
      .force('center', d3.forceCenter(this.size.width / 2, this.size.height / 2))
      .force(
        'collide',
        d3.forceCollide().radius(function () {
          return colideRadius;
        })
      );
  }

  filterLinks(graph) {
    const layoutLinks = [];
    for (let i = 0; i < graph.links.length; i++) {
      const link = graph.links[i];
      const sourceLevel = link.sourceNode.layout.level;
      const targetLevel = link.targetNode.layout.level;
      if (Math.abs(sourceLevel - targetLevel) === 1) {
        layoutLinks.push(link);
      }
    }
    layoutLinks.forEach((link) => {
      if (link.targetNode.layout.level === 3) {
        layoutLinks.forEach((alink, j) => {
          if (
            alink.linkId !== link.linkId &&
            (alink.targetNode.nodeId === link.targetNode.nodeId || alink.sourceNode.nodeId === link.targetNode.nodeId)
          ) {
            layoutLinks.splice(j, 1);
          }
        });
      }

      if (link.sourceNode.layout.level === 3) {
        layoutLinks.forEach((alink, j) => {
          if (
            alink.linkId !== link.linkId &&
            (alink.targetNode.nodeId === link.sourceNode.nodeId || alink.sourceNode.nodeId === link.sourceNode.nodeId)
          ) {
            layoutLinks.splice(j, 1);
          }
        });
      }
    });
    return layoutLinks;
  }

  getLayoutNode(graphData) {
    const layoutNode = {
      current: graphData._rootNode,
    };
    _.forEach(graphData.nodes, (node) => {
      if (!layoutNode[node.layout.level]) {
        layoutNode[node.layout.level] = [];
      }
      layoutNode[node.layout.level].push(node);
    });
    this._layoutNode = layoutNode;
    return layoutNode;
  }

  // 绘制图谱f
  drawChart(elements) {
    // let self = this
    const _currentKeyNo = _.get(this.data, '_rootNode.data.obj.properties.keyNo', '');
    let _isFocus = false;
    let activeNode = null;
    // let [_companyRadius, _personRadius, _circleMargin, _circleBorder] = [35, 15, 10, 3]
    this.cy = cytoscape({
      container: document.getElementById('relationChart'),
      motionBlur: false,
      textureOnViewport: false,
      wheelSensitivity: 0.1,
      elements,
      zoom: 0.7,
      minZoom: 0.4,
      maxZoom: 2.5,
      layout: {
        name: 'preset',
        componentSpacing: 40,
        nestingFactor: 12,
        padding: 10,
        edgeElasticity: 800,
        stop() {
          // 解决浏览器标签切换排列问题
          // if (document[state] === 'hidden') {
          //   _isNeedReload = true
          //   self.emit('needReload')
          // } else {
          //   _isNeedReload = false
          // }
          // setTimeout(function () {
          //   if (document[state] == 'hidden') {
          //     _isGraphLoaded = false
          //   } else {
          //     _isGraphLoaded = true
          //   }
          // }, 1000)
        },
      },
      style: [
        {
          selector: 'node',
          style: {
            shape: 'ellipse',
            width(ele) {
              // 当前节点有图片
              if (ele.data('type') === 'Person' && _currentKeyNo === ele.data('keyNo') && ele.data('hasImage')) {
                return 80;
              }
              // 有图片
              if (ele.data('hasImage') && ele.data('type') === 'Person') {
                return 60;
              }
              // 普通
              if (ele.data('type') === 'Company') {
                return 60;
              }
              return 45;
            },
            height(ele) {
              // 当前节点有图片
              if (ele.data('type') === 'Person' && _currentKeyNo === ele.data('keyNo') && ele.data('hasImage')) {
                return 80;
              }
              // 有图片
              if (ele.data('hasImage') && ele.data('type') === 'Person') {
                return 60;
              }
              // 普通
              if (ele.data('type') === 'Company') {
                return 60;
              }
              return 45;
            },
            'background-color': function (ele) {
              return ele.data('color');
            },
            'background-fit': 'cover',
            'background-image': function (ele) {
              const hasImage = ele.data('hasImage');
              const keyNo = ele.data('keyNo');
              const type = ele.data('type');
              if (hasImage && type === 'Person') {
                return companyUtil.getLogoByKeyNo(keyNo, hasImage);
              }
              return 'none';
            },
            // 'background-image-crossorigin': 'use-credentials',
            'border-color': function (ele) {
              return ele.data('borderColor');
            },
            'border-width': function (ele) {
              if (ele.data('hasImage') && ele.data('type') === 'Person') {
                return 3;
              }
              return 1;
            },
            'border-opacity': 1,
            label(ele) {
              const label = ele.data('name');
              const length = label.length;

              if (length <= 5) {
                // 4 5 4排列
                return label;
              }
              if (length >= 5 && length <= 9) {
                return `${label.substring(0, length - 5)}\n${label.substring(length - 5, length)}`;
              }
              if (length >= 9 && length <= 13) {
                return `${label.substring(0, 4)}\n${label.substring(4, 9)}\n${label.substring(9, 13)}`;
              }
              return `${label.substring(0, 4)}\n${label.substring(4, 9)}\n${label.substring(9, 12)}..`;
            },
            'z-index-compare': 'manual',
            'z-index': 20,
            color: '#fff',
            padding(ele) {
              if (ele.data('type') === 'Company') {
                return 3;
              }
              return 0;
            },
            'line-height': 1.4,
            'font-size': 12,
            'font-family': 'microsoft yahei',
            'text-wrap': 'wrap',
            'text-max-width': 60,
            'text-halign': 'center',
            'text-valign': 'center',
            'overlay-color': '#fff',
            'overlay-opacity': 0,
            'background-opacity': 1,
            'text-background-color': '#000',
            'text-background-shape': 'roundrectangle',
            'text-background-opacity': function (ele) {
              if (ele.data('hasImage') && ele.data('type') === 'Person') {
                return 0.3;
              }
              return 0;
            },
            'text-background-padding': 0,
            'text-margin-y': function (ele) {
              // 当前节点有图片
              if (ele.data('type') === 'Person' && _currentKeyNo === ele.data('keyNo') && ele.data('hasImage')) {
                return 23;
              }
              // 有图片
              if (ele.data('hasImage') && ele.data('type') === 'Person') {
                return 16;
              }
              if (ele.data('type') === 'Company') {
                return 4;
              }
              return 2;
            },
          },
        },
        {
          selector: 'edge',
          style: {
            color() {
              return '#999';
            },
            'line-style': function () {
              return 'solid';
            },
            'curve-style': 'bezier',
            'control-point-step-size': 20,
            'target-arrow-shape': 'triangle',
            'target-arrow-color': function (ele) {
              return ele.data('color');
            },
            'arrow-scale': 0.8,
            'line-color': '#D6D6D6',
            'text-opacity': 1,
            'font-size': 12,
            'background-color': function () {
              return '#ccc';
            },
            width: '0.6',
            'overlay-color': '#fff',
            'overlay-opacity': 0,
            'font-family': 'microsoft yahei',
          },
        },
        {
          selector: '.nodeActive',
          style: {
            'border-color': function (ele) {
              return ele.data('color');
            },
            'border-width': 10,
            'border-opacity': 0.5,
          },
        },
        {
          selector: '.edgeShow',
          style: {
            color: '#999',
            'text-opacity': 1,
            'font-weight': 'normal',
            label(ele) {
              return ele.data('label');
            },
            'font-size': 10,
          },
        },
        {
          selector: '.edgeActive',
          style: {
            color(ele) {
              return ele.data('color');
            },
            'arrow-scale': 0.8,
            width: 1.5,
            'text-opacity': 1,
            'font-size': 12,
            'text-background-color': '#fff',
            'text-background-opacity': 0.8,
            'text-background-padding': 0,
            'source-text-margin-y': 20,
            'target-text-margin-y': 20,
            'z-index-compare': 'manual',
            'z-index': 1,
            'line-color': function (ele) {
              return ele.data('color');
            },
            'target-arrow-color': function (ele) {
              return ele.data('color');
            },
            label(ele) {
              return ele.data('label');
            },
          },
        },
        {
          selector: '.hidetext',
          style: {
            'text-opacity': 0,
          },
        },
        {
          selector: '.dull',
          style: {
            'z-index': 1,
            opacity: 0.2,
          },
        },
        {
          selector: '.nodeHover',
          style: {
            shape: 'ellipse',
            'background-opacity': 0.9,
          },
        },
        {
          selector: '.edgeLevel1',
          style: {
            label(ele) {
              return ele.data('label');
            },
          },
        },
        {
          selector: '.edgeShowText',
          style: {
            label(ele) {
              return ele.data('label');
            },
          },
        },
        {
          selector: '.lineFixed', // 加载完成后，加载该类，修复线有锯齿的问题
          style: {
            'overlay-opacity': 0,
          },
        },
      ],
    });
    this.cy.on('click', 'node', (evt) => {
      if (+evt.target._private.style['z-index'].value === 20) {
        _isFocus = true;
        const node = evt.target;
        this.highLight([node._private.data.id], this.cy);
        if (node.hasClass('nodeActive')) {
          activeNode = null;
          node.removeClass('nodeActive');
          this.cy.collection('edge').removeClass('edgeActive');
        } else {
          this.cy.collection('node').addClass('nodeDull');
          activeNode = node;
          this.cy.collection('node').removeClass('nodeActive');
          this.cy.collection('edge').removeClass('edgeActive');
          node.addClass('nodeActive');
          node.neighborhood('edge').removeClass('opacity');
          node.neighborhood('edge').addClass('edgeActive');
          node.neighborhood('edge').connectedNodes().removeClass('opacity');
        }
      } else {
        _isFocus = false;
        activeNode = null;
        this.cy.collection('node').removeClass('nodeActive');
        this.cancelHighLight();
      }
    });
    let startX = 0;
    let startY = 0;
    this.cy.on('mousedown', (evt) => {
      if (evt.target._private.group === 'nodes') {
        startX = window.event.clientX;
        startY = window.event.clientY;
      }
    });

    this.cy.on('mousemove', (evt) => {
      if (evt.target && evt.target._private && evt.target._private.group === 'nodes' && startX && startY) {
        const endX = window.event.clientX;
        const endY = window.event.clientY;
        if ((endX - startX) * (endX - startX) + (endY - startY) * (endY - startY) >= 1) {
          if (evt.target._private.group === 'nodes' && evt.target._private.data.id) {
            this.emit('onNodeMouseout', { data: evt.target._private.data });
          }
        }
      }
    });

    this.cy.on('mouseup', () => {
      startX = 0;
      startY = 0;
    });

    this.cy.on('mouseover', 'node', (evt) => {
      const node = evt.target;
      if (evt.target._private.style['z-index'].value === 20) {
        node.addClass('nodeHover');
        if (!_isFocus && !this.isFiltered) {
          // this.cy.collection('edge').removeClass('edgeShow')
          this.cy.collection('edge').removeClass('edgeActive');
          node.neighborhood('edge').addClass('edgeActive');
        }
        // 显示弹框
        const position = node.renderedBoundingBox();
        this.emit('onNodeMouseover', {
          data: node._private.data,
          position: {
            left: position.x1,
            right: position.x2,
            top: position.y1 + 60,
            bottom: position.y2,
          },
          size: {
            width: position.w,
            height: position.h,
          },
        });
      }
    });

    this.cy.on('mouseout', 'node', (evt) => {
      const node = evt.target;
      node.removeClass('nodeHover');
      if (!_isFocus && !this.isFiltered) {
        this.cy.collection('edge').removeClass('edgeActive');
      }
      this.emit('onNodeMouseout', {
        data: node._private.data,
      });
    });

    this.cy.on('mouseover', 'edge', (evt) => {
      if (!_isFocus && !this.isFiltered) {
        const edge = evt.target;
        this.cy.collection('edge').removeClass('edgeActive');
        edge.addClass('edgeActive');
      }
    });

    this.cy.on('mouseout', 'edge', (evt) => {
      if (!_isFocus && !this.isFiltered) {
        const edge = evt.target;
        edge.removeClass('edgeActive');
        if (activeNode) {
          activeNode.neighborhood('edge').addClass('edgeActive');
        }
      }
    });

    this.cy.on('click', (evt) => {
      const evtTarget = evt.target;
      if (evtTarget === this.cy) {
        _isFocus = false;
        activeNode = null;
        this.emit('clickCy');
      }
    });
    this.cy.on('zoom', () => {
      if (this.cy.zoom() < 0.5) {
        this.cy.collection('node').addClass('hidetext');
        this.cy.collection('edge').addClass('hidetext');
      } else {
        this.cy.collection('node').removeClass('hidetext');
        this.cy.collection('edge').removeClass('hidetext');
      }

      // 加载完成后，加载该类，修复线有锯齿的问题
      setTimeout(() => {
        this.cy.collection('edge').removeClass('lineFixed');
        this.cy.collection('edge').addClass('lineFixed');
      }, 200);
      this.emit('onZoom', this.cy.zoom());
    });
    this.cy.on('pan', () => {
      // 加载完成后，加载该类，修复线有锯齿的问题
      setTimeout(() => {
        this.cy.collection('edge').removeClass('lineFixed');
        this.cy.collection('edge').addClass('lineFixed');
      }, 200);
    });
    this.cy.nodes().positions((node) => {
      if (node._private.data.nodeId === this.data._rootNode.nodeId) {
        const position = this.cy.pan();
        this.cy.pan({
          x: position.x - node._private.data.d3x,
          y: position.y - node._private.data.d3y,
        });
        // let currentPosition = node.renderedPosition()
        // console.log(111111, this.cy.width(), this.cy.height(), currentPosition)
        // self.cy.panBy({
        //   x: this.cy.width() / 2 - currentPosition.x,
        //   y: this.cy.height() / 2 - currentPosition.y
        // })
      }
      return {
        x: node._private.data.d3x,
        y: node._private.data.d3y,
      };
    });
    this.cy.ready(() => {
      this.cy.zoom({
        level: this.iframe ? 0.7 : 1.0000095043745896,
      });
      this.cy.center();
      this.emit('hideLoading');
      setTimeout(() => {
        this.cy.collection('edge').addClass('lineFixed');
      });

      // 首页的插入图 默认高亮第一层
    });
  }

  highLight(nodeIds, isFiltered = false) {
    this.isFiltered = isFiltered;
    this.cy.collection('node').removeClass('nodeActive');
    this.cy.collection('edge').removeClass('edgeActive');
    this.cy.collection('node').addClass('dull');
    this.cy.collection('edge').addClass('dull');
    for (let i = 0; i < nodeIds.length; i++) {
      const nodeId = nodeIds[i];
      this.cy.nodes((node) => {
        const nodeData = node._private.data;
        if (nodeData.id === nodeId) {
          node.removeClass('dull');
          node.neighborhood('edge').removeClass('dull');
          node.neighborhood('edge').addClass('edgeActive');
          node.neighborhood('edge').connectedNodes().removeClass('dull');
        }
      });
    }
  }

  highLightFilter(nodeIds, isFiltered = false) {
    this.isFiltered = isFiltered;
    this.cy.collection('node').removeClass('nodeActive');
    this.cy.collection('edge').removeClass('edgeActive');
    this.cy.collection('node').addClass('dull');
    this.cy.collection('edge').addClass('dull');
    this.cy.nodes((node) => {
      if (nodeIds.includes(node._private.data.id)) {
        node.removeClass('dull');
      }
    });
    this.cy.edges((edge) => {
      const data = edge._private.data;
      if (nodeIds.includes(data.target) && nodeIds.includes(data.source)) {
        edge.removeClass('dull');
        edge.addClass('edgeActive');
      }
    });
  }

  cancelHighLight() {
    this.isFiltered = false;
    this.cy.collection('node').removeClass('nodeActive');
    this.cy.collection('edge').removeClass('edgeActive');
    this.cy.collection('node').removeClass('dull');
    this.cy.collection('edge').removeClass('dull');
  }

  transformData(graphData) {
    const els = {
      nodes: [],
      edges: [],
    };

    graphData.links.forEach((link) => {
      els.edges.push({
        data: {
          data: link.data,
          color: this.getLinkColor(link.data.obj.type),
          id: link.linkId,
          label: this.getLinkLabel(link),
          source: link.sourceNode.nodeId,
          target: link.targetNode.nodeId,
        },
        classes: 'edgeShow',
      });
    });

    graphData.nodes.forEach((node) => {
      els.nodes.push({
        data: {
          nodeId: node.nodeId,
          type: node.data.obj.labels[0],
          keyNo: node.data.obj.properties.keyNo,
          data: node.data,
          id: node.nodeId,
          name: node.data.obj.properties.name,
          category: node.data.category,
          color: node.data.color,
          borderColor: node.data.strokeColor,
          layout: node.layout,
          d3x: node.x,
          d3y: node.y,
          hasImage: node.data.obj.properties.hasImage,
        },
        // position: { x: node.x, y: node.y }
      });
    });
    return els;
  }

  getLinkColor(type) {
    if (type === 'INVEST') {
      return settings._COLOR.line.invest;
    }
    if (type === 'EMPLOY') {
      return settings._COLOR.line.employ;
    }
    if (type === 'LEGAL') {
      return settings._COLOR.line.legal;
    }
  }

  getLinkLabel(link) {
    const type = link.data.obj.type;
    const role = link.data.obj.properties.role;
    if (type === 'INVEST') {
      return '投资';
    }
    if (type === 'EMPLOY') {
      return role || '任职';
    }
    if (type === 'LEGAL') {
      return role || '法定代表人';
    }
  }

  toggleText(showText) {
    this.showText = showText;
    if (this.showText) {
      this.cy.collection('edge').addClass('edgeShow');
    } else {
      this.cy.collection('edge').removeClass('edgeShow');
    }
  }

  zoomInOrOut({ isIn = false }) {
    const step = 0.2;
    const maxLevel = 3;
    const minLevel = 0.3;
    let currentLevel = this.cy.zoom() + step * (isIn ? 1 : -1);
    if (currentLevel >= maxLevel) currentLevel = maxLevel;
    if (currentLevel <= minLevel) currentLevel = minLevel;
    this.cy.zoom({
      level: currentLevel,
    });
    // this.cy.center()
  }

  // iframe 时使用
  delayZoom() {
    this.cy.zoomingEnabled(false);
    setTimeout(() => {
      this.cy.zoomingEnabled(true);
    }, 2000);
  }

  saveImg(companyName) {
    const imgdata = this.cy.png({ full: true, bg: 'rgba(255, 255, 255, 0)', scale: 1.5 });
    utils.downloadImage(imgdata, companyName, '关系图谱');
  }

  cleanup() {
    if (this.cy) {
      this.cy.destroy();
    }
  }

  sizeChange() {
    const newWidth = this.$selector.width();
    const newHeight = this.$selector.height();
    if (!this.cy) return;
    if (this.size.width === newWidth && this.size.height === newHeight) return;
    this.size = {
      width: newWidth,
      height: newHeight,
    };
    this.cy.center();
  }
}
