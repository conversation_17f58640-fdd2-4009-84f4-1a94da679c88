<template>
  <div id="relation-chart" :class="['relation-chart', containerName]">
    <div :id="containerName"></div>
    <!-- 操作栏 -->
    <div class="toolbox" v-show="isInit">
      <g-ui-toolbox>
        <g-ui-toolbox-action
          v-show="!isPerson"
          :action-type="actionTypes.filter"
          :is-active="isFilterOpened"
          @click="onFilter"
        ></g-ui-toolbox-action>
        <g-ui-toolbox-action :is-active="isShowText" :action-type="actionTypes.showText" @click="showText"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.zoomIn" @click="onZoomIn"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.zoomOut" @click="onZoomOut"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.refresh" @click="onRefresh"></g-ui-toolbox-action>
        <g-ui-toolbox-action v-show="!isFullScreen" :action-type="actionTypes.fullScreen" @click="onFullScreen"></g-ui-toolbox-action>
        <g-ui-toolbox-action
          v-show="isFullScreen"
          :action-type="actionTypes.exitFullScreen"
          @click="onExitFullScreen"
        ></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.save" :is-busy="isSaving" @click="onSave"></g-ui-toolbox-action>
      </g-ui-toolbox>
    </div>
    <!-- 免责 -->
    <g-ui-footer></g-ui-footer>
    <!-- 加载 -->
    <g-ui-loading v-if="isLoading"></g-ui-loading>
    <!-- 暂无数据 -->
    <g-ui-no-data v-if="noData"></g-ui-no-data>
    <div class="legend-container">
      <relation-app-legend></relation-app-legend>
    </div>
    <transition name="fade">
      <div class="filter" v-show="isFilterOpened">
        <relation-app-filter
          @changeFilter="changeFilter"
          @searchCancelFocus="searchCancelFocus"
          @searchFocus="searchFocus"
          @close="isFilterOpened = false"
          ref="filter"
        ></relation-app-filter>
      </div>
    </transition>
  </div>
</template>
<style lang="less" scoped src="./style.less"></style>
<script src="./component.js"></script>
