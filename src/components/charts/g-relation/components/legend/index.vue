<template>
  <div class="chart-legend-wrap">
    <div class="chart-tips">
      <div class="tips current-tips">当前节点</div>
      <div class="tips ent-tips">企业</div>
      <div class="tips person-tips">人员</div>
      <div class="tips-line tz-tips">投资</div>
      <div class="tips-line rz-tips">任职</div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'relation-app-legend',
};
</script>
<style lang="less" scoped>
.chart-legend-wrap {
  background-color: #fff;
  height: 30px;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 2;

  .chart-tips {
    overflow: hidden;
    width: 416px;
    margin: 0 auto;

    > div {
      float: left;
      line-height: 30px;
      margin-right: 50px;
      color: #333;
      font-size: 14px;
      position: relative;

      &.tips {
        padding-left: 16px;

        &::before {
          content: '';
          display: block;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translate(0, -50%);
        }
      }

      &.tips-line {
        display: block;

        &::before {
          content: '';
          display: block;
          width: 16px;
          height: 8px;
          border-radius: 0;
          position: absolute;
          left: -21px;
          top: 50%;
          transform: translate(0, -50%);
        }
      }

      &.current-tips {
        &::before {
          background-color: #ff9e01;
        }
      }

      &.ent-tips {
        &::before {
          background-color: #4ea2f0;
        }
      }

      &.person-tips {
        &::before {
          background-color: #fd485d;
        }
      }

      &.tz-tips {
        &::before {
          background: url('./images/icon-arrow-tz.svg') no-repeat;
          background-size: contain;
        }
      }

      &.rz-tips {
        &::before {
          background: url('./images/icon-arrow.svg') no-repeat;
          background-size: contain;
        }
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
