import _ from 'lodash';

import uiNoData from '../../../g-ui-no-data';

export default {
  name: 'relation-app-filter',
  components: {
    [uiNoData.name]: uiNoData,
  },
  data() {
    return {
      data: [],
      searchKey: '',
      list: [],
      isFocus: false,
      hasSetSearchKey: false,
      focusIds: [],
      levelList: [
        {
          key: 1,
          value: '一层',
        },
        {
          key: 2,
          value: '二层',
        },
      ],
      investList: [
        {
          key: 'all',
          value: '全部',
        },
        {
          key: 'direct',
          value: '直接投资',
        },
        {
          key: 'stockholder',
          value: '股东投资',
        },
        {
          key: 'legal',
          value: '董监高法投资',
        },
      ],
      range: 0,
      statusList: [
        {
          key: 'all',
          value: '全部',
        },
        {
          key: '吊销',
          value: '吊销',
        },
        {
          key: '注销',
          value: '注销',
        },
        {
          key: '存续',
          value: '存续',
        },
      ],
      level: 2,
      invest: 'all',
      status: 'all',
    };
  },
  methods: {
    close() {
      this.$emit('close');
    },
    searchInput: _.debounce(function () {
      this.list = [];
      _.forEach(this.data, (node) => {
        const name = _.get(node, 'data.obj.properties.name', '');
        if (name && name.match(this.searchKey)) {
          this.list.push(node);
        }
      });
    }, 200),
    setPanel(data) {
      this.data = data;
      this.list = [];
      _.forEach(data, (node) => {
        node.isChecked = true;
        this.list.push(node);
      });
    },
    resetFilter() {
      this.level = 2;
      this.range = 0;
      this.status = 'all';
      this.invest = 'all';
    },
    resetSearch() {
      this.isFocus = false;
      this.searchKey = '';
    },
    resetCheck() {
      if (!_.isEmpty(this.data)) {
        this.list = [];
        _.forEach(this.data, (node) => {
          node.isChecked = true;
          this.list.push(node);
        });
      }
    },
    setSearchKey(item) {
      if (this.focusIds !== item.id) {
        this.cancelHighLight();
      }
      this.searchKey = _.get(item, 'data.obj.properties.name', '');
      this.focusIds = [item.id];
      this.hasSetSearchKey = true;
    },
    focusSingle() {
      if (this.hasSetSearchKey) {
        this.isFocus = true;
        this.resetFilter();
        this.$emit('searchFocus', this.focusIds);
      }
    },
    cancelHighLight() {
      this.searchKey = '';
      this.isFocus = false;
      this.hasSetSearchKey = false;
      this.focusIds = [];
      this.list = [];
      _.forEach(this.data, (node) => {
        node.isChecked = true;
        this.list.push(node);
      });
      this.$forceUpdate();
      this.$emit('searchCancelFocus');
    },
    changeFocus(item) {
      this.focusIds = [];
      item.isChecked = !item.isChecked;
      _.forEach(this.list, (node) => {
        if (node.isChecked) {
          this.focusIds.push(node.id);
        }
      });
      this.$forceUpdate();

      this.resetFilter();
      this.$emit('searchFocus', this.focusIds);
    },
    changeFilter(type, value) {
      if (type === 'level') {
        this.level = value;
      } else if (type === 'invest') {
        this.invest = value;
      } else if (type === 'status') {
        this.status = value;
      }
      this.$emit('changeFilter', {
        level: this.level,
        range: this.range,
        invest: this.invest,
        status: this.status,
      });
    },
  },
};
