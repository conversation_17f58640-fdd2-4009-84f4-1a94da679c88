<template>
  <div class="chart-relation-filter">
    <div id="SelPanel" class="tp-sel animate" param-level="2">
      <div class="header">
        <div class="title">筛选</div>
        <div class="filter-close" @click="close"><span>&times;</span></div>
      </div>
      <div class="body">
        <div class="row-panel">
          <div class="key">层级</div>
          <div class="val">
            <a
              v-for="(item, index) in levelList"
              :key="`level-${index}`"
              :class="['btn btn-default', +level === +item.key && 'active']"
              @click="changeFilter('level', item.key)"
              >{{ item.value }}</a
            >
          </div>
        </div>
        <div class="row-panel">
          <div class="key">状态</div>
          <div class="val">
            <a
              v-for="(item, index) in statusList"
              :key="`status-${index}`"
              :class="['btn btn-default', status === item.key && 'active']"
              @click="changeFilter('status', item.key)"
              >{{ item.value }}</a
            >
          </div>
        </div>
        <div class="row-panel">
          <div class="key range">持股</div>
          <div class="val range">
            <div class="inputRange-div">
              <a-slider id="test" :default-value="0" :tooltip-visible="false" @change="changeFilter('range', '')" v-model="range" />
              <div id="RangeLabel">
                <div class="pull-left">0%</div>
                <div class="pull-center">
                  <span>{{ range ? `${range}%` : '未选择' }}</span>
                </div>
                <div class="pull-right">100%</div>
              </div>
            </div>
          </div>
        </div>
        <div class="row-panel">
          <div class="key">关系</div>
          <div class="val">
            <a
              v-for="(item, key) in investList"
              :key="`invest-${key}`"
              :class="['btn btn-default', invest === item.key && 'active']"
              @click="changeFilter('invest', item.key)"
              >{{ item.value }}</a
            >
          </div>
        </div>
        <div class="split-line"></div>
        <div class="filter-form-group">
          <div class="input-wrap">
            <span id="ClearInput" v-if="searchKey.length" @click="cancelHighLight">&times;</span>
            <input
              id="FocusInput"
              @input="searchInput"
              v-model="searchKey"
              class="form-control"
              type="text"
              placeholder="请输入您想查询的公司/个人"
              value=""
            />
          </div>
          <button
            v-if="!isFocus"
            id="FocusBt"
            type="submit"
            :class="['btn-search btn btn-primary focusDisable btn-icon btn-top', !hasSetSearchKey && 'disable']"
            style="height: 32px"
            @click="focusSingle()"
          >
            聚焦
          </button>
          <button
            v-else
            @click="cancelHighLight"
            id="FocusBt"
            type="submit"
            :class="['btn-search btn btn-primary focusDisable btn-icon btn-top']"
            style="height: 32px"
          >
            取消
          </button>
        </div>
        <div class="tp-list">
          <template v-if="list.length">
            <div v-for="(item, index) in list" class="list-item" :key="`filter-item-${index}`">
              <span @click="changeFocus(item)" :class="{ item: true, active: !!item.isChecked }"></span>
              <q-entity-avatar
                :size="32"
                :key-no="item.data.obj.properties.keyNo"
                :hasImage="item.data.obj.properties.hasImage"
                :name="item.data.obj.properties.name"
              ></q-entity-avatar>
              <span class="name" @click="setSearchKey(item)">{{ index + 1 }}. {{ item.data.obj.properties.name }}</span>
            </div>
          </template>
          <template v-else>
            <g-ui-no-data></g-ui-no-data>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
<script src="./component.js"></script>
<style lang="less" scoped src="./style.less"></style>
<style lang="less">
.chart-relation-filter {
  .ant-slider ::v-deep {
    width: 350px;
    margin-left: 0;
    margin-top: 2px;

    .ant-slider-track {
      background: #128bed;
    }

    .ant-slider:hover .ant-slider-handle:not(.ant-tooltip-open) {
      border-color: #128bed;
    }

    .ant-slider-handle {
      border-color: #128bed;
    }
  }
}
</style>
