.chart-relation-filter {
  .tp-sel {
    width: 500px;
    height: 560px;
    background: white;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
    position: fixed;
    bottom: 100px;
    right: 78px;
    border-radius: 6px;

    .btn-default {
      border: 1px solid #d6d6d6;
      color: #333;
      background-color: #fff;
    }

    .btn-primary {
      color: #fff;
      background-color: #128bed;
      border-color: #128bed;
      border-style: none;
      cursor: pointer;
    }

    .header {
      border-bottom: 1px solid #eee;
      height: 55px;
      line-height: 55px;
      font-size: 16px;
      font-weight: bold;
      position: relative;
      padding: 0 15px;

      .filter-close {
        position: absolute;
        right: 15px;
        display: inline-block;
        font-size: 26px;
        top: 50%;
        transform: translate(0, -50%);
        font-weight: bold;
        cursor: pointer;

        span {
          color: #128bed;
        }
      }
    }

    .body {
      padding: 15px 0;

      .row-panel {
        margin-bottom: 20px;
        overflow: hidden;
        padding: 0 15px;

        .key {
          float: left;
          margin-right: 15px;
          line-height: 32px;
        }

        .val {
          float: left;
          min-height: 32px;
          position: relative;

          &.range {
            margin-bottom: 5px;
            width: 350px;
            min-height: 40px;
          }

          .btn {
            display: inline-block;
            margin-right: 15px;
            width: 82px;
            height: 32px;
            box-sizing: border-box;
            border-radius: 2px;
            font-size: 12px;
            padding: 0;
            line-height: 30px;
            font-weight: normal;
            text-align: center;

            &:last-child {
              margin-right: 0;
            }
          }
        }
      }

      .split-line {
        height: 5px;
        background-color: #f6f6f6;
      }

      .item {
        position: relative;
        top: 9px;
        display: inline-block;
        width: 14px;
        height: 14px;
        background: url('./images/check box.png') 14px 14px;
        background-position: -14px 0;
        background-size: cover;
        margin-right: 5px;

        &.active {
          background-position: 0 0;
        }
      }

      .filter-form-group {
        padding: 20px 15px 10px;
        box-sizing: border-box;
        overflow: hidden;

        .btn-search {
          width: 74px;
          font-size: 14px;
          border-radius: 4px;
          float: right;

          &.disable {
            background-color: #ccc;
            border: #ccc 1px solid;
            cursor: not-allowed;
          }
        }

        .input-wrap {
          float: left;
          width: 381px;
          position: relative;

          &::after {
            clear: both;
          }

          span {
            cursor: pointer;
          }
        }

        input {
          width: 381px;
          float: left;
          border-radius: 4px;
          border: 1px solid #d6d6d6;
          box-shadow: none;
          height: 34px;
          padding: 6px 12px;
          font-size: 14px;
          color: #666;
          background-color: #fff;
          outline: none;

          &::placeholder {
            color: #d6d6d6;
          }
        }

        span {
          position: absolute;
          right: 15px;
          top: 50%;
          transform: translate(0, -50%);
          color: #d6d6d6;
        }
      }
    }
  }

  .tp-sel-close {
    height: 48px;
    width: 48px;
  }

  .tp-sel-close span {
    display: inline-block;
    font-size: 27px;
    color: #128bed;
    cursor: pointer;
  }

  .tp-list {
    height: 190px;
    overflow-y: auto;
    position: relative;

    .list-item {
      padding: 10px 15px;
      cursor: pointer;

      &:hover {
        background-color: #f3f9fd;
        color: #128bed;
      }

      input {
        margin-right: 5px;
      }

      .name {
        margin-left: 5px;
        width: 86%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        float: right;
        line-height: 32px;
      }
    }

    .chart-no-data ::v-deep {
      img {
        width: 140px !important;
        height: 140px !important;
      }
    }
  }

  .tp-list input {
    position: relative;
    top: 3px;
  }

  .inputRange-div {
    margin: auto 0;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translate(0, -50%);
  }

  #inputRange {
    margin: 8px 0;
    width: 350px;
    padding: 0;
    border-radius: 10px;
    height: 6px;
    -webkit-appearance: none;
    background: linear-gradient(#059cfa, #059cfa) no-repeat;
    background-size: 0% 100%;
  }

  #RangeLabel {
    position: absolute;
    left: 0;
    width: 350px;
    bottom: -10px;

    .pull-left {
      float: left;
    }

    .pull-right {
      float: right;
    }

    .pull-center {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }

  #RangeLabel div {
    color: #666;
    font-size: 12px;
  }

  .tp-sel a.active {
    background: white !important;
    color: #128bed !important;
    border: #128bed 1px solid !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
  }
}
