/* eslint-disable consistent-return */
import _ from 'lodash';

import { graph } from '@/shared/services';

import formatter from './formatter';

const loadGraph = (params) => {
  return new Promise((resolve, reject) => {
    graph
      .relationGetGraph(params)
      .then((data) => {
        if (+data.Status === 201 || _.isEmpty(data.results && data.results[0] && data.results[0].data)) {
          resolve([]);
        } else {
          formatter.init({ data: data.results[0].data, _currentKeyNo: params.keyNo });
          const detail = formatter.format(data.results[0].data);
          resolve(detail);
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};
export default {
  loadGraph,
};
