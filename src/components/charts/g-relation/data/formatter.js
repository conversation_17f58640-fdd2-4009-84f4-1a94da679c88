/* eslint-disable class-methods-use-this */
/* eslint-disable no-underscore-dangle */
import _ from 'lodash';

import settings from '../settings';

class Relation {
  init({ data, _currentKeyNo }) {
    this.data = data;
    this._currentKeyNo = _currentKeyNo;
    this._rootNode = null;
  }

  format(list) {
    const graph = {
      nodes: [],
      links: [],
    };
    for (let i = 0; i < list.length; i++) {
      const nodes = list[i].graph.nodes;
      for (let j = 0; j < nodes.length; j++) {
        const node = nodes[j];
        const o = {};
        o.nodeId = node.id;
        o.id = node.id;
        o.data = {};
        o.data.obj = node;
        o.data.showStatus = null; // NORMAL HIGHLIGHT DULL
        o.layout = {};
        o.layout.level = null;
        o.layout.singleLinkChildren = [];
        graph.nodes.push(o);

        if (this._currentKeyNo === o.data.obj.properties.keyNo) {
          this._rootNode = o;
        }
      }
    }
    graph.nodes = _.uniqBy(graph.nodes, 'nodeId');

    for (let i = 0; i < list.length; i++) {
      const relationships = list[i].graph.relationships;
      for (let j = 0; j < relationships.length; j++) {
        const relationship = relationships[j];
        const o = {};
        o.data = {};
        o.data.obj = relationship;
        o.data.showStatus = null;
        o.sourceNode = _.find(graph.nodes, (n) => n.nodeId === relationship.startNode);
        o.targetNode = _.find(graph.nodes, (n) => n.nodeId === relationship.endNode);
        o.linkId = relationship.id;
        o.source = this.getNodesIndex(relationship.startNode, graph.nodes);
        o.target = this.getNodesIndex(relationship.endNode, graph.nodes);
        graph.links.push(o);
      }
    }
    graph.links = _.uniqBy(graph.links, 'linkId');

    this.setLevel(graph.nodes, graph.links);
    this.setCategoryColor(graph.nodes, graph.links);

    return { ...graph, _rootNode: this._rootNode };
  }

  setCategoryColor(nodes, links) {
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      if (this._currentKeyNo === node.data.obj.properties.keyNo) {
        node.data.color = settings._COLOR.node.current;
        node.data.strokeColor = settings._COLOR.border.current;
      } else if (node.data.obj.labels[0] === 'Person') {
        node.data.color = settings._COLOR.node.person;
        node.data.strokeColor = settings._COLOR.border.person;
      } else {
        node.data.color = settings._COLOR.node.company;
        node.data.strokeColor = settings._COLOR.border.company;
      }
    }
  }

  setLevel(svgNodes, svgLinks) {
    let level = 1;
    let nodes = [];
    nodes.push(this._rootNode);
    while (nodes.length) {
      let nextNodes = [];
      for (let i = 0; i < nodes.length; i++) {
        const node = nodes[i];
        node.layout.level = level;
        nextNodes = nextNodes.concat(this.getNextNodes(node.nodeId, svgLinks, level));
      }
      level++;
      nodes = nextNodes;
    }
  }

  getNextNodes(nodeId, links, parentLevel) {
    const nextNodes = [];
    for (let i = 0; i < links.length; i++) {
      const link = links[i];
      if (nodeId === link.sourceNode.nodeId && !link.targetNode.layout.level) {
        link.targetNode.layout.level = parentLevel;
        nextNodes.push(link.targetNode);
      } else if (nodeId === link.targetNode.nodeId && !link.sourceNode.layout.level) {
        link.sourceNode.layout.level = parentLevel;
        nextNodes.push(link.sourceNode);
      }
    }
    return nextNodes;
  }

  getNodesIndex(nodeId, nodes) {
    let index = 0;
    for (let i = 0; i < nodes.length; i++) {
      if (nodes[i].nodeId === nodeId) {
        index = i;
        break;
      }
    }
    return index;
  }
}

export default new Relation();
