/* eslint-disable func-names */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-empty-function */
import moment from 'moment';
import _ from 'lodash';

import { graph } from '@/shared/services';

import { actionTypes, gUiToolbox, gUiToolboxAction } from '../g-ui-toolbox';
import gUiFooter from '../g-ui-footer';
import gUiLoading from '../g-ui-loading';
import gUiNoData from '../g-ui-no-data';
import list from './components/list';
import filter from './components/filter';
import popover from './components/popover';
import dataLoader from './data';
import Chart from './chart/index-svg';
import saveSvg from '../utils/save-svg';
import measure from '../utils/measure';
import popoverHelper from '../utils/popoverHelper';
import utils from '../utils/utils';

const CONTAINER_SELECTOR = 'group-member';
const PATH_SELECTOR = 'group-path';

export default {
  name: 'g-group-member',
  components: {
    [gUiToolbox.name]: gUiToolbox,
    [gUiToolboxAction.name]: gUiToolboxAction,
    [gUiFooter.name]: gUiFooter,
    [gUiNoData.name]: gUiNoData,
    [gUiLoading.name]: gUiLoading,
    [list.name]: list,
    [filter.name]: filter,
    // [popover.name]: popover,
  },
  props: {
    actionTypes: {
      type: Object,
      default: () => actionTypes,
    },
    keyNo: {
      type: String,
      defalut: '',
    },
    name: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      groupId: '',
      maxLevel: '',
      shortStatus: '',
      minPercent: '',
      loaded: false,
      isInit: false,
      isLoading: true,
      noData: false,
      pathNoData: false,
      showSearch: false,
      isFullScreen: false,
      isSaving: false,
      windowWidth: 0,
      windowHeight: 0,
      limitExceed: false,
      totalNodes: [],
      transform: { x: 0, y: 0, k: 1 },
      data: {},
      pathData: {},
      chartData: {},
      rootNode: {},
      // filter
      showFilter: false,
      showPath: false,
      limitLength: 400,
    };
  },
  mounted() {
    this.groupId = this.keyNo;
    this.loaded = true;
    this.onRefresh();

    this.isFullScreen = document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement;
    this.$nextTick(() => {
      document.addEventListener('fullscreenchange', () => {
        this.isFullScreen = document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement;
      });
      document.addEventListener('webkitfullscreenchange', () => {
        this.isFullScreen = document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement;
      });
      document.addEventListener('mozfullscreenchange', () => {
        this.isFullScreen = document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement;
      });
      document.addEventListener('MSFullscreenChange', () => {
        this.isFullScreen =
          document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement || document.msFullscreenElement;
      });
    });
  },
  methods: {
    cleanUp() {
      $('#group-member').empty();
      $('.detail-popover').remove();
    },
    // options
    onRefresh() {
      this.cleanUp();
      // init Chart
      this.chart = new Chart(CONTAINER_SELECTOR);
      // getData
      this.isLoading = true;
      this.noData = false;
      this.maxLevel = '';
      this.shortStatus = '';
      this.minPercent = '';
      dataLoader
        .getMemberGraph({
          groupId: this.groupId,
        })
        .then((res) => {
          this.isInit = true;
          this.noData = false;
          this.limitExceed = res.totalLength > this.limitLength;
          if (res.totalLength > this.limitLength) {
            this.maxLevel = 1;
            this.chart.setMaxLevel(this.maxLevel);
          }
          this.rootNode = res.rootNode;
          const acArr = ['h', 't', 'o', 'z'];
          if (res.mainCKeyNo && acArr.includes(res.mainCKeyNo[0])) {
            this.actText = '疑似控制实体';
          } else if (res.mainCKeyNo && !acArr.includes(res.mainCKeyNo[0])) {
            this.actText = '疑似实际控制人';
          } else {
            this.actText = '';
          }
          _.forEach(res.data, (data) => {
            this.totalNodes = this.totalNodes.concat(data.nodes);
          });
          this.data = res;
          this.chart.init({ data: res });
          this.chart.domUpdate();
        })
        .catch(() => {
          this.isLoading = false;
          this.noData = true;
        });
      this.chart.on('onScale', ({ newTransform }) => {
        this.transform = newTransform;
        $('.detail-popover').remove();
      });
      this.chart.on('showLoading', () => {
        this.isLoading = true;
      });
      this.chart.on('exceedLimit', () => {
        this.isLoading = false;
        this.$toasted.error('图谱节点过多，暂无法显示，您可直接搜索想查找的企业');
        this.showFilter = false;
        this.maxLevel = this.oldMaxLevel;
      });
      this.chart.on('onNodeDrag', () => {
        $('.detail-popover').remove();
      });
      this.chart.on('resetHighlight', () => {
        this.minPercent = '';
        this.shortStatus = '';
        this.chart.resetHighLight();
      });
      this.chart.on('cancelLoading', () => {
        this.isLoading = false;
      });
      this.chart.on('clickSvg', () => {
        if (this.showSearch || this.showFilter) {
          this.showFilter = false;
          this.showSearch = false;
        } else {
          this.minPercent = '';
          this.shortStatus = '';
          this.chart.resetHighLight();
        }
      });
      this.chart.on('onDataNodeMouseover', ({ eid, data, position, size }) => {
        data.delayTimer = setTimeout(() => {
          const $instance = $(`#detail-popover-${data.id}`);
          if ($instance.length > 0) {
            $instance.stop(true).fadeIn(0);
            return;
          }
          popoverHelper.showPopover({
            component: popover,
            data: {
              id: data.properties.keyNo,
              groupId: this.groupId,
              rootName: this.rootNode.properties.name,
            },
            targetPosition: {
              left: this.isFullScreen ? position.left : position.left - (180 + 60),
              top: this.isFullScreen ? position.top : position.top - (42 + 42 + 40),
            },
            container: $(`#${CONTAINER_SELECTOR}`),
            identity: `detail-popover-${data.id}`,
            targetSize: { width: size.width * this.transform.k, height: size.height * this.transform.k },
          });
        }, 400);
      });
      this.chart.on('onDataNodeMouseout', ({ data }) => {
        if (data.delayTimer) {
          clearTimeout(data.delayTimer);
          delete data.delayTimer;
        }
        $(`#detail-popover-${data.id}`).fadeOut(400, function () {
          $(this).remove();
        });
      });
    },
    handleFilterLevel(obj) {
      if (obj.maxLevel !== this.maxLevel) {
        this.oldMaxLevel = this.maxLevel;
        this.maxLevel = obj.maxLevel;
        this.isLoading = true;
        setTimeout(() => {
          this.chart.setMaxLevel(this.maxLevel);
          this.chart.formatChartData(this.data);
          if (this.filterShortStatus || this.filterPercent) {
            this.getHighLightIds().then((highLightIds) => {
              this.chart.highLightNodes2(highLightIds, false);
            });
          }
          this.chart.domUpdate();
        });
      }
    },
    getHighLightIds() {
      return new Promise((resolve) => {
        this.isLoading = true;
        graph
          .filterMember({
            groupId: this.groupId,
            maxLevel: '',
            minPercent: this.minPercent,
            shortStatus: this.shortStatus,
          })
          .then((res) => {
            const highLightIds = res.Result || [];
            resolve(highLightIds);
          })
          .catch(() => {});
      });
    },
    handleFilter(obj) {
      this.shortStatus = obj.shortStatus;
      this.filterShortStatus = !!obj.shortStatus;
      this.minPercent = obj.minPercent;
      this.filterPercent = !!obj.minPercent;
      this.getHighLightIds().then((highLightIds) => {
        this.chart.highLightNodes(highLightIds, this.maxLevel);
      });
    },
    handleChooseMember(item) {
      this.showSearch = false;
      if (item.id === this.rootNode.id) {
        const lighLightNodes = this.chart.getRelatedNodes(item.id);
        this.minPercent = '';
        this.shortStatus = '';
        this.chart.highLightNodes2(lighLightNodes);
        return;
      }
      this.showPath = true;
      this.pathChart = new Chart(PATH_SELECTOR);
      this.isLoading = true;
      this.windowHeight = $(window).height();
      this.windowWidth = $(window).width();
      dataLoader
        .getPaths({
          groupId: this.groupId,
          memberId: item.properties.keyNo,
        })
        .then((res) => {
          this.pathNoData = false;
          this.pathData = res;
          this.pathChart.init({ data: res });
          this.pathChart.domUpdate();
          let links = [];
          _.forEach(res.data, (v) => {
            links = links.concat(v.links);
          });
          if (!links.length) {
            this.$toasted.error(`${item.properties.name}与集团主公司无股权投资关系`);
          }
        })
        .finally(() => {
          const height = $(window).height();
        })
        .catch(() => {
          this.isLoading = false;
          this.pathNoData = true;
        });
      this.pathChart.on('onScale', () => {
        $('.detail-popover').remove();
      });
      this.pathChart.on('showLoading', () => {
        this.isLoading = true;
      });
      this.pathChart.on('cancelLoading', () => {
        this.isLoading = false;
      });
      this.pathChart.on('exceedLimit', () => {
        this.isLoading = false;
        this.$toasted.error('图谱节点过多，暂无法显示，请重置后重新查询');
      });
      this.pathChart.on('onNodeDrag', () => {
        $('.detail-popover').remove();
      });
      this.pathChart.on('clickSvg', () => {
        this.pathChart.resetHighLight();
      });
      this.pathChart.on('onDataNodeMouseover', ({ eid, data, position, size }) => {
        data.delayTimer = setTimeout(() => {
          const $instance = $(`#detail-popover-${data.id}`);
          if ($instance.length > 0) {
            $instance.stop(true).fadeIn(0);
            return;
          }
          popoverHelper.showPopover({
            component: popover,
            data: {
              id: data.properties.keyNo,
              groupId: this.groupId,
              rootName: this.rootNode.properties.name,
            },
            targetPosition: {
              left: this.isFullScreen ? position.left : position.left - (180 + 60),
              top: this.isFullScreen ? position.top : position.top - (42 + 42 + 40),
            },
            container: $(`#${PATH_SELECTOR}`),
            identity: `detail-popover-${data.id}`,
            targetSize: { width: size.width * this.transform.k, height: size.height * this.transform.k },
          });
        }, 400);
      });
      this.pathChart.on('onDataNodeMouseout', ({ data }) => {
        if (data.delayTimer) {
          clearTimeout(data.delayTimer);
          delete data.delayTimer;
        }
        $(`#detail-popover-${data.id}`).fadeOut(400, function () {
          $(this).remove();
        });
      });
    },
    onSearch() {
      this.showFilter = false;
      this.showSearch = !this.showSearch;
    },
    onZoomIn() {
      this.chart.zoomInOrOut(true);
    },
    onZoomOut() {
      this.chart.zoomInOrOut(false);
    },
    onFullScreen() {
      const element = $('.group-container')[0];
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
    },
    onExitFullScreen() {
      if (this.isFullScreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      }
    },
    onSave() {
      // 保存过程中，不能再保存
      if (this.isSaving) {
        return;
      }

      this.onExitFullScreen();
      this.isSaving = true;

      setTimeout(() => {
        const $svg = $(`#${CONTAINER_SELECTOR}>svg`);
        const $shadow = $('<div></div>')
          .css({
            width: 0,
            height: 0,
            position: 'fixed',
            top: -1000000,
            let: -1000000,
            zIndex: -1,
            overflow: 'hidden',
          })
          .appendTo('body');

        const $svgClone = $svg.clone().appendTo($shadow);
        const $g = $svgClone.find('.rootG');
        $g.find('.handle-circle').remove();
        const newK = 1;
        const center = { x: $svg.width() / 2, y: $svg.height() / 2 };
        const translate0 = { x: center.x, y: center.y };
        const l = { x: translate0.x * newK, y: translate0.y * newK };
        const newX = center.x - l.x;
        const newY = center.y - l.y;
        $g.attr('transform', `translate(${newX},${newY}) scale(${newK})`);
        const box = $g[0].getBBox();
        const contentSize = { width: box.width, height: box.height };
        const padding = { left: 90, right: 90, top: 30, bottom: 60 };

        const rect = {
          top: box.y - padding.top,
          left: box.x - padding.left,
          width: contentSize.width + padding.left + padding.right,
          height: contentSize.height + padding.top + padding.bottom,
        };

        // add water markter
        const n = Math.ceil(rect.width / 240.0);
        const m = Math.ceil(rect.height / 200.0);
        for (let i = 0; i < n; i++) {
          for (let j = 0; j < m; j++) {
            const x = rect.left + 240 * i;
            const y = rect.top + 200 * j;
            $svgClone.prepend(utils.getWaterMarkLogo().clone().attr('transform', `translate(${x}, ${y})`));
          }
        }

        // add white bg
        const $masker = $('<rect></rect>').attr({
          x: rect.left,
          y: rect.top,
          width: rect.width,
          height: rect.height,
          fill: 'rgba(255, 255, 255, 1)',
        });
        $svgClone.prepend($masker);

        // add bottom text
        const footerSize = measure.getTotal(
          '以上数据是企查查大数据分析引擎基于公开信息挖掘的成果，仅供参考。该成果不构成任何明示或暗示的观点或保证。',
          12
        );
        // 避免宽度过小 无法容纳免责文字 文字换行
        const footerArr = [];
        if (footerSize.width > rect.width) {
          footerArr.push('以上数据是企查查大数据分析引擎基于公开信息挖掘的成果，仅供参考');
          footerArr.push('该成果不构成任何明示或暗示的观点或保证');
        } else {
          footerArr.push('以上数据是企查查大数据分析引擎基于公开信息挖掘的成果，仅供参考。该成果不构成任何明示或暗示的观点或保证。');
        }
        _.forEach(footerArr, (str, index) => {
          const $textFooter = $('<text></text>')
            .attr({
              x: padding.left + contentSize.width / 2 + rect.left,
              y: rect.top + padding.top + contentSize.height + (index > 0 ? 50 : 30),
              'text-anchor': 'middle',
              fill: '#999',
              'font-size': '12px',
            })
            .text(str);
          $svgClone.append($textFooter);
        });
        const fileName = `${this.name ? `${this.name}-` : ''}集团成员图谱-${moment().format('YYYY-MM-DD')}.png`;

        const option = {
          ...rect,
          scale: 2,
        };

        saveSvg
          .saveAsImage($svgClone[0], fileName, option)
          .then(() => {
            $shadow.remove();
            this.isSaving = false;
          })
          .catch(() => {
            $shadow.remove();
            this.isSaving = false;
          });
      }, 50);
    },
    onMouseenter() {
      if (this.isInit) {
        this.chart.delayZoom();
      }
    },
  },
};
