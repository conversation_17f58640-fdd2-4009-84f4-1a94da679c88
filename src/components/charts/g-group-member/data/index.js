import { graph } from '@/shared/services';

// import mockData from './test'
// import mockData from './qcc-test'
import formatter from './formatter';
// let memberWorker = null
// if (__BROWSER__) {
//   // 使用如下域名的地址文件 不使用cdn
//   memberWorker = new Worker('/web/chart.worker.js?t=2')
// }
const getMemberGraph = (params) => {
  return new Promise((resolve, reject) => {
    graph
      .getMemberGraph(params)
      .then((res) => {
        const data = res.Result;
        // memberWorker.postMessage({
        //   event: 'init',
        //   payload: { ...params, data: data.results }
        // })
        // memberWorker.onmessage = e => {
        //   if (e.data.event === 'finishInit') {
        //     resolve(e.data.payload)
        //   }
        // }
        formatter.init({ ...params, data: data.results });
        const detail = formatter.format();
        resolve(detail);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const getPaths = (params) => {
  return new Promise((resolve, reject) => {
    graph
      .findPaths(params)
      .then((res) => {
        const data = res.Result;
        // memberWorker.postMessage({
        //   event: 'init',
        //   payload: { ...params, data: data.results }
        // })
        // memberWorker.onmessage = e => {
        //   if (e.data.event === 'finishInit') {
        //     resolve(e.data.payload)
        //   }
        // }
        formatter.init({ ...params, data: data.results });
        const detail = formatter.format();
        resolve(detail);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export default {
  getMemberGraph,
  getPaths,
};
