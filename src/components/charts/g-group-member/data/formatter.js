/* eslint-disable no-loop-func */
/* eslint-disable no-param-reassign */
/* eslint-disable class-methods-use-this */
/* eslint-disable @typescript-eslint/no-empty-function */
import _ from 'lodash';

import settings from '../settings';
import measure from '../../utils/measure';

class GroupMember {
  constructor() {
    this.reset();
  }

  init({ groupId, minPercent, minLevel, shortStatus, data }) {
    this.data = data;
    this.groupId = groupId;
    this.minLevel = minLevel;
    this.minPercent = minPercent;
    this.shortStatus = shortStatus;
    this._rootNode = null;
    this.relatedHasMainC = false;
    this.mainCKeyNo = '';
  }

  reset() {}

  format() {
    // 存在多棵树的情况 循环data数据进行处理
    const dataArr = [];
    let totalLength = 0;
    _.forEach(this.data[0].data, (data) => {
      totalLength += data.graph.nodes.length;
    });
    _.forEach(this.data[0].data, (data, index) => {
      // 当节点超过限制节点，仅显示50个左右，其余全部收起
      const res = this.formatGraph(data, index, totalLength);
      dataArr.push(res);
    });
    return {
      data: dataArr,
      totalLength,
      rootNode: this._rootNode,
      hasMainC: this.relatedHasMainC,
      mainCKeyNo: this.mainCKeyNo,
    };
  }

  formatGraph(data, index, totalLength) {
    const graph = {
      // total
      nodes: [],
      links: [],
      tree: {},
      // 用于绘图
      filterNodesIds: [],
    };
    const tempGraph = data.graph;
    const tempTree = data.tree;
    // formatNodes
    _.forEach(tempGraph.nodes, (item) => {
      const o = _.cloneDeep(item);
      o.nodeId = item.id;
      o.status = null;
      o.layout = {};
      o.layout.level = null;
      o.properties.isShow = true;
      o.layout.singleLinkChildren = [];
      const nameList = [];
      const name = item.properties.inNodeText || item.properties.name;
      let tempName = '';
      let tempSize = {};
      let nameHeight = 0;
      if (name) {
        tempName = name.slice(0, 4);
        tempSize = {
          height: settings.textSize + 1,
        };
        nameList.push({
          text: tempName,
          height: settings.textSize,
          // width: tempSize.width,
          props: {
            fill: settings.textColor,
          },
        });
        nameHeight += tempSize.height;
        if (name.length > 4) {
          tempName = name.slice(4, 9);
          tempSize = {
            height: settings.textSize + 1,
          };
          nameList.push({
            text: tempName,
            height: 16,
            // width: tempSize.width,
            props: {
              fill: settings.textColor,
            },
          });
          nameHeight += tempSize.height;
        }
        if (name.length > 9) {
          tempName = name.length > 13 ? `${name.slice(9, 12)}...` : name.slice(9, 13);
          tempSize = {
            height: settings.textSize + 1,
          };
          nameList.push({
            text: tempName,
            height: tempSize.height,
            // width: tempSize.width,
            props: {
              fill: settings.textColor,
            },
          });
          nameHeight += tempSize.height;
        }
      }
      o.nameHeight = nameHeight;
      o.nameList = nameList;
      if (+o.properties.isMain) {
        this._rootNode = o;
        if (index === 0) {
          // 避免出现与第一棵树无关联情况 导致第一层节点过多
          this.relatedHasMainC = true;
        }
      }
      if (o.properties.isMainC) {
        this.mainCKeyNo = o.properties.keyNo;
      }
      if (
        o.properties.keyNo &&
        o.properties.keyNo[0] === 'g' &&
        (name.indexOf('国资委') > -1 || name.indexOf('国有资产监督管理委员会') > -1)
      ) {
        o.properties.isGZW = true;
      }
      if (
        o.properties.keyNo &&
        (o.properties.keyNo[0] === 'z' || o.properties.keyNo[0] === 'o' || o.properties.keyNo[0] === 'h' || o.properties.keyNo[0] === 't')
      ) {
        o.properties.isJW = true;
      }
      graph.nodes.push(o);
    });

    // formatLinks
    _.forEach(tempGraph.relationships, (item) => {
      const o = _.cloneDeep(item);
      o.status = null;
      o.source = item.startNode;
      o.target = item.endNode;
      // o.sourceNode = this.getGraphNode(item.startNode, graph.nodes)
      // o.targetNode = this.getGraphNode(item.endNode, graph.nodes)
      o.linkId = o.id;
      const inLineText = o.properties.inLineText || '';
      o.inLineTextSize = measure.getTotal(inLineText, 10);
      // o.inLineTextSize = this.getLinkSize(inLineText)
      graph.links.push(o);
    });

    // formatTree对树的children进行调整，并根据限制将节点筛选组成filterNodes用于绘制图谱
    this.foldNodes(tempTree, graph.nodes, graph.filterNodesIds, totalLength);

    graph.tree = tempTree;
    // 出现两个节点间多条关系

    // this.setLevel(graph.links)
    this.setCategoryColor(graph.nodes, graph.links);
    return graph;
  }

  /* @__NODEFENCE__ */
  foldNodes(node, graphNodes, filterNodesIds, totalLength) {
    // 先把c=>children及其他数据加上重组
    const currentDetail = _.find(graphNodes, (n) => n.id === node.i);
    _.assignIn(node, currentDetail);
    // 超过400个默认显示1层
    if (node.l === 0) {
      filterNodesIds.push(node.i);
      node.children = node.c || null;
      _.forEach(node.children, (child) => {
        filterNodesIds.push(child.i);
      });
    } else {
      node.children = node.c || null;
      _.forEach(node.children, (child) => {
        filterNodesIds.push(child.i);
      });
    }
    node.layout.level = node.l;
    if (node.c && node.c.length > 0) {
      _.forEach(node.c, (d) => {
        this.foldNodes(d, graphNodes, filterNodesIds, totalLength);
      });
    }
  }

  // 设置节点层级
  setLevel(svgLinks) {
    const getNextNodes = (nodeId, links, parentLevel) => {
      let tempNodes = [];
      _.forEach(links, (item) => {
        if (nodeId === item.startNode && !item.targetNode.layout.level) {
          item.targetNode.layout.level = parentLevel;
          tempNodes.push(item.targetNode);
        } else if (nodeId === item.endNode && !item.sourceNode.layout.level) {
          item.sourceNode.layout.level = parentLevel;
          tempNodes.push(item.sourceNode);
        }
      });
      tempNodes = _.uniqBy(tempNodes, 'id');
      return tempNodes;
    };
    let level = 0;
    let nodes = [];
    nodes.push(this._rootNode);
    while (nodes.length) {
      let nextNodes = [];
      _.forEach(nodes, (item) => {
        if (item && item.layout) {
          item.layout.level = level;
          nextNodes = nextNodes.concat(getNextNodes(item.id, svgLinks, level));
        }
      });
      level++;
      nodes = nextNodes;
    }
  }

  // 数据处理：设置节点角色
  setCategoryColor(nodes, links) {
    // 初始化两点间连线信息
    _.forEach(links, (link) => {
      const sameLink = {
        length: 0,
        currentIndex: 0,
        isSetedSameLink: false,
      };
      link.sameLink = sameLink;
    });

    // 链接相同两个点的线(两点间存在多个关系)
    _.forEach(links, (baseLink) => {
      if (!baseLink.sameLink.isSetedSameLink) {
        baseLink.sameLink.isSetedSameLink = true;
        const nodeId1 = baseLink.startNode;
        const nodeId2 = baseLink.endNode;

        const sameLinks = [];
        sameLinks.push(baseLink);

        _.forEach(links, (otherLink) => {
          if (baseLink.id !== otherLink.id && !otherLink.sameLink.isSetedSameLink) {
            if (
              (otherLink.startNode === nodeId1 && otherLink.endNode === nodeId2) ||
              (otherLink.startNode === nodeId2 && otherLink.endNode === nodeId1)
            ) {
              sameLinks.push(otherLink);
              otherLink.sameLink.isSetedSameLink = true;
            }
          }
        });

        _.forEach(sameLinks, (oneLink, index) => {
          oneLink.sameLink.length = sameLinks.length; // 两点间连线数量
          oneLink.sameLink.currentIndex = index;
        });
      }
    });
  }

  getLinkSize(str) {
    let width = 0;
    _.forEach(str, (s) => {
      // 中文
      if (s >= 0x4e00 && s <= 0x29fa5) {
        width += 17.85;
      } else if (/^\d+$/.test(s)) {
        // 数字
        width += 11;
      } else if (/^[a-z]+$/.test(s)) {
        width += 16;
      } else if (/^[A-Z]+$/.test(s)) {
        width += 17;
      } else if (/\./.test(s)) {
        width += 10;
      } else if (/%+/.test(s)) {
        width += 15;
      }
      // 英文
      // 百分号
      // 英文句号
    });
    return {
      width,
      height: 14,
    };
  }
}

export default new GroupMember();
