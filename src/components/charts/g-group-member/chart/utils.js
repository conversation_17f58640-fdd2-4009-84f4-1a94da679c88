import _ from 'lodash';

const SVG_NS = 'http://www.w3.org/2000/svg';

const createVerticalName = (data, fontSize) => {
  const ele = document.createElementNS(SVG_NS, 'text');
  ele.setAttribute('stroke-width', 0);
  ele.setAttribute('style', 'font-family:"微软雅黑", Microsoft YaHei", Arial;');
  let prevHeight = 0;
  const nameList = data;
  for (let i = 0; i < nameList.length; ++i) {
    const o = nameList[i];
    const tspan = document.createElementNS(SVG_NS, 'tspan');
    tspan.textContent = o.text;
    _.chain(o.props)
      .keys()
      .each((key) => {
        tspan.setAttribute(key, o.props[key]);
      })
      .value();
    tspan.setAttribute('style', 'font-family:"微软雅黑", Microsoft YaHei", Arial;');
    tspan.setAttribute('dy', prevHeight + o.height / 2);
    tspan.setAttribute('x', 0);
    tspan.setAttribute('y', 0);
    prevHeight += o.height;
    ele.appendChild(tspan);
  }
  ele.setAttribute('font-size', fontSize);
  ele.setAttribute('text-anchor', 'middle');
  ele.setAttribute('dominant-baseline', 'middle');
  return ele;
};

export default {
  createVerticalName,
};
