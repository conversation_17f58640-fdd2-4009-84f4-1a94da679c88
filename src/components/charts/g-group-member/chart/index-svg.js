/* eslint-disable max-len */
/* eslint-disable @typescript-eslint/no-this-alias */
/* eslint-disable func-names */
/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-shadow */
import { EventEmitter } from 'eventemitter3';
import _ from 'lodash';
import * as d3 from 'd3';

import settings from '../settings';
import { deviceInfo } from '../../utils/device-info';
import utils from './utils';

// let simulation
let strength = -300;
let distanceMax = 400;
let distanceMin = 80;
// let theta = 0
let distance = 100;
const colideRadius = 70;
const defaultOffset = 12;

export default class Chart extends EventEmitter {
  constructor(selector) {
    super();
    this.selector = `#${selector}`;
    this.$selector = $(`#${selector}`);
    this.maxLevel = '';
    this.limitLength = 400;
    this.mainNodeX = 0;
    this.mainNodeY = 0;
    this.clickFalg = false;
  }

  formatChartData(data) {
    this.totalNodes = [];
    this.totalLinks = [];
    const chartData = {
      nodes: [],
      links: [],
      formatLinks: [],
    };
    let ac = {};
    // 为了保证无关联节点跑的特别远的情况
    const noRelationNodes = [];
    const sortedData = _.sortBy(data.data, (d) => {
      return -d.nodes.length;
    });
    _.forEach(sortedData, (d) => {
      // 兼容实际控制人的节点不存在于第一棵树中且没有关联的情况
      if (d.nodes.length === 1 && !d.links.length && d.nodes[0].properties.isMainC) {
        ac = d.nodes[0];
      }
      if (+this.maxLevel > 0 && !_.find(d.nodes, (node) => node.properties.isMain)) {
        return;
      }
      // 能有团的情况
      if (d.nodes.length > 1) {
        // this.totalNodes = this.totalNodes.concat(d.nodes)
        this.totalLinks = this.totalLinks.concat(d.links);
        const limitLevel = this.maxLevel;
        // let limitLevel = !this.data.hasMainC ? this.maxLevel : +this.maxLevel + 1
        const rootNodes = d3.hierarchy(d.tree);
        // 用于计算位置的links及nodes
        _.forEach(rootNodes.links(), (link) => {
          if (link.source && link.target) {
            if (+this.maxLevel > 0) {
              if (+link.source.depth <= limitLevel && +link.target.depth <= limitLevel) {
                chartData.formatLinks.push({
                  source: _.cloneDeep(link.source.data.id),
                  target: _.cloneDeep(link.target.data.id),
                });
              }
            } else {
              chartData.formatLinks.push({
                source: _.cloneDeep(link.source.data.id),
                target: _.cloneDeep(link.target.data.id),
              });
            }
          }
        });
        _.forEach(rootNodes.descendants(), (node) => {
          if (+this.maxLevel > 0) {
            if (+node.depth <= limitLevel) {
              chartData.nodes.push(node.data);
            }
          } else {
            chartData.nodes.push(node.data);
          }
        });
        chartData.nodes = _.uniqBy(chartData.nodes, 'id');
        // 用于计算所有的links
        const exitNodeIds = _.map(chartData.nodes, (d) => d.id);
        const filterLinks = _.filter(this.totalLinks, (link) => exitNodeIds.includes(link.startNode) && exitNodeIds.includes(link.endNode));
        chartData.links = chartData.links.concat(filterLinks);
        // 筛选在当前节点中的links
        _.forEach(filterLinks, (link) => {
          link.sourceNode = _.find(chartData.nodes, (node) => node.id === link.startNode);
          link.targetNode = _.find(chartData.nodes, (node) => node.id === link.endNode);
        });
      } else {
        if (!d.nodes[0].properties.isMainC) {
          noRelationNodes.push(d.nodes[0]);
        }
        // 主公司无关联情况
        if (d.nodes[0].properties.isMain) {
          chartData.nodes.push(d.nodes[0]);
        }
      }
    });
    if (!_.isEmpty(ac)) {
      chartData.nodes.push(ac);
      chartData.formatLinks.push({
        source: ac.id,
        target: chartData.nodes[0].id,
      });
    }
    // 非筛选层级情况下 筛入单个无关联且不是实际控制人的节点
    if (!+this.maxLevel) {
      if (chartData.nodes.length > 0) {
        _.forEach(noRelationNodes, (node) => {
          if (!node.properties.isMainC) {
            chartData.formatLinks.push({
              source: node,
              target: chartData.nodes[0],
            });
          }
        });
      }
      chartData.nodes = chartData.nodes.concat(noRelationNodes);
    }
    this.chartData = chartData;
    // 筛选后chartData改变 防止点击操作修改的是新的chartData
    if (+this.maxLevel > 0 && _.isEmpty(this.oldChartData)) {
      this.oldChartData = _.cloneDeep(this.chartData);
    }
  }

  init({ data }) {
    // let self = this
    this.data = data;
    this.totalLength = data.totalLength;
    this.formatChartData(data);
    // remove first
    this.$selector.find('svg').remove();
    const width = this.$selector.width();
    const height = this.$selector.height();
    this.size = {
      width,
      height,
    };
    this.svg = d3
      .select(this.selector)
      .append('svg')
      .attr('width', width)
      .attr('height', height)
      .attr('cursor', 'move')
      .on('click', () => {
        if (d3.event.target.nodeName === 'svg') {
          this.emit('clickSvg');
          // if () {
          //   self.emit('closeFilter')
          // }
          // self.emit('resetHighlight')
          // self.resetHighLight()
        }
      });

    // prepare root g
    this.root = this.svg.append('g').attr('class', 'rootG');
    // this.transform = {
    //   k: 1,
    //   x: 0,
    //   y: 0
    // }

    this.rootBoard = this.root.append('g').attr('class', 'root-board');
    // .attr('transform', `translate(${this.size.width / 2}, ${this.size.height / 2})`)
    this.rootBoard
      .append('defs')
      .append('marker')
      .attr('id', 'arrowIn')
      .attr('viewBox', '0 0 6 6')
      .attr('markerUnits', 'strokeWidth')
      .attr('refX', 26)
      .attr('refY', 3)
      .attr('markerWidth', 18)
      .attr('markerHeight', 18)
      .attr('orient', 'auto')
      .append('path')
      .attr('d', 'M1,1 L5,3 L1,5 L1,1')
      .attr('fill', settings.linkColor.active);

    this.rootBoard
      .append('defs')
      .append('marker')
      .attr('id', 'arrowInDisable')
      .attr('viewBox', '0 0 6 6')
      .attr('markerUnits', 'strokeWidth')
      .attr('refX', 26)
      .attr('refY', 3)
      .attr('markerWidth', 18)
      .attr('markerHeight', 18)
      .attr('orient', 'auto')
      .append('path')
      .attr('d', 'M1,1 L5,3 L1,5 L1,1')
      .attr('fill', settings.linkColor.disable2)
      .attr('stroke-width', 1);

    this.rootBoard
      .append('defs')
      .append('marker')
      .attr('id', 'arrowIn1')
      .attr('viewBox', '0 0 12 12')
      .attr('markerUnits', 'strokeWidth')
      .attr('refX', 26)
      .attr('refY', 3)
      .attr('markerWidth', 18)
      .attr('markerHeight', 18)
      .attr('orient', 'auto')
      .append('path')
      .attr('d', 'M1,1 L5,3 L1,5 L1,1')
      .attr('fill', settings.linkColor.active);

    this.linkBoard = this.rootBoard.append('g');

    this.nodeBoard = this.rootBoard.append('g');

    let zoom = null;
    zoom = d3
      .zoom()
      .scaleExtent([0.1, 2])
      .on('zoom', () => {
        const transform = d3.zoomTransform(this.svg.node());
        this.root.attr('transform', transform);
        this.emit('onScale', { newTransform: { ...transform } });
        // this.transform = { ...transform }
      });

    this.zoom = zoom;

    this.scaleStep = 0.1;

    // enable zoom
    this.svg
      .call(zoom)
      // disable dbclick for zooming
      .on('dblclick.zoom', null);
  }

  getD3Position2() {
    // 根据节点数量调节
    const num = this.chartData.nodes.length;
    if (num < 10) {
      strength = -500;
      distanceMax = 400;
      distance = 200;
    } else if (num < 30) {
      strength = -100;
      distanceMax = 400;
      distance = 200;
    } else if (num < 50) {
      strength = -400;
      distanceMax = 400;
      distance = 200;
    } else if (num > 50 && num < 100) {
      strength = -100;
      distanceMax = 350;
      distance = 200;
    } else if (num > 100 && num < 150) {
      strength = -400;
      distanceMax = 800;
      distanceMin = 200;
      distance = 200;
    } else if (num > 150 && num < 200) {
      strength = -500;
      distanceMax = 500;
      distanceMin = 200;
      distance = 200;
    } else if (num > 200) {
      strength = -600;
      distanceMax = 800;
      distance = 200;
    }
    this.simulation = d3
      .forceSimulation(this.chartData.nodes)
      .force('charge', d3.forceManyBody().strength(strength).distanceMin(distanceMin).distanceMax(distanceMax))
      .force('center', d3.forceCenter(this.size.width / 2, this.size.height / 2))
      .force(
        'collide',
        d3.forceCollide().radius(function () {
          return colideRadius / 2;
        })
      )
      .force(
        'link',
        d3
          .forceLink(this.chartData.formatLinks)
          .id((d) => d.id)
          .distance(distance)
      );
  }

  domUpdate() {
    this.chartData.nodes = _.uniqBy(this.chartData.nodes, 'id');
    if (this.chartData.nodes.length > this.limitLength) {
      this.emit('exceedLimit');
      this.chartData = this.oldChartData;
      return;
    }
    // 清空rootBoard
    $(`${this.selector} .root-board .node`).remove();
    $(`${this.selector} .root-board .link`).remove();
    this.oldChartData = this.chartData;
    this.getD3Position2();
    // let delayTime = 10000
    // let nodeNum = this.chartData.nodes.length
    // if (nodeNum <= 10) {
    //   delayTime = 200
    // } else if (nodeNum <= 50) {
    //   delayTime = 800
    // } else if (nodeNum <= 100) {
    //   delayTime = 3000
    // } else if (nodeNum <= 200) {
    //   delayTime = 2000
    // } else if (nodeNum <= 300) {
    //   delayTime = 3500
    // } else if (nodeNum <= 400) {
    //   delayTime = 4500
    // } else if (nodeNum <= 500) {
    //   delayTime = 5500
    // } else if (nodeNum <= 600) {
    //   delayTime = 6500
    // } else if (nodeNum <= 700) {
    //   delayTime = 7500
    // }
    // setTimeout(() => {
    //   // this.drawChart(this.transformGraph())
    //   this.simulation.stop()
    // }, delayTime)
    for (let i = 0, n = Math.ceil(Math.log(this.simulation.alphaMin()) / Math.log(1 - this.simulation.alphaDecay())); i < n; ++i) {
      this.simulation.tick();
    }
    this.draw(true);
  }

  draw(needPosition = false) {
    this.emit('cancelLoading');
    this.drawLinks();
    this.drawNodes();
    // 将主公司移动到屏幕正中心
    if (needPosition) {
      this.position();
    }
  }

  position() {
    if (!this.mainNodeX && !this.mainNodeY) {
      return;
    }
    const trans = d3.zoomTransform(this.svg.node());
    const center = { x: this.size.width / 2, y: this.size.height / 2 };
    const currentNode = { x: this.mainNodeX, y: this.mainNodeY };
    if (currentNode.y > 0) {
      trans.y = -(currentNode.y * trans.k) + center.y;
    } else {
      trans.y = -currentNode.y * trans.k + center.y;
    }
    if (currentNode.x > 0) {
      trans.x = -(currentNode.x * trans.k) + center.x;
    } else {
      trans.x = -currentNode.x * trans.k + center.x;
    }

    this.zoom.transform(this.root, trans);
  }

  setMaxLevel(level) {
    this.maxLevel = level;
  }

  drawLinks() {
    const d3Links = this.rootBoard.selectAll('.link').data(this.chartData.links, (d) => d.id);

    const linkEnter = d3Links.enter().append('g').attr('class', 'link');

    linkEnter.append('line').attr('stroke-width', 0.5).attr('fill', 'none').attr('shape-rendering', 'geometricPrecision');

    const linkText = linkEnter.append('g').attr('class', 'link-text');

    linkText
      .append('rect')
      .attr('class', 'link-rect')
      .attr('width', (d) => d.inLineTextSize.width)
      .attr('height', (d) => d.inLineTextSize.height)
      .attr('fill', 'rgba(255, 255, 255, 0.2)');

    linkText
      .append('text')
      .text((d) => d.properties.inLineText || '')
      .attr('font-size', 10)
      .attr('transform', 'translate(0, 12)');

    const updateLink = d3Links.merge(linkEnter);

    updateLink.filter((d) => d.sourceNode.properties.isShow && d.targetNode.properties.isShow);

    updateLink
      .selectAll('line')
      .attr('x1', (d) => {
        if (d.sameLink.length <= 1) {
          return d.sourceNode.x;
        }
        let theta = 0;
        if (d.targetNode.y > d.sourceNode.y) {
          theta = Math.atan2(d.targetNode.y - d.sourceNode.y, d.targetNode.x - d.sourceNode.x);
        } else {
          theta = Math.atan2(d.sourceNode.y - d.targetNode.y, d.sourceNode.x - d.targetNode.x);
        }
        let paramX = d.sameLink.currentIndex ? 1 : -1;
        // let paramY = d.sameLink.currentIndex ? 1 : -1
        if ((d.targetNode.y - d.sourceNode.y) / (d.targetNode.x - d.sourceNode.x)) {
          paramX *= -1;
        }
        // let offsetY = defaultOffset * Math.cos(theta) * paramY
        const offsetX = defaultOffset * Math.sin(theta) * paramX;
        return d.sourceNode.x + offsetX;
      })
      .attr('x2', (d) => {
        if (d.sameLink.length <= 1) {
          return d.targetNode.x;
        }
        let theta = 0;
        if (d.targetNode.y > d.sourceNode.y) {
          theta = Math.atan2(d.targetNode.y - d.sourceNode.y, d.targetNode.x - d.sourceNode.x);
        } else {
          theta = Math.atan2(d.sourceNode.y - d.targetNode.y, d.sourceNode.x - d.targetNode.x);
        }
        let paramX = d.sameLink.currentIndex ? 1 : -1;
        // let paramY = d.sameLink.currentIndex ? 1 : -1
        if ((d.targetNode.y - d.sourceNode.y) / (d.targetNode.x - d.sourceNode.x)) {
          paramX *= -1;
        }
        // let offsetY = defaultOffset * Math.cos(theta) * paramY
        const offsetX = defaultOffset * Math.sin(theta) * paramX;
        return d.targetNode.x + offsetX;
      })
      .attr('y1', (d) => {
        if (d.sameLink.length <= 1) {
          return d.sourceNode.y;
        }
        let theta = 0;
        if (d.targetNode.y > d.sourceNode.y) {
          theta = Math.atan2(d.targetNode.y - d.sourceNode.y, d.targetNode.x - d.sourceNode.x);
        } else {
          theta = Math.atan2(d.sourceNode.y - d.targetNode.y, d.sourceNode.x - d.targetNode.x);
        }
        const paramY = d.sameLink.currentIndex ? 1 : -1;
        const offsetY = defaultOffset * Math.cos(theta) * paramY;
        // let offsetX = defaultOffset * Math.sin(theta) * paramX
        return d.sourceNode.y + offsetY;
      })
      .attr('y2', (d) => {
        if (d.sameLink.length <= 1) {
          return d.targetNode.y;
        }
        let theta = 0;
        if (d.targetNode.y > d.sourceNode.y) {
          theta = Math.atan2(d.targetNode.y - d.sourceNode.y, d.targetNode.x - d.sourceNode.x);
        } else {
          theta = Math.atan2(d.sourceNode.y - d.targetNode.y, d.sourceNode.x - d.targetNode.x);
        }
        const paramY = d.sameLink.currentIndex ? 1 : -1;
        const offsetY = defaultOffset * Math.cos(theta) * paramY;
        // let offsetX = defaultOffset * Math.sin(theta) * paramX
        return d.targetNode.y + offsetY;
      })
      .attr('stroke', () => {
        return settings.linkColor.disable;
      })
      .attr('stroke-width', 0.5)
      .attr('marker-end', function (d) {
        return d.sourceNode.properties.isShow && d.targetNode.properties.isShow ? 'url(#arrowIn)' : 'url(#arrowInDisable)';
      });

    updateLink.selectAll('.link-text').attr('transform', (d) => {
      let offset = 0;
      let rotateOffset = 0;
      const theta = (180 * Math.atan((d.targetNode.y - d.sourceNode.y) / (d.targetNode.x - d.sourceNode.x))) / Math.PI;
      if (d.sameLink.length === 1) {
        offset = -16;
      } else if (d.sameLink.length % 2 === 0) {
        offset = d.sameLink.currentIndex % 2 === 0 ? -20 * 2 : 9;
        rotateOffset = theta < 0 ? -180 : 0;
      }
      const rectWidth = d.inLineTextSize.width;
      const degree = (180 * Math.atan((d.targetNode.y - d.sourceNode.y) / (d.targetNode.x - d.sourceNode.x))) / Math.PI + rotateOffset;
      const rotateX = (d.targetNode.x + d.sourceNode.x) / 2;
      const rotateY = (d.targetNode.y + d.sourceNode.y) / 2;
      const offsetX = (d.targetNode.x + d.sourceNode.x - rectWidth) / 2;
      const offsetY = (d.targetNode.y + d.sourceNode.y + offset) / 2;
      return `rotate(${degree} ${rotateX}, ${rotateY}) translate(${offsetX}, ${offsetY})`;
    });
    updateLink.selectAll('text').attr('fill', () => {
      return settings.linkColor.disable1;
    });

    d3Links.exit().remove();
  }

  drawNodes() {
    const self = this;
    const dragged = function (d) {
      d.x = d3.event.x;
      d.y = d3.event.y;
      self.emit('onNodeDrag');
      d3.selectAll('.node').raise();
      const linkTarget = d3
        .selectAll('.link')
        .filter((dd) => {
          return dd.source === d.id || dd.target === d.id;
        })
        .raise();

      linkTarget
        .select('line')
        .attr('x1', (d) => {
          if (d.sameLink.length <= 1) {
            return d.sourceNode.x;
          }
          let theta = 0;
          if (d.targetNode.y > d.sourceNode.y) {
            theta = Math.atan2(d.targetNode.y - d.sourceNode.y, d.targetNode.x - d.sourceNode.x);
          } else {
            theta = Math.atan2(d.sourceNode.y - d.targetNode.y, d.sourceNode.x - d.targetNode.x);
          }
          let paramX = d.sameLink.currentIndex ? 1 : -1;
          // let paramY = d.sameLink.currentIndex ? 1 : -1
          if ((d.targetNode.y - d.sourceNode.y) / (d.targetNode.x - d.sourceNode.x)) {
            paramX *= -1;
          }
          // let offsetY = defaultOffset * Math.cos(theta) * paramY
          const offsetX = defaultOffset * Math.sin(theta) * paramX;
          return d.sourceNode.x + offsetX;
        })
        .attr('x2', (d) => {
          if (d.sameLink.length <= 1) {
            return d.targetNode.x;
          }
          let theta = 0;
          if (d.targetNode.y > d.sourceNode.y) {
            theta = Math.atan2(d.targetNode.y - d.sourceNode.y, d.targetNode.x - d.sourceNode.x);
          } else {
            theta = Math.atan2(d.sourceNode.y - d.targetNode.y, d.sourceNode.x - d.targetNode.x);
          }
          let paramX = d.sameLink.currentIndex ? 1 : -1;
          // let paramY = d.sameLink.currentIndex ? 1 : -1
          if ((d.targetNode.y - d.sourceNode.y) / (d.targetNode.x - d.sourceNode.x)) {
            paramX *= -1;
          }
          // let offsetY = defaultOffset * Math.cos(theta) * paramY
          const offsetX = defaultOffset * Math.sin(theta) * paramX;
          return d.targetNode.x + offsetX;
        })
        .attr('y1', (d) => {
          if (d.sameLink.length <= 1) {
            return d.sourceNode.y;
          }
          let theta = 0;
          if (d.targetNode.y > d.sourceNode.y) {
            theta = Math.atan2(d.targetNode.y - d.sourceNode.y, d.targetNode.x - d.sourceNode.x);
          } else {
            theta = Math.atan2(d.sourceNode.y - d.targetNode.y, d.sourceNode.x - d.targetNode.x);
          }
          const paramY = d.sameLink.currentIndex ? 1 : -1;
          const offsetY = defaultOffset * Math.cos(theta) * paramY;
          // let offsetX = defaultOffset * Math.sin(theta) * paramX
          return d.sourceNode.y + offsetY;
        })
        .attr('y2', (d) => {
          if (d.sameLink.length <= 1) {
            return d.targetNode.y;
          }
          let theta = 0;
          if (d.targetNode.y > d.sourceNode.y) {
            theta = Math.atan2(d.targetNode.y - d.sourceNode.y, d.targetNode.x - d.sourceNode.x);
          } else {
            theta = Math.atan2(d.sourceNode.y - d.targetNode.y, d.sourceNode.x - d.targetNode.x);
          }
          const paramY = d.sameLink.currentIndex ? 1 : -1;
          const offsetY = defaultOffset * Math.cos(theta) * paramY;
          // let offsetX = defaultOffset * Math.sin(theta) * paramX
          return d.targetNode.y + offsetY;
        });

      linkTarget.select('.link-rect').attr('fill', 'rgba(255, 255, 255, 0.8)');

      linkTarget.select('.link-text').attr('transform', (d) => {
        let offset = 0;
        let rotateOffset = 0;
        const theta = (180 * Math.atan((d.targetNode.y - d.sourceNode.y) / (d.targetNode.x - d.sourceNode.x))) / Math.PI;
        if (d.sameLink.length === 1) {
          offset = -16;
        } else if (d.sameLink.length % 2 === 0) {
          offset = d.sameLink.currentIndex % 2 === 0 ? -20 * 2 : 9;
          rotateOffset = theta < 0 ? -180 : 0;
        }
        const rectWidth = d.inLineTextSize.width;
        const degree = (180 * Math.atan((d.targetNode.y - d.sourceNode.y) / (d.targetNode.x - d.sourceNode.x))) / Math.PI + rotateOffset;
        const rotateX = (d.targetNode.x + d.sourceNode.x) / 2;
        const rotateY = (d.targetNode.y + d.sourceNode.y) / 2;
        const offsetX = (d.targetNode.x + d.sourceNode.x - rectWidth) / 2;
        const offsetY = (d.targetNode.y + d.sourceNode.y + offset) / 2;
        return `rotate(${degree} ${rotateX}, ${rotateY}) translate(${offsetX}, ${offsetY})`;
      });

      const lighLightNodes = self.getRelatedNodes(d.id);
      d3.selectAll('.node')
        .filter((d) => lighLightNodes.includes(d.id))
        .raise();

      d3.select(this)
        .raise()
        .attr('transform', (d) => `translate(${d.x}, ${d.y})`);
    };

    const dragStart = function (d) {
      d.startX = d3.event.x;
      d.startY = d3.event.y;
    };

    const dragEnd = function (d) {
      // 是否为点击事件 根据startXY及xy的偏移值来决定
      const dStartX = d.startX - d.x;
      const dStartY = d.startY - d.y;
      if (dStartX * dStartX + dStartY * dStartY <= 1) {
        // 找到相关节点
        const lighLightNodes = self.getRelatedNodes(d.id);
        self.emit('resetHighlight');
        self.clickFalg = true;

        const highLightLinks = self.getRelatedLinks(d.id);
        const update = d3
          .selectAll('.link')
          .filter((d) => highLightLinks.includes(d.id))
          .raise();

        const disableupdate = d3.selectAll('.link').filter((d) => !highLightLinks.includes(d.id));

        update.selectAll('line').attr('stroke', settings.linkColor.active).attr('stroke-width', 1).attr('marker-end', 'url(#arrowIn1)');

        update.selectAll('text').attr('fill', settings.linkColor.active);

        update.selectAll('.link-rect').attr('fill', 'rgba(255, 255, 255, 0.8)');

        disableupdate.selectAll('line').attr('stroke', settings.linkColor.disable).attr('marker-end', 'url(#arrowInDisable)');

        disableupdate.selectAll('text').attr('fill', settings.linkColor.disable);

        disableupdate.selectAll('.link-rect').attr('fill', 'rgba(255, 255, 255, 0.2)');

        const clickUpdateNodes = d3
          .selectAll('.node')
          .filter((d) => lighLightNodes.includes(d.id))
          .raise();

        const disableUpdateNodes = d3.selectAll('.node').filter((d) => !lighLightNodes.includes(d.id));

        clickUpdateNodes
          .select('.node-circle')
          .attr('fill', (d) => {
            if (+d.properties.isMain) {
              return d.properties.isShow ? settings.nodeColor.main : settings.nodeColor.mainDisable;
            }
            if (d.labels[0] === 'Person') {
              return d.properties.isShow ? settings.nodeColor.person : settings.nodeColor.personDisable;
            }
            return d.properties.isShow ? settings.nodeColor.ent : settings.nodeColor.entDisable;
          })
          .attr('stroke', (d) => {
            if (+d.properties.isMain) {
              return d.properties.isShow ? settings.borderColor.main : settings.borderColor.mainDisable;
            }
            if (d.labels[0] === 'Person') {
              return d.properties.isShow ? settings.borderColor.person : settings.borderColor.personDisable;
            }
            return d.properties.isShow ? settings.borderColor.ent : settings.borderColor.entDisable;
          });

        clickUpdateNodes.select('.jw-board').attr('fill', '#3882FF');

        clickUpdateNodes.select('.start-board').attr('fill', '#F9AD14');

        clickUpdateNodes.select('.gzw-board').attr('fill', '#829E62');

        disableUpdateNodes
          .select('.node-circle')
          .attr('fill', (d) => {
            if (+d.properties.isMain) {
              return settings.nodeColor.mainDisable;
            }
            if (d.labels[0] === 'Person') {
              return settings.nodeColor.personDisable;
            }
            return settings.nodeColor.entDisable;
          })
          .attr('stroke', (d) => {
            if (+d.properties.isMain) {
              return settings.borderColor.mainDisable;
            }
            if (d.labels[0] === 'Person') {
              return settings.borderColor.personDisable;
            }
            return settings.borderColor.entDisable;
          });

        disableUpdateNodes.select('.jw-board').attr('fill', '#99bfff');

        disableUpdateNodes.select('.start-board').attr('fill', '#fcdc9c');

        disableUpdateNodes.select('.gzw-board').attr('fill', '#cdd8c0');
      }
    };

    const dragNode = d3.drag().on('start', dragStart).on('drag', dragged).on('end', dragEnd);

    const d3Nodes = this.rootBoard.selectAll('.node').data(this.chartData.nodes, (d) => d.id);

    const nodeEnter = d3Nodes.enter().append('g').attr('class', 'node').call(dragNode);

    nodeEnter
      .append('circle')
      .attr('class', 'node-circle')
      .attr('r', (d) => {
        return d.labels[0] === 'Person' ? settings.nodeSize.person : settings.nodeSize.others;
      })
      .attr('cursor', 'pointer')
      .attr('stroke-width', 1);

    // 名称
    const textG = nodeEnter
      .append('g')
      .attr('class', 'text-g')
      .attr('cursor', 'pointer')
      .attr('transform', (d) => {
        let offset = 0;
        if (d.nameList.length !== 2) {
          offset = deviceInfo.isSafari() ? 4 : 2;
        }
        return `translate(0, ${-d.nameHeight / 2 + offset})`;
      });

    textG.append((d) => {
      return utils.createVerticalName(d.nameList, settings.textSize);
    });

    // 标签
    nodeEnter
      .filter((d) => d.properties.isMainC)
      .append('g')
      .attr('stroke', 'none')
      .attr('stroke-width', 1)
      .attr('fill', 'none')
      .attr('fill-rule', 'evenodd')
      .append('g')
      .attr('class', 'start-board')
      .attr('transform', 'translate(-432.000000, -63.000000)')
      .append('polygon')
      .attr(
        'points',
        '433 34 429.473288 35.854102 430.14683 31.927051 427.293661 29.145898 431.236644 28.572949 433 25 434.763356 28.572949 438.706339 29.145898 435.85317 31.927051 436.526712 35.854102'
      );

    const gzwBoard = nodeEnter
      .filter((d) => d.properties.isGZW)
      .append('g')
      .attr('stroke', 'none')
      .attr('stroke-width', 1)
      .attr('fill', 'none')
      .attr('fill-rule', 'evenodd')
      .append('g')
      .attr('class', 'gzw-board');

    gzwBoard.append('rect').attr('width', 22).attr('height', 10).attr('fill', '#fff').attr('transform', 'translate(-11, 26)');

    gzwBoard
      .append('path')
      .attr('transform', 'translate(-452.000000, -29)')
      .attr(
        'd',
        'M462.930112,53 C464.310035,53 465,53.7272726 465,55.1818222 L465,62.8181778 C465,64.2727229 464.310039,65 462.930112,65 L441.069888,65 C439.689965,65 439,64.2727274 439,62.8181778 L439,55.1818222 C439,53.7272771 439.689961,53 441.069888,53 L462.930112,53 Z M452.379144,60.8374332 L452.045455,61.3379679 C452.879679,61.5465241 453.690731,61.8338681 454.47861,62.2 L454.840107,61.6508021 C454.222163,61.3843137 453.549505,61.1516191 452.822133,60.9527184 L452.379144,60.8374332 Z M458.15615,58.8144385 L457.794652,59.3566845 L455.827273,59.3566845 L455.827273,59.9754011 L457.342781,59.9754011 L456.737968,60.740107 C457.354367,60.8791444 457.910517,61.0228164 458.406417,61.171123 C457.82143,61.4018221 457.11377,61.5299883 456.283436,61.5556216 L455.96631,61.5604278 L456.334759,62.1374332 C457.530481,62.1096257 458.496791,61.873262 459.23369,61.4283422 C459.859358,61.6322638 460.503565,61.873262 461.16631,62.1513369 L461.576471,61.6090909 C461.020321,61.3912656 460.434046,61.187344 459.817647,60.9973262 C460.036399,60.789697 460.246253,60.5301604 460.447209,60.2187166 L460.596257,59.9754011 L461.826738,59.9754011 L461.826738,59.3566845 L458.56631,59.3566845 C458.580214,59.3393048 458.605849,59.302373 458.643215,59.245889 L458.684492,59.1828877 L458.802674,59.0090909 L458.15615,58.8144385 Z M451.586631,59.6069519 C451.531016,60.2836007 451.350267,60.7609626 451.044385,61.0390374 C450.727807,61.3 450.091362,61.4642123 449.135047,61.5316742 L448.889305,61.5465241 L449.146524,62.144385 C450.444207,62.0331551 451.266845,61.7944742 451.614439,61.4283422 C451.909366,61.1671204 452.110454,60.6569335 452.2177,59.8977814 L452.247059,59.6625668 L451.586631,59.6069519 Z M447.839572,55.9919786 L442,55.9919786 L442,62.1304813 L442.66738,62.1304813 L442.66738,61.8663102 L447.172193,61.8663102 L447.172193,62.1304813 L447.839572,62.1304813 L447.839572,55.9919786 Z M447.172193,56.6176471 L447.172193,61.2475936 L442.66738,61.2475936 L442.66738,56.6176471 L447.172193,56.6176471 Z M454.14492,58.8074866 L449.584492,58.8074866 L449.584492,60.955615 L450.24492,60.955615 L450.24492,59.4192513 L453.47754,59.4192513 L453.47754,60.8860963 L454.14492,60.8860963 L454.14492,58.8074866 Z M459.887166,59.9754011 C459.687879,60.285918 459.432977,60.557041 459.12246,60.7887701 C458.832026,60.7022579 458.530263,60.622955 458.217172,60.5508616 L457.739037,60.4481283 L458.12139,59.9754011 L459.887166,59.9754011 Z M446.636898,57.0973262 L443.195722,57.0973262 L443.195722,57.6951872 L444.586096,57.6951872 L444.586096,58.5502674 L443.418182,58.5502674 L443.418182,59.1342246 L444.586096,59.1342246 L444.586096,60.1005348 L443.008021,60.1005348 L443.008021,60.6983957 L446.838503,60.6983957 L446.838503,60.1005348 L446.129412,60.1005348 L446.504813,59.9336898 C446.43066,59.7668449 446.344147,59.6041196 446.245276,59.445514 L446.087701,59.2106952 L445.56631,59.3775401 C445.662092,59.522757 445.752723,59.6782729 445.838206,59.8440879 L445.962567,60.1005348 L445.253476,60.1005348 L445.253476,59.1342246 L446.42139,59.1342246 L446.42139,58.5502674 L445.253476,58.5502674 L445.253476,57.6951872 L446.636898,57.6951872 L446.636898,57.0973262 Z M460.874332,55.7347594 C459.748128,55.926631 458.421086,56.0321604 456.893203,56.0513476 L456.376471,56.0545455 L456.571123,56.6385027 C457.082086,56.6385027 457.569586,56.6306818 458.033623,56.6150401 L458.48984,56.5967914 L458.48984,57.0139037 L455.987166,57.0139037 L455.987166,57.6326203 L457.752941,57.6326203 C457.373369,57.9204278 456.785449,58.1913422 455.989182,58.4453636 L455.716043,58.5294118 L456.091444,59.1272727 C457.123797,58.7406714 457.871863,58.2898136 458.335643,57.7746992 L458.45508,57.6326203 L458.48984,57.6326203 L458.48984,58.8005348 L459.143316,58.8005348 L459.143316,57.6326203 L459.178075,57.6326203 C459.636898,58.1891563 460.354695,58.650281 461.331465,59.0159945 L461.604278,59.113369 L461.903209,58.4877005 C461.128719,58.2981977 460.51169,58.0519355 460.052123,57.748914 L459.887166,57.6326203 L461.687701,57.6326203 L461.687701,57.0139037 L459.143316,57.0139037 L459.143316,56.5550802 C459.743957,56.5068806 460.30159,56.4408841 460.816214,56.3570909 L461.194118,56.2909091 L460.874332,55.7347594 Z M451.378075,55.7 C451.244251,56.1176916 451.000429,56.4750613 450.646608,56.772109 L450.488235,56.8957219 L450.87754,57.4101604 C451.068717,57.2606952 451.239037,57.0994987 451.388503,56.9265709 L451.531016,56.7497326 L452.28877,56.7497326 C452.196078,57.0695187 452.038503,57.3313725 451.816043,57.5352941 C451.564745,57.7536344 451.163311,57.9298629 450.611738,58.0639797 L450.397861,58.1122995 L450.641176,58.7032086 C451.438324,58.5085561 451.999109,58.2490196 452.323529,57.9245989 C452.402317,57.8365419 452.474153,57.7484848 452.539037,57.6604278 C452.979323,58.1795009 453.688414,58.5386809 454.66631,58.7379679 L454.902674,58.1609626 C453.878431,57.9755793 453.180927,57.6349376 452.81016,57.1390374 C452.841058,57.0494355 452.866805,56.9618934 452.887403,56.8764112 L452.914439,56.7497326 L453.929412,56.7497326 C453.883066,56.9042187 453.828481,57.0504654 453.765657,57.188473 L453.665241,57.3893048 L454.297861,57.5770053 C454.384373,57.348366 454.458526,57.1228164 454.520321,56.9003565 L454.603743,56.568984 L454.603743,56.1727273 L451.87861,56.1727273 C451.918776,56.0831254 451.953793,55.9955833 451.98366,55.910101 L452.024599,55.7834225 L451.378075,55.7 Z M450.13369,57.1529412 C449.847891,57.4155674 449.489676,57.6749752 449.059046,57.9311646 L448.791979,58.084492 L449.229947,58.5919786 C449.662507,58.3332145 450.033918,58.0615765 450.344177,57.7770648 L450.522995,57.6048128 L450.13369,57.1529412 Z M449.250802,55.8251337 L448.924064,56.3187166 C449.313369,56.4484848 449.68877,56.6245989 450.050267,56.8470588 L450.404813,56.3117647 C450.137861,56.1634581 449.838282,56.0299822 449.506075,55.9113369 L449.250802,55.8251337 Z'
      );

    const jwBoard = nodeEnter
      .filter((d) => d.properties.isJW)
      .append('g')
      .attr('stroke', 'none')
      .attr('stroke-width', 1)
      .attr('fill', 'none')
      .attr('fill-rule', 'evenodd')
      .append('g')
      .attr('class', 'jw-board');

    jwBoard.append('rect').attr('width', 16).attr('height', 10).attr('fill', '#fff').attr('transform', 'translate(-9, 25)');

    jwBoard
      .append('path')
      .attr('transform', 'translate(-455, -7)')
      .attr(
        'd',
        'M462.846143,31 C464.282045,31 465,31.7272726 465,33.1818222 L465,40.8181778 C465,42.2727229 464.282049,43 462.846143,43 L446.153857,43 C444.717955,43 444,42.2727274 444,40.8181778 L444,33.1818222 C444,31.7272771 444.717951,31 446.153857,31 L462.846143,31 Z M453.608854,36.2745773 L449.97716,36.2745773 L449.97716,38.7425219 L450.71543,38.7425219 C450.63862,38.9914686 450.490863,39.2124395 450.290364,39.3782059 C450.021902,39.5876076 449.58938,39.7521378 448.992798,39.864316 L449.261261,40.5 C449.992074,40.3205135 450.514082,40.0886752 450.82729,39.7970092 C451.095752,39.5202994 451.282184,39.168804 451.371671,38.7425232 L452.00554,38.7425232 L452.00554,39.6923083 C452.00554,40.1485045 452.221802,40.3803413 452.669238,40.3803413 L453.489539,40.3803413 C453.713258,40.3803413 453.884775,40.3279909 454.004092,40.22329 C454.115951,40.1111106 454.197981,39.7745773 454.242724,39.2136756 L453.616312,39.0117528 C453.601398,39.3856846 453.571569,39.6025637 453.526825,39.6773507 C453.489539,39.7371796 453.437338,39.7745773 453.370222,39.7745773 L452.863127,39.7745773 C452.751268,39.7745773 452.699066,39.6997866 452.699066,39.5576923 L452.699066,38.7425219 L453.608854,38.7425219 L453.608854,36.2745773 Z M459.850597,33.5897512 L459.134698,33.5897512 L459.134698,40.4700954 L459.850597,40.4700954 L459.850595,36.5961574 C460.238373,36.9252176 460.708182,37.373935 461.260021,37.9497882 L461.7,37.2841907 C461.108677,36.7598186 460.491429,36.265605 459.850597,35.803421 L459.850597,33.5897512 Z M456.397867,33.5822664 C456.099576,34.8985045 455.532823,35.9455131 454.697614,36.7232921 L455.189794,37.3365396 C455.786376,36.7831214 456.263642,36.095087 456.621592,35.2574772 L457.948988,35.2574772 C457.855772,35.8557675 457.700412,36.4125096 457.478592,36.9277079 L457.337491,37.2318418 C457.007403,36.9609159 456.650001,36.7253076 456.2711,36.5288512 L455.898236,37.0822664 C456.2711,37.2617557 456.643963,37.493594 457.00937,37.7927389 C456.494818,38.5705165 455.786376,39.2136778 454.884043,39.7222245 L455.368765,40.3279931 C457.300208,39.1762814 458.388964,37.4636779 458.642513,35.2051291 L458.642513,34.5769239 L456.875134,34.5769239 C456.964621,34.2927367 457.039194,34.0010692 457.106309,33.7019243 L456.397867,33.5822664 Z M448.746712,33.612187 L448.03827,33.612187 L448.03827,35.3397468 L447.322371,35.3397468 L447.322371,36.0277797 L448.03827,36.0277797 L448.03827,38.6153917 C447.793937,38.6822718 447.547747,38.7421253 447.3,38.794878 L447.486432,39.5726556 C448.232159,39.3482846 448.933145,39.0715762 449.589384,38.7425159 L449.589384,38.0245676 C449.306008,38.159183 449.030088,38.2788409 448.746712,38.3835418 L448.746712,36.0277797 L449.335836,36.0277797 L449.335836,35.3397468 L448.746712,35.3397468 L448.746712,33.612187 Z M452.960066,37.7478651 L452.960066,38.1666685 L450.62594,38.1666685 L450.62594,37.7478651 L452.960066,37.7478651 Z M452.960066,36.8354727 L452.960066,37.2542761 L450.62594,37.2542761 L450.62594,36.8354727 L452.960066,36.8354727 Z M451.945877,33.5 L451.192692,33.6271367 C451.259808,33.7692306 451.326923,33.9262815 451.386581,34.0982905 L449.783264,34.0982905 L449.783264,34.6816241 L450.409676,34.6816241 C450.499163,34.883547 450.573736,35.1004275 450.648308,35.3397431 L449.559545,35.3397431 L449.559545,35.9305541 L454.078661,35.9305541 L454.078661,35.3397424 L452.878039,35.3397424 C452.967527,35.1303407 453.049557,34.9059827 453.109214,34.6816234 L453.862399,34.6816234 L453.862399,34.0982898 L452.117395,34.0982898 C452.065194,33.8814092 452.005536,33.6794864 451.945877,33.5 Z M452.460433,34.6816261 C452.398146,34.9111928 452.307984,35.1322205 452.191972,35.3397453 L451.319468,35.3397453 C451.252353,35.1079085 451.185237,34.891028 451.118122,34.6816261 L452.460433,34.6816261 Z'
      );

    nodeEnter
      .append('circle')
      .attr('class', 'handle-circle')
      .attr('r', (d) => {
        return d.labels[0] === 'Person' ? settings.nodeSize.person + 1 : settings.nodeSize.others + 1;
      })
      .attr('cursor', 'pointer')
      .attr('fill', 'transparent')
      .attr('stroke', 'transparent')
      .on('mouseover', function (d) {
        // 高亮关联link
        // 显示popover
        if (d.properties.keyNo && d.properties.keyNo[0] !== 'p') {
          const $sender = $(window.event.target);
          let position = $sender.position();
          if (position.left === 0 && position.top === 0) {
            // 都是0的情况下，说明没有取到正确值，因此做个补充
            const rect = $sender[0].getBoundingClientRect();
            position = {
              left: rect.x,
              top: rect.y,
            };
          }
          let size = {
            width: $sender.width(),
            height: $sender.height(),
          };
          if (size.width === 0 && size.height === 0) {
            // 都是0的情况下，说明没有取到正确值，因此做个补充
            const rect = $sender[0].getBBox();
            size = {
              width: rect.width,
              height: rect.height,
            };
          }
          self.emit('onDataNodeMouseover', {
            eid: d.properties.keyNo,
            data: d,
            position,
            size,
          });
        }
        if (!self.clickFalg) {
          // 此处使用raise导致ie下mouseout无法识别
          d3.selectAll('.node');
          // .raise()
          const highLightLinks = self.getRelatedLinks(d.id);
          const update = d3.selectAll('.link').filter((d) => highLightLinks.includes(d.id));
          // .raise()

          update.selectAll('line').attr('stroke', settings.linkColor.active).attr('stroke-width', 1).attr('marker-end', 'url(#arrowIn1)');

          update.selectAll('.link-rect').attr('fill', 'rgba(255, 255, 255, 0.8)');
          update.selectAll('text').attr('fill', settings.linkColor.active);

          const lighLightNodes = self.getRelatedNodes(d.id);
          d3.selectAll('.node').filter((d) => lighLightNodes.includes(d.id));
          // .raise()
        }
      })
      .on('mouseout', function (d) {
        self.emit('onDataNodeMouseout', {
          data: d,
        });
        if (!self.clickFalg) {
          const highLightLinks = self.getRelatedLinks(d.id);
          const update = d3
            .selectAll('.link')
            .filter((d) => highLightLinks.includes(d.id))
            .lower();

          update.selectAll('line').attr('stroke', settings.linkColor.disable).attr('stroke-width', 0.5).attr('marker-end', 'url(#arrowIn)');

          update.selectAll('.link-rect').attr('fill', 'rgba(255, 255, 255, 0.2)');

          update.selectAll('text').attr('fill', settings.linkColor.disable1);
        }
      });

    const updateNode = d3Nodes.merge(nodeEnter).attr('transform', (d) => {
      if (d.properties.isMain) {
        this.mainNodeX = d.x;
        this.mainNodeY = d.y;
      }
      return `translate(${d.x}, ${d.y})`;
    });

    updateNode
      .selectAll('.node-circle')
      .attr('fill', (d) => {
        if (+d.properties.isMain) {
          return d.properties.isShow ? settings.nodeColor.main : settings.nodeColor.mainDisable;
        }
        if (d.labels[0] === 'Person') {
          return d.properties.isShow ? settings.nodeColor.person : settings.nodeColor.personDisable;
        }
        return d.properties.isShow ? settings.nodeColor.ent : settings.nodeColor.entDisable;
      })
      .attr('stroke', (d) => {
        if (+d.properties.isMain) {
          return d.properties.isShow ? settings.borderColor.main : settings.borderColor.mainDisable;
        }
        if (d.labels[0] === 'Person') {
          return d.properties.isShow ? settings.borderColor.person : settings.borderColor.personDisable;
        }
        return d.properties.isShow ? settings.borderColor.ent : settings.borderColor.entDisable;
      });

    updateNode.selectAll('.start-board').attr('fill', (d) => (d.properties.isShow ? '#F9AD14' : '#fcdc9c'));

    updateNode.selectAll('.gzw-board').attr('fill', (d) => (d.properties.isShow ? '#829E62' : '#cdd8c0'));

    updateNode.selectAll('.jw-board').attr('fill', (d) => (d.properties.isShow ? '#3882FF' : '#99bfff'));

    d3Nodes.exit().remove();
  }

  highLightNodes(highLightIds, level) {
    if (this.maxLevel && this.maxLevel !== level) {
      this.maxLevel = level;
      this.formatChartData(this.data);
      this.domUpdate();
    }
    this.highLightNodes2(highLightIds);
  }

  getRelatedNodes(id) {
    const lighLightNodes = [];
    _.forEach(this.chartData.links, (link) => {
      if (link.sourceNode.id === id) {
        lighLightNodes.push(link.targetNode.id);
      } else if (link.targetNode.id === id) {
        lighLightNodes.push(link.sourceNode.id);
      }
    });
    lighLightNodes.push(id);
    return lighLightNodes;
  }

  getRelatedLinks(id) {
    const lighLightLinks = [];
    _.forEach(this.chartData.links, (link) => {
      if (link.sourceNode.id === id || link.targetNode.id === id) {
        lighLightLinks.push(link.id);
      }
    });
    return lighLightLinks;
  }

  highLightNodes2(highLightIds, needDraw = true) {
    _.forEach(this.chartData.nodes, (node) => {
      node.properties.isShow = false;
      if (highLightIds.includes(node.id)) {
        node.properties.isShow = true;
      }
    });
    if (needDraw) {
      this.draw();
    }
  }

  resetHighLight() {
    _.forEach(this.chartData.nodes, (node) => {
      node.properties.isShow = true;
    });
    this.clickFalg = false;
    this.draw();
  }

  zoomInOrOut(zoomIn) {
    const trans = d3.zoomTransform(this.svg.node());
    const k = trans.k;
    const x = trans.x;
    const y = trans.y;
    let newK = 0;
    if (_.isBoolean(zoomIn)) {
      newK = k + (zoomIn ? this.scaleStep : -this.scaleStep);
    } else {
      newK = zoomIn;
    }
    if (newK >= 2) {
      newK = 2;
    } else if (newK <= 0.1) {
      newK = 0.1;
    }
    const center = { x: this.size.width / 2, y: this.size.height / 2 };
    const translate0 = { x: (center.x - trans.x) / trans.k, y: (center.y - trans.y) / trans.k };
    const l = { x: translate0.x * newK + trans.x, y: translate0.y * newK + trans.y };
    const newX = x + center.x - l.x;
    const newY = y + center.y - l.y;
    trans.x = newX;
    trans.y = newY;
    trans.k = newK;
    this.zoom.transform(this.root, trans);
  }

  delayZoom() {
    this.svg.on('wheel.zoom', null);
  }
}
