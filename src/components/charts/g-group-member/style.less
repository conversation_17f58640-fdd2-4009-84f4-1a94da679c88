.group-container {
  position: absolute;
  inset: 0;
  overflow: hidden;
  background: url('../utils/images/shuiying6.png') repeat;
  background-size: 360px 280px;
  line-height: 1.2;

  .group-main {
    position: absolute;
    inset: 0;
  }

  &.iframe-index {
    z-index: 1;
    height: 417px;
  }

  .charts-iframe-mask {
    position: absolute;
    inset: 0;

    a {
      display: block;
      width: 100%;
      height: 100%;
    }
  }

  > svg {
    user-select: none;
  }

  .chart-footer {
    bottom: 30px;
  }

  .toolbox {
    position: fixed;
    width: 46px;
    right: 20px;
    bottom: 50px;
    font-size: 18px;
    z-index: 20;
  }

  .filter {
    position: fixed;
    right: 78px;
    bottom: 100px;
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s;
  }

  .fade-enter,
  .fade-leave-to {
    opacity: 0;
  }

  #group-member {
    position: absolute;
    inset: 0;
  }

  .loading-wrap {
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;
    inset: 0;
    background: rgb(255, 255, 255);
  }

  .path-container {
    position: absolute;
    inset: 0;
    z-index: 999;
    background: url('./images/shuiying6.png') repeat;
    background-size: 360px 280px;
    background-color: #fff;

    .group-path {
      position: absolute;
      inset: 0;
    }

    .back {
      color: #128bed;
      position: absolute;
      top: 0;
      line-height: 42px;
      padding-left: 32px;
      cursor: pointer;

      &.full-screen {
        top: 0;
      }

      i {
        height: 44px;
        width: 44px;
        display: block;
        background: url('./images/icon.png') no-repeat;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translate(0, -50%) scale(0.5);
        background-position: -264px -44px;
      }
    }
  }

  .filter-modal {
    position: fixed;
    right: 78px;
    bottom: 100px;
    width: 502px;
    background: #fff;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    border: 1px solid #eee;
    box-sizing: border-box;
    z-index: 2;
  }

  .chart-tips2 {
    text-align: center;
    font-size: 12px;
    line-height: 14px;
    position: absolute;
    color: #aaa;
    width: 100%;
    bottom: 30px;
    z-index: 9;
    padding: 10px 0 6px;
    background: #fff;

    img {
      display: inline-block;
      width: 55px;
    }
  }

  .chart-legend-wrap {
    background-color: #fff;
    height: 30px;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 20;

    .chart-tips {
      overflow: hidden;
      width: 740px;
      margin: 0 auto;

      > div {
        float: left;
        line-height: 30px;
        margin-right: 50px;
        color: #333;
        font-size: 14px;
        position: relative;

        &.tips {
          padding-left: 16px;

          &::before {
            content: '';
            display: block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translate(0, -50%);
          }
        }

        &.current-tips {
          &::before {
            background-color: #ff9e01;
          }
        }

        &.ent-tips {
          &::before {
            background-color: #4ea2f0;
          }
        }

        &.person-tips {
          &::before {
            background-color: #fd485d;
          }
        }

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .filter-panel {
    position: absolute;
    right: 20px;
    top: 181px;
    z-index: 21;
    font-size: 14px;

    .single-filter {
      cursor: pointer;
      height: 42px;
      width: 42px;
      position: relative;
      margin-bottom: 10px;

      .more {
        width: 267px;
        background-color: #f6f6f6;
        overflow: hidden;
        padding: 9px 16px;
        border-radius: 21px;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translate(0, -50%);
        z-index: 20;

        .more-wrap {
          width: 230px;
          overflow: hidden;

          > div {
            float: left;
            height: 24px;
            line-height: 24px;
            color: #333;
            margin-right: 22px;

            &.active {
              background-color: #128bed;
              border-radius: 2px;
              padding: 0 8px;
              color: #fff;
            }

            &:last-child {
              margin-right: 0;
            }
          }
        }
      }

      .title-wrap {
        height: 42px;
        width: 42px;
        position: absolute;
        background-color: #fff;
        right: 0;
        top: 0;
        z-index: 25;
        border-radius: 50%;

        &.active {
          background-color: #f6f6f6;

          .title {
            border-color: #128bed;
            background-color: #e9f3ff;
          }
        }
      }

      .title {
        color: #128bed;
        border: 1px solid #d6d6d6;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        line-height: 32px;
        font-size: 12px;
        text-align: center;
        position: absolute;
        right: 5px;
        top: 50%;
        transform: translate(0, -50%);
        z-index: 25;
      }

      &.percent-filter {
        .more {
          width: 275px;
        }
      }

      &.level-filter {
        .more {
          width: 163px;
        }
      }
    }
  }
}
