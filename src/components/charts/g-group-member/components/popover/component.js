/* eslint-disable consistent-return */
import { graph } from '@/shared/services';
import * as companyUtil from '@/utils/firm';

export default {
  name: 'group-member-popover',
  data() {
    return {
      isLoaded: false,
      data: {},
      id: '',
      groupId: '',
      rootName: '',
      paths: [],
      arr: ['在业', '存续', '迁入', '迁出'],
    };
  },
  mounted() {
    if (this.id && this.groupId) {
      graph
        .getMemberOverview({
          memberId: this.id,
          groupId: this.groupId,
        })
        .then((res) => {
          this.data = res.Result;
          this.paths = res.Result.Paths || [];
          this.isLoaded = true;
        })
        .catch(() => {
          this.isLoaded = false;
        });
    }
  },
  methods: {
    getLogo(keyNo, hasImage) {
      return companyUtil.getLogoByKeyNo(keyNo, hasImage);
    },
    noJump(keyNo) {
      return (keyNo[0] === 'c' && keyNo[1] === 'r') || (keyNo[0] === 'p' && keyNo[1] === 'r');
    },
    judge(amount) {
      if (amount) {
        const percent = amount.split('%')[0];
        if (+percent && +percent > 0) {
          return `${amount}万`;
        }
        return '-';
      }
    },
  },
};
