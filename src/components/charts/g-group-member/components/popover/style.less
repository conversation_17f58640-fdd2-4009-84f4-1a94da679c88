.group-member-popover {
  width: 453px;
  border-radius: 2px;
  border: 1px solid #eee;
  background-color: #fff;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);

  .loading {
    width: 340px;
    height: 120px;
    background: url('./images/loading.png') center center;
    background-size: cover;
    margin: 15px 0;
  }

  .header {
    padding: 15px;
    position: relative;
    padding-left: 80px;
    min-height: 80px;

    .logo-container {
      border-radius: 8px;
      background-color: #fff;
      width: 50px;
      height: 50px;
      position: absolute;
      left: 15px;
      top: 15px;
    }

    .name {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: #128bed;
      font-size: 16px;
      display: block;
    }

    .row-item {
      padding-top: 7px;
    }

    .padding-top-0 {
      padding-top: 0;
    }

    .app-tag {
      padding: 3px 6px;
      display: inline-block;
      font-size: 12px;
      margin-right: 5px;
      border-radius: 2px;
      margin-top: 5px;
      color: #f9ad14;
      background-color: #fef3dc;

      &.tag-green {
        color: #094;
        background-color: #ebfff4;
      }
    }

    .label-text {
      color: #999;
      font-size: 14px;
    }

    .content {
      color: #333;
      font-size: 14px;
    }

    .detail {
      position: absolute;
      right: 15px;
      top: 15px;
      color: #128bed;
    }
  }

  .body {
    border-top: 5px solid #f6f6f6;
    padding: 15px;
    overflow-y: auto;
    max-height: 200px;
    position: relative;

    .tips {
      color: #999;
      font-size: 12px;
      position: absolute;
      right: 15px;
      top: 15px;

      img {
        display: block;
        width: 15px;
        height: 15px;
        cursor: pointer;
      }
    }

    .section {
      margin-top: 15px;

      &.first {
        margin-top: 0;
      }

      .title {
        color: #128bed;
        font-size: 12px;
      }

      .path {
        line-height: 30px;
        font-size: 12px;
        margin-top: 8px;

        .name {
          padding-left: 5px;

          &.not-first-name {
            padding-left: 10px;
          }
        }

        .line {
          position: relative;
          top: -8px;
          color: #999;
          padding: 0 10px;
          display: inline-block;

          &::before {
            content: '';
            height: 1px;
            width: 100%;
            position: absolute;
            bottom: 5px;
            left: 0;
            background-color: #999;
          }

          &::after {
            content: '';
            height: 0;
            width: 0;
            border-width: 5px;
            display: block;
            border-style: solid;
            border-color: transparent transparent transparent #999;
            position: absolute;
            bottom: 0;
            right: -10px;
          }
        }
      }
    }
  }
}
