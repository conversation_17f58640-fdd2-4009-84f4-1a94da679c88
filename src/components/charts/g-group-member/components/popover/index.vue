<template>
  <div class="group-member-popover">
    <transition>
      <div v-if="isLoaded">
        <div class="header">
          <div class="logo-container">
            <q-entity-avatar class="logo" :key-no="data.KeyNo" :has-image="data.HasImage" :name="data.Name" :size="50"></q-entity-avatar>
          </div>

          <q-entity-link :coy-obj="{ KeyNo: data.KeyNo, Name: data.Name }"></q-entity-link>
          <div class="row-item padding-top-0" v-if="data.ShortStatus">
            <span :class="['app-tag', arr.includes(data.ShortStatus) && 'tag-green']">{{ data.ShortStatus }}</span>
          </div>
          <div class="row-item" v-if="data.RegistCapi">
            <span class="label-text">注册资本：</span><span class="content">{{ judge(data.RegistCapi) }}</span>
          </div>
        </div>
        <div class="body" v-if="paths && paths.length">
          <span class="tips">
            <a-tooltip placement="top">
              <template slot="title">
                <span>最多显示前10条投资链路</span>
              </template>
              <img src="../../images/tips.png" class="tips-tag" alt="" /> </a-tooltip
          ></span>
          <div :class="['section', index === 0 && 'first']" v-for="(item, index) in paths" :key="index">
            <div class="title">投资链{{ index + 1 }} {{ item[item.length - 1].PercentTotal }}</div>
            <div class="path">
              <span class="name1">{{ rootName }}</span>
              <span v-for="(val, idx) in item" :key="`path-${idx}`">
                <span class="line">{{ val.Percent }}</span
                ><span class="name not-first-name">{{ val.Name }}</span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </transition>
    <div v-if="!isLoaded" class="loading"></div>
  </div>
</template>
<script src="./component.js"></script>
<style lang="less" scoped src="./style.less"></style>
