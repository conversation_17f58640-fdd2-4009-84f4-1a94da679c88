.group-member-list {
  .header {
    height: 56px;
    line-height: 56px;
    border-bottom: 1px solid #eee;
    font-size: 16px;
    color: #333;
    position: relative;
    padding: 0 15px;

    i {
      position: absolute;
      right: 15px;
      top: 15px;
      cursor: pointer;
      color: #128bed;
      font-size: 20px;
    }
  }

  .body {
    padding: 20px 0;
  }

  .list-container {
    height: 259px;
    overflow: auto;
    position: relative;
    margin-top: 15px;

    .single-ent {
      height: 60px;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: keep-all;
      cursor: pointer;
      color: #333;
      font-size: 14px;
      position: relative;
      padding-left: 62px;
      padding-right: 15px;

      &:hover {
        background-color: #f3f9fd;
        color: #128bed;
      }

      .logo-container {
        width: 32px;
        height: 32px;
        border: 1px solid #eee;
        background-color: #fff;
        border-radius: 4px;
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translate(0, -50%);
        overflow: hidden;

        img {
          display: block;
          width: 32px;
          object-fit: contain;
          height: 32px;
        }

        .logo-span {
          display: inline-block;
          width: 100%;
          height: 100%;
          line-height: 30px !important;
          color: #fff;
          text-align: center;
        }
      }

      .name {
        line-height: 60px;
      }
    }

    .no-data {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);

      > img {
        display: block;
        width: 200px;
        height: 200px;
      }

      > p {
        color: #999;
        font-size: 14px;
        text-align: center;
      }
    }
  }

  .input-container {
    padding: 0 15px;
  }
}
