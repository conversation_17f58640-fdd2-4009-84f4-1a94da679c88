import _, { random } from 'lodash';

import * as companyUtil from '@/utils/firm';

import gUiNoData from '../../../g-ui-no-data';

export default {
  name: 'group-member-list',
  components: {
    [gUiNoData.name]: gUiNoData,
  },
  props: {
    totalNodes: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      searchKey: '',
      noData: false,
      page: 0,
      filterTotalNodes: [],
      initNodes: [],
      filterNodes: [],
    };
  },
  computed: {
    loadMore() {
      return this.filterNodes.length < this.initNodes.length;
    },
  },
  watch: {
    searchKey(val) {
      let str = val.trim();
      str = str.replace(/\s+/g, '');
      str = str.toLowerCase();
      const reg = new RegExp(str);
      const res = _.filter(this.filterTotalNodes, (node) => {
        let testStr = node.properties.inNodeText || node.properties.name;
        testStr = testStr.toLowerCase();
        testStr = testStr.replace(/\s+/g, '');
        return reg.test(testStr);
      });
      this.filterNodes = res.slice(0, 10);
      this.initNodes = res;
      this.noData = !this.initNodes.length;
    },
  },
  mounted() {
    // 去重 去除自然人
    this.filterTotalNodes = _.uniqBy(this.totalNodes, 'id');
    this.filterTotalNodes = _.reject(this.filterTotalNodes, (o) => o.labels[0] !== 'Company');
    this.$nextTick(() => {
      if (_.isEmpty(this.filterTotalNodes)) {
        this.noData = true;
      } else {
        this.initNodes = this.filterTotalNodes;
        this.filterNodes = this.filterTotalNodes.slice(0, 10);
      }
      $(this.$refs.list).on(
        'scroll',
        _.debounce(() => {
          const distance = this.$refs.list.scrollHeight - this.$refs.list.scrollTop - this.$refs.list.clientHeight;
          if (distance < 20 && this.loadMore) {
            this.page++;
            this.filterNodes = this.filterNodes.concat(this.initNodes.slice(this.page, (this.page + 1) * 10));
          }
        }, 50)
      );
    });
  },
  methods: {
    chooseMember(item) {
      this.$emit('choose', item);
    },
    getLogo(keyNo, hasImage) {
      return companyUtil.getLogoByKeyNo(keyNo, hasImage);
    },
    close() {
      this.$emit('closeSearch');
    },
    getBackBG() {
      const colors = ['#E79177', '#97a2e2', '#62b4cc', '#7db3d9', '#c5c273', '#bc9de0', '#e9a67c'];
      return colors[random(colors.length, false)];
    },
    notTrueKey(keyNo) {
      return (keyNo[0] === 'c' && keyNo[1] === 'r') || (keyNo[0] === 'p' && keyNo[1] === 'r');
    },
  },
  destroyed() {
    this.filterNodes = [];
    this.page = 0;
    $(this.$refs.list).off('scroll');
  },
};
