<template>
  <div class="group-member-list">
    <div class="header">
      搜索
      <a-icon type="close" @click="close" />
    </div>
    <div class="body">
      <div class="input-container">
        <a-input v-model="searchKey" placeholder="请输入名字以筛选企业" allow-clear />
      </div>
      <div class="list-container" ref="list">
        <div class="list" v-show="!noData">
          <div class="single-ent" v-for="(item, index) in filterNodes" :key="index" @click="chooseMember(item)">
            <div class="logo-container">
              <q-entity-avatar
                class="logo"
                :key-no="item.properties.keyNo"
                :has-image="!!item.properties.hasImage"
                :name="item.properties.inNodeText || item.properties.name"
                :size="30"
              ></q-entity-avatar>
            </div>
            <span class="name">{{ item.properties.inNodeText || item.properties.name }}</span>
          </div>
        </div>
        <g-ui-no-data v-if="noData"></g-ui-no-data>
      </div>
    </div>
  </div>
</template>
<script src="./component.js"></script>
<style lang="less" scoped src="./style.less"></style>
