export default {
  name: 'group-member-filter',
  props: {
    limitExceed: {
      default: false,
      type: <PERSON>olean,
    },
    shortStatus: {
      default: '',
      type: String,
    },
    maxLevel: {
      default: '',
      type: [String, Number],
    },
    minPercent: {
      default: '',
      type: String,
    },
  },
  data() {
    return {
      filterType: this.shortStatus,
      filterPercent: this.minPercent,
      filterLevel: this.maxLevel,
    };
  },
  watch: {
    shortStatus(val) {
      this.filterType = val;
    },
    maxLevel: {
      handler(val) {
        this.filterLevel = val;
      },
      deep: true,
      immediate: true,
    },
    minPercent(val) {
      this.filterPercent = val;
    },
  },
  methods: {
    handleClickLevel(type, value) {
      if (type === 2) {
        if (this.filterLevel !== value) {
          this.filterLevel = value;
          this.$emit('filterLevel', { maxLevel: this.filterLevel });
        }
        return;
      }
      let isChange = false;
      if (type === 0) {
        if (this.filterType !== value) {
          isChange = true;
        }
        this.filterType = value;
      } else if (type === 1) {
        if (this.filterPercent !== value) {
          isChange = true;
        }
        this.filterPercent = value;
      }
      if (isChange) {
        this.$emit('filter', { shortStatus: this.filterType, minPercent: this.filterPercent });
      }
    },
    close() {
      this.$emit('close');
    },
  },
};
