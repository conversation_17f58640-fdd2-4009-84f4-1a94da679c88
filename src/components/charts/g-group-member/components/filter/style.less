
.group-member-filter {
  padding-bottom: 50px;

  .header {
    height: 56px;
    line-height: 56px;
    border-bottom: 1px solid #eee;
    font-size: 16px;
    color: #333;
    position: relative;
    padding: 0 15px;

    i {
      position: absolute;
      right: 15px;
      top: 15px;
      cursor: pointer;
      color: #128bed;
      font-size: 20px;
    }
  }

  .section {
    padding: 0 15px;
    margin-top: 20px;

    .title {
      font-size: 14px;
      color: #666;
      position: relative;

      img {
        position: absolute;
        left: 35px;
        top: 50%;
        transform: translate(0, -50%);
        display: block;
        width: 15px;
        height: 15px;
        cursor: pointer;
      }
    }

    .btns {
      overflow: hidden;

      >div {
        float: left;
        line-height: 32px;
        color: #333;
        cursor: pointer;
        width: 17.6%;
        height: 32px;
        border: 1px solid #D6D6D6;
        text-align: center;
        border-radius: 2px;
        box-sizing: border-box;
        margin-top: 15px;
        margin-right: 3%;

        &:nth-child(5n) {
          margin-right: 0;
        }

        &.active {
          border-color: #128bed;
          color: #128bed;
        }

        &.disable {
          padding: 0 8px;
          color: #999;
          border-radius: 2px;

          &:hover {
            cursor: not-allowed;
          }
        }

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}
