<template>
  <div :class="['group-container']">
    <div class="group-main group-member" v-show="loaded">
      <div id="group-member"></div>
      <!-- toolbox -->
      <div class="toolbox" v-show="isInit">
        <g-ui-toolbox>
          <g-ui-toolbox-action
            :action-type="actionTypes.filter2"
            :is-active="showFilter"
            @click="
              showFilter = !showFilter;
              showSearch = false;
            "
          ></g-ui-toolbox-action>
          <g-ui-toolbox-action :action-type="actionTypes.search" :is-active="showSearch" @click="onSearch"></g-ui-toolbox-action>
          <g-ui-toolbox-action :action-type="actionTypes.zoomIn" @click="onZoomIn"></g-ui-toolbox-action>
          <g-ui-toolbox-action :action-type="actionTypes.zoomOut" @click="onZoomOut"></g-ui-toolbox-action>
          <g-ui-toolbox-action :action-type="actionTypes.refresh" @click="onRefresh()"></g-ui-toolbox-action>
          <g-ui-toolbox-action v-show="!isFullScreen" :action-type="actionTypes.fullScreen" @click="onFullScreen"> </g-ui-toolbox-action>
          <g-ui-toolbox-action v-show="isFullScreen" :action-type="actionTypes.exitFullScreen" @click="onExitFullScreen">
          </g-ui-toolbox-action>
          <g-ui-toolbox-action :action-type="actionTypes.save" :is-busy="isSaving" @click="onSave"></g-ui-toolbox-action>
        </g-ui-toolbox>
      </div>

      <div class="chart-legend-wrap" v-if="isInit">
        <div class="chart-tips">
          <div class="act-tips" v-if="actText">
            <img alt="star" src="./images/icon-star.svg" />
            {{ actText }}
          </div>
          <div class="tips current-tips">主公司</div>
          <div class="tips ent-tips">企业</div>
          <div class="tips person-tips">人员</div>
          <div class="invest-tips">
            <img alt="投资" src="./images/icon-arrow.svg" />
            投资
          </div>
          <div class="overseas-tips">
            <img alt="境外" src="./images/icon-jw.svg" />
            境外
          </div>
          <div class="gzw">
            <img alt="国资委" src="./images/icon_gzw.svg" />
            国资委
          </div>
        </div>
      </div>
      <!-- 搜索 -->
      <transition name="fade">
        <div v-if="showSearch" class="filter-modal">
          <group-member-list @closeSearch="showSearch = false" :total-nodes="totalNodes" @choose="handleChooseMember"></group-member-list>
        </div>
      </transition>
      <transition name="fade">
        <div v-show="showFilter" class="filter-modal">
          <group-member-filter
            @close="showFilter = false"
            @filterLevel="handleFilterLevel"
            @filter="handleFilter"
            :short-status="shortStatus"
            :max-level="maxLevel + ''"
            :min-percent="minPercent"
            :limit-exceed="limitExceed"
          ></group-member-filter>
        </div>
      </transition>
      <transition name="slide-fade">
        <div class="path-container" v-show="showPath" id="group-path">
          <div
            :class="['back container']"
            @click="
              showPath = false;
              pathNoData = false;
            "
          >
            <img alt="返回" src="./images/ic_reply.png" />
            {{ name || '' }}
          </div>
        </div>
      </transition>
      <!-- 加载 -->
      <g-ui-loading v-if="isLoading"></g-ui-loading>
      <!-- 暂无数据 -->
      <g-ui-no-data v-if="noData || pathNoData"></g-ui-no-data>
      <!-- 免责 -->
      <g-ui-footer></g-ui-footer>
    </div>
  </div>
</template>
<style lang="less" scoped src="./style.less"></style>
<style lang="less">
.slide-fade-enter-active {
  transition: all 0.3s ease;
}
.slide-fade-leave-active {
  transition: all 0.3s ease;
}
.slide-fade-enter, .slide-fade-leave-to
/* .slide-fade-leave-active for below version 2.1.8 */ {
  transform: translateX(1200px);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

@keyframes typeSlideIn {
  0% {
    width: 0;
    opacity: 0;
  }

  80% {
    width: 267px;
  }

  100% {
    width: 267px;
    opacity: 1;
  }
}

@keyframes typeSlideOut {
  0% {
    width: 267px;
    opacity: 1;
  }

  100% {
    opacity: 0;
    width: 0;
  }
}

@keyframes percentSlideIn {
  0% {
    width: 0;
    opacity: 0;
  }

  80% {
    width: 275px;
  }

  100% {
    width: 275px;
    opacity: 1;
  }
}

@keyframes percentSlideOut {
  0% {
    width: 275px;
    opacity: 1;
  }

  100% {
    opacity: 0;
    width: 0;
  }
}

@keyframes levelSlideIn {
  0% {
    width: 0;
    opacity: 0;
  }

  80% {
    width: 163px;
  }

  100% {
    width: 163px;
    opacity: 1;
  }
}

@keyframes levelSlideOut {
  0% {
    width: 163px;
    opacity: 1;
  }

  80% {
    opacity: 0;
  }

  100% {
    opacity: 0;
    width: 0;
  }
}
</style>
<script src="./component.js"></script>
