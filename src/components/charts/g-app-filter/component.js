/* eslint-disable no-param-reassign */
import _ from 'lodash';

export default {
  name: 'g-app-filter',
  props: {
    typeList: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
      default: '筛选',
    },
    visible: {
      type: Boolean,
      default: () => false,
    },
  },
  methods: {
    handleClick(key, value = '') {
      const targetList = _.find(this.typeList, (type) => type.key === key);
      if (targetList) {
        _.forEach(targetList.list, (list) => {
          if (targetList.isMuti) {
            // 多选
            if (list.key === value) {
              list.selected = !list.selected;
            }
          } else {
            // 单选
            list.selected = false;
            if (list.key === value) {
              list.selected = true;
            }
          }
        });
        this.$emit('chooseFilter', this.typeList);
      }
    },
    close() {
      this.$emit('close');
    },
  },
};
