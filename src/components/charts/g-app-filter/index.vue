<template>
  <div class="g-app-filter">
    <div class="header">
      筛选
      <a-icon type="close" @click="close" />
    </div>
    <div class="g-app-filter-section" v-for="(item, index) in typeList" :key="`type-${index}`">
      <div class="title">{{ item.title }}</div>
      <div class="btns">
        <div
          v-for="(val, idx) in item.list"
          :key="`item-${idx}`"
          :class="[val.selected && 'active']"
          @click="handleClick(item.key, val.key)"
        >
          {{ val.value }}
        </div>
      </div>
    </div>
  </div>
</template>
<script src="./component.js"></script>
<style lang="less" scoped src="./style.less"></style>
