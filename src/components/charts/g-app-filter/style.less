.g-app-filter {
  position: fixed;
  right: 78px;
  bottom: 100px;
  width: 502px;
  background: #FFF;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  border: 1px solid #EEE;
  box-sizing: border-box;
  padding-bottom: 50px;
  z-index: 2;

  .header {
    height: 56px;
    line-height: 56px;
    border-bottom: 1px solid #eee;
    font-size: 16px;
    color: #333;
    position: relative;
    padding: 0 15px;

    i {
      position: absolute;
      right: 15px;
      top: 15px;
      cursor: pointer;
      color: #128bed;
      font-size: 20px;
    }
  }
}

  .g-app-filter-section {
    padding: 12px 15px 0;

    &:first-child {
      padding-top: 0;
    }

    .title {
      font-size: 14px;
      color: #666;
      position: relative;
      margin-bottom: 0;

      img {
        position: absolute;
        left: 35px;
        top: 50%;
        transform: translate(0, -50%);
        display: block;
        width: 15px;
        height: 15px;
        cursor: pointer;
      }
    }

    .btns {
      overflow: hidden;

      >div {
        float: left;
        line-height: 32px;
        color: #333;
        cursor: pointer;
        width: 17.6%;
        height: 32px;
        border: 1px solid #D6D6D6;
        text-align: center;
        border-radius: 2px;
        box-sizing: border-box;
        margin-top: 15px;
        margin-right: 3%;

        &:nth-child(5n) {
          margin-right: 0;
        }

        &.active {
          border-color: #128bed;
          color: #128bed;
        }

        &.disable {
          padding: 0 8px;
          color: #999;
          border-radius: 2px;

          &:hover {
            cursor: not-allowed;
          }
        }

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
