<template>
  <div :class="['equity-chart-container', containerName]">
    <div :id="containerName"></div>
    <div class="toolbox" v-show="isInit">
      <g-ui-toolbox>
        <g-ui-toolbox-action
          :action-type="actionTypes.select"
          :is-active="isFilterOpened"
          @close="closeFilter"
          @click="onFilter"
        ></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.fullName" v-if="!isFullName" @click="switchFullName"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.shortName" v-else @click="switchFullName"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.edit" :is-active="isEdit" @click="edit"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.zoomIn" @click="onZoomIn"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.zoomOut" @click="onZoomOut"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.refresh" @click="onRefresh()"></g-ui-toolbox-action>
        <g-ui-toolbox-action v-show="!isFullScreen" :action-type="actionTypes.fullScreen" @click="onFullScreen"> </g-ui-toolbox-action>
        <g-ui-toolbox-action v-show="isFullScreen" :action-type="actionTypes.exitFullScreen" @click="onExitFullScreen">
        </g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.save" :is-busy="isSaving" @click="onSave"></g-ui-toolbox-action>
      </g-ui-toolbox>
    </div>
    <transition name="fade">
      <div class="filter" v-show="isFilterOpened">
        <equity-chart-filter
          @changeTagDisplay="handleTagDisplay"
          @changeEntTags="handleEntTagsChanged"
          @changed="onFilterChanged"
          @close="isFilterOpened = false"
          ref="filter"
        ></equity-chart-filter>
      </div>
    </transition>
    <!-- 免责 -->
    <g-ui-footer></g-ui-footer>
    <!-- 加载 -->
    <g-ui-loading v-if="isLoading"></g-ui-loading>
    <!-- 暂无数据 -->
    <g-ui-no-data v-if="noData"></g-ui-no-data>
  </div>
</template>
<style lang="less" scoped src="./style.less"></style>
<script src="./component.js"></script>
