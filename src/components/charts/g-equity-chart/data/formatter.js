import _ from 'lodash';

import tagTypes from '../../utils/tags';
import utils from '../chart/utils';

class FormatGuquan {
  constructor() {
    this.reset();
  }

  init(keyNo, data) {
    this.data = data;
    this.rootData = {
      id: 'root',
      KeyNo: keyNo,
      Name: data.gudong?.Name || data.touzi?.Name,
      isRoot: true,
      depth: 0,
      isFold: false,
    };
    this.entity = 0;
    if (!this.rootData.Name) {
      if (data.syr && data.syr.CompanyName) {
        this.rootData.Name = data.syr.CompanyName;
      } else if (data.kzr && data.kzr.CompanyName) {
        this.rootData.Name = data.kzr.CompanyName;
      } else if (data.kg && data.kg.CompanyName) {
        this.rootData.Name = data.kg.CompanyName;
      }
    }
  }

  reset() {
    this.data = {};
    this.rootData = {};
    this.syrKeyNos = [];
    this.kzrKeyNos = [];
    this.kgKeyNos = [];
  }

  format() {
    this.syrObj = {};
    this.syrKeyNos = this.data?.syr?.Names?.forEach((vo) => {
      if (vo.KeyNo) {
        this.syrObj[vo.KeyNo] = vo.PercentTotal;
      } else if (vo.Name) {
        this.syrObj[vo.Name] = vo.PercentTotal;
      }
    });
    this.kzrKeyNos = [this.data?.kzr?.ControllerData?.KeyNo];
    this.kgKeyNos = _.map(this.data?.kg?.Names, 'KeyNo');
    const gdTree = this.transTree({ ...this.rootData, DetailList: this.data?.gudong?.DetailList }, 0, true, [this.rootData.KeyNo]);
    const investTree = this.transTree({ ...this.rootData, DetailList: this.data?.touzi?.DetailList }, 0, false, [this.rootData.KeyNo]);
    return { ...this.rootData, gdTree, investTree, syr: this.syrObj, kzr: this.kzrKeyNos };
  }

  transTree(currentData, depth, isGudong, exitKeyNos = []) {
    this.entity += 1;
    const node = {
      id: utils.generateUUID(),
      isFold: depth > 0,
      keyNo: currentData.KeyNo,
      name: currentData.Name,
      depth,
      isGudong,
      isRoot: currentData.isRoot,
      org: currentData.Org,
      Org: currentData.Org,
      entity: this.entity,
      amount: '',
      hasChildren: +currentData.DetailCount > 0 || false,
      // public: '',
      public: '',
      // financing: '',
      financing: '',
      detailList: [],
      tags: [],
      labelList: [],
      exitKeyNos: _.cloneDeep(exitKeyNos),
      shortStatus: currentData.ShortStatus,
    };
    node.capi = currentData.StockRightNum || currentData.ShouldCapi;
    // tags
    // 企业登记状态
    const registerTags = [];
    if (currentData.ShortStatus && +currentData.Org === 0) {
      registerTags.push({ Name: currentData.ShortStatus, module: 'ent_status' });
    }
    // 其他tag状态
    const listingTags = [];
    const financingTags = [];
    const areaTag = [];
    // 简称-210产品-208投资机构
    const productObj = {};
    const orgObj = {};
    if (!_.isEmpty(currentData.Tags)) {
      _.each(currentData.Tags, (tag) => {
        if (+tag.Type === 210) {
          productObj.name = tag.Name;
          productObj.id = tag.DataExtend;
          productObj.type = tag.Type;
        } else if (+tag.Type === 208) {
          orgObj.name = tag.Name;
          orgObj.id = tag.DataExtend;
          orgObj.type = tag.Type;
        }
        const module = tagTypes[tag.Type] && tagTypes[tag.Type].module;
        if (module) {
          tag.module = module;
          if (module === 'listing_status') {
            listingTags.push(tag);
          } else if (module === 'financing_status') {
            financingTags.push(tag);
          } else if (module === 'area') {
            areaTag.push(tag);
          }
        }
      });
    }
    // 简称 产品优先
    if (!_.isEmpty(productObj)) {
      node.shortObj = productObj;
    } else if (!_.isEmpty(orgObj)) {
      node.shortObj = orgObj;
    }
    // 企业登记状态
    if (!_.isEmpty(registerTags)) {
      _.each(registerTags, (tag) => {
        node.tags.push({
          type: tag.module,
          text: tag.Name,
        });
      });
    }
    // 上市状态
    if (!_.isEmpty(listingTags)) {
      node.tags.push({
        type: 'listing_status',
        text: '上市',
      });
      node.isPublic = true;
      const tempListingTag = listingTags[0];
      node.public = depth !== 0 ? `${tempListingTag.Name}：${tempListingTag.ShortName} ${tempListingTag.DataExtend}` : '';
    }
    // 融资轮次
    if (!_.isEmpty(financingTags)) {
      const tempFinancingTag = financingTags[0];
      node.financing = depth !== 0 ? `融资轮次：${tempFinancingTag.Name}` : '';
    }
    // 企业地区
    if (!_.isEmpty(areaTag)) {
      _.each(areaTag, (tag) => {
        node.tags.push({
          type: tag.module,
          text: tag.Name,
        });
      });
    }
    if (this.kzrKeyNos.includes(node.keyNo) && node.keyNo) {
      node.kzr = true;
    }
    if (this.syrObj[node.keyNo] || (!node.keyNo && this.syrObj[node.name])) {
      const percent = this.syrObj[node.keyNo] || this.syrObj[node.name];
      if (+percent.split('%')[0] && +percent.split('%')[0] > 0) {
        node.syr = this.syrObj[node.keyNo] || this.syrObj[node.name];
      }
    }
    if (this.kgKeyNos.includes(node.keyNo) && !node.isGudong && node.keyNo) {
      node.kg = true;
    }
    // 控股比例
    if (currentData.Percent || currentData.PercentTotal) {
      node.percent = currentData.Percent || currentData.PercentTotal;
    }
    if (currentData.DetailList?.length > 0) {
      currentData.DetailList.forEach((v) => {
        const obj = this.transTree(v, depth + 1, isGudong, node.exitKeyNos);
        // 处理重复循环股东
        if (obj.keyNo && obj.exitKeyNos.indexOf(obj.keyNo) > -1) {
          obj.hasChildren = false;
        } else if (obj.keyNo) {
          obj.exitKeyNos.push(obj.keyNo);
        }
        node.detailList.push(obj);
      });
    }
    return node;
  }
}

export default new FormatGuquan();
