import { graph } from '@/shared/services';

import formatter from './formatter';

const loadGd = ({ keyNo }) => {
  return new Promise((resolve, reject) => {
    graph
      .getOwnershipStructure({ keyNo })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const loadTz = ({ keyNo }) => {
  return new Promise((resolve, reject) => {
    graph
      .getEquityInvestment({ keyNo })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const loadKg = ({ keyNo }) => {
  return new Promise((resolve, reject) => {
    graph
      .getHoldingCompany({ keyNo })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const loadSyr = ({ keyNo }) => {
  return new Promise((resolve, reject) => {
    graph
      .getUltimateBeneficiaryNoPath({ keyNo })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const loadKzr = ({ keyNo }) => {
  return new Promise((resolve, reject) => {
    graph
      .getSuspectedActualControllerNoPathV2({ keyNo })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const loadPersonInvest = ({ keyNo }) => {
  return new Promise((resolve, reject) => {
    graph
      .getBossEquityInvestment({ keyNo })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const loadGuquanPerson = (keyNo, name) => {
  return new Promise((resolve, reject) => {
    Promise.all([loadPersonInvest({ keyNo }), loadKg({ keyNo })])
      .then(([tzData, kgData]) => {
        const result = {};
        let touzi = {
          Name: name,
        };
        if (tzData.Result) {
          touzi = {
            Name: tzData.Result.CompanyName,
            DetailList: tzData.Result.EquityShareDetail,
          };
        }
        result.touzi = touzi;
        result.kg = kgData.Result || [];
        formatter.init(keyNo, result);
        const detail = formatter.format();
        resolve(detail);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const loadGuquanData = (keyNo, name) => {
  return new Promise((resolve, reject) => {
    Promise.all([loadGd({ keyNo }), loadTz({ keyNo }), loadSyr({ keyNo }), loadKg({ keyNo }), loadKzr({ keyNo })])
      .then(([gdData, tzData, syrData, kgData, kzrData]) => {
        const result = {};
        let gudong = {
          Name: name,
        };
        if (gdData.Result) {
          gudong = {
            Name: gdData.Result.CompanyName,
            DetailList: gdData.Result.EquityShareDetail,
          };
        }
        result.gudong = gudong;
        let touzi = {
          Name: name,
        };
        if (tzData.Result) {
          touzi = {
            Name: tzData.Result.CompanyName,
            DetailList: tzData.Result.EquityShareDetail,
          };
        }
        result.touzi = touzi;
        result.syr = syrData.Result || [];
        result.kg = kgData.Result || [];
        result.kzr = kzrData?.Result || [];
        formatter.init(keyNo, result);
        const detail = formatter.format();
        resolve(detail);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const loadGuquanDataRequire = (nodeData) => {
  const exitKeyNos = nodeData.exitKeyNos || [];
  return new Promise((resolve, reject) => {
    Promise.all([nodeData.isGudong ? loadGd({ keyNo: nodeData.keyNo }) : loadTz({ keyNo: nodeData.keyNo })])
      .then(([data]) => {
        let result = {};
        if (data.Result) {
          result = {
            Name: data.Result.CompanyName,
            DetailList: data.Result.EquityShareDetail,
          };
        }
        const detail = formatter.transTree(result, nodeData.depth, nodeData.isGudong, exitKeyNos);
        resolve(detail.detailList);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export default {
  loadGuquanData,
  loadGuquanPerson,
  loadGuquanDataRequire,
};
