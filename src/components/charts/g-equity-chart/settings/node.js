const defaultNodeSettings = {
  cursor: 'pointer',
  fontSize: 14,
  // control rect style
  textColor: '#333333',
  textColor2: '#999',
  bgColor: '#fff',
  innerBorderColor: '#128BED',
  outerBorderColor: '#cfe7fa',
  outerBorderOpacity: 0,
  activeOuterBorderOpacity: 1,
  innerBorderWidth: 1,
  outerBorderWidth: 6,
  radius: 2,
  // eidt
  editCircleRadius: 10,
  editCircleBg: 'rgb(245, 34, 45)',
  // control plus style
  circleColor: '#999',
  activeCircleColor: '#128BED',
  circleWidth: 1,
  circleRadius: 10,
  amountSize: 12,
  // tags
  tagSize: 12,
  tagsColor: {
    listing_status: { bgColor: '#fff4ed', color: '#ec9662' },
    area: { bgColor: '#eaebf8', color: '#6f77d1' },
  },
  status_color: {
    active: {
      color: '#009944',
      bgColor: '#ebfff4',
    },
    negative: {
      color: '#F04040',
      bgColor: '#feeded',
    },
    other: {
      color: '#128bed',
      bgColor: '#e7f4ff',
    },
  },
  // 新三板、融资轮次等信息部分
  statusSize: 12,
  statusColor: '#128bed',
  statusBg: '#e9f3ff',
  // 最终受益人等标签
  labelSize: 12,
  label: ['#128bed', '#F04040'],
  // 竖排列
};

const centerNodeSettings = {
  ...defaultNodeSettings,
  fontSize: 16,
  bgColor: '#128bed',
  textColor: '#fff',
};

const companyNodeSettings = {
  ...defaultNodeSettings,
};

const personNodeSettings = {
  ...defaultNodeSettings,
  innerBorderColor: '#F04040',
  outerBorderColor: '#ffe4e7',
};

const verticalNodeSettings = {
  width: 194,
  minHeight: 70,
  maxLength: 22,
};

const horizontalNodeSettings = {
  height: 60,
  gap: 60,
  minWidth: 100,
};

export default {
  centerNodeSettings,
  companyNodeSettings,
  personNodeSettings,
  verticalNodeSettings,
  horizontalNodeSettings,
};

export { centerNodeSettings, companyNodeSettings, personNodeSettings, verticalNodeSettings, horizontalNodeSettings };
