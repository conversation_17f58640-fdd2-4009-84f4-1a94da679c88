import * as d3 from 'd3';

import defaultArrowSettings from './arrow';
import { centerNodeSettings, companyNodeSettings, personNodeSettings, verticalNodeSettings, horizontalNodeSettings } from './node';
import defaultLineSettings from './line';

const defaultScale = 0.8;
export default {
  debug: false,
  // 是否启用自动调整
  autoAdjust: true,
  // 节点内部padding
  padding: {
    h: 10,
    v: 8,
  },
  // 默认缩放
  defaultScale,
  scaleLevel: 15,
  // fade效果
  fadeDuration: 400,
  // 缩放范围
  scaleRange: [defaultScale * 0.1, defaultScale * 3],
  // 节点大小，y无效
  nodeSize: [210, 0],
  horizontalNodeSize: [150, 10],
  // 两层之间最小距离
  gap: 110,
  gap1: 145,
  // 横向排版下的最小距离
  gap2: 300,
  // 同级节点距离比
  nodeSeparation: 1.6,
  // 动画持续时间
  duration: 290,
  // 缓动参数
  ease: (d3 && d3.easeQuad) || null,
  // 中心节点设置
  centerNodeSettings,
  // 人员节点配置
  personNodeSettings,
  // 企业节点类型参数配置
  companyNodeSettings,
  // 横向纵向一些节点信息
  verticalNodeSettings,
  horizontalNodeSettings,
  // 默认线配置
  defaultLineSettings,
  // 默认箭头配置
  defaultArrowSettings,
};
