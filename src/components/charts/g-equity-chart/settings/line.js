const defaultLineSettings = {
  fontSize: 12,
  // line style
  lineWidth: 1,
  lineColor: '#D6D6D6',
  lineFill: 'none',
  lineOpacity: 0.9,
  // 是否启用线流动效果
  enableFlowAnimation: true,
  flowLineColor: '#128BED',
  kgBg: '#e7f4ff',
  kgColor: '#128bed',
  // flowLineColor: {
  //   company: '#128BED',
  //   person: '#F04040'
  // },
  flowLineOpacity: 1,
  // type: 'horizontal'
  type: 'horizontalPolyline',
  // type: 'curveCardinal'
};

export default defaultLineSettings;
