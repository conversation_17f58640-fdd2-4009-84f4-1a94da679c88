/* eslint-disable func-names */
/* eslint-disable consistent-return */
/* eslint-disable no-bitwise */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable prefer-const */
import _ from 'lodash';

import settings from '../settings';
import measure from '../../utils/measure';

const SVG_NS = 'http://www.w3.org/2000/svg';

const createVerticalName = (data, fontSize) => {
  const ele = document.createElementNS(SVG_NS, 'text');
  ele.setAttribute('stroke-width', 0);
  ele.setAttribute('style', 'font-family:"微软雅黑", Microsoft YaHei", Arial;');
  let prevHeight = 0;
  const nameList = data;
  for (let i = 0; i < nameList.length; ++i) {
    const o = nameList[i];
    const tspan = document.createElementNS(SVG_NS, 'tspan');
    tspan.textContent = o.text;
    _.chain(o.props)
      .keys()
      .each((key) => {
        tspan.setAttribute(key, o.props[key]);
      })
      .value();
    tspan.setAttribute('style', 'font-family:"微软雅黑", Microsoft YaHei", Arial;');
    tspan.setAttribute('dy', prevHeight + o.height / 2);
    tspan.setAttribute('x', 0);
    tspan.setAttribute('y', 0);
    prevHeight += o.height;
    ele.appendChild(tspan);
  }
  ele.setAttribute('font-size', fontSize);
  ele.setAttribute('text-anchor', 'middle');
  ele.setAttribute('dominant-baseline', 'middle');
  return ele;
};

const createRichText = (data, fontSize) => {
  let ele;
  ele = document.createElementNS(SVG_NS, 'text');
  ele.setAttribute('stroke-width', 0);
  ele.setAttribute('style', 'font-family:"微软雅黑", Microsoft YaHei", Arial;');
  for (let i = 0; i < data.length; ++i) {
    const o = data[i];
    const tspan = document.createElementNS(SVG_NS, 'tspan');
    tspan.textContent = o.text;
    _.chain(o.props)
      .keys()
      .each((key) => {
        tspan.setAttribute(key, o.props[key]);
      })
      .value();
    tspan.setAttribute('style', 'font-family:"微软雅黑", Microsoft YaHei", Arial;');
    ele.appendChild(tspan);
  }
  ele.setAttribute('font-size', fontSize);
  ele.setAttribute('text-anchor', 'middle');
  ele.setAttribute('dominant-baseline', 'middle');
  return ele;
};

const prepareVerticalText = ({ entity, isGudong = false, options = {} }) => {
  entity.textList = {};
  entity.nameOffset = 0;
  entity.amountOffset = 0;
  const splitLength = 11;
  const maxLength = entity.settings?.maxLength || 22;
  const gap = 5;
  let totalHeight = 0;
  let nameHeight = 0;
  let nameWidth = 0;
  let statusHeight = 0;
  let amountHeight = 0;

  // 名称
  const nameList = [];
  const name = entity.name;
  let tempName = '';
  let tempSize = {};

  if (name) {
    if (entity.depth !== 0) {
      tempName = name.slice(0, splitLength);
      tempSize = measure.getTotal(tempName, entity.settings.fontSize);
      nameList.push({
        text: tempName,
        height: tempSize.height,
        width: tempSize.width,
        props: {
          fill: entity.settings.textColor,
        },
      });
      nameHeight += tempSize.height;

      if (name.length > splitLength) {
        tempName = `${name.slice(splitLength, maxLength)}${name.length > maxLength ? '...' : ''}`;
        nameList.push({
          text: tempName,
          height: measure.getTotal(name.slice(splitLength, maxLength), entity.settings.fontSize).height + gap,
          width: measure.getTotal(name.slice(splitLength, maxLength), entity.settings.fontSize).width,
          props: {
            fill: entity.settings.textColor,
          },
        });
        nameHeight += tempSize.height + gap;
      }
    } else {
      const tempSize = measure.getTotal(name, entity.settings.fontSize);
      nameList.push({
        text: name,
        height: tempSize.height + gap,
        width: tempSize.width,
        props: {
          fill: entity.settings.textColor,
        },
      });
      nameHeight += tempSize.height + gap;
      nameWidth = tempSize.width + 20;
      // 第一层节点进行限制
      // splitLength = 11
      // let maxStep = Math.ceil(entity.name.length / splitLength)
      // for (let i = 0; i < maxStep; i++) {
      //   let tempName = entity.name.slice(i * splitLength, (i + 1) * splitLength)
      //   let tempSize = measure.getTotal(tempName, entity.settings.fontSize)
      //   nameList.push({
      //     text: tempName,
      //     height: tempSize.height + gap,
      //     width: tempSize.width,
      //     props: {
      //       fill: entity.settings.textColor
      //     }
      //   })
      //   nameHeight += tempSize.height + gap
      // }
    }
  }
  // 公司名称totalHeight
  entity.nameHeight = nameHeight;
  entity.nameWidth = nameWidth;
  totalHeight += nameHeight;
  entity.textList = { nameList };

  // 简称
  let shortName = '';
  const shortNameList = [];
  let shortNameHeight = 0;
  if (!_.isEmpty(entity.shortObj)) {
    shortName = entity.shortObj.name;
    if (entity.depth !== 0) {
      tempName = shortName.slice(0, splitLength);
      tempSize = measure.getTotal(tempName, entity.settings.fontSize);
      shortNameList.push({
        text: tempName,
        height: tempSize.height,
        width: tempSize.width,
        props: {
          fill: entity.settings.textColor,
        },
      });
      shortNameHeight += tempSize.height;

      if (shortName.length > splitLength) {
        tempName = `${shortName.slice(splitLength, maxLength)}${shortName.length > maxLength ? '...' : ''}`;
        shortNameList.push({
          text: tempName,
          height: measure.getTotal(name.slice(splitLength, maxLength), entity.settings.fontSize).height + gap,
          width: measure.getTotal(name.slice(splitLength, maxLength), entity.settings.fontSize).width,
          props: {
            fill: entity.settings.textColor,
          },
        });
        shortNameHeight += tempSize.height + gap;
      }
    }
    entity.shortNameList = shortNameList;
  }

  // 上市股票或企业融资状态(企业状态)
  if (entity.public || entity.financing) {
    const statusText = entity.public || entity.financing;
    const limitStatusLength = 20;
    const statusList = [];
    let tempStatus = '';
    let tempSize = {};
    if (statusText) {
      tempStatus = statusText.slice(0, limitStatusLength);
      tempSize = measure.getTotal(tempStatus);
      statusList.push({
        text: tempStatus,
        height: tempSize.height + 4,
        width: tempSize.width,
        props: {
          fill: settings.companyNodeSettings.statusColor,
        },
      });
      statusHeight += tempSize.height + 4;
      if (statusText.length > limitStatusLength) {
        tempStatus = statusText.slice(limitStatusLength, limitStatusLength * 2) + (statusText.length > limitStatusLength * 2 ? '...' : '');
        tempSize = measure.getTotal(tempStatus);
        statusList.push({
          text: tempStatus,
          height: tempSize.height + 4,
          width: tempSize.width,
          props: {
            fill: settings.companyNodeSettings.statusColor,
          },
        });
        statusHeight += tempSize.height + 4;
      }
    }
    entity.textList.statusList = statusList;
    entity.statusHeight = statusHeight + 5;
    totalHeight += statusHeight + 5;
  }

  // 注册资本
  if (entity.amount) {
    const amount = entity.amount;
    const limitAmountLength = 24;
    const amountList = [];
    let temp = amount.slice(0, limitAmountLength);
    let tempSize = measure.getTotal(temp, settings.companyNodeSettings.statusSize);
    amountHeight = tempSize.height;
    amountList.push({
      text: temp,
      width: tempSize.width,
      height: tempSize.height,
      props: {
        fill: settings.companyNodeSettings.textColor2,
      },
    });
    if (amount.length > limitAmountLength) {
      temp = amount.slice(limitAmountLength, limitAmountLength * 2) + (amount.length > limitAmountLength * 2 ? '...' : '');
      tempSize = measure.getTotal(temp, settings.companyNodeSettings.statusSize);
      amountList.push({
        text: temp,
        width: tempSize.width,
        height: tempSize.height,
        props: {
          fill: settings.companyNodeSettings.textColor2,
        },
      });
      amountHeight += tempSize.height;
    }
    entity.textList.amountList = amountList;
    entity.amountHeight = amountHeight;
    totalHeight += amountHeight;
  }

  // calcoffset
  const calcHeight = totalHeight + settings.padding.h * 2;
  const resHeight = calcHeight > entity.settings.minHeight ? calcHeight : entity.settings.minHeight;
  if (name && (entity.public || entity.financing) && entity.amount) {
    // 名称+融资轮次或上市信息+注册资本
    const gap = (resHeight - nameHeight - amountHeight - statusHeight) / 3;
    entity.nameOffset = resHeight / 2 - gap - 2;
    entity.amountOffset = resHeight / 2 - gap - nameHeight - gap + 2;
  } else if (name && (entity.public || entity.financing)) {
    // 名称+融资伦茨或注册资本
    const gap = (resHeight - nameHeight - statusHeight) / 2;
    entity.nameOffset = resHeight / 2 - gap;
  } else if (name && entity.amount) {
    // 名称+注册资本
    const gap = (resHeight - nameHeight - amountHeight) / 3;
    entity.nameOffset = resHeight / 2 - gap - 4; // 4为偏移值
    entity.amountOffset = -resHeight / 2 + amountHeight + gap + 2; // 2为偏移值
  } else {
    entity.nameOffset = nameHeight / 2 - 2; // 2为偏移值
  }

  if (shortName && (entity.public || entity.financing) && entity.amount) {
    // 简称+融资轮次或上市信息+注册资本
    const gap = (resHeight - shortNameHeight - amountHeight - statusHeight) / 3;
    entity.shortNameOffset = resHeight / 2 - gap - 2;
  } else if (shortName && (entity.public || entity.financing)) {
    // 简称+融资伦茨或注册资本
    const gap = (resHeight - shortNameHeight - statusHeight) / 2;
    entity.shortNameOffset = resHeight / 2 - gap;
  } else if (shortName && entity.amount) {
    // 简称+注册资本
    const gap = (resHeight - shortNameHeight - amountHeight) / 3;
    entity.shortNameOffset = resHeight / 2 - gap - 4; // 4为偏移值
  } else {
    entity.shortNameOffset = shortNameHeight / 2 - 2; // 2为偏移值
  }

  // tag标签
  entity.tagList = [];
  entity.tagTotalSize = { width: 0, height: 0 };

  const formatTags = entity.tags;
  if (!_.isEmpty(formatTags)) {
    const gap = 0;
    const padding = 5;
    entity.tagTotalSize.width = 10; // 初始化padding-right
    const statusFilter = {
      在业: 'active',
      存续: 'active',
      筹建: 'active',
      仍注册: 'active',
      迁入: 'other',
      迁出: 'other',
      清算: 'negative',
      停业: 'negative',
      撤销: 'negative',
      吊销: 'negative',
      注销: 'negative',
    };
    formatTags.forEach((tag) => {
      const props = {};
      if (tag.type === 'ent_status') {
        const t = statusFilter[tag.text] || 'active';
        props.fill = settings.companyNodeSettings.status_color[t] && settings.companyNodeSettings.status_color[t].color;
        props.bg = settings.companyNodeSettings.status_color[t] && settings.companyNodeSettings.status_color[t].bgColor;
      } else {
        props.fill = settings.companyNodeSettings.tagsColor[tag.type] && settings.companyNodeSettings.tagsColor[tag.type].color;
        props.bg = settings.companyNodeSettings.tagsColor[tag.type] && settings.companyNodeSettings.tagsColor[tag.type].bgColor;
      }
      const singleTagSize = measure.getTotal(tag.text, settings.companyNodeSettings.tagSize);
      singleTagSize.width += 12;
      singleTagSize.height += 4;
      entity.tagList.push({
        type: tag.type,
        text: tag.text,
        offset: entity.tagTotalSize.width,
        size: singleTagSize,
        props,
      });
      entity.tagTotalSize.width += singleTagSize.width + padding + gap;
      entity.tagTotalSize.height = singleTagSize.height;
    });
  }

  // 受益人等标签
  const labelList = [];
  let labelHeight = 0;
  let labelWidth = 0;
  const labelGap = 5;
  if (entity.kzr) {
    labelList.push({
      text: '实际控制人',
      props: {
        fill: '#fff',
      },
      height: measure.getTotal('实际控制人', settings.companyNodeSettings.labelSize).height + labelGap,
      width: measure.getTotal('实际控制人', settings.companyNodeSettings.labelSize).width + 30,
    });
    labelHeight += measure.getTotal('实际控制人', settings.companyNodeSettings.labelSize).height + labelGap;
    labelWidth = measure.getTotal('实际控制人', settings.companyNodeSettings.labelSize).width + 30;
  }
  if (entity.syr) {
    // labelList.push({
    //   text: '最终受益人',
    //   props: {
    //     fill: '#fff'
    //   },
    //   height: measure.getTotal('最终受益人', settings.companyNodeSettings.labelSize).height + labelGap,
    //   width: measure.getTotal('最终受益人', settings.companyNodeSettings.labelSize).width + 30
    // })
    // labelHeight += (measure.getTotal('最终受益人', settings.companyNodeSettings.labelSize).height + labelGap)
    const syrPercent = entity.syr.split('%')[0];
    const syrText = +syrPercent && +syrPercent > 0 ? `最终受益人: ${entity.syr}` : '最终受益人';
    labelList.push({
      text: syrText,
      props: {
        fill: '#fff',
      },
      height: measure.getTotal(syrText, settings.companyNodeSettings.labelSize).height + labelGap,
      width: measure.getTotal(syrText, settings.companyNodeSettings.labelSize).width + 30,
    });
    labelHeight += measure.getTotal(syrText, settings.companyNodeSettings.labelSize).height + labelGap;
    labelWidth = measure.getTotal(syrText, settings.companyNodeSettings.labelSize).width + 30;
  }
  if (labelList.length > 0) {
    entity.textList.labelList = labelList;
    entity.labelInfo = {
      height: labelHeight + 10,
      width: labelWidth,
      bg: settings.companyNodeSettings.label[labelList.length - 1],
    };
  }
  entity.totalHeight = totalHeight;
};

const prepareHorizontalText = ({ entity, options = {} }) => {
  const maxLength = entity.settings?.maxLength || 24;
  entity.textList = {};
  entity.nameOffset = 0;
  entity.amountOffset = 0;
  // 名称部分
  let nameList = [];
  let nameText = entity.name.slice(0, maxLength) + (entity.name.length > maxLength ? '...' : '');
  if (entity.depth === 0) {
    nameText = entity.name;
  }
  nameList.push({
    text: nameText,
    props: {
      fill: entity.settings.textColor,
    },
  });
  if (entity.public || entity.financing) {
    const stateList = [
      {
        text: '【',
        props: {
          fill: entity.settings.statusColor,
        },
      },
      {
        text: entity.public || entity.financing,
        props: {
          fill: entity.settings.statusColor,
        },
      },
      {
        text: '】',
        props: {
          fill: entity.settings.statusColor,
        },
      },
    ];
    nameList = _.union(nameList, stateList);
    nameText += `【${entity.public || entity.financing}】`;
  }
  entity.nameSize = measure.getTotal(nameText, entity.settings.fontSize);
  entity.textList.nameList = nameList;

  // 注册资本
  const amountList = [
    {
      text: entity.amount,
      width: measure.getTotal(entity.amount, settings.companyNodeSettings.amountSize).width,
      height: measure.getTotal(entity.amount, settings.companyNodeSettings.amountSize).height,
      props: {
        fill: entity.settings.textColor2,
      },
    },
  ];
  entity.textList.amountList = amountList;
  entity.amountSize = measure.getTotal(entity.amount, entity.settings.amountSize);

  const calcWidth =
    entity.nameSize.width > entity.amountSize.width
      ? entity.nameSize.width + settings.padding.h * 2
      : entity.amountSize.width + settings.padding.h * 2;
  entity.totalWidth = calcWidth < entity.settings.minWidth ? entity.settings.minWidth : calcWidth;

  // calcOffset
  if (entity.amount) {
    const gap = (entity.settings.height - entity.amountSize.height - entity.nameSize.height) / 3;
    entity.nameOffset = gap;
    entity.amountOffset = gap - 2;
  }

  // 受益人等标签
  const labelList = [];
  let labelHeight = 0;
  let labelWidth = 0;
  if (entity.kzr) {
    labelList.push({
      text: '实际控制人',
      props: {
        fill: '#fff',
      },
      height: measure.getTotal('实际控制人', settings.companyNodeSettings.labelSize).height,
      width: measure.getTotal('实际控制人', settings.companyNodeSettings.labelSize).width + 30,
    });
    labelHeight += measure.getTotal('实际控制人', settings.companyNodeSettings.labelSize).height;
    labelWidth = measure.getTotal('实际控制人', settings.companyNodeSettings.labelSize).width + 30;
  }
  if (entity.syr) {
    // labelList.push({
    //   text: '最终受益人',
    //   props: {
    //     fill: '#fff'
    //   },
    //   height: measure.getTotal('最终受益人', settings.companyNodeSettings.labelSize).height,
    //   width: measure.getTotal('最终受益人', settings.companyNodeSettings.labelSize).width + 30
    // })
    // labelHeight += measure.getTotal('最终受益人', settings.companyNodeSettings.labelSize).height
    const syrPercent = entity.syr.split('%')[0];
    const syrText = +syrPercent && +syrPercent > 0 ? `最终受益人: ${entity.syr}` : '最终受益人';
    labelList.push({
      text: syrText,
      props: {
        fill: '#fff',
      },
      height: measure.getTotal(syrText, settings.companyNodeSettings.labelSize).height,
      width: measure.getTotal(syrText, settings.companyNodeSettings.labelSize).width + 30,
    });
    labelHeight += measure.getTotal(syrText, settings.companyNodeSettings.labelSize).height;
    labelWidth = measure.getTotal(syrText, settings.companyNodeSettings.labelSize).width + 30;
  }
  if (labelList.length > 0) {
    entity.textList.labelList = labelList;
    entity.labelInfo = {
      height: labelHeight + 10,
      width: labelWidth,
      bg: settings.companyNodeSettings.label[labelList.length - 1],
    };
  }

  // tag标签
  entity.tagList = [];
  entity.tagTotalSize = { width: 0, height: 0 };
  const formatTags = entity.tags;
  if (formatTags) {
    const gap = 0;
    const padding = 10;
    entity.tagTotalSize.width = 10; // 初始化padding-right
    const statusFilter = {
      在业: 'active',
      存续: 'active',
      筹建: 'active',
      迁入: 'other',
      迁出: 'other',
      清算: 'negative',
      停业: 'negative',
      撤销: 'negative',
      吊销: 'negative',
      注销: 'negative',
    };
    formatTags.forEach((tag) => {
      const props = {};
      if (tag.type === 'ent_status') {
        const t = statusFilter[tag.text];
        props.fill = settings.companyNodeSettings.status_color[t].color;
        props.bg = settings.companyNodeSettings.status_color[t].bgColor;
      } else {
        props.fill = settings.companyNodeSettings.tagsColor[tag.type].color;
        props.bg = settings.companyNodeSettings.tagsColor[tag.type].bgColor;
      }
      const singleTagSize = measure.getTotal(tag.text, settings.companyNodeSettings.tagSize);
      singleTagSize.width += 12;
      singleTagSize.height += 4;
      entity.tagList.push({
        type: tag.type,
        text: tag.text,
        offset: entity.tagTotalSize.width,
        size: singleTagSize,
        props,
      });
      entity.tagTotalSize.width += singleTagSize.width + padding + gap;
      entity.tagTotalSize.height = singleTagSize.height + 4;
    });
  }
};

const prepareLinkText = (data) => {
  let relation = '';
  if (data.data.isGudong) {
    const sourceName = data.parent && data.parent.data.name;
    relation = sourceName && sourceName.indexOf('有限合伙') > -1 ? '出资' : '持股';
  } else {
    relation = '投资';
  }
  data.data.relationText = relation;
  data.data.linkText = [];
  let percent = data.data.percent;
  if (data.data.percent && percent.split('%') && +percent.split('%')[0] !== 0) {
    data.data.linkText = [
      {
        text: percent,
        props: {
          fill: '#128bed',
        },
      },
    ];
  } else {
    percent = '';
  }

  // data.data.linkText.push(
  //   {
  //     text: relation,
  //     props: {
  //       fill: '#128bed'
  //     }
  //   })
  const text = percent;
  // let text = percent + relation
  const size = measure.getTotal(text, settings.defaultLineSettings.fontSize);
  const kgSize = {
    width: 46,
    height: 22,
  };
  const offset = 10;
  if (data.data.kg) {
    data.data.linkTotalSize = {
      width: size.width + kgSize.width + offset,
      height: size.height > kgSize.height ? size.height : kgSize.height,
    };
  }
  data.data.linkTextSize = size;
};

const generateUUID = (data) => {
  let d = new Date().getTime();
  if (window.performance && typeof window.performance.now === 'function') {
    d += performance.now(); // use high-precision timer if available
  }
  const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (d + Math.random() * 16) % 16 | 0;
    d = Math.floor(d / 16);
    return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
  });
  return uuid;
};

const isCompany = (data) => {
  const org = data.org;
  if (+org === 2) {
    return false;
  }
  if (data.keyNo && data.keyNo[0] === 'p') {
    return false;
  }
  if ((!org || org === -1) && data.name && data.name.length < 5) {
    return false;
  }
  return true;
};

const isPerson = (data) => {
  const org = data.org;
  if (+org === 2 || (data.keyNo && data.keyNo[0] === 'p') || ((!org || org === -1) && data.name && data.name.length < 5)) {
    return true;
  }
};

const createSettings = (node, isGudong) => {
  let specialSettings = {};
  if (node.depth === 0) {
    specialSettings = settings.centerNodeSettings;
  } else if (isCompany(node)) {
    specialSettings = settings.companyNodeSettings;
  } else if (isPerson(node)) {
    specialSettings = settings.personNodeSettings;
  }
  return {
    isGudong,
    x: 0,
    y: 0,
    width: 0,
    height: 0,
    maxSliblings: 0,
    textSize: {
      width: 0,
      height: 0,
    },
    topLeft: {
      x: 0,
      y: 0,
    },
    topCenter: {
      x: 0,
      y: 0,
    },
    topRight: {
      x: 0,
      y: 0,
    },
    rightCenter: {
      x: 0,
      y: 0,
    },
    bottomRight: {
      x: 0,
      y: 0,
    },
    bottomCenter: {
      x: 0,
      y: 0,
    },
    bottomLeft: {
      x: 0,
      y: 0,
    },
    leftCenter: {
      x: 0,
      y: 0,
    },
    pointsForLinkIn: [
      { x: 0, y: 0 },
      { x: 0, y: 0 },
      { x: 0, y: 0 },
      { x: 0, y: 0 },
      { x: 0, y: 0 },
    ],
    ...specialSettings,
  };
};

const foldNodes = (node) => {
  node.children = null;
  node.isFolded = false;
  if (node.depth < 1) {
    node.children = node.detailList;
    node.isFolded = true;
  }
  if (node.detailList && node.detailList.length > 0) {
    _.forEach(node.detailList, (d) => {
      foldNodes(d);
    });
  }
};

const jump = (data) => {
  if (data.keyNo) {
    const org = data.org || data.Org;
    let res = '';
    if (+org === 13) {
      // res = `/investor/${data.keyNo}`;
    } else if (+org === 20 || +org === 21) {
      res = `/group/${data.keyNo}`;
    } else if (+org === 6) {
      // res = `/simu_${data.keyNo}`;
    } else {
      res = `/firm/${data.keyNo}`;
    }
    if (!res) {
      window.open(res);
    }
  }
};

export default {
  createVerticalName,
  createRichText,
  prepareVerticalText,
  prepareLinkText,
  generateUUID,
  createSettings,
  isCompany,
  isPerson,
  foldNodes,
  prepareHorizontalText,
  jump,
};
