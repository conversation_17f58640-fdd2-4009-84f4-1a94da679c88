/* eslint-disable no-underscore-dangle */
/* eslint-disable consistent-return */
/* eslint-disable no-nested-ternary */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable func-names */
/* eslint-disable @typescript-eslint/no-this-alias */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-multi-assign */
/* eslint-disable no-loop-func */
/* eslint-disable no-param-reassign */
import { EventEmitter } from 'eventemitter3';
import _ from 'lodash';
import * as d3 from 'd3';
import resizeDetector from 'element-resize-detector';

import utils from './utils';
import { deviceInfo } from '../../utils/device-info';
import chartSettings from '../settings';
import chartLines from './lines';

let firstPosition = '';
export default class Chart extends EventEmitter {
  constructor(selector) {
    super();
    this.selector = selector;
    this.$selector = $(selector);
    this.isVip = true;
    this.specialBrowser = deviceInfo.isIE() || deviceInfo.isSafari();
    this.isVertical = false;
    this.filterType = '';
    this.filterValue = 0;
    this.filterChosenTags = [];
    this.filterRegisterStatus = false;
    this.isFullName = true;
    this.options = {
      showText: true,
      isEdit: false,
    };

    this.stratify = {
      nodeId: (d) => {
        return d.data.id;
      },
      linkId: (d) => {
        return d.target.data.id;
      },
    };
  }

  init(isVertical = true, options = {}) {
    if (!_.isEmpty(options)) {
      this.options = options;
    }
    this.isVertical = isVertical;
    // remove first
    this.$selector.find('svg').remove();
    const width = this.$selector.width();
    const height = this.$selector.height();
    this.size = {
      width,
      height,
    };
    this.svg = d3.select(this.selector).append('svg').attr('width', width).attr('height', height).attr('cursor', 'move');

    // prepare root g
    this.root = this.svg.append('g').attr('class', 'rootG');
    this.transform = {
      k: 1,
      x: 0,
      y: 0,
    };

    this.root
      .append('defs')
      .append('filter')
      .attr('id', 'blur')
      .append('feGaussianBlur')
      .attr('in', 'SourceGraphic')
      .attr('stdDeviation', '3');

    // create right g
    this.rightContainer = this.root.append('g').attr('transform', `translate(${this.size.width / 2}, ${this.size.height / 2})`);
    this.rightLinksContainer = this.rightContainer.append('g').classed('links', true);
    this.rightNodesContainer = this.rightContainer.append('g').classed('nodes', true);

    // // create left g
    this.leftContainer = this.root.append('g').attr('transform', `translate(${this.size.width / 2}, ${this.size.height / 2})`);
    this.leftLinksContainer = this.leftContainer.append('g').classed('links', true);
    this.leftNodesContainer = this.leftContainer.append('g').classed('nodes', true);
    let zoom = null;
    zoom = d3
      .zoom()
      .scaleExtent(chartSettings.scaleRange)
      .on('zoom', () => {
        const transform = d3.zoomTransform(this.svg.node());
        this.root.attr('transform', transform);
        this.emit('onScale', { oldTransform: this.transform, newTransform: { ...transform } });
        this.transform = { ...transform };
      });

    this.scaleStep = (chartSettings.scaleRange[1] - chartSettings.scaleRange[0]) / (1.0 * chartSettings.scaleLevel);

    // enable zoom
    this.svg
      .call(zoom)
      // disable dbclick for zooming
      .on('dblclick.zoom', null);
    // .on('wheel.zoom', null)

    this.zoom = zoom;
    if (this.isVertical) {
      this.tree = d3
        .tree()
        .nodeSize(chartSettings.nodeSize)
        .separation((a, b) => {
          return a.parent === b.parent ? 1 : chartSettings.nodeSeparation;
        });
    } else {
      this.tree = d3
        .tree()
        .nodeSize(chartSettings.horizontalNodeSize)
        .separation((a, b) => {
          return a.parent === b.parent ? 1 : chartSettings.nodeSeparation;
        });
    }

    this.attachResizeDetector();
  }

  attachResizeDetector() {
    this.resizeDetector = resizeDetector();
    this.resizeDetector.listenTo(
      this.$selector[0],
      _.debounce(() => {
        this.size = {
          width: this.$selector.width(),
          height: this.$selector.height(),
        };
        this.svg.attr('width', this.size.width).attr('height', this.size.height);
        this.leftContainer.attr('transform', `translate(${this.size.width / 2}, ${this.size.height / 2})`);
        this.rightContainer.attr('transform', `translate(${this.size.width / 2}, ${this.size.height / 2})`);
      }, 200)
    );
  }

  cleanup() {
    this.resizeDetector && this.resizeDetector.removeAllListeners(this.$selector[0]);
  }

  render(data, options = {}) {
    if (firstPosition) {
      // 使用暴力方式解决重置位置不对的问题
      this.initPosition();
    }
    if (!_.isEmpty(options)) {
      this.options = options;
    }
    this.data = data;
    utils.foldNodes(this.data.gdTree);
    utils.foldNodes(this.data.investTree);
    this.refresh();
  }

  refresh({ source, options } = { source: null, options: {} }) {
    if (!_.isEmpty(options)) {
      this.options = options;
    }
    this.updateTree(true, source);
    this.updateTree(false, source);
    this.filterTreeByRegisterStatus(this.filterRegisterStatus);
    this.filterTreeByFullOrShortName(this.isFullName);
  }

  prepareNodes(nodes, isGudong) {
    // 此处计算每个点的位置需要区分纵向横向排列
    nodes.forEach((d) => {
      d.isVip = this.isVip;
      utils.prepareLinkText(d);
      if (this.isVertical) {
        utils.prepareVerticalText({ entity: d.data, isVertical: true, isGudong, options: this.options });
      } else {
        utils.prepareHorizontalText({ entity: d.data, options: this.options });
      }

      const maxDepth = _.maxBy(nodes, (o) => o.depth).depth;
      if (this.isVertical) {
        // control position
        d.data.settings.x = d.x;
        // control size
        // 纵向宽度固定 高度计算
        const calcHeight = d.data.totalHeight + chartSettings.padding.h * 2;
        d.data.settings.height = calcHeight > d.data.settings.minHeight ? calcHeight : d.data.settings.minHeight;
        if (d.depth === 0) {
          d.data.settings.height = calcHeight;
          if (d.data.nameWidth) {
            d.data.settings.width = d.data.nameWidth;
          }
        }

        let startY = _.find(nodes, (o) => o.depth === 0).data.settings.height / 2 + chartSettings.gap1;
        // startY为节点中心点 应为不同位置 这样不同高度的点会在同一水平线
        for (let i = 1; i <= maxDepth; ++i) {
          const list = _.filter(nodes, (o) => o.depth === i);
          const maxHeight = _.maxBy(list, (o) => o.data.settings.height).data.settings.height;
          _.each(list, (o) => {
            // 偏移问题
            o.y = o.data.settings.y = startY;
            o.data.settings.maxSliblings = maxHeight;
          });
          startY += maxHeight + chartSettings.gap;
        }
      } else {
        // control position
        const x = d.x;
        d.y = d.data.settings.y = x;

        if (d.depth === 0) {
          d.x = isGudong ? -d.data.settings.width / 2 : d.data.settings.width / 2;
        }
        // control size
        d.data.settings.width = d.data.totalWidth;
        let startX = _.find(nodes, (o) => o.depth === 0).data.settings.width / 2 + chartSettings.gap2;
        for (let i = 1; i <= maxDepth; ++i) {
          const list = _.filter(nodes, (o) => o.depth === i);
          const maxWidth = _.maxBy(list, (o) => o.data.settings.width).data.settings.width;
          _.each(list, (o) => {
            // o.data.settings.x = startX - (maxWidth - o.data.settings.width) / 2 + maxWidth / 2
            // o.data.settings.x = (startX + o.data.settings.width / 2) * (isGudong ? -1 : 1)
            o.data.settings.x = isGudong
              ? -startX + (maxWidth - o.data.settings.width) / 2 - maxWidth / 2
              : startX - (maxWidth - o.data.settings.width) / 2 + maxWidth / 2;
            o.data.settings.maxSliblings = maxWidth;
          });
          startX += chartSettings.gap2 + maxWidth;
        }
      }
    });

    _.each(nodes, (o) => {
      if (this.isVertical) {
        const halfHeight = o.data.settings.height / 2;
        let halfMaxHeight = 0;
        const halfWidth = o.data.settings.width / 2;
        let offset = 0;
        if (o.depth !== 0) {
          halfMaxHeight = o.data.settings.maxSliblings / 2;
          offset = halfMaxHeight - halfHeight - 2 || 0; // 2为偏移值
        }
        o.data.settings.topLeft = {
          x: o.data.settings.x - halfWidth,
          y: o.data.settings.y - halfHeight - offset,
        };

        o.data.settings.topCenter = {
          x: o.data.settings.x,
          y: o.data.settings.y - halfHeight - offset,
        };

        o.data.settings.topRight = {
          x: o.data.settings.x + halfWidth,
          y: o.data.settings.y - halfHeight - offset,
        };

        o.data.settings.rightCenter = {
          x: o.data.settings.x + halfWidth,
          y: o.data.settings.y,
        };

        o.data.settings.bottomRight = {
          x: o.data.settings.x + halfWidth,
          y: o.data.settings.y + halfHeight - offset,
        };

        o.data.settings.bottomCenter = {
          x: o.data.settings.x,
          y: o.data.settings.y + halfHeight - offset,
        };

        o.data.settings.bottomLeft = {
          x: o.data.settings.x - halfWidth,
          y: o.data.settings.y + halfHeight - offset,
        };

        o.data.settings.leftCenter = {
          x: o.data.settings.x - halfWidth,
          y: o.data.settings.y,
        };
      } else {
        const halfHeight = o.data.settings.height / 2;
        const width = o.data.settings.width;
        const halfWidth = width / 2;
        o.data.settings.topLeft = {
          x: o.data.settings.x - halfWidth,
          y: o.data.settings.y - halfHeight,
        };

        o.data.settings.topCenter = {
          x: o.data.settings.x,
          y: o.data.settings.y - halfHeight,
        };

        o.data.settings.topRight = {
          x: o.data.settings.x + halfWidth,
          y: o.data.settings.y - halfHeight,
        };

        o.data.settings.rightCenter = {
          x: o.data.settings.x + halfWidth,
          y: o.data.settings.y,
        };

        o.data.settings.bottomRight = {
          x: o.data.settings.x + halfWidth,
          y: o.data.settings.y + halfHeight,
        };

        o.data.settings.bottomCenter = {
          x: o.data.settings.x,
          y: o.data.settings.y + halfHeight,
        };

        o.data.settings.bottomLeft = {
          x: o.data.settings.x - halfWidth,
          y: o.data.settings.y + halfHeight,
        };

        o.data.settings.leftCenter = {
          x: o.data.settings.x - halfWidth,
          y: o.data.settings.y,
        };
      }
    });
  }

  prepareLinks(links, isGudong) {
    links.forEach((d) => {
      d.id = utils.generateUUID();
      _.assignIn(d, chartSettings.defaultLineSettings);
      let p1;
      let p2;
      let p3;
      let p4;
      let p5;
      if (this.isVertical) {
        // p1 为起点
        p1 = {
          x: d.source.data.settings.bottomCenter.x,
          y: (isGudong ? -1 : 1) * d.source.data.settings.bottomCenter.y,
        };
        // p5 为终点
        p5 = {
          x: d.source === d.target ? d.source.data.settings.topCenter.x : d.target.data.settings.topCenter.x,
          y: d.srouce === d.target ? d.source.data.settings.topCenter.y : (isGudong ? -1 : 1) * d.target.data.settings.topCenter.y,
        };
        if (d.source.depth === 0) {
          p2 = {
            x: d.source.data.settings.bottomCenter.x,
            y: ((isGudong ? -1 : 1) * (d.source.data.settings.bottomCenter.y + d.target.data.settings.topCenter.y)) / 2,
          };

          p3 = {
            x: d.source.data.settings.bottomCenter.x,
            y: ((isGudong ? -1 : 1) * (d.source.data.settings.bottomCenter.y + d.target.data.settings.topCenter.y)) / 2,
          };

          p4 = {
            x: d.target.data.settings.topCenter.x,
            y: ((isGudong ? -1 : 1) * (d.source.data.settings.bottomCenter.y + +d.target.data.settings.topCenter.y)) / 2,
          };
        } else {
          p2 = {
            x: d.source.data.settings.bottomCenter.x,
            y:
              (isGudong ? -1 : 1) *
              (d.source.data.settings.bottomCenter.y + (d.source.data.settings.maxSliblings - d.source.data.settings.height)),
          };

          p3 = {
            x: d.source.data.settings.bottomCenter.x,
            y:
              (isGudong ? -1 : 1) *
              (d.source.data.settings.bottomCenter.y +
                (d.source.data.settings.maxSliblings - d.source.data.settings.height) +
                chartSettings.gap / 2),
          };

          p4 = {
            x: d.target.data.settings.topCenter.x,
            y:
              (isGudong ? -1 : 1) *
              (d.source.data.settings.bottomCenter.y +
                (d.source.data.settings.maxSliblings - d.source.data.settings.height) +
                chartSettings.gap / 2),
          };
        }
      } else if (isGudong) {
        p1 = {
          x: d.source.data.settings.leftCenter.x,
          y: -d.source.data.settings.leftCenter.y,
        };
        p5 = {
          x: d.source === d.target ? d.source.data.settings.leftCenter.x : d.target.data.settings.rightCenter.x,
          y: d.source === d.target ? d.source.data.settings.leftCenter.y : -d.target.data.settings.rightCenter.y,
        };
        if (d.source.depth === 0) {
          p2 = {
            x: (d.source.data.settings.leftCenter.x + d.target.data.settings.rightCenter.x) / 2,
            y: -d.source.data.settings.leftCenter.y,
          };
          p3 = {
            x: (d.source.data.settings.leftCenter.x + d.target.data.settings.rightCenter.x) / 2,
            y: -d.source.data.settings.leftCenter.y,
          };
          p4 = {
            x: (d.source.data.settings.leftCenter.x + d.target.data.settings.rightCenter.x) / 2,
            y: -d.target.data.settings.rightCenter.y,
          };
        } else {
          p2 = {
            x: d.source.data.settings.leftCenter.x + d.target.data.settings.maxSliblings - d.source.data.settings.width,
            y: -d.source.data.settings.leftCenter.y,
          };
          p3 = {
            x: (d.source.data.settings.leftCenter.x + d.target.data.settings.rightCenter.x) / 2,
            y: -d.source.data.settings.leftCenter.y,
          };
          p4 = {
            x: (d.source.data.settings.leftCenter.x + d.target.data.settings.rightCenter.x) / 2,
            y: -d.target.data.settings.rightCenter.y,
          };
        }
      } else {
        p1 = {
          x: d.source.data.settings.rightCenter.x,
          y: d.source.data.settings.rightCenter.y,
        };
        p5 = {
          x: d.source === d.target ? d.source.data.settings.leftCenter.x : d.target.data.settings.leftCenter.x,
          y: d.source === d.target ? d.source.data.settings.leftCenter.y : d.target.data.settings.leftCenter.y,
        };
        if (d.source.depth === 0) {
          p2 = {
            x: (d.source.data.settings.rightCenter.x + d.target.data.settings.leftCenter.x) / 2,
            y: d.source.data.settings.rightCenter.y,
          };
          p3 = {
            x: (d.source.data.settings.rightCenter.x + d.target.data.settings.leftCenter.x) / 2,
            y: d.source.data.settings.rightCenter.y,
          };
          p4 = {
            x: (d.source.data.settings.rightCenter.x + d.target.data.settings.leftCenter.x) / 2,
            y: d.target.data.settings.leftCenter.y,
          };
        } else {
          p2 = {
            x: d.source.data.settings.rightCenter.x + d.target.data.settings.maxSliblings - d.source.data.settings.width,
            y: d.source.data.settings.rightCenter.y,
          };
          p3 = {
            x: (d.source.data.settings.rightCenter.x + d.target.data.settings.leftCenter.x) / 2,
            y: d.source.data.settings.rightCenter.y,
          };
          p4 = {
            x: (d.source.data.settings.rightCenter.x + d.target.data.settings.leftCenter.x) / 2,
            y: d.target.data.settings.leftCenter.y,
          };
        }
      }

      d.target.data.settings.pointsForLinkIn = [p1, p2, p3, p4, p5];
      // d.source.data.targetSettings = d.target.data.settings
      // d.target.data.sourceSettings = d.source.data.settings
    });
  }

  updateTree(isGudong, source) {
    const self = this;
    const haveSource = !!source;
    const rootNode = d3.hierarchy(isGudong ? this.data.gdTree : this.data.investTree);
    const nodesContainer = isGudong ? this.leftNodesContainer : this.rightNodesContainer;
    this.nodesContainer = nodesContainer;
    const linksContainer = isGudong ? this.leftLinksContainer : this.rightLinksContainer;
    if (!source) {
      source = rootNode;
    }
    rootNode.eachBefore((node) => {
      node.data.settings = utils.createSettings(node.data, isGudong);
      node.data.settings = _.assignIn(
        node.data.settings,
        this.isVertical ? chartSettings.verticalNodeSettings : chartSettings.horizontalNodeSettings
      );
    });
    this.tree(rootNode);
    const nodes = rootNode.descendants();
    if (isGudong) {
      this.gdNodes = nodes;
    } else {
      this.investNodes = nodes;
    }
    const links = rootNode.links();
    this.prepareNodes(nodes, isGudong);
    this.prepareLinks(links, isGudong);

    const node = nodesContainer.selectAll('g.node').data(nodes, self.stratify.nodeId);

    const nodeEnter = node
      .enter()
      .append('g')
      .classed('node', true)
      .attr('cursor', (d) => d.data.settings.cursor)
      .attr('fill-opacity', 0)
      .attr('stroke-opacity', 0)
      .attr('transform', () => {
        if (source.depth !== 0) {
          return `translate(${source.data.settings.x}, ${(isGudong ? -1 : 1) * source.y})`;
        }
        return 'translate(0, 0)';
      });

    nodeEnter
      .append('rect')
      .attr('class', 'node-rect')
      .attr('fill', (d) => d.data.settings.bgColor)
      // .attr('fill', 'transparent')
      .attr('width', (d) => d.data.settings.width)
      .attr('stroke', (d) => d.data.settings.innerBorderColor)
      .attr('stroke-width', (d) => d.data.settings.innerBorderWidth)
      .attr('height', (d) => d.data.settings.height)
      .attr('x', (d) => -d.data.settings.width / 2)
      .attr('y', (d) => {
        let offset = 0;
        if (this.isVertical && d.depth !== 0) {
          offset = (d.data.settings.maxSliblings - d.data.settings.height) / 2;
        }
        return -d.data.settings.height / 2 + offset * (isGudong ? 1 : -1);
      });
    // .attr('rx', d => d.data.settings.radius)
    // .attr('ry', d => d.data.settings.radius)

    // 公司名称
    const textG = nodeEnter
      .append('g')
      .attr('class', 'text-g')
      .attr('style', (d) => `${(!this.isVip && d.depth <= 1) || this.isVip ? '' : 'filter:url(#blur)'}`);
    // .attr('transform', d => {
    //   let offset = 0
    //   if (this.isVertical && d.depth !== 0) {
    //     offset = (d.data.settings.maxSliblings - d.data.settings.height) / 2
    //   }
    //   let offset2 = (isGudong ? 0 : (d.data.textList.nameList.length >= 2 ? 0 : 1))
    //   return `translate(0, ${-d.data.nameOffset - ((offset) * (isGudong ? -1 : 1)) - offset2 || 0})`
    //   // return `translate(0, ${-d.data.nameOffset - (offset) * (isGudong ? -1 : 1) || 0})`
    // })

    textG
      .append(function (d) {
        // isVertical
        if (self.isVertical) {
          return utils.createVerticalName(d.data.textList.nameList, d.data.settings.fontSize);
        }
        return utils.createRichText(d.data.textList.nameList, d.data.settings.fontSize);
      })
      .attr('transform', () => {
        if (this.specialBrowser) {
          return 'translate(0, 5)';
        }
        return 'translate(0, 0)';
      });

    // 简称
    const shorNameG = nodeEnter
      .filter((d) => {
        return !_.isEmpty(d.data.shortObj);
      })
      .append('g')
      .attr('class', 'short-text-g')
      .attr('style', (d) => `${(!this.isVip && d.depth <= 1) || this.isVip ? '' : 'filter:url(#blur)'}`);

    shorNameG.append(function (d) {
      if (self.isVertical) {
        return utils.createVerticalName(d.data.shortNameList, d.data.settings.fontSize);
      }
      return utils.createRichText(d.data.shortNameList, d.data.settings.fontSize);
    });
    //   .attr('transform', d => {
    //     if (this.specialBrowser) {
    //       return 'translate(0, 5)'
    //     } else {
    //       return 'translate(0, 0)'
    //     }
    //   })

    const amountText = nodeEnter
      .filter((d) => d.data.amount)
      .append('g')
      .attr('class', 'text-amount')
      .attr('transform', (d) => {
        let offset = 0;
        if (this.isVertical) {
          if (d.depth !== 0) {
            offset = (d.data.settings.maxSliblings - d.data.settings.height) / 2;
          }
          return `translate(0, ${-d.data.amountOffset + (this.specialBrowser ? 3 : 0) - offset || 0})`;
        }
        return `translate(${-d.data.settings.width / 2 + d.data.amountSize.width / 2 + chartSettings.padding.h}, ${d.data.amountOffset})`;
      });

    amountText.append(function (d) {
      return utils.createVerticalName(d.data.textList.amountList, d.data.settings.statusSize);
    });

    if (this.isVertical) {
      // 融资轮次或上市企业状态
      const entStatus = nodeEnter
        .filter((d) => d.data.public || d.data.financing)
        .append('g')
        .attr('class', 'text-status')
        .attr('style', (d) => `${(!this.isVip && d.depth <= 1) || this.isVip ? '' : 'filter:url(#blur)'}`);
      entStatus
        .append('rect')
        .attr('width', (d) => d.data.settings.width - 2)
        .attr('height', (d) => d.data.statusHeight)
        .attr('x', (d) => -d.data.settings.width / 2 + 1)
        .attr('fill', chartSettings.companyNodeSettings.statusBg);
      entStatus
        .append(function (d) {
          return utils.createVerticalName(d.data.textList.statusList, d.data.settings.statusSize);
        })
        .attr('transform', () => {
          return `translate(0, ${this.specialBrowser ? 8 : 4})`;
        });
    }

    nodeEnter
      .append('rect')
      .attr('class', 'node-handle-rect')
      .attr('fill', 'transparent')
      .attr('width', (d) => d.data.settings.width + 8)
      .attr('stroke', 'transparent')
      .attr('stroke-width', (d) => d.data.settings.innerBorderWidth)
      .attr('height', (d) => d.data.settings.height + 8)
      .attr('x', (d) => -d.data.settings.width / 2 - 4)
      .attr('y', (d) => {
        let offset = 0;
        if (this.isVertical && d.depth !== 0) {
          offset = (d.data.settings.maxSliblings - d.data.settings.height) / 2;
        }
        return -d.data.settings.height / 2 + offset * (isGudong ? -1 : 1) - 1;
      })
      // .attr('rx', d => d.data.settings.radius)
      // .attr('ry', d => d.data.settings.radius)
      .on('mouseover', function (d) {
        // 蚂蚁爬效果 & 显示弹框更多信息 & rect添加outerBorder
        if (d.depth !== 0) {
          const links = linksContainer.selectAll(`[link-flow-animation-id="${d.data.id}"]`);
          const links2 = linksContainer.selectAll(`[target-link-flow-animation-id="${d.data.id}"]`);
          links
            .classed('link-flow-in', function () {
              d3.select(this.parentNode).raise();
              const a = d3.select(this).attr('link-flow-style');
              return a === 'in';
            })
            .classed('link-flow-out', function () {
              d3.select(this.parentNode).raise();
              const a = d3.select(this).attr('link-flow-style');
              return a === 'out';
            })
            .attr('stroke', chartSettings.defaultLineSettings.flowLineColor)
            .attr('stroke-opacity', chartSettings.defaultLineSettings.flowLineOpacity);
          links2
            .classed('link-flow-in', function () {
              d3.select(this.parentNode).raise();
              const a = d3.select(this).attr('link-flow-style');
              return a === 'in';
            })
            .classed('link-flow-out', function () {
              d3.select(this.parentNode).raise();
              const a = d3.select(this).attr('link-flow-style');
              return a === 'out';
            })
            .attr('stroke', chartSettings.defaultLineSettings.flowLineColor)
            .attr('stroke-opacity', chartSettings.defaultLineSettings.flowLineOpacity);
        }
        if (d.data.id && d.data.id.split('-')[0] !== 'others') {
          const $sender = $(window.event.target);
          let position = $sender.position();
          if (position.left === 0 && position.top === 0) {
            // 都是0的情况下，说明没有取到正确值，因此做个补充
            const rect = $sender[0].getBoundingClientRect();
            position = {
              left: rect.x,
              top: rect.y,
            };
          }

          let size = {
            width: $sender.width(),
            height: $sender.height(),
          };
          if (size.width === 0 && size.height === 0) {
            // 都是0的情况下，说明没有取到正确值，因此做个补充
            const rect = $sender[0].getBBox();
            size = {
              width: rect.width,
              height: rect.height,
            };
          }
          self.emit('onDataNodeMouseover', {
            eid: (d.parent && d.parent.data.keyNo) || '',
            data: d.data,
            position,
            size,
            isGudong,
          });
        }
        const parent = d3.select(this.parentNode);
        if (d.depth !== 0) {
          parent.selectAll('.text-g').selectAll('tspan').attr('fill', '#000');
        }
      })
      .on('mouseout', function (d) {
        const links = linksContainer.selectAll(`[link-flow-animation-id="${d.data.id}"]`);
        links
          .classed('link-flow-in', false)
          .classed('link-flow-out', false)
          .attr('stroke', chartSettings.defaultLineSettings.lineColor)
          .attr('stroke-opacity', chartSettings.defaultLineSettings.lineOpacity);

        const links2 = linksContainer.selectAll(`[target-link-flow-animation-id="${d.data.id}"]`);
        links2
          .classed('link-flow-in', false)
          .classed('link-flow-out', false)
          .attr('stroke', chartSettings.defaultLineSettings.lineColor)
          .attr('stroke-opacity', chartSettings.defaultLineSettings.lineOpacity);

        self.emit('onDataNodeMouseout', {
          data: d.data,
        });
        const parent = d3.select(this.parentNode);
        if (d.depth !== 0) {
          parent.selectAll('.text-g').selectAll('tspan').attr('fill', '#333');
        }
      });

    const circleG = nodeEnter
      .filter((d) => !d.data.syr && !d.data.kzr && (this.isVip || (!this.isVip && d.depth <= 1)))
      .append('g')
      .attr('class', 'circle-g')
      .attr('visibility', function (d) {
        return d.data.hasChildren && d.depth !== 0 ? 'visible' : 'hidden';
      });

    circleG
      .append('circle')
      .attr('class', 'circle')
      .attr('fill', '#fff')
      .attr('stroke', (d) => {
        return !d.data.isFolded ? d.data.settings.activeCircleColor : d.data.settings.circleColor;
      })
      .attr('r', (d) => d.data.settings.circleRadius);

    circleG
      .filter((d) => d.data.hasChildren && !d.data.syr && !d.data.kzr)
      .append('line')
      .attr('class', 'vertical-line')
      .attr('stroke', (d) => {
        return !d.data.isFolded ? d.data.settings.activeCircleColor : d.data.settings.circleColor;
      })
      .attr('stroke-width', (d) => d.data.settings.circleWidth)
      .attr('x1', 0)
      .attr('y1', (d) => -d.data.settings.circleRadius + 5)
      .attr('x2', 0)
      .attr('y2', (d) => d.data.settings.circleRadius - 5);

    circleG
      .append('line')
      .attr('class', 'horizontal-line')
      .attr('stroke', (d) => {
        return !d.data.isFolded ? d.data.settings.activeCircleColor : d.data.settings.circleColor;
      })
      .attr('stroke-width', (d) => d.data.settings.circleWidth)
      .attr('x1', (d) => -d.data.settings.circleRadius + 5)
      .attr('y1', 0)
      .attr('x2', (d) => d.data.settings.circleRadius - 5)
      .attr('y2', 0);

    circleG
      .append('circle')
      .attr('class', 'handle-circle')
      .attr('fill', 'transparent')
      .attr('stroke', (d) => {
        return !d.data.isFolded ? d.data.settings.activeCircleColor : d.data.settings.circleColor;
      })
      .attr('r', (d) => d.data.settings.circleRadius)
      .on('click', function (d) {
        if (d.data.hasChildren) {
          if (_.isEmpty(d.data.children) && _.isEmpty(d.data.detailList)) {
            self.emit('onRequireData', { d, isGudong });
          } else {
            d.data.children = d.data.isFolded ? null : _.clone(d.data.detailList);
            d.data.isFolded = !d.data.isFolded;
            if (d.data.isFolded) {
              self.filterTreeByPercent({ type: self.filterType, value: self.filterValue, source: d });
              if (!_.isEmpty(self.filterChosenTags)) {
                self.filterTreeByTags(isGudong, self.filterChosenTags, d);
              }
            }
            self.filterTreeByRegisterStatus(self.filterRegisterStatus);
            self.filterTreeByFullOrShortName(self.isFullName);
            self.emit('onRequireUpdate', { d, isGudong });
          }
        }
      });

    const tagG = nodeEnter
      .filter((d) => this.isVip || (!this.isVip && d.depth <= 1))
      .append('g')
      .attr('class', 'tags-g');

    const tag = tagG
      .selectAll('single-tag')
      .data((d) => d.data.tagList)
      .enter()
      .append('g')
      .attr('transform', (d) => {
        return `translate(${d.offset}, ${this.specialBrowser ? -1 : 0})`;
      });
    tag
      .filter((d) => {
        return d.type === 'ent_status';
      })
      .attr('class', 'tag-register')
      .attr('tag-register-type', (d) => d.text)
      .attr('visibility', 'hidden');

    tag
      .append('rect')
      .attr('fill', (d) => d.props.bg)
      .attr('width', (d) => d.size.width)
      .attr('height', (d) => (this.specialBrowser ? d.size.height + 2 : d.size.height))
      .attr('transform', (d) => `translate(0, ${-d.size.height / 2})`)
      .attr('rx', 2)
      .attr('ry', 2);

    tag
      .append('text')
      .attr('fill', (d) => d.props.fill)
      .attr('font-size', '12')
      .attr('transform', 'translate(6, 4.5)')
      .text((d) => d.text);

    // 编辑按钮
    const optionsG = nodeEnter
      .append('g')
      .attr('class', 'node-options')
      // .attr('transform', d => {
      //   let offset = 0
      //   if (d.depth !== 0 && this.isVertical) {
      //     offset = (d.data.settings.maxSliblings - d.data.settings.height) / 2
      //   }
      //   return `translate(${d.data.settings.width / 2}, ${-d.data.settings.height / 2 - offset})`
      // })
      .attr('visibility', (d) => (this.options.isEdit && d.depth !== 0 ? 'hidden' : 'visible'));

    optionsG
      .append('circle')
      .attr('fill', (d) => d.data.settings.editCircleBg)
      .attr('r', (d) => d.data.settings.editCircleRadius);

    optionsG
      .append('line')
      .attr('stroke', '#fff')
      .attr('stroke-width', 1)
      .attr('x1', (d) => d.data.settings.editCircleRadius / 2)
      .attr('y1', (d) => d.data.settings.editCircleRadius / 2)
      .attr('x2', (d) => -d.data.settings.editCircleRadius / 2)
      .attr('y2', (d) => -d.data.settings.editCircleRadius / 2);

    optionsG
      .append('line')
      .attr('stroke', '#fff')
      .attr('stroke-width', 1)
      .attr('x1', (d) => -d.data.settings.editCircleRadius / 2)
      .attr('y1', (d) => d.data.settings.editCircleRadius / 2)
      .attr('x2', (d) => d.data.settings.editCircleRadius / 2)
      .attr('y2', (d) => -d.data.settings.editCircleRadius / 2);

    optionsG
      .append('circle')
      .attr('fill', 'transparent')
      .attr('r', (d) => d.data.settings.editCircleRadius)
      .on('click', (d) => {
        if (d.parent && d.parent.data) {
          d.parent.data.isFolded = false;
        }
        const tempChildren = _.reject(d.parent.data.children, (node) => node.id === d.data.id);
        d.parent.data.children = tempChildren;
        self.emit('onRequireUpdate', { d: d.parent, isGudong });
      });

    // 实际控制人标签
    const labelG = nodeEnter
      .filter((d) => d.data.labelInfo && (this.isVip || (!this.isVip && d.depth <= 1)))
      .append('g')
      .attr('class', 'label-g');

    labelG
      .append('rect')
      .attr('fill', (d) => d.data.labelInfo.bg)
      .attr('width', (d) => d.data.labelInfo.width)
      .attr('height', (d) => d.data.labelInfo.height)
      .attr('x', (d) => -d.data.labelInfo.width / 2)
      .attr('rx', chartSettings.centerNodeSettings.radius)
      .attr('ry', chartSettings.centerNodeSettings.radius);

    labelG
      .append('path')
      .attr('fill', (d) => d.data.labelInfo.bg)
      .attr('d', () => {
        if (this.isVertical) {
          return 'M0,0 L5,-5 L-5,-5 L0,0 Z';
        }
        if (isGudong) {
          return 'M0,0 L-5,5 L-5,-5 L0,0 Z';
        }
        return 'M0,0 L5,5 L5,-5 L0,0 Z';
      })
      .attr('transform', (d) => {
        if (this.isVertical) {
          return `translate(0, ${d.data.labelInfo.height + 5})`;
        }
        const x = (isGudong ? 1 : -1) * (d.data.labelInfo.width / 2 + 5);
        return `translate(${x}, ${d.data.labelInfo.height / 2})`;
      });

    labelG
      .append(function (d) {
        // isVertical
        return utils.createVerticalName(d.data.textList.labelList, chartSettings.companyNodeSettings.labelSize);
      })
      .attr('transform', `translate(0, ${this.specialBrowser ? 8 : 5})`);

    const updateNode = node
      .merge(nodeEnter)
      .transition()
      .duration(chartSettings.duration)
      .ease(chartSettings.ease)
      .attr('transform', (d) => {
        let y = d.data.settings.y;
        if (isGudong) {
          y = -d.data.settings.y;
        }
        // return `translate(${d.data.settings.x}, ${-d.data.settings.height / 2 - (offset) * (isGudong ? -1 : 1)})`
        return `translate(${d.data.settings.x}, ${y})`;
        // return `translate(${d.data.settings.x}, ${(isGudong ? -1 : 1) * d.data.settings.y})`
      })
      .attr('fill-opacity', 1)
      .attr('stroke-opacity', 1);

    updateNode.selectAll('.node-rect').attr('y', (d) => {
      let offset = 0;
      if (this.isVertical && d.depth !== 0) {
        offset = (d.data.settings.maxSliblings - d.data.settings.height) / 2;
      }
      return -d.data.settings.height / 2 + offset * (isGudong ? 1 : -1);
    });

    updateNode.selectAll('.text-g').attr('transform', (d) => {
      let offset = 0;
      if (this.isVertical && d.depth !== 0) {
        offset = (d.data.settings.maxSliblings - d.data.settings.height) / 2;
      }
      const offset2 = isGudong ? 0 : d.data.textList.nameList.length >= 2 ? 0 : 1;
      return `translate(0, ${-d.data.nameOffset - offset * (isGudong ? -1 : 1) - offset2 || 0})`;
      // return `translate(0, ${-d.data.nameOffset - (offset) * (isGudong ? -1 : 1) || 0})`
    });
    updateNode.selectAll('.short-text-g').attr('transform', (d) => {
      let offset = 0;
      if (this.isVertical && d.depth !== 0) {
        offset = (d.data.settings.maxSliblings - d.data.settings.height) / 2;
      }
      const offset2 = isGudong ? 0 : d.data.shortNameList.length >= 2 ? 0 : 1;
      return `translate(0, ${-d.data.shortNameOffset - offset * (isGudong ? -1 : 1) - offset2 || 0})`;
    });

    updateNode.selectAll('.text-status').attr('transform', (d) => {
      let offset = 0;
      if (this.isVertical && d.depth !== 0) {
        offset = (d.data.settings.maxSliblings - d.data.settings.height) / 2;
      }
      return `translate(0, ${d.data.settings.height / 2 - d.data.statusHeight - 1 - offset * (isGudong ? -1 : 1)})`;
    });

    updateNode
      .selectAll('.node-options')
      .attr('transform', (d) => {
        let offset = 0;
        if (d.depth !== 0 && this.isVertical) {
          offset = (d.data.settings.maxSliblings - d.data.settings.height) / 2;
        }
        offset *= d.data.isGudong ? -1 : 1;
        return `translate(${d.data.settings.width / 2}, ${-d.data.settings.height / 2 - offset})`;
      })
      .attr('visibility', (d) => (this.options.isEdit && d.depth !== 0 ? 'visible' : 'hidden'));

    updateNode
      .selectAll('.circle-g')
      .attr('visibility', function (d) {
        return d.data.hasChildren && d.depth !== 0 ? 'visible' : 'hidden';
      })
      .attr('transform', (d) => {
        // 2 为加号与rect的距离
        if (this.isVertical) {
          let offset = 0;
          if (d.depth !== 0) {
            offset = (d.data.settings.maxSliblings - d.data.settings.height) / 2;
          }
          return `translate(0, ${(d.data.settings.height / 2 + d.data.settings.circleRadius + 2 - offset) * (isGudong ? -1 : 1)})`;
        }
        return `translate(${(d.data.settings.width / 2 + d.data.settings.circleRadius + 2) * (isGudong ? -1 : 1)}, 0)`;
      });

    updateNode.selectAll('.circle').attr('stroke', (d) => {
      return !d.data.isFolded ? d.data.settings.activeCircleColor : d.data.settings.circleColor;
    });

    updateNode.selectAll('.horizontal-line').attr('stroke', (d) => {
      return !d.data.isFolded ? d.data.settings.activeCircleColor : d.data.settings.circleColor;
    });

    updateNode.selectAll('.vertical-line').attr('visibility', function (d) {
      if (d.data.hasChildren) {
        if (!_.isEmpty(self.filterChosenTags) && d.children && !d.children.length && d.data.isFolded) {
          return 'hidden';
        }
        return d.data.isFolded ? 'hidden' : 'visible';
      }
      return 'hidden';
    });

    updateNode.selectAll('.tags-g').attr('transform', (d) => {
      let offset = 0;
      if (d.depth !== 0 && this.isVertical) {
        offset = (d.data.settings.maxSliblings - d.data.settings.height) / 2;
      }
      return `translate(${d.data.settings.width / 2 - d.data.tagTotalSize.width - 10}, ${
        -d.data.settings.height / 2 - offset * (isGudong ? -1 : 1)
      })`;
    });

    updateNode.selectAll('.label-g').attr('transform', (d) => {
      if (this.isVertical) {
        let offset = 0;
        if (d.depth !== 0 && this.isVertical) {
          offset = (d.data.settings.maxSliblings - d.data.settings.height) / 2;
        }
        return `translate(0, ${-d.data.settings.height / 2 - d.data.labelInfo.height - 8 + offset})`;
      }
      const x = (isGudong ? -1 : 1) * (d.data.labelInfo.width / 2 + d.data.settings.width / 2 + 10);
      const y = -d.data.labelInfo.height / 2;
      return `translate(${x}, ${y})`;
    });

    const updateArrow = updateNode.selectAll('.arrow-g').attr('transform', (d) => {
      if (isGudong) {
        return 'translate(0, 0)';
      }
      let offset = 0;
      if (d.depth !== 0 && this.isVertical) {
        offset = (d.data.settings.maxSliblings - d.data.settings.height) / 2;
      }
      return `translate(0, ${-d.data.settings.height / 2 - offset * (isGudong ? -1 : 1)})`;
    });

    updateArrow.selectAll('.arrow-path').attr('transform', (d) => {
      if (this.isVertical) {
        const length = isGudong
          ? d.data.settings.height / 2 + Math.abs(d.data.settings.pointsForLinkIn[3].y - d.data.settings.pointsForLinkIn[4].y)
          : 0;
        const offset = isGudong ? (d.data.settings.maxSliblings - d.data.settings.height) / 2 - 2 : 0;
        return `translate(0, ${length + offset})`;
      }
      if (isGudong) {
        return `translate(${
          Math.abs(d.data.settings.pointsForLinkIn[2].x - d.data.settings.pointsForLinkIn[0].x) + d.data.settings.width / 2 - 15
        }, 0)`;
      }
      return `translate(${-d.data.settings.width / 2 - 15}, ${d.data.settings.height / 2})`;
    });

    node
      .exit()
      .transition()
      .duration(chartSettings.duration)
      .ease(chartSettings.ease)
      .attr('transform', () => {
        if (source.depth !== 0) {
          return `translate(${source.data.settings.x}, ${(isGudong ? -1 : 1) * source.data.settings.y})`;
        }
        return 'translate(0, 0)';
      })
      .attr('fill-opacity', 0)
      .attr('stroke-opacity', 0)
      .remove();

    // 画arrow
    const arrow = nodeEnter
      .append('g')
      .attr('class', 'arrow-g')
      .attr('transform', (d) => {
        if (isGudong) {
          return 'translate(0, 0)';
        }
        let offset = 0;
        if (d.depth !== 0 && this.isVertical) {
          offset = (d.data.settings.maxSliblings - d.data.settings.height) / 2;
        }
        return `translate(0, ${-d.data.settings.height / 2 - offset * (isGudong ? -1 : 1)})`;
      });

    arrow
      .append('path')
      .attr('class', 'arrow-path')
      .filter((d) => d.depth !== 0)
      .attr('fill', () => chartSettings.defaultArrowSettings.fill)
      .attr('stroke-width', 0)
      .attr('d', () => {
        if (this.isVertical) {
          return 'M0,0 L5,-15 L-5,-15 L0,0';
        }
        return 'M0,5 L0,-5 L15,0';
      })
      .attr('transform', (d) => {
        if (this.isVertical) {
          const length = isGudong
            ? d.data.settings.height / 2 + Math.abs(d.data.settings.pointsForLinkIn[3].y - d.data.settings.pointsForLinkIn[4].y)
            : 0;
          const offset = isGudong ? (d.data.settings.maxSliblings - d.data.settings.height) / 2 - 2 : 0;
          return `translate(0, ${length + offset})`;
        }
        if (isGudong) {
          return `translate(${
            Math.abs(d.data.settings.pointsForLinkIn[2].x - d.data.settings.pointsForLinkIn[0].x) + d.data.settings.width / 2 - 15
          }, 0)`;
        }
        return `translate(${-d.data.settings.width / 2 - 15}, ${d.data.settings.height / 2})`;
      });

    const link = linksContainer.selectAll('g.link').data(links, self.stratify.linkId).attr('cursor', 'pointer');

    const linkEnter = link
      .enter()
      .append('g')
      .attr('class', 'link')
      .on('mouseover', function () {
        const tempLinkG = d3.select(this);
        tempLinkG.raise();
        const link = tempLinkG.selectAll('path');
        link
          .classed('link-flow-in', function () {
            const a = link.attr('link-flow-style');
            return a === 'in';
          })
          .classed('link-flow-out', function () {
            const a = link.attr('link-flow-style');
            return a === 'out';
          })
          .attr('stroke', chartSettings.defaultLineSettings.flowLineColor)
          .attr('stroke-opacity', chartSettings.defaultLineSettings.flowLineOpacity);
      })
      .on('mouseout', function () {
        const tempLinkG = d3.select(this);
        const link = tempLinkG.selectAll('path');
        link
          .classed('link-flow-in', false)
          .classed('link-flow-out', false)
          .attr('stroke', chartSettings.defaultLineSettings.lineColor)
          .attr('stroke-opacity', chartSettings.defaultLineSettings.lineOpacity);
      });

    linkEnter
      .append('path')
      .attr('class', 'link-path')
      .attr('fill', chartSettings.defaultLineSettings.lineFill)
      .attr('stroke', chartSettings.defaultLineSettings.lineColor)
      .attr('stroke-width', chartSettings.defaultLineSettings.lineWidth)
      .attr('link-flow-animation-id', (d) => {
        return d.source.data.id;
      })
      .attr('target-link-flow-animation-id', (d) => {
        return d.target.data.id;
      })
      .attr('link-flow-style', function () {
        return isGudong ? 'in' : 'out';
      })
      .attr('d', () => {
        if (this.isVertical) {
          if (source.depth !== 0) {
            if (isGudong) {
              return chartLines.createDefault([
                { x: source.data.settings.bottomCenter.x, y: -source.data.settings.bottomCenter.y },
                { x: source.data.settings.bottomCenter.x, y: -source.data.settings.bottomCenter.y },
                { x: source.data.settings.bottomCenter.x, y: -source.data.settings.bottomCenter.y },
                { x: source.data.settings.bottomCenter.x, y: -source.data.settings.bottomCenter.y },
                { x: source.data.settings.bottomCenter.x, y: -source.data.settings.bottomCenter.y },
              ]);
            }
            return chartLines.createDefault([
              source.data.settings.bottomCenter,
              source.data.settings.bottomCenter,
              source.data.settings.bottomCenter,
              source.data.settings.bottomCenter,
              source.data.settings.bottomCenter,
            ]);
          }
          return chartLines.createDefault([
            source.data.settings.topCenter,
            source.data.settings.topCenter,
            source.data.settings.topCenter,
            source.data.settings.topCenter,
            source.data.settings.topCenter,
          ]);
        }
        return isGudong
          ? chartLines.createDefault([
              source.data.settings.leftCenter,
              source.data.settings.leftCenter,
              source.data.settings.leftCenter,
              source.data.settings.leftCenter,
              source.data.settings.leftCenter,
            ])
          : chartLines.createDefault([
              source.data.settings.rightCenter,
              source.data.settings.rightCenter,
              source.data.settings.rightCenter,
              source.data.settings.rightCenter,
              source.data.settings.rightCenter,
            ]);
      });

    const linkText = linkEnter
      .append('g')
      .attr('cursor', 'pointer')
      .attr('class', 'link-text')
      .attr('visibility', () => {
        return this.options.showText ? 'visible' : 'hidden';
      })
      .attr('transform', (d) => {
        if (d.source.depth === 0) {
          return 'translate(0, 0)';
        }
        return `translate(${d.source.x}, ${d.source.y * (d.source.data.isGudong ? -1 : 1)})`;
      })
      .on('mouseover', function (d) {
        if (d.target.data.id && d.target.data.id.split('-')[0] !== 'others') {
          const $sender = $(window.event.target);
          let position = $sender.position();
          if (position.left === 0 && position.top === 0) {
            // 都是0的情况下，说明没有取到正确值，因此做个补充
            const rect = $sender[0].getBoundingClientRect();
            position = {
              left: rect.x,
              top: rect.y,
            };
          }
          const size = {
            width: $sender.width(),
            height: $sender.height(),
          };
          self.emit('onDataLineMouseover', {
            data: d.target.data,
            position,
            size,
            isGudong,
          });
        }
      })
      .on('mouseout', function (d) {
        self.emit('onDataLineMouseout', {
          data: d.target.data,
        });
      });
    // linkText
    //   .append('rect')
    //   .attr('fill', '#fff')
    //   .attr('width', d => d.target.data.linkTextSize.width)
    //   .attr('height', d => d.target.data.linkTextSize.height)

    linkText
      .append((d) => {
        return utils.createRichText(d.target.data.linkText, chartSettings.defaultLineSettings.fontSize);
      })
      .attr('transform', (d) => {
        const offset = 5;
        const y = this.specialBrowser ? d.target.data.linkTextSize.height / 2 + 5 : d.target.data.linkTextSize.height / 2;
        // if (d.target.data.kg) {
        //   offset = -23
        //   y = this.specialBrowser ? (d.target.data.linkTextSize.height - 7) : d.target.data.linkTextSize.height / 2
        // }
        const x = d.target.data.linkTextSize.width;
        return `translate(${x + offset}, ${y})`;
      });

    // 控股标签
    const linkKgG = linkText
      .filter((d) => d.target.data.kg && (this.isVip || (!this.isVip && d.depth <= 1)))
      .append('g')
      .attr('class', 'link-kg-text')
      .attr('transform', function () {
        const offset = 5;
        const x = -36;
        return `translate(${x + offset}, -4)`;
      });

    linkKgG
      .append('rect')
      .attr('fill', chartSettings.defaultLineSettings.kgBg)
      .attr('width', 36)
      .attr('height', 22)
      .attr('rx', 2)
      .attr('ry', 2);

    linkKgG
      .append('text')
      .text('控股')
      .attr('font-size', chartSettings.defaultLineSettings.fontSize)
      .attr('fill', chartSettings.defaultLineSettings.kgColor)
      .attr('transform', 'translate(6, 15)');

    linkText
      .append('rect')
      .attr('fill', 'transparent')
      .attr('width', (d) => {
        return d.target.data.linkTextSize.width;
      })
      .attr('height', (d) => d.target.data.linkTextSize.height);

    const updateLink = link.merge(linkEnter);

    updateLink
      .selectAll('path.link-path')
      .transition()
      .duration(chartSettings.duration)
      .ease(chartSettings.ease)
      .attr('d', function (d) {
        return chartLines.createDefault(d.target.data.settings.pointsForLinkIn);
      });

    updateLink
      .selectAll('g.link-text')
      .attr('visibility', () => {
        return this.options.showText ? 'visible' : 'hidden';
      })
      .transition()
      .duration(chartSettings.duration)
      .ease(chartSettings.ease)
      .attr('transform', function (d) {
        // 文字位置为拐点处的位置+偏移
        if (self.isVertical) {
          const x = d.target.data.settings.pointsForLinkIn[3].x;
          let y1 = d.target.data.settings.pointsForLinkIn[3].y;
          if (
            d.target.data.settings.pointsForLinkIn[0].x === d.target.data.settings.pointsForLinkIn[3].x &&
            d.source.data.children &&
            d.source.data.children.length <= 1 &&
            !d.target.data.isGudong
          ) {
            y1 = d.target.data.settings.pointsForLinkIn[0].y;
          }
          const y2 = isGudong ? d.target.data.settings.pointsForLinkIn[4].y : d.target.data.settings.topCenter.y;
          return `translate(${x - d.target.data.linkTextSize.width / 2}, ${(y1 + y2) / 2 - d.target.data.linkTextSize.height / 2 - 7})`;
        }
        const linkWidth = d.target.data.linkTextSize.width;
        const linkHeight = d.target.data.linkTextSize.height;
        let x1 = d.target.data.settings.pointsForLinkIn[3].x;
        if (
          d.target.data.settings.pointsForLinkIn[0].y === d.target.data.settings.pointsForLinkIn[3].y &&
          d.source.data.children &&
          d.source.data.children.length <= 1 &&
          !d.target.data.isGudong
        ) {
          x1 = d.target.data.settings.pointsForLinkIn[0].x;
        }
        const x2 = d.target.data.settings.pointsForLinkIn[4].x;
        const y = d.target.data.settings.pointsForLinkIn[3].y;
        return `translate(${(x1 + x2) / 2 - linkWidth}, ${y - linkHeight - 5})`;
        // return `translate(${(x1 + x2) / 2 - linkWidth / 2 - 7}, ${y - linkHeight - 5})`
      });

    const exitLink = link.exit().remove();

    exitLink
      .selectAll('path.link-path')
      .transition()
      .duration(chartSettings.duration)
      .ease(chartSettings.ease)
      .attr('d', () => {
        if (source.depth !== 0) {
          if (isGudong) {
            return chartLines.createDefault([
              { x: 0, y: 0 },
              { x: 0, y: 0 },
              { x: 0, y: 0 },
              { x: 0, y: 0 },
              { x: 0, y: 0 },
            ]);
          }
          return chartLines.createDefault([
            source.data.settings.bottomCenter,
            source.data.settings.bottomCenter,
            source.data.settings.bottomCenter,
            source.data.settings.bottomCenter,
            source.data.settings.bottomCenter,
          ]);
        }
      });

    exitLink
      .selectAll('g.link-text')
      .transition()
      .duration(chartSettings.duration)
      .ease(chartSettings.ease)
      .attr('transfrom', () => {
        if (source.depth !== 0) {
          return `translate(${source.data.x}, ${source.data.y})`;
        }
        return 'translate(0, 0)';
      });
    if (haveSource) {
      this.autoAdjust(source, source.data.isGudong);
    }
  }

  applyTransform(transform, [newX, newY, newK]) {
    const x = transform.x;
    const y = transform.y;
    const k = transform.k;

    this.root
      .transition()
      .duration(600)
      .attrTween('transform', () => {
        const interpolator = d3.interpolate([x, y, k], [newX, newY, newK]);
        return (t) => {
          // t 为当前时间变量
          const view = interpolator(t);
          transform.x = view[0];
          transform.y = view[1];
          transform.k = view[2];
          this.emit('onScale', { oldTransform: this.transform, newTransform: { ...transform } });
          return transform;
        };
      })
      .on('end', () => {
        this.zoom.transform(this.root, transform);
        this.emit('onScale', { oldTransform: this.transform, newTransform: { ...transform } });
        this.transform = { ...transform };
      });
  }

  autoAdjust(d, isGudong) {
    setTimeout(() => {
      if (chartSettings.autoAdjust === false) {
        return false;
      }
      let x = d.data.settings.x;
      let y = d.data.settings.y;

      if (d.data.children) {
        x = d.data.settings.x;
        y = d.data.settings.y;
      } else if (d.data.detailList) {
        x = d.parent.data.settings.x;
        y = d.parent.data.settings.y;
      }
      const transform = d3.zoomTransform(this.svg.node());
      const k = transform.k;
      const newK = k;

      const originOffset = {
        x: this.size.width / 2 - (this.size.width / 2) * newK,
        y: this.size.height / 2 - (this.size.height / 2) * newK,
      };

      const newX = -x * newK + originOffset.x;
      const newY = (isGudong ? 1 : -1) * y * newK + originOffset.y;

      this.applyTransform(transform, [newX, newY, newK]);
    });
  }

  initPosition() {
    if (firstPosition) {
      const transform = d3.zoomTransform(this.svg.node());
      this.applyTransform(transform, firstPosition);
    }
  }

  zoomInOrOut(directionOrScale) {
    const transform = d3.zoomTransform(this.svg.node());

    const k = transform.k;
    const x = transform.x;
    const y = transform.y;

    let newK;

    if (_.isNumber(directionOrScale)) {
      newK = directionOrScale;
    } else {
      newK = k + (directionOrScale ? this.scaleStep : -this.scaleStep);
    }

    if (newK < chartSettings.scaleRange[0]) {
      newK = chartSettings.scaleRange[0];
    }

    if (newK > chartSettings.scaleRange[1]) {
      newK = chartSettings.scaleRange[1];
    }

    const center = { x: this.size.width / 2, y: this.size.height / 2 };
    const translate0 = { x: (center.x - x) / k, y: (center.y - y) / k };

    const l = { x: translate0.x * newK + x, y: translate0.y * newK + y };
    const newX = x + center.x - l.x;
    const newY = y + center.y - l.y;

    if (!firstPosition) {
      firstPosition = [newX, newY, newK];
    }
    this.applyTransform(transform, [newX, newY, newK]);
  }

  filterTreeByPercent({ type, value, source = null }) {
    this.filterType = type;
    this.filterValue = value;
    let data = type === 'gd' ? this.data.gdTree : this.data.investTree;
    if (source) {
      data = source.data;
    }
    this.filterByPercent(type, value, data);
    this.refresh({ source });
    if (firstPosition) {
      // 使用暴力方式解决重置位置不对的问题
      this.initPosition();
    }
  }

  filterByPercent(type, value, data) {
    if (data.children || data._children) {
      if (!data._children) {
        data._children = data.children;
      }
      const tempChildren = [];
      const otherObj = {
        id: `others-${utils.generateUUID()}`,
        name: type === 'gd' ? '其他股东' : '其他对外投资',
        isGudong: type === 'gd',
      };
      let otherPercent = 0;
      _.each(data.detailList, (d) => {
        if (d.children) {
          this.filterByPercent(type, value, d);
        }
        let percent = 100;
        if (d.percent) {
          percent = d.percent && +d.percent.split('%')[0];
        }
        if (percent >= +value) {
          tempChildren.push(d);
        } else {
          otherPercent += percent;
        }
      });
      if (type === 'gd') {
        if (otherPercent) {
          const floatNum = String(otherPercent).split('.')[1];
          otherObj.percent = (floatNum && floatNum.length) > 4 ? otherPercent.toFixed(4) : otherPercent;
          if (+otherObj.percent >= 100) {
            otherObj.percent = 100;
          }
          otherObj.percent += '%';
          tempChildren.push(otherObj);
        }
      }
      if (_.isEmpty(tempChildren)) {
        data.hasChildren = false;
      }
      data.children = tempChildren;
    }
  }

  filterTreeByTags(isGudong, chosenTags, source = null) {
    this.filterChosenTags = chosenTags;
    let data = isGudong ? this.data.gdTree : this.data.investTree;
    if (source) {
      data = source.data;
    }
    if (!_.isEmpty(data)) {
      this.filterByTags(isGudong, chosenTags, data);
      this.refresh({ source });
      if (firstPosition) {
        // 使用暴力方式解决重置位置不对的问题
        this.initPosition();
      }
    }
  }

  filterByTags(isGudong, chosenTags, data) {
    if (data.children || data._children) {
      if (!data._children) {
        data._children = data.children;
      }
      const tempChildren = [];
      _.each(data.detailList, (d) => {
        if (d.children) {
          this.filterByTags(isGudong, chosenTags, d);
        }
        // 不是全选
        if (d.org !== 0) {
          tempChildren.push(d);
        } else if (!_.isEmpty(d.tags)) {
          _.each(d.tags, (tag) => {
            const filterArr = _.find(chosenTags, (t) => {
              return t === tag.text;
            });
            if (filterArr) {
              if (!_.find(tempChildren, (v) => v.id === d.id)) {
                tempChildren.push(d);
              }
            }
          });
        }
      });
      data.isFolded = !_.isEmpty(tempChildren);
      data.hasChildren = !_.isEmpty(tempChildren);
      data.children = tempChildren;
    }
  }

  filterTreeByRegisterStatus(isDisplay) {
    this.filterRegisterStatus = isDisplay;
    const registerTags = this.rightNodesContainer.selectAll('.tag-register');
    registerTags.attr('visibility', 'hidden');
    if (!isDisplay) {
      const zxTags = this.rightNodesContainer.selectAll('[tag-register-type="注销"]');
      zxTags.attr('visibility', 'visible');
      const hxTags = this.rightNodesContainer.selectAll('[tag-register-type="吊销"]');
      hxTags.attr('visibility', 'visible');
    } else {
      registerTags.attr('visibility', 'visible');
    }

    const registerTags2 = this.leftNodesContainer.selectAll('.tag-register');
    registerTags2.attr('visibility', 'hidden');
    if (!isDisplay) {
      const zxTags2 = this.leftNodesContainer.selectAll('[tag-register-type="注销"]');
      zxTags2.attr('visibility', 'visible');
      const hxTags2 = this.leftNodesContainer.selectAll('[tag-register-type="吊销"]');
      hxTags2.attr('visibility', 'visible');
    } else {
      registerTags2.attr('visibility', 'visible');
    }
  }

  filterTreeByFullOrShortName(isFull) {
    this.isFullName = isFull;
    const shortName = this.rightNodesContainer.selectAll('.short-text-g');
    const fullName = this.rightNodesContainer.selectAll('.text-g');
    shortName.attr('visibility', isFull ? 'hidden' : 'visible');
    fullName.attr('visibility', (d) => {
      if (_.isEmpty(d.data.shortObj)) {
        return 'visible';
      }
      return isFull ? 'visible' : 'hidden';
    });
    const shortName2 = this.leftNodesContainer.selectAll('.short-text-g');
    const fullName2 = this.leftNodesContainer.selectAll('.text-g');
    shortName2.attr('visibility', isFull ? 'hidden' : 'visible');
    fullName2.attr('visibility', (d) => {
      if (_.isEmpty(d.data.shortObj)) {
        return 'visible';
      }
      return isFull ? 'visible' : 'hidden';
    });
  }

  getSize() {
    const size = {};
    const radiuSize = 20;
    const offset = 20;
    if (this.isVertical) {
      const maxXNode = _.maxBy(_.union(this.gdNodes, this.investNodes), (node) => {
        return node.x;
      });
      const minXNode = _.minBy(_.union(this.gdNodes, this.investNodes), (node) => {
        return node.x;
      });
      const maxYNode = _.maxBy(this.investNodes, (node) => {
        return node.data.settings.bottomCenter.y;
      });
      const minYNode = _.minBy(this.gdNodes, (node) => {
        const labelHeight = (node.data.labelInfo && node.data.labelInfo.height) || 0;
        const y = -(node.data.settings.bottomCenter.y + labelHeight);
        return y;
      });
      const left = minXNode.x - minXNode.data.settings.width / 2;
      const right = maxXNode.x + maxXNode.data.settings.width / 2;
      const top = -(minYNode.data.settings.bottomCenter.y + ((minYNode.data.labelInfo && minYNode.data.labelInfo.height) || 0) + radiuSize);
      const bottom = maxYNode.y + maxYNode.data.settings.height / 2 + radiuSize;
      size.width = right - left + offset;
      size.height = bottom - top + offset;
      size.x = left + $(window).width() / 2;
      size.y = $(window).height() / 2 + top;
    }
    return size;
  }

  delayZoom() {
    // disable zoom for zooming
    this.svg.on('wheel.zoom', null);
  }
}
