/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-empty-function */
/* eslint-disable consistent-return */
/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-expressions */
import moment from 'moment';
import _ from 'lodash';

import dataLoader from './data';
import { actionTypes, gUiToolbox, gUiToolboxAction } from '../g-ui-toolbox';
import gUiFooter from '../g-ui-footer';
import gUiLoading from '../g-ui-loading';
import gUiNoData from '../g-ui-no-data';
import appPerson from '../g-app-person';
import appCompany from '../g-app-company';
import appFilter from './components/filter';
import appLine from './components/line';
import Chart from './chart';
import popoverHelper from '../utils/popoverHelper';
import utils from './chart/utils';
import globalUtils from '../utils/utils';
import saveSvg from '../utils/save-svg';
import settings from './settings';
import { deviceInfo } from '../utils/device-info';

const defaultScale = 0.8;

export default {
  name: 'g-equity-chart',
  props: {
    actionTypes: {
      type: Object,
      default: () => actionTypes,
    },
    containerName: {
      type: String,
      default: 'equity-chart',
    },
    keyNo: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
  },
  components: {
    [gUiToolbox.name]: gUiToolbox,
    [gUiToolboxAction.name]: gUiToolboxAction,
    [gUiFooter.name]: gUiFooter,
    [gUiLoading.name]: gUiLoading,
    [gUiNoData.name]: gUiNoData,
    [appFilter.name]: appFilter,
  },
  data() {
    return {
      isPerson: false,
      defaultScale,
      data: {},
      isLoading: false,
      noData: false,
      isInit: false,
      isFullScreen: false,
      isSaving: false,
      isVertical: true,
      isFilterOpened: false,
      filterPercent: {},
      filterTags: [],
      filterRegisterStatus: false,
      showText: true,
      isEdit: false,
      transform: { x: 0, y: 0, k: defaultScale },
      controllerData: null,
      actualControl: null,
      iframe: false,
      showReport: false,
      isFullName: true,
      reportDetail: [],
    };
  },
  mounted() {
    this.isPerson = this.keyNo && this.keyNo[0] === 'p';
    this.$nextTick(() => {
      this.onRefresh();
      this.isFullScreen = document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement;
      this.$nextTick(() => {
        document.addEventListener('fullscreenchange', () => {
          this.isFullScreen = document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement;
        });
        document.addEventListener('webkitfullscreenchange', () => {
          this.isFullScreen = document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement;
        });
        document.addEventListener('mozfullscreenchange', () => {
          this.isFullScreen = document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement;
        });
        document.addEventListener('MSFullscreenChange', () => {
          this.isFullScreen =
            document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement || document.msFullscreenElement;
        });
      });
    });
    $(`#${this.containerName}`).on('click', 'svg', () => {
      this.isFilterOpened = false;
      $('.detail-popover').remove();
    });
  },
  beforeDestroy() {
    document.removeEventListener('fullscreenchange', () => {});
    document.removeEventListener('webkitfullscreenchange', () => {});
    document.removeEventListener('mozfullscreenchange', () => {});
    document.removeEventListener('MSFullscreenChange', () => {});
  },
  methods: {
    cleanUp() {
      this.isInit = false;
      this.chart && this.chart.cleanup();
      $('.detail-popover').remove();
    },
    onRefresh() {
      this.cleanUp();
      this.isEdit = false;
      this.isFilterOpened = false;
      this.isLoading = true;
      this.noData = false;
      this.chart = new Chart(`#${this.containerName}`);
      this.chart.init(this.isVertical, {});
      if (this.$refs.filter) {
        this.$refs.filter.initPercent();
        this.$refs.filter.initChosen();
      }
      Promise.all([
        this.keyNo[0] === 'p' ? dataLoader.loadGuquanPerson(this.keyNo, this.name) : dataLoader.loadGuquanData(this.keyNo, this.name),
      ])
        .then(([data]) => {
          this.isInit = true;
          this.data = data;
          this.isLoading = false;
          this.chart.zoomInOrOut(this.defaultScale);
          this.chart.render(data, this.isVertical);
        })
        .catch(() => {
          this.noData = true;
          this.isLoading = false;
        });
      this.chart.on('onRequireData', ({ d, isGudong }) => {
        // 数据中没有ID的情况下，不查数据
        if (!d.data.keyNo) {
          return false;
        }
        d.data.isFolded = true;

        dataLoader
          .loadGuquanDataRequire(d.data)
          .then((data) => {
            d.data.children = data;
            d.data.detailList = data;
            let filterFlag = false;
            if (!_.isEmpty(this.filterPercent)) {
              filterFlag = true;
              this.chart.filterTreeByPercent(_.assign(this.filterPercent, { source: d }));
            }
            if (!_.isEmpty(this.filterTags)) {
              filterFlag = true;
              this.chart.filterTreeByTags(isGudong, this.filterTags, d);
            }
            if (!filterFlag) {
              this.chart.updateTree(isGudong, d, this.isVertical);
              this.chart.filterTreeByRegisterStatus(this.filterRegisterStatus);
            }
            this.chart.filterTreeByFullOrShortName(this.isFullName);
          })
          .catch((err) => {
            console.error(err);
          });
      });
      this.chart.on('onRequireUpdate', ({ d, isGudong }) => {
        this.$nextTick(() => {
          this.chart.updateTree(isGudong, d, this.isVertical);
        });
      });
      this.chart.on('onScale', ({ oldTransform, newTransform }) => {
        this.transform = newTransform;
        $('.detail-popover').remove();
      });
      this.chart.on('onDataNodeMouseover', ({ eid, data, position, size, isGudong }) => {
        if (!this.isVip && data.depth > 1) {
          return;
        }
        data.settings.delayTimer = setTimeout(() => {
          const $instance = $(`#detail-popover-${data.id}`);
          if ($instance.length > 0) {
            $instance.stop(true).fadeIn(0);
            return;
          }
          const vmData = { id: data.keyNo, name: data.name, keyNo: data.keyNo };
          if (utils.isPerson(data)) {
            vmData.eid = eid;
          }
          if (data.org === 6) {
            return;
          }
          popoverHelper.showPopover({
            component: utils.isCompany(data) ? appCompany : appPerson,
            isLeft: isGudong,
            data: vmData,
            targetPosition: {
              left: this.isFullScreen ? position.left : position.left - (180 + 60),
              top: this.isFullScreen ? position.top : position.top - (42 + 42 + 40),
            },
            container: $(`.${this.containerName}`),
            identity: `detail-popover-${data.id}`,
            targetSize: { width: size.width * this.transform.k, height: size.height * this.transform.k },
          });
        }, settings.fadeDuration);
      });

      this.chart.on('onDataNodeMouseout', ({ data }) => {
        if (!this.isVip && data.depth > 1) {
          return;
        }
        if (data.settings.delayTimer) {
          clearTimeout(data.settings.delayTimer);
          delete data.settings.delayTimer;
        }

        $(`#detail-popover-${data.id}`).fadeOut(settings.fadeDuration, () => {
          $(this).remove();
        });
      });

      this.chart.on('onDataLineMouseover', ({ data, position, size, isGudong }) => {
        data.settings.delayTimer = setTimeout(() => {
          const $instance = $(`#detail-popover-${data.id}`);
          if ($instance.length > 0) {
            $instance.stop(true).fadeIn(0);
            return;
          }
          const vmData = {
            id: data.id,
            percent: data.percent,
            capi: data.capi,
            text: data.relationText,
            isPublic: data.isPublic,
          };
          popoverHelper.showPopover({
            component: appLine,
            isLeft: false,
            data: vmData,
            targetPosition: {
              left: this.isFullScreen ? position.left : position.left - (180 + 60),
              top: this.isFullScreen ? position.top : position.top - (42 + 42 + 40),
            },
            container: $(`.${this.containerName}`),
            identity: `detail-popover-${data.id}`,
            targetSize: { width: size.width * this.transform.k, height: size.height * this.transform.k },
          });
        }, settings.fadeDuration);
      });

      this.chart.on('onDataLineMouseout', ({ data }) => {
        if (data.settings.delayTimer) {
          clearTimeout(data.settings.delayTimer);
          delete data.settings.delayTimer;
        }

        $(`#detail-popover-${data.id}`).fadeOut(settings.fadeDuration, () => {
          $(this).remove();
        });
      });
    },
    handleTagDisplay(isDisplay) {
      this.filterRegisterStatus = isDisplay;
      this.chart.filterTreeByRegisterStatus(this.filterRegisterStatus);
    },
    handleEntTagsChanged(tags) {
      if (this.$refs.filter) {
        this.$refs.filter.initPercent(false);
        this.filterPercent = {};
        this.chart.filterTreeByPercent({ type: 'gd', value: 0 });
        this.chart.filterTreeByPercent({ type: 'invest', value: 0 });
      }
      this.filterTags = tags;
      this.chart.filterTreeByTags(true, tags);
      this.chart.filterTreeByTags(false, tags);
    },
    onFilterChanged(data) {
      const staticTags = ['在业', '存续', '筹建', '清算', '迁入', '迁出', '停业', '撤销', '吊销', '注销'];
      if (this.$refs.filter && this.filterTags.length > 0 && this.filterTags.length !== staticTags.length) {
        this.$refs.filter.initChosen(false);
        this.filterTags = [];
        this.filterRegisterStatus = false;
        this.chart.filterTreeByTags(true, staticTags);
        this.chart.filterTreeByTags(false, staticTags);
        this.chart.filterTreeByRegisterStatus(false);
        this.chart.filterTreeByFullOrShortName(this.isFullName);
      }

      this.filterPercent = data;
      this.chart.filterTreeByPercent(data);
    },
    // options
    onFilter() {
      this.isFilterOpened = !this.isFilterOpened;
    },
    closeFilter() {
      this.isFilterOpened = false;
    },
    edit() {
      this.isEdit = !this.isEdit;
      const options = {
        showText: this.showText,
        isEdit: this.isEdit,
      };
      this.chart.refresh({ options });
    },
    switchFullName() {
      this.isFullName = !this.isFullName;
      this.chart.filterTreeByFullOrShortName(this.isFullName);
    },
    getReport() {
      // this.reportDetail = []
      // Promise.all([
      //   service.getReport({ keyNo: this.companyKeyNo, goodsId: 18 }),
      //   service.getReport({ keyNo: this.companyKeyNo, goodsId: 140 })
      // ])
      //   .then(([res, res1]) => {
      //     if (!_.isEmpty(res.data)) {
      //       this.reportDetail.push(res.data)
      //     }
      //     if (!_.isEmpty(res1.data)) {
      //       this.reportDetail.push(res1.data)
      //     }
      //     if (this.reportDetail.length) {
      //       this.showReport = true
      //     } else {
      //       this.$toasted.error('该企业暂不支持股权穿透报告')
      //     }
      //   })
      //   .catch(err => {
      //     this.$toasted.error(err.message || '请重试！')
      //   })
    },
    onZoomIn() {
      this.chart.zoomInOrOut(true);
    },
    onZoomOut() {
      this.chart.zoomInOrOut(false);
    },
    onFullScreen() {
      const element = $(`.${this.containerName}`)[0];
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
    },
    onExitFullScreen() {
      if (this.isFullScreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      }
    },
    onSave() {
      // 保存过程中，不能再保存
      if (this.isSaving) {
        return;
      }

      this.onExitFullScreen();
      this.isSaving = true;

      setTimeout(() => {
        const $svg = $(`#${this.containerName}>svg`);
        const $shadow = $('<div></div>')
          .css({
            width: 0,
            height: 0,
            position: 'fixed',
            top: -1000000,
            let: -1000000,
            zIndex: -1,
            overflow: 'hidden',
          })
          .appendTo('body');

        const $svgClone = $svg.clone().appendTo($shadow);
        const $g = $svgClone.children('g');
        // 移除transparent内容 存图会将其变成黑色
        $g.find('.node-handle-rect').remove();
        $g.find('.handle-circle').remove();
        $g.attr('transform', 'translate(0,0) scale(1)');
        let box = $g[0].getBBox();
        if (deviceInfo.isIE()) {
          box = this.chart.getSize();
        }
        const contentSize = { width: box.width, height: box.height };
        const padding = { left: 80, right: 80, top: 30, bottom: 60 };
        const rect = {
          top: box.y - padding.top,
          left: box.x - padding.left,
          width: contentSize.width + padding.left + padding.right,
          height: contentSize.height + padding.top + padding.bottom,
        };
        // add water markter
        const n = Math.ceil(rect.width / 240.0);
        const m = Math.ceil(rect.height / 200.0);
        for (let i = 0; i < n; i++) {
          for (let j = 0; j < m; j++) {
            const x = rect.left + 240 * i;
            const y = rect.top + 200 * j;
            $svgClone.prepend(globalUtils.getWaterMarkLogo().clone().attr('transform', `translate(${x}, ${y})`));
          }
        }

        // add white bg
        const $masker = $('<rect></rect>').attr({
          x: rect.left,
          y: rect.top,
          width: rect.width,
          height: rect.height,
          fill: 'rgba(255, 255, 255, 1)',
        });
        $svgClone.prepend($masker);

        // add bottom text
        if (rect.width < 600) {
          const $textFooter = $('<text></text>')
            .attr({
              x: padding.left + contentSize.width / 2 + rect.left,
              y: rect.top + padding.top + contentSize.height + 20,
              'text-anchor': 'middle',
              fill: '#999',
              'font-size': '12px',
            })
            .text('以上数据是企查查大数据分析引擎基于公开信息挖掘的成果，仅供参考');
          $svgClone.append($textFooter);
          const $textFooter1 = $('<text></text>')
            .attr({
              x: padding.left + contentSize.width / 2 + rect.left,
              y: rect.top + padding.top + contentSize.height + 40,
              'text-anchor': 'middle',
              fill: '#999',
              'font-size': '12px',
            })
            .text('该成果不构成任何明示或暗示的观点或保证。');
          $svgClone.append($textFooter1);
        } else {
          const $textFooter = $('<text></text>')
            .attr({
              x: padding.left + contentSize.width / 2 + rect.left,
              y: rect.top + padding.top + contentSize.height + 40,
              'text-anchor': 'middle',
              fill: '#999',
              'font-size': '12px',
            })
            .text('以上数据是企查查大数据分析引擎基于公开信息挖掘的成果，仅供参考。该成果不构成任何明示或暗示的观点或保证。');
          $svgClone.append($textFooter);
        }

        const fileName = `${this.name}-股权穿透图谱-${moment().format('YYYY-MM-DD')}.png`;

        const option = {
          ...rect,
          scale: 1,
          chartType: 'equity_chart',
        };
        saveSvg
          .saveAsImage($svgClone[0], fileName, option)
          .then(() => {
            $shadow.remove();
            this.isSaving = false;
          })
          .catch(() => {
            $shadow.remove();
            this.$toasted.error('由于浏览器大小限制，暂无法为您保存该图片，请筛选隐藏部分节点后重试。');
            // eventBus.triggerToastr(err, 'error')
            this.isSaving = false;
          });
      }, 50);
    },
    onMouseenter() {
      if (this.iframe && this.isInit) {
        this.chart.delayZoom();
      }
    },
  },
};
