export default {
  name: 'equity-chart-line',
  data() {
    return {
      unit: '',
      capiTitle: '认缴出资额：',
      percent: '', // 持股比例
      capi: '', // 认缴金额
    };
  },
  computed: {
    filterPercent() {
      let res = '-';
      if (this.percent && this.percent.split('%') && +this.percent.split('%')[0] !== 0) {
        res = this.percent;
      }
      return res;
    },
  },
  mounted() {
    if (this.capi) {
      if (this.capi.indexOf('美元') > -1) {
        this.unit = '';
      } else if (this.capi.indexOf('元') > -1) {
        this.unit = '人民币';
      } else {
        this.unit = '股';
        this.capiTitle = '持股数：';
      }
    } else {
      this.unit = '';
    }
  },
};
