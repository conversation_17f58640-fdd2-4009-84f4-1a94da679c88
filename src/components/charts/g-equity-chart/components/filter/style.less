.equity-filter-container::v-deep {
  font-family: "Microsoft YaHei", Arial, sans-serif;
  width: 500px;
  height: 400px;
  box-shadow: rgba(0, 0, 0, 0.2) 0 2px 4px 0;
  background-color: #fff;

  .equity-filter-header {
    position: relative;
    height: 56px;
    color: #333;
    font-size: 16px;
    line-height: 56px;
    padding: 0 15px;
    border-bottom: 1px solid #eee;
    font-weight: bold;

    .vip-tag {
      height: 22px;
      position: absolute;
      left: 55px;
      top: 50%;
      transform: translate(0, -50%);
    }

    >.equity-close {
      position: absolute;
      display: inline-block;
      right: 20px;
      color: #128bed;
      line-height: 52px;
      font-size: 30px;
      font-weight: bold;
      cursor: pointer;
    }
  }

  >.equity-filter-body {
    padding: 0 15px;

    .section {
      padding-top: 20px;

      .section-header {
        color: #666;
        font-size: 14px;
        position: relative;

        .header-option {
          float: right;
          position: relative;

          input {
            position: relative;
            top: 1px;
            cursor: pointer;
          }
        }
      }

      .slider-container {
        position: relative;
        margin-top: 15px;
        width: 400px;

        .slider.slider-horizontal {
          height: 10px;
          width: 400px;
          box-sizing: border-box;
        }

        .slider-track,
        .slider-track-low,
        .slider-track-high,
        .slider-selection {
          height: 6px;
          width: 400px;
        }

        .slider-track {
          height: 8px;
          border: 1px solid #d6d6d6;
          background-color: #fff;
          box-shadow: none;
          background-image: none;
        }

        .slider-selection {
          box-shadow: none;
          background-image: none;
          background-color: #128bed;
        }

        .slider-handle {
          height: 15px;
          width: 15px;
          border: 1px solid #d6d6d6;
          background-image: none;
          background-color: #fff;
          top: -4px;
        }

        .range-value {
          color: #999;
          font-size: 14px;
          display: inline-block;

          &.value-100 {
            float: right;
          }

          &.value-float {
            position: absolute;
            color: #128bed;
            left: 50%;
            transform: translate(-50%, 0);
          }
        }
      }

      .main {
        width: 100%;
        overflow: hidden;

        .tag {
          box-sizing: border-box;
          font-size: 12px;
          text-align: center;
          line-height: 31px;
          float: left;
          display: block;
          cursor: pointer;
          width: 17.4%;
          height: 32px;
          border-radius: 2px;
          border: 1px solid rgba(214, 214, 214, 1);
          margin-right: 15px;
          margin-top: 15px;

          &:nth-child(5n) {
            margin-right: 0;
          }

          &.active {
            color: #128bed;
            border-color: #128bed;
          }
        }
      }
    }
  }

  ::v-deep .ant-slider {
    margin-left: 0;
    margin-right: 0;

    .ant-slider-track {
      background: #128bed;
    }

    .ant-slider:hover .ant-slider-handle:not(.ant-tooltip-open) {
      border-color: #128bed;
    }

    .ant-slider-handle {
      border-color: #128bed;
    }
  }
}
