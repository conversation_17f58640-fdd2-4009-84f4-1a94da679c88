<template>
  <div class="equity-filter-container">
    <div class="equity-filter-header">
      筛选
      <div class="equity-close" @click="close">&times;</div>
    </div>
    <div class="equity-filter-body">
      <div class="section">
        <div class="section-header">
          企业登记状态
          <div class="header-option">
            <input type="checkbox" v-model="showTags" @click="changeTagsShow" />
            是否显示企业状态
          </div>
        </div>
        <div class="main">
          <span
            v-for="(item, index) in entStatus"
            :key="index"
            :class="['tag', hasChosen(item.id) && 'active']"
            @click="handleSingleTag(item.id)"
            >{{ item.text }}</span
          >
        </div>
      </div>
      <div class="section">
        <div class="section-header">股东持股比例</div>
        <div class="slider-container">
          <a-slider
            id="gdSlider"
            :default-value="0"
            :max="50"
            :min="0"
            :tooltip-visible="false"
            @change="changeGdSlider"
            v-model="gdRange"
          />
          <span class="range-value value-0">0</span>
          <span class="range-value value-float">{{ gdRange + '%' }}</span>
          <span class="range-value value-100">高于50%</span>
        </div>
      </div>
      <div class="section">
        <div class="section-header">对外投资比例</div>
        <div class="slider-container">
          <a-slider
            id="gdSlider"
            :default-value="0"
            :max="100"
            :min="0"
            :tooltip-visible="false"
            @change="changeInvestSlider"
            v-model="investRange"
          />
          <div class="slider-active" :style="{ width: calcInvestWidth + 'px' }"></div>
          <span class="range-value value-0">0</span>
          <span class="range-value value-float">{{ investRange + '%' }}</span>
          <span class="range-value value-100">100%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" src="./style.less" scoped></style>
<script src="./component.js"></script>
