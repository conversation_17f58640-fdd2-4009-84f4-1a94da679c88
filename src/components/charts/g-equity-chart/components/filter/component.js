/* eslint-disable @typescript-eslint/no-unused-vars */
import _ from 'lodash';

const staticStaus = ['在业', '存续', '筹建', '清算', '迁入', '迁出', '停业', '撤销', '吊销', '注销'];

export default {
  name: 'equity-chart-filter',
  data() {
    return {
      // 状态筛选
      entStatus: [
        {
          text: '在业/存续',
          id: ['在业', '存续'],
        },
        {
          text: '其他状态',
          id: ['筹建', '清算', '迁入', '迁出', '停业', '撤销', '吊销', '注销'],
        },
      ],
      chosenStatus: staticStaus,
      showTags: false,
      // 比例筛选
      gdRange: 0,
      investRange: 0,
      calcGdWidth: 0,
      calcInvestWidth: 0,
    };
  },
  methods: {
    close() {
      this.$emit('close');
    },
    initChosen(isFirst = true) {
      this.showTags = false;
      this.chosenStatus = staticStaus;
    },
    initPercent(isFirst = true) {
      this.gdRange = 0;
      this.calcGdWidth = 0;
      this.investRange = 0;
      this.calcInvestWidth = 0;
    },
    changeGdSlider() {
      const options = {
        type: 'gd',
        value: this.gdRange,
      };
      this.$emit('changed', options);
    },
    changeInvestSlider() {
      const options = {
        type: 'invest',
        value: this.investRange,
      };
      this.$emit('changed', options);
    },
    hasChosen(arr) {
      if (_.isEmpty(arr)) {
        return true;
      }
      return !_.isEmpty(_.intersection(this.chosenStatus, arr));
    },
    changeTagsShow(e) {
      this.showTags = !this.showTags;
      this.$emit('changeTagDisplay', this.showTags);
    },
    handleSingleTag(arr) {
      if (this.hasChosen(arr)) {
        this.chosenStatus = _.difference(this.chosenStatus, arr);
      } else {
        this.chosenStatus = _.union(this.chosenStatus, arr);
      }
      this.$emit('changeEntTags', this.chosenStatus);
    },
  },
};
