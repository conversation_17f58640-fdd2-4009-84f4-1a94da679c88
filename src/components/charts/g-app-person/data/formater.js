import _ from 'lodash';

const formatPersonOverview = (data) => {
  // // 人ID
  // id: '',
  // // 姓名
  // name: '',
  // // 头像
  // image: '',
  // // 标签
  // tags: [{
  //   name: 'xxxx',
  //   style: 'style-1'
  // }],
  // // 公司名
  // ename: '',
  // // 公司编号
  // eid: '',
  // // 职位
  // jobTitle: '',
  // summary: [
  //  {
  //    label: '担任法人',
  //    count: 12
  //  },
  //  {
  //    label: '对外投资',
  //    count: 12
  //  },
  //  {
  //     label: '在外任职',
  //     count: 8
  //  },
  //  {
  //     label: '控股企业',
  //     count: 45
  // }]

  const nodeData = {
    id: data.Id,
    name: data.Name,
    image: data.Image,
    ename: data.EnterpriseName,
    eid: data.EnterpriseKeyNo,
    jobTitle: data.JobTitle,
    tags: [],
    summary: [],
  };

  if (data.Tags) {
    _.map(data.Tags, (tag) => {
      if (tag !== '国有企业') {
        nodeData.tags.push({
          type: 0,
          name: tag,
        });
      }
    });
  }

  if (data.Count) {
    nodeData.summary.push({
      label: '担任法人',
      count: data.Count.OperCount || 0,
    });

    nodeData.summary.push({
      label: '对外投资',
      count: data.Count.PartnerCount || 0,
    });

    nodeData.summary.push({
      label: '在外任职',
      count: data.Count.EmployeeCount || 0,
    });

    nodeData.summary.push({
      label: '控股企业',
      count: data.Count.NameCount || 0,
    });
  }

  return nodeData;
};

export default {
  formatPersonOverview,
};
