import { graph } from '@/shared/services';

import formater from './formater';

const loadPersonDetail = (eid, pid) => {
  return new Promise((resolve, reject) => {
    graph
      .getPersonOverview({ eid, pid })
      .then((data) => {
        if (data.Result) {
          const detail = formater.formatPersonOverview(data.Result);
          if (eid) {
            detail.eid = eid;
          }
          resolve(detail);
        } else {
          resolve({
            code: 500,
          });
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export default {
  loadPersonDetail,
};
