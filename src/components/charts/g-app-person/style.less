
.person-container {
  font-family: "Microsoft YaHei", Arial, sans-serif;
  width: 360px;
  box-shadow: rgba(0, 0, 0, 0.1) 0 2px 8px;
  background-color: #fff;
  border-radius: 4px;

  .wrap {
    padding: 16px;
  }

  .logo {
    float: left;
  }

  .loading {
    width: 360px;
    height: 152px;
    background: url('./images/loading.png') center center;
    background-size: cover;
    margin-top: 0;
    margin-right: 0;
    margin-bottom: 0;

  }

  a {
    color: #128BED;
    text-decoration: none;

    &:hover {
      color: #3071a9;
    }
  }

  .to-analysis {
    font-size: 13px;

    a {
      color: #ff7220 !important;
    }

    cursor: pointer;
  }

  .peron-link {
    font-size: 16px;
    word-break: break-all;
  }

  .content-container {
    width: 260px;
  }

  .content-container,
  .summary-container {
    float: left;
    padding-left: 12px;

    .row-item {
      margin-top: 4px;
      display: flex;
      flex-wrap: wrap;
      line-height: 20px;

      &.row-item-btn {
        padding-top: 16px;
      }

      .btn {
        font-size: 12px;
        line-height: 18px;
        padding: 4px 16px;
        text-align: center;
        background-color: #128BED;
        color: #fff;
        border-radius: 2px;
      }
    }

    .label-text {
      color: #999;
      font-size: 13px;
      flex-shrink: 0;
    }

    .content {
      color: #666;
      font-size: 13px;
      flex: 1;
    }
  }

  .gap {
    margin-top: 16px;
    height: 1px;
    background: #eee;
  }

  .summary-container {
    float: unset;
    padding-top: 4px;
    padding-left: 0;

    .col-item {
      width: 50%;
      display: inline-block;

      &.row-item {
        margin-top: 8px;
      }
    }
  }

  .path{
    color: #333;
    padding-top: 16px;

    .path-title-wrap {
      display: flex;
      align-items: center;
    }

    .path-title {
      font-weight: normal;
      font-size: 13px;
      color: #333;
      margin-right: 8px;
    }
  }
}
