<template>
  <div class="risk-chart-container" id="risk-chart-container">
    <div id="risk-chart"></div>
    <!-- 操作栏 -->
    <div class="toolbox" v-show="isInit">
      <g-ui-toolbox>
        <g-ui-toolbox-action
          :action-type="actionTypes.select"
          :is-active="isFilterOpened"
          @click="isFilterOpened = !isFilterOpened"
        ></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.showText" :is-active="isShowText" @click="showText"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.zoomIn" @click="onZoomIn"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.zoomOut" @click="onZoomOut"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.refresh" @click="redraw"></g-ui-toolbox-action>
        <g-ui-toolbox-action v-show="!isFullScreen" :action-type="actionTypes.fullScreen" @click="onFullScreen"> </g-ui-toolbox-action>
        <g-ui-toolbox-action v-show="isFullScreen" :action-type="actionTypes.exitFullScreen" @click="onExitFullScreen">
        </g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.save" :is-busy="isSaving" @click="onSave"></g-ui-toolbox-action>
      </g-ui-toolbox>
    </div>
    <!-- 图例 -->
    <div class="chart-legend-wrap">
      <div class="chart-tips">
        <div class="tips current-tips">当前节点</div>
        <div class="tips ent-tips">企业</div>
        <div class="tips person-tips">人员</div>
        <div class="tips gov-tips">政府/法院</div>
        <div class="tips-line sf-tips">司法风险</div>
        <div class="tips-line jg-tips">监管风险</div>
        <div class="tips-line jy-tips">经营风险</div>
      </div>
    </div>
    <!-- 免责 -->
    <g-ui-footer></g-ui-footer>
    <!-- 加载 -->
    <g-ui-loading v-if="isLoading"></g-ui-loading>
    <!-- 暂无数据 -->
    <g-ui-no-data v-if="noData"></g-ui-no-data>
    <!-- 筛选弹框 -->
    <transition name="fade">
      <div v-show="isFilterOpened" class="filter-modal">
        <g-app-filter
          :visible="isFilterOpened"
          :typeList="typeList"
          title="筛选"
          @close="isFilterOpened = false"
          @chooseFilter="handleFilter"
        ></g-app-filter>
      </div>
    </transition>
  </div>
</template>
<script src="./component.js"></script>
<style lang="less" scoped src="./style.less"></style>
