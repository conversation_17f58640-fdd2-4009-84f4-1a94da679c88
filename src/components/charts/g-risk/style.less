
.risk-chart-container {
  position: absolute;
  inset: 0;
  overflow: hidden;
  background: url("./images/shuiying6.png") repeat;
  background-size: 360px 280px;
  line-height: 1.2;

  >svg {
    user-select: none;
  }

  #risk-chart {
    width: 100%;
    height: 100%;

    >svg {
      user-select: none;
    }
  }

  .toolbox {
    position: fixed;
    width: 46px;
    right: 20px;
    bottom: 50px;
    font-size: 18px;
    z-index: 20;
  }


  .filter {
    position: absolute;
    right: 78px;
    bottom: 100px;
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity .3s;
  }

  .fade-enter,
  .fade-leave-to {
    opacity: 0;
  }

  .chart-footer {
    bottom: 30px;
  }

  .chart-legend-wrap {
    background-color: #fff;
    height: 30px;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 2;

    .chart-tips {
      overflow: hidden;
      width: 740px;
      margin: 0 auto;

      >div {
        float: left;
        line-height: 30px;
        margin-right: 50px;
        color: #333;
        font-size: 14px;
        position: relative;

        &.tips {
          padding-left: 16px;

          &::before {
            content: '';
            display: block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translate(0, -50%);
          }
        }

        &.tips-line {
          display: block;

          &::before {
            content: '';
            display: block;
            width: 16px;
            height: 8px;
            border-radius: 0;
            position: absolute;
            left: -21px;
            top: 50%;
            transform: translate(0, -50%);
          }
        }

        &.current-tips {
          &::before {
            background-color: #FF9E01;
          }
        }

        &.ent-tips {
          &::before {
            background-color: #4EA2F0;
          }
        }

        &.person-tips {
          &::before {
            background-color: #FD485D;
          }
        }

        &.gov-tips {
          &::before {
            background-color: #2c9900;
          }
        }


        &.jy-tips {
          &::before {
            background: url('./images/arrow_jy.png') no-repeat;
            background-size: contain;
          }
        }

        &.jg-tips {
          &::before {
            background: url('./images/arrow_jg.png') no-repeat;
            background-size: contain;
          }
        }

        &.sf-tips {
          &::before {
            background: url('./images/arrow_sf.png') no-repeat;
            background-size: contain;
          }
        }

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}
