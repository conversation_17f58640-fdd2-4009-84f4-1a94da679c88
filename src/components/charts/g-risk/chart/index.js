/* eslint-disable @typescript-eslint/no-this-alias */
/* eslint-disable consistent-return */
/* eslint-disable func-names */
/* eslint-disable no-underscore-dangle */
import { EventEmitter } from 'eventemitter3';
import _ from 'lodash';
import * as d3 from 'd3';

import settings from '../settings';

const cytoscape = window.cytoscape;

let strength = -300;
let distanceMax = 400;
const distanceMin = 80;
let distance = 100;
const colideRadius = 64;

export default class Chart extends EventEmitter {
  constructor(selector) {
    super();
    this.selector = selector;
    this.$selector = $(`#${selector}`);
  }

  init({ data, iframe = false }) {
    this.data = data;
    this.chosenDesc = ['type', 'count'];
    this.size = {
      width: this.$selector.width(),
      height: this.$selector.height(),
    };
    this.iframe = !!iframe;
  }

  /* @__NODEFENCE__ */
  getD3Position2() {
    // 根据节点数量调节
    const num = this.data.nodes.length;
    if (num <= 20) {
      strength = -3200;
      distanceMax = 200;
      distance = 100;
    } else if (num < 50) {
      strength = -1600;
      distanceMax = 200;
      distance = 100;
    } else if (num < 100) {
      strength = -1600;
      distanceMax = 200;
      distance = 100;
    } else if (num < 150) {
      strength = -1000;
      distanceMax = 200;
      distance = 100;
    } else if (num <= 200) {
      strength = -800;
      distanceMax = 200;
      distance = 100;
    } else {
      strength = -200;
      distanceMax = 200;
      distance = 100;
    }
    this.simulation = d3
      .forceSimulation(this.data.nodes)
      .force('charge', d3.forceManyBody().strength(strength).distanceMin(distanceMin).distanceMax(distanceMax))
      .force(
        'link',
        d3
          .forceLink(this.data.chartLinks)
          .id((d) => d.id)
          .distance(distance)
      )
      // .velocityDecay(0.2) // 速度衰减率,值越小，收敛效果越好，但震荡越明显
      .alphaDecay(0.1) // α衰变率,值越小，收敛效果越好，但震荡越明显
      .force(
        'collide',
        d3.forceCollide().radius(function () {
          return colideRadius;
        })
      )
      .stop();
  }

  /* @__NODEFENCE__ */
  domUpdate() {
    this.transformGraph();
    this.getD3Position2();
    // let delayTime = 8000
    // let nodeNum = this.data.nodes.length
    // if (nodeNum <= 10) {
    //   delayTime = 1000
    // } else if (nodeNum <= 50) {
    //   delayTime = 1500
    // } else if (nodeNum <= 100) {
    //   delayTime = 2500
    // } else if (nodeNum <= 200) {
    //   delayTime = 3000
    // } else if (nodeNum <= 300) {
    //   delayTime = 4000
    // } else if (nodeNum <= 400) {
    //   delayTime = 6000
    // }
    // setTimeout(() => {
    //   // this.simulation.stop()
    //   // this.draw(this.transCyLayout())
    // }, delayTime)
    for (let i = 0, n = Math.ceil(Math.log(this.simulation.alphaMin()) / Math.log(1 - this.simulation.alphaDecay())); i < n; ++i) {
      this.simulation.tick();
    }
    this.draw(this.transCyLayout());
  }

  /* @__NODEFENCE__ */
  transformGraph() {
    // 调整成为根据不同节点分类的区块图
    const chartLinks = [];
    let index = 0;
    const currentNode = _.find(this.data.nodes, (node) => node.isMain);
    const personNode = _.filter(this.data.nodes, (node) => node.isPerson && !node.isMain);
    const govNode = _.filter(this.data.nodes, (node) => node.isGov && !node.isMain);
    const entNode = _.filter(this.data.nodes, (node) => node.isCompany && !node.isMain);
    this.currentNode = currentNode;
    // 创造生成位置的线条-》按照同色排列
    if (personNode.length) {
      chartLinks.push({
        id: index++,
        source: currentNode.id,
        target: personNode[0].id,
      });
    }
    if (govNode.length) {
      chartLinks.push({
        id: index++,
        source: currentNode.id,
        target: govNode[0].id,
      });
    }
    if (entNode.length) {
      chartLinks.push({
        id: index++,
        source: currentNode.id,
        target: entNode[0].id,
      });
    }
    if (personNode.length > 1) {
      for (let i = 1; i < personNode.length; i++) {
        chartLinks.push({
          id: index++,
          source: personNode[0].id,
          target: personNode[i].id,
        });
      }
    }
    if (govNode.length > 1) {
      for (let i = 1; i < govNode.length; i++) {
        chartLinks.push({
          id: index++,
          source: govNode[0].id,
          target: govNode[i].id,
        });
      }
    }
    if (entNode.length > 1) {
      for (let i = 1; i < entNode.length; i++) {
        chartLinks.push({
          id: index++,
          source: entNode[0].id,
          target: entNode[i].id,
        });
      }
    }
    this.data.chartLinks = chartLinks;
  }

  /* @__NODEFENCE__ */
  transCyLayout() {
    const els = {
      nodes: [],
      edges: [],
    };
    _.forEach(this.data.relationships, (rel) => {
      els.edges.push({
        data: {
          id: rel.id,
          source: rel.startNode,
          target: rel.endNode,
          sourceNode: rel.source,
          targetNode: rel.target,
          properties: rel.properties,
          type: rel.type,
          linkText: rel.linkText,
        },
        classes: ['edge'],
      });
    });
    _.forEach(this.data.nodes, (node) => {
      els.nodes.push({
        data: node,
        position: { x: node.x, y: node.y },
        classes: ['node'],
      });
    });
    return els;
  }

  /* @__NODEFENCE__ */
  draw(elements) {
    const self = this;
    this.cy = cytoscape({
      container: document.getElementById(this.selector),
      motionBlur: false,
      textureOnViewport: false,
      wheelSensitivity: 0.1,
      elements,
      minZoom: 0.4,
      maxZoom: 2.5,
      zoom: 1,
      layout: {
        name: 'preset',
        componentSpacing: 40,
        nestingFactor: 12,
        padding: 10,
        edgeElasticity: 800,
      },
      style: [
        {
          selector: 'node',
          style: {
            shape: 'ellipse',
            width(ele) {
              if (!ele.data('hasImage') && ele.data('label') === 'Person') {
                return 46;
              }
              return 64;
            },
            height(ele) {
              if (!ele.data('hasImage') && ele.data('label') === 'Person') {
                return 46;
              }
              return 64;
            },
            'background-color': function (ele) {
              if (ele.data('isMain')) {
                return settings.nodeColor.main;
              }
              if (ele.data('isGov')) {
                return settings.nodeColor.gov;
              }
              if (ele.data('isPerson')) {
                return settings.nodeColor.person;
              }
              return settings.nodeColor.ent;
            },
            'background-fit': 'cover',
            'border-color': function (ele) {
              if (ele.data('isMain')) {
                return settings.borderColor.main;
              }
              if (ele.data('isGov')) {
                return settings.borderColor.gov;
              }
              if (ele.data('isPerson')) {
                return settings.borderColor.person;
              }
              return settings.borderColor.ent;
            },
            'border-opacity': 1,
            label(ele) {
              const name = _.get(ele, '_private.data.properties.name', '');
              if (name.length <= 5) {
                return name;
              }
              if (name.length < 9) {
                return `${name.slice(0, 4)}\n${name.slice(4, 9)}`;
              }
              const temp = name.length > 13 ? `${name.slice(0, 12)}...` : name;
              return `${temp.slice(0, 4)}\n${temp.slice(4, 9)}\n${temp.slice(9)}`;
            },
            'z-index-compare': 'manual',
            'z-index': 20,
            color: '#fff',
            padding(ele) {
              return ele.data('isPerson') ? 0 : 3;
            },
            'text-margin-y': function (ele) {
              const name = _.get(ele, '_private.data.properties.name', '');
              let offsetY = 4;
              if (name.length <= 5) {
                offsetY = 2;
              } else if (name.length < 9) {
                offsetY = 2;
              }
              return offsetY;
            },
            'font-size': 12,
            'font-family': 'microsoft yahei',
            'text-wrap': 'wrap',
            'line-height': 1.4,
            'text-max-width': 60,
            'text-halign': 'center',
            'text-valign': 'center',
            'overlay-color': '#fff',
            'overlay-opacity': 0,
            'background-opacity': 1,
            'text-background-color': '#000',
            'text-background-shape': 'roundrectangle',
          },
        },
        {
          selector: 'edge',
          style: {
            color: '#999',
            'line-style': 'solid',
            'curve-style': 'bezier',
            'control-point-step-size': 60, // 控制相同两个节点间多条关系的距离
            'target-arrow-shape': 'triangle',
            'target-arrow-color': function (ele) {
              const type = ele.data('type');
              if (type === 'RRISK') {
                return settings.linkColor.jg;
              }
              if (type === 'BRISK') {
                return settings.linkColor.jy;
              }
              if (type === 'JRISK') {
                return settings.linkColor.sf;
              }
            },
            'arrow-scale': 0.8,
            'line-color': '#D6D6D6',
            'text-opacity': 1,
            'font-size': 12,
            'background-color': '#ccc',
            width: 0.6,
            'overlay-color': '#fff',
            'overlay-opacity': 0,
            'font-family': 'microsoft yahei',
          },
        },
        {
          selector: '.showText',
          style: {
            label(ele) {
              const text = ele.data('linkText');
              const strArr = [];
              _.forEach(text, (obj) => {
                const single = [];
                if (self.chosenDesc.includes('type')) {
                  if (obj._type) {
                    single.push(obj._type);
                  }
                }
                if (self.chosenDesc.includes('count')) {
                  if (obj._count) {
                    single.push(obj._count);
                  }
                }
                if (self.chosenDesc.includes('amount')) {
                  if (obj._amount) {
                    single.push(obj._amount);
                  }
                }
                if (single.length) {
                  strArr.push(single.join(' '));
                }
              });
              return strArr.join(';');
            },
            'text-background-color': '#fff',
            'text-background-opacity': 0.8,
            'text-background-padding': 0,
            'source-text-margin-y': 20,
            'target-text-margin-y': 20,
          },
        },
        {
          selector: '.edgeActive',
          style: {
            'arrow-scale': 0.8,
            width: 1.5,
            color(ele) {
              const type = ele.data('type');
              if (type === 'RRISK') {
                return settings.linkColor.jg;
              }
              if (type === 'BRISK') {
                return settings.linkColor.jy;
              }
              if (type === 'JRISK') {
                return settings.linkColor.sf;
              }
            },
            'line-color': function (ele) {
              const type = ele.data('type');
              if (type === 'RRISK') {
                return settings.linkColor.jg;
              }
              if (type === 'BRISK') {
                return settings.linkColor.jy;
              }
              if (type === 'JRISK') {
                return settings.linkColor.sf;
              }
            },
            'z-index': 10,
          },
        },
        {
          selector: '.edgeHover',
          style: {
            'z-index': 10,
          },
        },
        {
          selector: '.dull',
          style: {
            'text-opacity': 0,
            'z-index': 1,
            opacity: 0.2,
          },
        },
        {
          selector: '.lineFixed', // 加载完成后，加载该类，修复线有锯齿的问题
          style: {
            'overlay-opacity': 0,
          },
        },
      ],
    });

    this.cy.on('click', 'node', function (evt) {
      // 点击后取消筛选
      const node = evt.target;
      if (node._private.style['z-index'] && node._private.style['z-index'].value === 20) {
        if (!self.isFocus) {
          self.isFocus = true;
          self.cy.collection('node').addClass('dull');
          self.cy.collection('edge').addClass('dull');
          // 当前节点为根节点 则所有节点和线条都是高亮的 因为只有一层关系
          if (self.currentNode.id === node._private.data.id) {
            self.cy.collection('node').removeClass('dull');
            self.cy.collection('edge').removeClass('dull');
            self.cy.collection('edge').addClass('edgeActive');
          } else {
            node.removeClass('dull');
            node.neighborhood('edge').removeClass('dull');
            node.neighborhood('edge').addClass('edgeActive');
            // 由于此图谱只有一层关系 所以点击某个点必定与当前节点有关联
            const relatedNodes = node.neighborhood('edge').connectedNodes();
            const currentNode = self.cy.$(`#${self.currentNode.id}`);
            const otherEdges = currentNode.edgesWith(relatedNodes);
            otherEdges.removeClass('dull');
            otherEdges.addClass('edgeActive');
            relatedNodes.removeClass('dull');
          }
        }
      } else {
        self.cancelHighLight();
      }
      self.emit('resetFilterType');
    });
    // hover
    this.cy.on('mouseover', 'node', function (evt) {
      // 非暗淡状态下->既没有点击focus也没有参与筛选
      const node = evt.target;
      if (!(self.isFocus || self.isFiltered)) {
        node.neighborhood('edge').addClass('edgeActive');
        node.neighborhood('edge').addClass('edgeHover');
      }
      // 显示弹框
      const position = node.renderedBoundingBox();
      // node.renderedBoundingBox() 获取当前节点相对于屏幕的位置
      self.emit('showNodeDetail', {
        data: node._private.data,
        position: {
          left: position.x1,
          right: position.x2,
          top: position.y2,
          bottom: position.y2,
        },
        size: {
          width: position.w,
          height: position.h,
        },
      });
    });

    this.cy.on('mouseout', 'node', function (evt) {
      // 非暗淡状态下
      const node = evt.target;
      if (!(self.isFocus || self.isFiltered)) {
        node.neighborhood('edge').removeClass('edgeActive');
        node.neighborhood('edge').removeClass('edgeHover');
      }
      self.emit('hideNodeDetail', {
        data: node._private.data,
      });
    });

    this.cy.on('mouseover', 'edge', function (evt) {
      if (!(self.isFocus || self.isFiltered)) {
        const curEdge = evt.target;
        self.cy.collection('edge').removeClass('edgeActive');
        self.cy.collection('edge').removeClass('edgeHover');
        curEdge.addClass('edgeActive');
        curEdge.addClass('edgeHover');
      }
    });

    this.cy.on('mouseout', 'edge', function (evt) {
      if (!(self.isFocus || self.isFiltered)) {
        const curEdge = evt.target;
        curEdge.removeClass('edgeActive');
        curEdge.removeClass('edgeHover');
      }
    });
    let startX = 0;
    let startY = 0;
    this.cy.on('mousedown', function (evt) {
      if (evt.target._private.group === 'nodes') {
        startX = window.event.clientX;
        startY = window.event.clientY;
      }
    });
    this.cy.on('mousemove', function (evt) {
      if (evt.target && evt.target._private && evt.target._private.group && evt.target._private.group === 'nodes' && startX && startY) {
        const endX = window.event.clientX;
        const endY = window.event.clientY;
        if ((endX - startX) * (endX - startX) + (endY - startY) * (endY - startY) >= 1) {
          if (evt.target._private.group === 'nodes' && evt.target._private.data.id) {
            self.emit('hideNodeDetail', { data: evt.target._private.data });
          }
        }
      }
    });
    this.cy.on('mouseup', function (evt) {
      startX = 0;
      startY = 0;
      if (evt.target._private.group === 'nodes' && evt.target._private.data.id) {
        self.emit('hideNodeDetail', { data: evt.target._private.data });
      }
    });
    // 点击
    this.cy.on('click', function (evt) {
      if (evt.target === self.cy) {
        self.emit('clickCy');
      } else {
        const node = evt.target;
        if (self.isFocus && node._private.style['z-index'] && node._private.style['z-index'].value !== 20) {
          self.cancelHighLight();
        }
      }
    });
    this.cy.on('zoom', function () {
      self.emit('cyZoom', { k: self.cy.zoom() });
    });

    this.cy.ready(() => {
      this.emit('cancelLoading');
      this.chosenDesc = ['type', 'count'];
      this.cy.collection('edge').addClass('showText');
      this.cy.zoom({
        level: this.iframe ? 0.6 : 1,
      });

      this.cy.collection('edge').removeClass('lineFixed');
      this.cy.collection('edge').addClass('lineFixed');
      this.cy.center();
    });

    this.cy.nodes().positions(function (node) {
      // 保持居中
      if (node._private.data.isMain) {
        const currentPosition = node.renderedPosition();
        self.cy.panBy({
          x: self.cy.width() / 2 - currentPosition.x,
          y: self.cy.height() / 2 - currentPosition.y,
        });
      }
    });
  }

  showText(isShowText) {
    this.isShowText = isShowText;
    this.cy.collection('edge').removeClass('showText');
    if (this.isShowText) {
      this.cy.collection('edge').addClass('showText');
    }
  }

  zoomInOrOut({ setScale = 1, isIn = false }) {
    if (setScale !== 1) {
      this.cy.zoom({
        level: setScale,
      });
      return;
    }
    const step = 0.1;
    const maxLevel = 3;
    const minLevel = 0.5;
    let currentLevel = this.cy.zoom() + step * (isIn ? 1 : -1);
    if (currentLevel >= maxLevel) currentLevel = maxLevel;
    if (currentLevel <= minLevel) currentLevel = minLevel;
    this.cy.zoom({
      level: currentLevel,
    });
  }

  toImage() {
    return this.cy.png({ full: true, bg: 'rgba(255, 255, 255, 0)', scale: 1 });
  }

  /* @__NODEFENCE__ */
  handleDescFilter(arr) {
    if (arr.length) {
      this.chosenDesc = arr;
      this.showText(true);
    } else {
      this.showText(false);
    }
  }

  /* @__NODEFENCE__ */
  handleTypeFilter(arr) {
    this.chosenType = arr;
    const self = this;
    // 取消高亮
    this.cancelHighLight();
    this.cy.collection('edge').addClass('dull');
    this.cy.collection('node').addClass('dull');
    this.cy.edges(function (ele) {
      const edgeType = ele.data('type');
      if (self.chosenType.includes(edgeType)) {
        ele.removeClass('dull');
        ele.addClass('edgeActive');
        ele.connectedNodes().removeClass('dull');
      }
    });
    this.isFocus = true;
    this.isFiltered = arr.length < 3;
  }

  cancelHighLight() {
    // 取消高亮
    this.isFocus = false;
    this.isFiltered = false;
    this.cy.collection('node').removeClass('dull');
    this.cy.collection('edge').removeClass('dull');
    this.cy.collection('edge').removeClass('edgeActive');
  }

  // iframe 时使用
  delayZoom() {
    this.cy.zoomingEnabled(false);
    setTimeout(() => {
      this.cy.zoomingEnabled(true);
    }, 2000);
  }

  sizeChange() {
    const newWidth = this.$selector.width();
    const newHeight = this.$selector.height();
    if (!this.cy) return;
    if (this.size.width === newWidth && this.size.height === newHeight) return;
    this.size = {
      width: newWidth,
      height: newHeight,
    };
    this.cy.center();
  }
}
