/* eslint-disable func-names */
/* eslint-disable no-param-reassign */
/* eslint-disable consistent-return */
import resizeDetector from 'element-resize-detector';
import _ from 'lodash';

import dataLoader from './data';
import { actionTypes, gUiToolbox, gUiToolboxAction } from '../g-ui-toolbox';
import gUiFooter from '../g-ui-footer';
import gUiLoading from '../g-ui-loading';
import gUiNoData from '../g-ui-no-data';
import appPerson from '../g-app-person';
import appCompany from '../g-app-company';
import appFilter from '../g-app-filter';
import Chart from './chart';
import popoverHelper from '../utils/popoverHelper';
import globalUtils from '../utils/utils';

export default {
  name: 'g-risk',
  components: {
    [gUiToolbox.name]: gUiToolbox,
    [gUiToolboxAction.name]: gUiToolboxAction,
    [gUiFooter.name]: gUiFooter,
    [gUiLoading.name]: gUiLoading,
    [gUiNoData.name]: gUiNoData,
    [appFilter.name]: appFilter,
  },
  props: {
    actionTypes: {
      type: Object,
      default: () => actionTypes,
    },
    containerName: {
      type: String,
      default: 'risk-chart',
    },
    keyNo: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isInit: false,
      isLoading: true,
      noData: false,
      scale: 1,
      paths: [],
      // 操作栏
      isFullScreen: false,
      isSaving: false,
      isFilterOpened: false,
      isShowText: true,
      typeList: [
        {
          key: 'type',
          title: '风险类型',
          tips: '',
          isMuti: true,
          list: [
            { key: 'JRISK', value: '司法风险', selected: true, default: true },
            { key: 'RRISK', value: '监管风险', selected: true, default: true },
            { key: 'BRISK', value: '经营风险', selected: true, default: true },
          ],
        },
        {
          key: 'desc',
          title: '风险描述字段',
          tips: '',
          isMuti: true,
          list: [
            { key: 'type', value: '风险行为', selected: true, default: true },
            { key: 'count', value: '次数', selected: true, default: true },
            { key: 'amount', value: '金额', selected: false, default: false },
          ],
        },
      ],
    };
  },
  mounted() {
    this.cleanup();
    this.$nextTick(() => {
      this.attachResizeDetector();
      this.onRefresh();
      this.isFullScreen =
        document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement || document.msFullscreenElement;
      this.$nextTick(() => {
        document.addEventListener('fullscreenchange', () => {
          this.isFullScreen = !!document.fullscreenElement;
        });
        document.addEventListener('webkitfullscreenchange', () => {
          this.isFullScreen = !!document.webkitFullscreenElement;
        });
        document.addEventListener('mozfullscreenchange', () => {
          this.isFullScreen = !!document.mozFullScreenElement;
        });
        document.addEventListener('MSFullscreenChange', () => {
          this.isFullScreen = !!document.msFullscreenElement;
        });
      });
    });
  },
  methods: {
    cleanup() {
      $('#risk-chart').empty();
    },
    onRefresh() {
      this.cleanup();
      // 初始化筛选项
      this.resetChosen();
      this.isLoading = true;
      this.chart = new Chart(this.containerName);
      dataLoader
        .getRiskGraph({ keyNo: this.keyNo })
        .then((res) => {
          if (!res.relationships.length && !res.nodes.length) {
            this.noData = true;
            return;
          }
          this.noData = false;
          this.chart.init({ data: res, iframe: this.iframe });
          setTimeout(() => {
            this.chart.domUpdate();
          });
        })
        .catch(() => {
          this.isLoading = false;
          this.noData = true;
        });
      this.chart.on('cancelLoading', () => {
        this.isInit = true;
        this.isLoading = false;
      });

      this.chart.on('resetFilterType', () => {
        this.resetChosen('type');
      });

      this.chart.on('clickCy', () => {
        if (this.isFilterOpened) {
          this.isFilterOpened = false;
        } else {
          this.chart.cancelHighLight();
          this.resetChosen('type');
        }
      });

      this.chart.on('showNodeDetail', ({ data, position, size }) => {
        if (!data.properties.keyNo && data.properties.name && data.properties.name.length <= 13) {
          return;
        }
        let component = appCompany;
        const nodeType = data.labels[0];
        if (nodeType === 'Person') {
          component = appPerson;
        } else if (nodeType === 'Government' && data.properties.name && data.properties.name.length <= 13) {
          return;
        }
        if (data) {
          data.delayTimer = setTimeout(() => {
            const $instance = $(`#detail-popover-${data.id}`);
            if ($instance.length > 0) {
              $instance.stop(true).fadeIn(0);
              return;
            }

            const vmData = { id: data.properties.keyNo, name: data.properties.name, nodeType, type: 'risk' };
            if (data.properties.keyNo && data.properties.keyNo[0] === 'p') {
              vmData.eid = this.keyNo;
            }
            popoverHelper.showPopover({
              component,
              data: vmData,
              targetPosition: position,
              container: $('#risk-chart-container'),
              identity: `detail-popover-${data.id}`,
              targetSize: { width: size.width * this.scale, height: size.height * this.scale },
            });
          }, 400);
        }
      });
      this.chart.on('hideNodeDetail', ({ data }) => {
        if (data && data.delayTimer) {
          clearTimeout(data.delayTimer);
          delete data.delayTimer;
        }

        $(`#detail-popover-${data.id}`).fadeOut(400, function () {
          $(this).remove();
        });
      });
      this.chart.on('cyZoom', ({ k }) => {
        this.scale = k;
      });
    },
    showText() {
      this.isShowText = !this.isShowText;
      if (this.isShowText) {
        this.chosenDesc = ['type', 'count'];
        this.chart.handleDescFilter(this.chosenDesc);
      } else {
        this.chosenDesc = [];
      }
      this.chart.showText(this.isShowText);
    },
    handleFilter(list) {
      this.typeList = list;
      const chosenList = {};
      _.forEach(list, (singleList) => {
        chosenList[singleList.key] = [];
        _.forEach(singleList.list, (o) => {
          if (o.selected) {
            chosenList[singleList.key].push(o.key);
          }
        });
      });
      this.changeDesc(chosenList.desc || []);
      this.changeType(chosenList.type || []);
    },
    changeDesc(list) {
      // 操作风险行为时，若文字为未选中 则勾选
      this.isShowText = list.length > 0;
      this.chart.handleDescFilter(list);
    },
    changeType(list) {
      this.chart.handleTypeFilter(list);
    },
    resetChosen(type) {
      _.forEach(this.typeList, (singleList) => {
        if (!type || (type && singleList.key === type)) {
          _.forEach(singleList.list, (o) => {
            o.selected = o.default;
          });
        }
      });
    },
    redraw() {
      // 初始化筛选项
      this.resetChosen();
      this.isLoading = true;
      this.cleanup();
      this.chart.domUpdate();
    },
    onZoomIn() {
      this.chart.zoomInOrOut({ isIn: true });
    },
    onZoomOut() {
      this.chart.zoomInOrOut({ isIn: false });
    },
    onFullScreen() {
      const element = $('.risk-chart-container')[0];
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
      this.isFullScreen = true;
    },
    onExitFullScreen() {
      if (this.isFullScreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
        this.isFullScreen = false;
      }
    },
    onSave() {
      const imgData = this.chart.toImage();
      globalUtils.downloadImage(imgData, this.name, '风险图谱');
    },
    attachResizeDetector() {
      this.resizeDetector = resizeDetector();
      this.resizeDetector.listenTo(
        $('.risk-chart-container')[0],
        _.debounce(() => {
          if (this.chart) {
            this.chart.sizeChange();
          }
        }, 200)
      );
    },
  },
};
