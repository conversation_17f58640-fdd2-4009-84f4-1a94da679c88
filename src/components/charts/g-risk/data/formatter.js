/* eslint-disable no-param-reassign */
/* eslint-disable no-underscore-dangle */
import _ from 'lodash';

import riskType from './riskType';

class GroupMember {
  constructor() {
    this.reset();
  }

  init({ keyNo, data }) {
    this.data = data;
    this.keyNo = keyNo;
    this._rootNode = null;
  }

  reset() {
    this.data = {};
    this.keyNo = '';
    this._rootNode = null;
  }

  /* @__NODEFENCE__ */
  format() {
    // 关系整合
    let afterFilterRel = [];
    // let hasFilteredId = []
    // 限制关系最多为1500
    this.data.relationships = this.data.relationships.slice(0, 1500);
    for (let i = 0; i < this.data.relationships.length; i++) {
      const currentRel = this.data.relationships[i];
      if (currentRel) {
        currentRel.source = _.cloneDeep(currentRel.startNode);
        currentRel.target = _.cloneDeep(currentRel.endNode);
        // 组合text
        const descObj = riskType.getRiskDesc(currentRel.properties);
        currentRel.linkText = [descObj];
        afterFilterRel.push(currentRel);
      }
    }
    _.forEach(this.data.nodes, (node) => {
      const keyNo = _.get(node, 'properties.keyNo', '');
      const nodeType = _.get(node, 'labels.[0]', '');
      if (keyNo === this.keyNo) {
        node.isMain = true;
      }
      if (nodeType === 'Government') {
        node.isGov = true;
      } else if (nodeType === 'Person') {
        node.isPerson = true;
      } else {
        node.isCompany = true;
      }
    });
    afterFilterRel = _.uniqBy(afterFilterRel, 'id');
    // this.data.nodes = _.uniqBy(this.data.nodes, 'id')

    return { relationships: afterFilterRel, nodes: this.data.nodes };
  }
}

export default new GroupMember();
