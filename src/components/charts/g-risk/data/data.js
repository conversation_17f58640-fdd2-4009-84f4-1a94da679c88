/* eslint-disable */
export default {
  "Status": 200,
  "Message": "OK",
  "Result": {
    "results": [{
      "columns": [],
      "data": [{
        "graph": {
          "nodes": [{
            "id": "-7265803277487577000",
            "labels": ["Company"],
            "properties": {
              "keyNo": "9cce0780ab7644008b73bc2120479d31",
              "name": "小米科技有限责任公司"
            }
          }, {
            "id": "4064263720500495000",
            "labels": ["Government"],
            "properties": {
              "keyNo": "g1",
              "name": "这应该可以组成三行的"
            }
          }, {
            "id": "-9032840179063523000",
            "labels": ["Person"],
            "properties": {
              "keyNo": "p1",
              "name": "这应该可以组成"
            }
          }, {
            "id": "8069292045054636000",
            "labels": ["Person"],
            "properties": {
              "keyNo": "p8ca4283fb80cf988920545e99aef3a4",
              "name": "黎万强"
            }
          }, {
            "id": "12",
            "labels": ["Person"],
            "properties": {
              "keyNo": "p2",
              "name": "这应该可以组成三行且超出省略号的文字"
            }
          }, {
            "id": "13",
            "labels": ["Person"],
            "properties": {
              "keyNo": "p2",
              "name": "这应该可以组成三行且超出省略号的文字"
            }
          }, {
            "id": "14",
            "labels": ["Person"],
            "properties": {
              "keyNo": "p2",
              "name": "这应该可以组成三行且超出省略号的文字"
            }
          }, {
            "id": "15",
            "labels": ["Person"],
            "properties": {
              "keyNo": "p2",
              "name": "这应该可以组成三行且超出省略号的文字"
            }
          }, {
            "id": "16",
            "labels": ["Person"],
            "properties": {
              "keyNo": "p2",
              "name": "这应该可以组成三行且超出省略号的文字"
            }
          }, {
            "id": "17",
            "labels": ["Person"],
            "properties": {
              "keyNo": "p2",
              "name": "这应该可以组成三行且超出省略号的文字"
            }
          }, {
            "id": "18",
            "labels": ["Person"],
            "properties": {
              "keyNo": "p2",
              "name": "这应该可以组成三行且超出省略号的文字"
            }
          }, {
            "id": "19",
            "labels": ["Person"],
            "properties": {
              "keyNo": "p2",
              "name": "这应该可以组成三行且超出省略号的文字"
            }
          }, {
            "id": "20",
            "labels": ["Person"],
            "properties": {
              "keyNo": "p2",
              "name": "这应该可以组成三行且超出省略号的文字"
            }
          }, {
            "id": "21",
            "labels": ["Person"],
            "properties": {
              "keyNo": "p2",
              "name": "这应该可以组成三行且超出省略号的文字"
            }
          }, {
            "id": "22",
            "labels": ["Person"],
            "properties": {
              "keyNo": "p2",
              "name": "这应该可以组成三行且超出省略号的文字"
            }
          }, {
            "id": "23",
            "labels": ["Person"],
            "properties": {
              "keyNo": "p2",
              "name": "这应该可以组成三行且超出省略号的文字"
            }
          }, {
            "id": "24",
            "labels": ["Person"],
            "properties": {
              "keyNo": "p2",
              "name": "这应该可以组成三行且超出省略号的文字"
            }
          }, {
            "id": "25",
            "labels": ["Person"],
            "properties": {
              "keyNo": "p2",
              "name": "这应该可以组成三行且超出省略号的文字"
            }
          }, {
            "id": "11111",
            "labels": ["Government"],
            "properties": {
              "keyNo": "11111",
              "name": "XX政府"
            }
          }, {
            "id": "222",
            "labels": ["Government"],
            "properties": {
              "keyNo": "222",
              "name": "XX政府"
            }
          }, {
            "id": "333",
            "labels": ["Government"],
            "properties": {
              "keyNo": "333",
              "name": "XX政府"
            }
          }, {
            "id": "444",
            "labels": ["Government"],
            "properties": {
              "keyNo": "444",
              "name": "XX政府"
            }
          }, {
            "id": "555",
            "labels": ["Government"],
            "properties": {
              "keyNo": "555",
              "name": "XX政府"
            }
          }, {
            "id": "666",
            "labels": ["Government"],
            "properties": {
              "keyNo": "666",
              "name": "XX政府"
            }
          }],
          "relationships": [{
            "id": "8ff9c7cacb59cc048c8fea7a65d21fd9",
            "type": "JRISK",
            "startNode": "11111",
            "endNode": "-9032840179063523000",
            "properties": {
              "riskType": "TAXG",
              "cnt": "1011",
              "amount": "",
              "amountadd": "",
              "amountpunish": "",
              "inLineText": "同案被限制高消费, 11次"
            }
          }, {
            "id": "8ff9c7cacb59cc048c8fea7a65d21fd9",
            "type": "JRISK",
            "startNode": "-9032840179063523000",
            "endNode": "8069292045054636000",
            "properties": {
              "riskType": "TAXG",
              "cnt": "1011",
              "amount": "",
              "amountadd": "",
              "amountpunish": "",
              "inLineText": "同案被限制高消费, 1011次"
            }
          }, {
            "id": "8ff9c7cacb59cc048c8fea7a65d21f99",
            "type": "BRISK",
            "startNode": "-9032840179063523000",
            "endNode": "8069292045054636000",
            "properties": {
              "riskType": "DCDY",
              "cnt": "1051",
              "amount": "2023",
              "amountadd": "",
              "amountpunish": "",
              "inLineText": "向TA抵押动产, 1051次, 担保主债券2023万元"
            }
          }, {
            "id": "25f0be59b6093cba983926a074f16e80",
            "type": "BRISK",
            "startNode": "-9032840179063523000",
            "endNode": "-7265803277487577000",
            "properties": {
              "riskType": "DCDY",
              "cnt": "1051",
              "amount": "2023",
              "amountadd": "",
              "amountpunish": "",
              "inLineText": "向TA抵押动产, 1051次, 担保主债券2023万元"
            }
          }, {
            "id": "03c78101d0839276d73d8265fda3ad07",
            "type": "JRISK",
            "startNode": "-9032840179063523000",
            "endNode": "4064263720500495000",
            "properties": {
              "riskType": "QSXZ",
              "cnt": "1023",
              "amount": "",
              "amountadd": "",
              "amountpunish": "",
              "inLineText": "行政起诉, 1023次"
            }
          }, {
            "id": "ac27f77e8aaf0b8d69f5babb417a19ba",
            "type": "JRISK",
            "startNode": "-7265803277487577000",
            "endNode": "-9032840179063523000",
            "properties": {
              "riskType": "PCCZ",
              "cnt": "1002",
              "amount": "",
              "amountadd": "",
              "amountpunish": "",
              "inLineText": "申请破产重整, 1002次"
            }
          }, {
            "id": "ac27f77e8aaf0b8d69f5babb417a1999",
            "type": "RRISK",
            "startNode": "-7265803277487577000",
            "endNode": "-9032840179063523000",
            "properties": {
              "riskType": "PCCZ",
              "cnt": "1005",
              "amount": "",
              "amountadd": "",
              "amountpunish": "",
              "inLineText": "申请破产重整, 1005次"
            }
          }, {
            "id": "b807ade9a6d5f52b2da036fd1bc8a169",
            "type": "RRISK",
            "startNode": "4064263720500495000",
            "endNode": "-9032840179063523000",
            "properties": {
              "riskType": "QSGG",
              "cnt": "1041",
              "amount": "2015",
              "amountadd": "3004",
              "amountpunish": "",
              "inLineText": "欠税公告, 处理1041次, 欠税余额2015万元, 当前新发生欠税余额3004万元"
            }
          }, {
            "id": "c8cb315bccb353944e32c71ac9a21db5",
            "type": "JRISK",
            "startNode": "-7265803277487577000",
            "endNode": "4064263720500495000",
            "properties": {
              "riskType": "QSXZ",
              "cnt": "1022",
              "amount": "",
              "amountadd": "",
              "amountpunish": "",
              "inLineText": "行政起诉, 1022次"
            }
          }, {
            "id": "c8cb315bccb353944e32c71ac9a21d85",
            "type": "JRISK",
            "startNode": "-7265803277487577000",
            "endNode": "4064263720500495000",
            "properties": {
              "riskType": "PCCZ",
              "cnt": "1022",
              "amount": "",
              "amountadd": "",
              "amountpunish": "",
              "inLineText": "申请破产重整, 1022次"
            }
          }, {
            "id": "34cbf299714e5dcce106ffe1e2a016e7",
            "type": "RRISK",
            "startNode": "4064263720500495000",
            "endNode": "-7265803277487577000",
            "properties": {
              "riskType": "WGCL",
              "cnt": "1042",
              "amount": "2016",
              "amountadd": "",
              "amountpunish": "",
              "inLineText": "违规处理, 1042次, 处分2016万元"
            }
          }]
        }
      }]
    }],
    "errors": []
  }
}
/* eslint-disable */