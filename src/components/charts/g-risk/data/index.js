import { graph } from '@/shared/services';

// import mockData from './data'
import formatter from './formatter';

const getRiskGraph = ({ keyNo }) => {
  return new Promise((resolve, reject) => {
    graph
      .getRiskGraph({ keyNo })
      .then((res) => {
        const data = res.Result.results[0].data[0].graph;
        formatter.init({ keyNo, data });
        const detail = formatter.format();
        resolve(detail);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export default {
  getRiskGraph,
};
