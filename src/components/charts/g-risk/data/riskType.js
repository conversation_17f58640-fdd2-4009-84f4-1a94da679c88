/* eslint-disable */
/* @__NODEFENCE__ */
const getRiskDesc = ({ riskType, cnt, amount, amountadd, amountpunish }) => {
  let obj = {}
  switch (riskType) {
    case 'PCCZ':
      obj = {
        _type: '申请破产重整',
        _count: `${cnt}次`
      }
      break
    case 'SQXG':
      obj = {
        _type: '申请限制高消费',
        _count: `${cnt}次`
      }
      break
    case 'GLXG':
      obj = {
        _type: '使TA被关联限制高消费',
        _count: `${cnt}次`
      }
      break
    case 'TAXG':
      obj = {
        _type: '同案被限制高消费',
        _count: `${cnt}次`
      }
      break
    case 'TASX':
      obj = {
        _type: '同案失信被执行人',
        _count: `${cnt}次`
      }
      break
    case 'SQZX':
      obj = {
        _type: '申请成为被执行人',
        _count: `${cnt}次`,
        _amount: +amount ? `${(amount / 10000).toFixed(2)}万元` : ''
      }
      break
    case 'QSMS':
      obj = {
        _type: '起诉',
        _count: `${cnt}次`
      }
      break
    case 'QSXZ':
      obj = {
        _type: '起诉',
        _count: `${cnt}次`
      }
      break
    case 'SFPM':
      obj = {
        _type: '司法拍卖',
        _count: `${cnt}个资产`,
        _amount: +amount ? `起拍价总计${(amount / 10000).toFixed(2)}万元` : ''
      }
      break
    case 'XJPG':
      obj = {
        _type: '询价评估',
        _count: `${cnt}个标的物`,
        _amount: +amount ? `评估结果${(amount / 10000).toFixed(2)}万元` : ''
      }
      break
    case 'GQDJ':
      obj = {
        _type: '股权冻结',
        _count: `${cnt}次`
      }
      break
    case 'YZWF':
      obj = {
        _type: '严重违法',
        _count: `当前列入${cnt}次`
      }
      break
    case 'JYYC':
      obj = {
        _type: '经营异常',
        _count: `当前列入${cnt}次`
      }
      break
    case 'XZCF':
      obj = {
        _type: '行政处罚',
        _count: `${cnt}次`,
        _amount: +amount ? `处罚${(amount / 10000).toFixed(2)}万元` : ''
      }
      break
    case 'HBCF':
      obj = {
        _type: '环保处罚',
        _count: `${cnt}次`
      }
      break
    case 'SSWF':
      let calcAmount = () => {
        let str = ''
        if (+amountadd && +amountpunish) {
          str = `追缴税款${(amountadd / 10000).toFixed(2)}万元，罚款${(amountpunish / 10000).toFixed(2)}万元`
        } else if (+amountadd) {
          str = `追缴税款${(amountadd / 10000).toFixed(2)}万元`
        } else if (+amountpunish) {
          str = `罚款${(amountpunish / 10000).toFixed(2)}万元`
        }
        return str
      }
      obj = {
        _type: '税收违法',
        _count: `处罚${cnt}次`,
        _amount: calcAmount()
      }
      break
    case 'QSGG':
      let calcAmount1 = () => {
        let str = ''
        if (+amount && +amountadd) {
          str = `欠税余额${(amount / 10000).toFixed(2)}万元，当前新发生欠税余额${(amountadd / 10000).toFixed(2)}万元`
        } else if (+amount) {
          str = `欠税余额${(amount / 10000).toFixed(2)}万元`
        } else if (+amountadd) {
          str = `当前新发生欠税余额${(amountadd / 10000).toFixed(2)}万元`
        }
        return str
      }
      obj = {
        _type: '欠税公告',
        _count: `处理${cnt}次`,
        _amount: calcAmount1()
      }
      break
    case 'WGCL':
      obj = {
        _type: '违规处理',
        _count: `${cnt}次`,
        _amount: +amount ? `处分${(amount / 10000).toFixed(2)}万元` : ''
      }
      break
    case 'CCJC':
      obj = {
        _type: '抽查检查',
        _count: `${cnt}次不合格`
      }
      break
    case 'GQCZ':
      obj = {
        _type: '向TA出质股权',
        _count: `${cnt}次`
      }
      break
    case 'DCDY':
      obj = {
        _type: '向TA抵押动产',
        _count: `${cnt}次`,
        _amount: +amount ? `担保主债权${(amount / 10000).toFixed(2)}万元` : ''
      }
      break
    case 'TDDY':
      let calcAmount2 = () => {
        let str = ''
        if (+amount && +amountadd) {
          str = `抵押${+amountadd * 10000}平方米、${(amount / 10000).toFixed(2)}万元`
        } else if (+amountadd) {
          str = `抵押${+amountadd * 10000}平方米`
        } else if (+amount) {
          str = `抵押${(amount / 10000).toFixed(2)}万元`
        }
        return str
      }
      obj = {
        _type: '向TA抵押土地',
        _count: `${cnt}次`,
        _amount: calcAmount2()
      }
      break
    case 'DWDB':
      obj = {
        _type: '请TA做担保',
        _count: `${cnt}次`,
        _amount: +amount ? `担保${(amount / 10000).toFixed(2)}万元` : ''
      }
      break
    case 'GSCG':
      obj = {
        _type: '申请TA持有的票据无效',
        _count: `${cnt}次`,
        _amount: +amount ? `票面金额${(amount / 10000).toFixed(2)}万元` : ''
      }
      break
  }
  return obj
}

export default { getRiskDesc }

/* eslint-disable */
