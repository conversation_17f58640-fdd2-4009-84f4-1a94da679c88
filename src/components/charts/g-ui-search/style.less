.ui-chart-search {
  .header {
    height: 56px;
    line-height: 56px;
    border-bottom: 1px solid #eee;
    font-size: 16px;
    color: #333;
    position: relative;
    padding: 0 15px;

    i {
      position: absolute;
      right: 15px;
      top: 15px;
      cursor: pointer;
      color: #128bed;
      font-size: 20px;
    }
  }

  .body {
    padding: 15px 0;
  }

  .list-container {
    height: 259px;
    overflow: auto;
    position: relative;
    margin-top: 15px;

    .single-ent {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: keep-all;
      cursor: pointer;
      color: #333;
      font-size: 16px;
      position: relative;
      padding: 10px 15px;

      &:hover {
        background-color: #f3f9fd;
        color: #128bed;
      }

      .name {
        line-height: 24px;
        position: relative;
        top: 2px;
        margin-left: 8px;
      }
    }

    .no-data {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);

      > img {
        display: block;
        width: 200px;
        height: 200px;
      }

      > p {
        color: #999;
        font-size: 14px;
        text-align: center;
      }
    }
  }

  .input-container {
    padding: 0 15px;
  }
}
