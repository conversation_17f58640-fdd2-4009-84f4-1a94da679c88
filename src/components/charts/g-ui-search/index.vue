<template>
  <div class="ui-chart-search">
    <div class="header">
      搜索
      <a-icon type="close" @click="close" />
    </div>
    <div class="body">
      <div class="input-container">
        <a-input v-model="searchKey" placeholder="请输入集团成员名称或人员名称进行搜索" allow-clear />
      </div>
      <div class="list-container" ref="list">
        <g-ui-loading v-if="searchLoading"></g-ui-loading>
        <div v-if="searchNoData" class="no-data">
          <g-ui-no-data></g-ui-no-data>
        </div>
        <div class="list" v-else-if="isVisible">
          <div class="single-ent" v-for="(item, index) in renderList" :key="index" @click="chooseMember(item)">
            <q-entity-avatar class="logo" :key-no="item.keyNo" :has-image="!!item.hasImage" :name="item.name" :size="30"></q-entity-avatar>
            <span class="name">{{ item.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script src="./component.js"></script>
<style lang="less" scoped src="./style.less"></style>
