import _ from 'lodash';

import uiLoading from '../g-ui-loading';
import uiNoData from '../g-ui-no-data';

export default {
  name: 'g-ui-search',
  components: {
    [uiLoading.name]: uiLoading,
    [uiNoData.name]: uiNoData,
  },
  props: {
    searchLoading: {
      default: true,
      type: Boolean,
    },
    searchNoData: {
      default: false,
      type: Boolean,
    },
    searchList: {
      // 手动做分页
      default: () => [],
      type: Array,
    },
    isVisible: {
      default: false,
      type: Boolean,
    },
  },
  data() {
    return {
      searchKey: '',
      page: 1,
      renderList: [],
    };
  },
  computed: {
    loadMore() {
      return this.renderList.length < this.searchList.length;
    },
  },
  watch: {
    isVisible(val) {
      if (val) {
        this.searchKey = '';
      }
      this.isVisible = val;
    },
    searchKey(val) {
      const str = val.trim();
      this.$emit('search', str);
    },
    searchList: {
      handler(val) {
        this.page = 1;
        this.renderList = this.searchList.slice(this.page - 1, this.page * 10);
      },
      immediate: true,
    },
    searchLoading(val) {
      this.searchLoading = val;
    },
    searchNoData(val) {
      this.searchNoData = val;
    },
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs.list) {
        $(this.$refs.list).on(
          'scroll',
          _.debounce(() => {
            const distance = this.$refs.list.scrollHeight - this.$refs.list.scrollTop - this.$refs.list.clientHeight;
            if (distance < 20 && this.loadMore) {
              this.page++;
              this.renderList = this.renderList.concat(this.searchList.slice((this.page - 1) * 10, this.page * 10));
            }
          }, 50)
        );
      }
    });
  },
  methods: {
    chooseMember(item) {
      this.$emit('choose', item);
    },
    close() {
      this.$emit('close');
    },
  },
  destroyed() {
    $(this.$refs.list).off('scroll');
  },
};
