<template>
  <div class="chart-footer">
    <div>
      以上数据是
      <img alt="footer" src="./images/logo.svg" />
      大数据分析引擎基于公开信息挖掘的成果，仅供参考。该成果不构成任何明示或暗示的观点或保证。
    </div>
    <div class="time" v-if="refreshTime">当前图谱更新时间：{{ refreshTime }}</div>
  </div>
</template>

<script>
export default {
  name: 'gUiFooter',
  props: {
    text: {
      type: String,
      default: '',
    },
    refreshTime: {
      type: String,
      default: '',
    },
  },
};
</script>

<style lang="less" scoped>
.chart-footer {
  text-align: center;
  font-size: 12px;
  line-height: 14px;
  position: absolute;
  color: #aaa;
  width: 100%;
  bottom: 0;
  z-index: 8;
  padding: 10px 0 6px 0;
  background: #fff;
  > div {
    display: inline-block;

    img {
      vertical-align: middle;
      display: inline-block;
      width: 55px;
    }

    &.time {
      margin-left: 60px;
    }
  }
}
</style>
