ul {
  width: 48px;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  border: 1px solid #dedede;
  background: #fff;

  li {
    width: 46px;
    height: 64px;
    text-align: center;
    box-sizing: border-box;
    font-size: 12px;
    color: #666;
    padding: 5px;
    list-style-type: none;
    cursor: pointer;
    user-select: none;
    position: relative;
    padding-top: 8px;

    >span {
      display: block;
      width: 30px;
      height: 30px;
      margin: 0 auto;
      background: url("./images/icons.png");
      background-size: 480px 60px;

      &.refresh {
        background-position: -60px 0;
      }

      &.zoom-in {
        background-position: -90px 0;
      }

      &.zoom-out {
        background-position: -120px 0;
      }

      &.save {
        background-position: -150px 0;
      }

      &.full-screen {
        background-position: -180px 0;
      }

      &.exit-full-screen {
        background-position: -210px 0;
      }

      &.filter {
        background-position: -240px 0;
      }

      &.show-text {
        background-position: -270px 0;
      }

      &.select {
        background-position: -300px 0;
      }

      &.report {
        background-position: -330px 0;
      }

      &.horizontal {
        background-position: -360px 0;
      }

      &.vertical {
        background-position: -390px 0;
      }

      &.full-name {
        background-position: -420px 0;
      }

      &.short-name {
        background-position: -450px 0;
      }

      &.fold {
        background-position: -30px 0;
      }

      &.expand {
        background-position: 0 0;
      }
    }

    &:hover,
    &.active {
      color: #fff;
      background-color: #128BED;

      >.refresh {
        background-position: -60px -30px;
      }

      >.zoom-in {
        background-position: -90px -30px;
      }

      >.zoom-out {
        background-position: -120px -30px;
      }

      >.save {
        background-position: -150px -30px;
      }

      >.full-screen {
        background-position: -180px -30px;
      }

      >.exit-full-screen {
        background-position: -210px -30px;
      }

      >.filter {
        background-position: -240px 30px;
      }

      >.show-text {
        background-position: -270px -30px;
      }

      >.select {
        background-position: -300px -30px;
      }

      >.report {
        background-position: -330px -30px;
      }

      >.horizontal {
        background-position: -360px -30px;
      }

      >.vertical {
        background-position: -390px -30px;
      }

      >.full-name {
        background-position: -420px -30px;
      }

      >.short-name {
        background-position: -450px -30px;
      }

      >.fold {
        background-position: -30px -30px;
      }

      >.expand {
        background-position: 0 -30px;
      }
    }

    &.busy {
      color: #fff;
      background-color: #128BED;

      >span {
        background: url('./images/loading.png') !important;
        background-size: 30px 30px !important;
        background-position: 0 0 !important;
        animation: spin 2s linear infinite !important;
      }

      @keyframes spin {
        0% {
          -webkit-transform: rotate(360deg);
          -ms-transform: rotate(360deg);
          transform: rotate(360deg);
        }

        100% {
          -webkit-transform: rotate(0deg);
          -ms-transform: rotate(0deg);
          transform: rotate(0deg);
        }
      }
    }
  }
}