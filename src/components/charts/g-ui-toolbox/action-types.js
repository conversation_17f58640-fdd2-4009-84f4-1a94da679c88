export default {
  zoomIn: {
    text: '放大',
    className: 'zoom-in',
  },
  zoomOut: {
    text: '缩小',
    className: 'zoom-out',
  },
  refresh: {
    text: '刷新',
    className: 'refresh',
  },
  fullScreen: {
    text: '全屏',
    className: 'full-screen',
  },
  exitFullScreen: {
    text: '退出',
    className: 'exit-full-screen',
  },
  save: {
    text: '保存',
    className: 'save',
  },
  filter: {
    text: '模板',
    className: 'filter',
  },
  edit: {
    text: '编辑',
    className: 'filter',
  },
  showText: {
    text: '文字',
    className: 'show-text',
  },
  vertical: {
    text: '纵向',
    className: 'vertical',
  },
  horizontal: {
    text: '横向',
    className: 'horizontal',
  },
  select: {
    text: '筛选',
    className: 'select',
  },
  search: {
    text: '搜索',
    className: 'select',
  },
  filter2: {
    text: '筛选',
    className: 'filter',
  },
  report: {
    text: '报告',
    className: 'report',
  },
  fullName: {
    text: '全称',
    className: 'full-name',
  },
  shortName: {
    text: '简称',
    className: 'short-name',
  },
  fold: {
    text: '收起',
    className: 'fold',
  },
  expand: {
    text: '展开',
    className: 'expand',
  },
};
