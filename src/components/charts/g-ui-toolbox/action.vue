<!--
  Created by <PERSON> on - 2019/07/01.
-->

<template>
  <div @click="onClick" :class="{ active: isActive, busy: isBusy }" @mouseenter="onMouseEnter" @mouseleave="onMouseLeave">
    <span :class="actionType.className"></span>
    {{ (actionType.className === 'fold' || actionType.className === 'expand') && isBusy ? '处理中' : actionType.text }}
  </div>
</template>

<script>
export default {
  name: 'g-ui-toolbox-action',

  props: {
    actionType: {
      text: String,
      className: String,
    },
    isActive: {
      type: <PERSON>olean,
    },
    isBusy: {
      type: Boolean,
    },
  },

  methods: {
    onClick(e) {
      this.$emit('click', e);
    },
    onMouseEnter(e) {
      this.$emit('mouseenter', e);
    },
    onMouseLeave(e) {
      this.$emit('mouseleave', e);
    },
  },
};
</script>

<style src="./style.less" scoped lang="less"></style>
