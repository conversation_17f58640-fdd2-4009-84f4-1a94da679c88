<template>
  <div class="path-container">
    <div class="path-scroll" style="max-height: 150px">
      <div class="content">
        <div v-if="finalPercent != '0%'" class="path-title">
          最终受益股份<a-popover placement="bottom" v-if="tips">
            <div slot="content" style="width: 260px">
              {{ tips }}
            </div>
            <q-icon type="icon-a-shuomingxian" class="icon-zhushi"></q-icon> </a-popover
          >：<span>{{ finalPercent }}</span>
        </div>
        <div v-for="(path, index) in paths" :key="`path-${index}`" class="tdpath">
          <p v-if="pathData.DataType == 6" class="tips">注：未识别出股比>25%的自然人，因此决定以最终法定代表人作为受益所有人结果</p>
          <span v-for="(co, i) in path" :key="`p-${i}`">
            <q-entity-link :coy-obj="{ KeyNo: co.KeyNo, Name: co.Name }"></q-entity-link>
            <span class="line-wrap">
              <span class="line"></span>
              <span v-if="pathData.DataType == 6">法定代表人</span>
              <span v-if="pathData.DataType != 6" :class="co.DataType == 5 ? 't5' : 't'">{{ co.Percent || '0%' }}</span>
            </span>
          </span>
          <q-entity-link :coy-obj="{ KeyNo: eid, Name: ename }"></q-entity-link>
        </div>
      </div>
    </div>
  </div>
</template>
<script src="./component.js"></script>
<style lang="less" src="./style.less" scoped></style>
