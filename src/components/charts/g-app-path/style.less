.path-container {
  .path-bg {
    background: #f6f6f6;
    height: 5px;
  }

  .path-scroll {
    max-height: 350px;
    overflow-y: auto;
  }

  .content {
    padding-top: 16px;
  }
}

.path-title {
  font-size: 13px;
  color: #999;
  margin-bottom: 8px;

  .icon-zhushi {
    margin-left: 4px;
  }

  span {
    color: #333;
  }
}

.tdpath {
  margin-top: 8px;
  line-height: 34px;

  .tips {
    font-size: 12px;
    color: #999;
    margin-bottom: 10px;
    line-height: 1.4;
  }

  .pt {
    font-size: 14px;
    color: #333;
    font-weight: bold;
    margin-bottom: 5px;
  }

  a {
    color: #666;
    text-decoration: none;
    font-size: 13px;
  }

  a:hover {
    color: #128bed;
  }

  .line-wrap {
    margin: 0 8px;
    position: relative;
    padding: 0 12px 0 4px;
    display: inline-block;

    .line {
      width: 100%;
      display: inline-block;
      position: absolute;
      height: 1px;
      background-color: #bbb;
      left: 0;
      top: 50%;
      transform: translate(0, -50%);

      &::after {
        content: '';
        display: inline-block;
        width: 0;
        height: 0;
        border-top: 4px solid transparent;
        border-left: 8px solid #bbb;
        border-bottom: 4px solid transparent;
        position: absolute;
        right: -1px;
        top: 50%;
        transform: translate(0, -50%);
      }
    }
  }

  span.t {
    font-size: 12px;
    color: #128bed;
    text-align: center;
    position: relative;
    top: -8px;
  }

  span.t5 {
    font-size: 12px;
    color: #128bed;
    text-align: center;
    position: relative;
    top: -8px;
  }
}
