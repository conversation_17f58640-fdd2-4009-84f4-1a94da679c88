/* eslint-disable @typescript-eslint/no-this-alias */
/* eslint-disable func-names */
/* eslint-disable @typescript-eslint/no-empty-function */
/* eslint-disable no-param-reassign */
import _ from 'lodash';

import gUiLoading from '../../../g-ui-loading';
import gUiNoData from '../../../g-ui-no-data';
import gAppPathGlf from '../../../g-app-path-glf';
import dataLoader from '../../data';
import operType from '../../../utils/operType';

export default {
  name: 'glf-search-box',
  components: {
    [gUiLoading.name]: gUiLoading,
    [gUiNoData.name]: gUiNoData,
    [gAppPathGlf.name]: gAppPathGlf,
  },
  props: {
    keyNo: {
      type: String,
      default: '',
    },
    rule: {
      type: String,
      default: 'sh',
    },
    visible: {
      type: Boolean,
      default: () => false,
    },
    boxData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      title: '',
      total: null,
      type: 'allCompany',
      searchType: 'Company',
      showAllDetail: false,
      searchRequest: null,
      searchRightCompaniesRequest: null,
      searchRightPersonsRequest: null,
      rightCompanies: [],
      rightPersons: [],
      // 搜索相关
      searchInput: '',
      // 分页相关
      items: [],
      isLoading: true,
      noData: false,
      currentPage: 1, // 分页相关
      nextPageLoading: false, // 分页相关
      noNextPage: false, // 分页相关
    };
  },
  watch: {
    boxData: {
      handler(val) {
        // 点击单个box查看情况下
        this.boxData = val;
        this.searchInput = '';
        this.showSearchBox(_.isEmpty(val) ? 'allCompany' : 'other');
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    getOperType(type) {
      return operType.getOperTypeName(type);
    },
    showSearchBox(node = 'allCompany') {
      // init
      this.items = [];
      this.isLoading = true;
      this.currentPage = 1; // 分页相关
      this.nextPageLoading = false; // 分页相关
      this.noNextPage = false; // 分页相关
      if (node === 'allCompany') {
        this.type = 'allCompany';
        this.searchType = 'Company';
      } else if (node === 'allPerson') {
        this.type = 'allPerson';
        this.searchType = 'Person';
      } else {
        this.type = 'other';
        this.title = this.boxData.data && this.boxData.data.Title;
        this.total = this.boxData.data && this.boxData.data.Total;
        this.searchType = this.boxData.data && this.boxData.data.NodeType;
      }
      // get collection2
      this.searchNode(this.searchType, this.currentPage);
    },
    searchNode(searchType, pageIndex) {
      this.searchRequest = Promise.all([
        searchType === 'Company' || searchType === 'Person'
          ? dataLoader.glfSearchAll({
              keyNo: this.keyNo,
              searchType,
              rule: this.rule,
              pageIndex,
              searchKey: this.searchInput.trim(),
            })
          : dataLoader.loadSearchNode({
              keyNo: this.keyNo,
              nodeType: searchType,
              rule: this.rule,
              pageIndex,
              searchKey: this.searchInput.trim(),
            }),
      ]).then(([re]) => {
        this.nextPageLoading = false;
        if (re) {
          this.noNextPage = !re.Paging || (re.Paging && !re.Paging.NextPage);
          let collection = re.Result && re.Result.Collection;
          if (searchType === 'Company' || searchType === 'Person') {
            collection = re.Result;
          }
          if (collection && collection.length) {
            this.setCollectionInfo(collection);
          } else {
            this.isLoading = false;
            this.items = [];
          }
        } else {
          this.isLoading = false;
          this.items = [];
        }
      });
    },
    setCollectionInfo(collection) {
      const that = this;

      // get ids
      let companyIds = '';
      let personIds = '';
      collection.forEach(function (t) {
        if (t.Id && t.Id[0] !== 'p') {
          companyIds += `${t.Id},`;
        }
        if (t.Id && t.Id[0] === 'p') {
          personIds += `${t.Id},`;
        }
      });
      companyIds = companyIds.substring(0, companyIds.length - 1);
      personIds = personIds.substring(0, personIds.length - 1);
      if (companyIds) {
        that.getRightCompanies(companyIds).then(() => {
          const companies = [];
          collection.forEach(function (t) {
            if (t.Id && t.Id[0] !== 'p') {
              const detail = { show: false };
              if (that.showAllDetail) {
                detail.show = true;
              }
              detail.company = _.find(that.rightCompanies, (item) => t.Id === item.KeyNo);
              if (!detail.company.Oper) {
                // 兼容无数据的情况
                detail.company.Oper = {
                  Name: null,
                  KeyNo: null,
                  OperType: 1,
                };
              }
              t.detail = detail;
              companies.push(t);
            }
          });

          that.isLoading = false;
          that.items = that.items.concat(companies);
        });
      }

      if (personIds) {
        that.getRightPersons(personIds, that.keyNo).then(() => {
          const persons = [];
          _.forEach(collection, (t) => {
            if (t.Id && t.Id[0] === 'p') {
              const detail = { show: false };
              if (that.showAllDetail) {
                detail.show = true;
              }

              detail.person = _.find(that.rightPersons, (item) => t.Id === item.Id);
              if (!detail.person) {
                detail.person = {
                  Name: t.Name,
                };
              }
              detail.person.noImageBgColor = `color-${Math.floor(Math.random() * 14 + 1)}`;
              t.detail = detail;
              persons.push(t);
            }
          });

          that.isLoading = false;
          that.items = that.items.concat(persons);
        });
      }
    },
    getRightCompanies(ids) {
      return new Promise((resolve) => {
        this.searchRightCompaniesRequest = dataLoader.glfGetDetailsByIds({ ids }).then((re) => {
          if (re && re.Result) {
            this.rightCompanies = this.rightCompanies.concat(re.Result);
            this.rightCompanies = _.uniqBy(this.rightCompanies, 'KeyNo');
          }
          resolve();
        });
      });
    },

    getRightPersons(pids, eid) {
      return new Promise((resolve) => {
        this.searchRightPersonsRequest = dataLoader.glfPersonOverviewBath({ eid, pids }).then((re) => {
          if (re && re.length) {
            this.rightPersons = this.rightPersons.concat(re);
            this.rightPersons = _.uniqBy(this.rightPersons, 'Id');
          }
          resolve();
        });
      });
    },
    scrollFunc(e) {
      const dom = e.currentTarget;
      if (this.noNextPage) {
        return;
      }
      if (this.nextPageLoading) {
        return;
      }

      const $dom = $(dom);

      const scrollTop = $dom.scrollTop();
      const height = $dom.height();
      const scrollHeight = dom.scrollHeight;
      const deltScroll = scrollTop + height + 50;
      if (deltScroll >= scrollHeight) {
        // next page
        this.nextPageLoading = true;
        this.getNexPageCollection();
      }
    },
    search() {
      if (this.searchTime) {
        clearTimeout(this.searchTime);
      }
      this.searchTime = setTimeout(() => {
        this.currentPage = 1;
        this.isLoading = true;
        this.items = [];
        this.searchNode(this.searchType, this.currentPage);
      }, 200);
    },
    clearAll() {
      this.searchInput = '';
      this.showSearchBox(this.type);
    },
    getNexPageCollection() {
      this.currentPage += 1;
      this.searchNode(this.searchType, this.currentPage);
    },

    showAllDetailToggle() {
      this.showAllDetail = !this.showAllDetail;
      this.items.forEach((t) => {
        t.detail.show = this.showAllDetail;
      });
    },
    toggleDetail(item, index) {
      if (this.showAllDetail) {
        this.showAllDetail = false;
      }
      this.items.forEach((t) => {
        if (t.Id !== item.Id) {
          t.detail.show = false;
        }
      });
      item.detail.show = !item.detail.show;
      if (item.detail.show) {
        $('.ma_items-container').animate({ scrollTop: `${index * 60}px` }, 300);
      }
    },
    handleOk() {
      this.$emit('close');
    },
    handleClose() {
      this.$emit('close');
    },
  },
};
