<template>
  <div class="glf-search-box-container">
    <div class="glf-search-box-title ma_top-bar e_close-bar">
      <span v-if="type === 'other'" class="ma_bar-normal ea_title"
        >{{ title }}&nbsp;&nbsp;<span style="color: #128bed">{{ total }}</span></span
      >
      <span v-if="type == 'allCompany' || type == 'allPerson'" class="ea_title">
        <span :class="['ma_bar-all', { current: type == 'allCompany' }]" @click="showSearchBox('allCompany')">所有关联企业</span>
        <span :class="['ma_bar-all', { current: type == 'allPerson' }]" @click="showSearchBox('allPerson')">所有关联自然人</span>
      </span>
      <span class="ea_close" @click="handleClose">×</span>
    </div>
    <div class="glf-search-box">
      <div class="ma_search-group input-group">
        <a v-show="searchInput.length" @click="clearAll" class="clear-searchkey"></a>
        <input
          v-model="searchInput"
          @keyup="search"
          class="ma_search-input form-control headerKey"
          type="text"
          placeholder="请输入筛选名称"
          value=""
          autocomplete="off"
        />

        <label :class="{ active: showAllDetail }" @click="showAllDetailToggle">
          <span v-if="showAllDetail">全部收起 <q-icon type="icon-a-xianduanshang"></q-icon> </span>
          <span v-else>全部展开 <q-icon type="icon-a-xianduanxia"></q-icon></span>
        </label>
      </div>
      <div class="glf-search-box-section">
        <g-ui-loading v-if="isLoading"></g-ui-loading>
        <template v-else-if="items.length">
          <div class="ma_items-container" @scroll="scrollFunc">
            <div v-for="(item, i) in items" :id="i" :key="`item-${i}`">
              <div :class="['ma_item', { active: item.detail.show }]">
                <div class="ma_item-top">
                  <q-entity-avatar
                    class="logo"
                    :key-no="item.Id"
                    :src="(item.detail.company && item.detail.company.ImageUrl) || (item.detail.person && item.detail.person.Image)"
                    :name="item.Name"
                    :size="30"
                  ></q-entity-avatar>
                  <q-entity-link style="margin-right: 8px" :coy-obj="{ KeyNo: item.Id, Name: item.Name }"></q-entity-link>

                  <q-tag type="success" v-if="item.detail.company && item.detail.company.Status">{{ item.detail.company.Status }}</q-tag>

                  <span class="ma_arrow pull-right text-muted" @click="toggleDetail(item, i)">
                    <q-icon :type="item.detail.show ? 'icon-a-xianduanshang' : 'icon-a-xianduanxia'"></q-icon>
                  </span>
                </div>
                <div v-if="item.detail.show" class="ma_item-detail">
                  <div v-if="item.detail.company">
                    <div class="detail-cell">
                      <span>{{ getOperType(item.detail.company.Oper.OperType) }}: </span>
                      <q-entity-link
                        :coy-obj="{
                          KeyNo: item.detail.company.Oper.KeyNo,
                          Name: item.detail.company.Oper.Name,
                        }"
                      ></q-entity-link>
                    </div>
                    <div class="detail-cell">
                      <span>注册资本：</span><span class="val">{{ item.detail.company.RegistCapi || '-' }}</span>
                    </div>
                    <div class="detail-cell">
                      <span>成立日期：</span><span class="val">{{ item.detail.company.StartDate || '-' }}</span>
                    </div>
                  </div>
                  <div v-if="item.detail.person">
                    <div class="detail-cell">
                      <span>担任法人：</span><span>{{ (item.detail.person.Count && item.detail.person.Count.OperCount) || '-' }}</span>
                    </div>
                    <div class="detail-cell">
                      <span>对外投资：</span><span>{{ (item.detail.person.Count && item.detail.person.Count.PartnerCount) || '-' }}</span>
                    </div>
                    <div class="detail-cell">
                      <span>在外任职：</span><span>{{ (item.detail.person.Count && item.detail.person.Count.EmployeeCount) || '-' }}</span>
                    </div>
                    <div class="detail-cell">
                      <span>控股企业：</span><span>{{ (item.detail.person.Count && item.detail.person.Count.NameCount) || '-' }}</span>
                    </div>
                  </div>
                </div>
                <div v-if="item.detail.show" class="ma_item-path">
                  <div class="e_path">
                    <div class="ea_path-title">
                      关联方认定详情
                      <q-tag v-if="rule == 'sh'" customClass="tag" type="primary">上交所</q-tag>
                      <q-tag v-if="rule == 'sz'" customClass="tag" type="warning">深交所</q-tag>
                      <q-tag v-if="rule == 'kj'" customClass="tag" type="pl">会计准则</q-tag>
                    </div>

                    <g-app-path-glf :paths="item.Path" :key-no="keyNo" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <g-ui-no-data v-else></g-ui-no-data>
      </div>
    </div>
  </div>
</template>
<script src="./component.js"></script>
<style lang="less" scoped src="./style.less"></style>
