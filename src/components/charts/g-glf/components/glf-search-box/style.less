.glf-search-box-container {
  width: 500px;
  height: 600px;
  border-radius: 5px;
  position: fixed;
  right: 80px;
  top: 125px;
  background: #fff;
  -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  -moz-box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  box-shadow: rgba(0, 0, 0, 0.2) 0 2px 4px 0;
  z-index: 10;
  user-select: none;

  .ea_title {
    color: #333;
    font-size: 18px;
    line-height: 52px;
    font-weight: bold;
  }
}

.glf-search-box-title {
  border-bottom: 1px solid #eee;
  padding: 0 15px;
  position: relative;

  &.ma_top-bar .ma_bar-all {
    font-size: 16px;
    position: relative;
    cursor: pointer;
    height: 51px;
    padding: 0;
    display: inline-block;
    margin-right: 30px;
    line-height: 51px;
  }

  &.ma_top-bar .ma_bar-all.current {
    color: #128bed;
    border-bottom: 2px solid #128bed;
  }

  .ea_close {
    color: #128bed;
    font-size: 30px;
    font-weight: bold;
    line-height: 52px;
    float: right;
    cursor: pointer;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translate(0, -50%);
  }
}

.glf-search-box {
  overflow: hidden;

  .glf-search-box-section {
    position: relative;
    height: 480px;
  }

  .ma_search-group {
    border-bottom: 1px solid #eee;
    position: relative;
    padding: 15px;
    width: 100%;
  }

  .ma_search-group input {
    position: relative;
    display: inline-block;
    box-shadow: none;
    border: 1px solid #eee;
    height: 34px;
    padding: 6px 12px;
    appearance: none;
    outline: none;
  }

  .ma_search-group input.ma_search-input {
    width: 300px;
  }

  .ma_search-group input.ea_check-input {
    top: 20px;
  }

  .ma_search-group span {
    display: inline-block;
    color: #666;
    font-size: 14px;
    font-weight: normal !important;
    float: right;
  }

  .ma_search-group label {
    cursor: pointer;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translate(0, -50%);
  }

  .ma_search-group label.active span {
    color: #128bed;
  }

  .ma_items-container {
    max-height: 480px;
    overflow-y: auto;
  }

  .ma_item .ma_item-top {
    padding: 14px 15px;
    height: 60px;
    position: relative;
    display: flex;
    align-items: center;
  }

  .ma_item .ma_item-top:hover {
    background: #f3f9fd;
  }

  .ma_item-top .logo {
    margin-right: 8px;
  }

  .ma_item-top img {
    width: 30px;
    height: 30px;
    display: block;
    border-radius: 3px;
    float: left;
    border: 1px solid #eee;
    object-fit: contain;
  }

  .ma_item-top .ma_plogo {
    width: 30px;
    height: 30px;
    line-height: 28px;
    border-radius: 3px;
    color: white;
    float: left;
    box-sizing: border-box;
    text-align: center;
    border: 1px solid #eee;

    &.color-1 {
      background: #52abfb !important;
    }

    &.color-2 {
      background: #91c765 !important;
    }

    &.color-3 {
      background: #61b4cc !important;
    }

    &.color-4 {
      background: #bc9ede !important;
    }

    &.color-5 {
      background: #9eb5de !important;
    }

    &.color-6 {
      background: #c5c271 !important;
    }

    &.color-7 {
      background: #d4a64d !important;
    }

    &.color-8 {
      background: #ef9898 !important;
    }

    &.color-9 {
      background: #e9a37c !important;
    }

    &.color-10 {
      background: #83caae !important;
    }

    &.color-11 {
      background: #e488c0 !important;
    }

    &.color-12 {
      background: #96a2e2 !important;
    }

    &.color-13 {
      background: #7db3d6 !important;
    }

    &.color-14 {
      background: #e79177 !important;
    }
  }

  .ma_item-top .ma_name {
    font-size: 14px;
    color: #333;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    height: 32px;
    line-height: 32px;
    margin-left: 10px;
    float: left;
  }

  .ma_item-top .ma_name:hover span {
    color: #128bed;
  }

  .ma_item-top .ma_tag {
    font-size: 12px;
    margin-top: 5px;
    margin-left: 5px;
  }

  .ma_item-top .ma_follow {
    float: right;
    position: relative;
    top: 20px;
  }

  .ma_item-top .ma_arrow {
    font-size: 14px;
    color: #666;
    float: right;
    cursor: pointer;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translate(0, -50%);
  }

  .ma_item .ma_item-detail {
    padding: 10px 15px 0;
    background: #f3f9fd;
  }

  .ma_item .ma_item-detail > div {
    border-bottom: 1px #eee solid;
    padding-bottom: 10px;
  }

  .ma_item .ma_item-detail > div .detail-cell {
    display: inline-block;
    margin-right: 15px;
    font-size: 14px;
    line-height: 22px;
  }

  .ma_item .ma_item-detail > div .detail-cell span {
    font-size: 14px;
    color: #999;

    &.val {
      color: #333;
    }
  }

  .ma_item .ma_item-detail > div .detail-cell a {
    font-size: 14px;
    color: #128bed;
  }

  .ma_item .ma_item-path {
    background: #f3f9fd;
    padding: 10px 15px;
  }

  .ma_item.active .fa-fw,
  .ma_item.active .ma_name {
    color: #128bed;
  }

  /* loading */
  .load_data {
    top: 0;
    left: 0;
    right: 0;
    position: absolute;
    margin-top: 280px;
    text-align: center;
    z-index: 100;

    img {
      width: 100px;
      height: 100px;
    }
  }

  .mao-loading {
    z-index: 9;
    background-size: 360px 280px;
  }

  .mao-loading img {
    width: auto;
  }

  .black-bg {
    position: absolute;
    width: 100%;
    height: 100%;
  }

  .detail-panel {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 70%;
    background: white;

    .title {
      height: 42px;
      width: 100%;
      line-height: 42px;
      text-align: center;
      border-bottom: #eee 1px solid;
      font-size: 14px;
      color: #333;
      position: absolute;
      top: 0;
      background: white;
      z-index: 9999;

      .total {
        display: inline-block;
        margin-left: 5px;
        color: #128bed;
      }
    }

    .content-wrap {
      overflow-y: scroll;
      height: 100%;
      padding-top: 42px;
    }
  }

  .animated {
    -webkit-animation-fill-mode: both;
    -moz-animation-fill-mode: both;
    -ms-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-duration: 0.5s;
    -moz-animation-duration: 0.5s;
    -ms-animation-duration: 0.5s;
    animation-duration: 0.5s;
  }

  @keyframes fade-in {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  @keyframes fade-in {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  @keyframes fade-in {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  @keyframes fade-in {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  .fadeIn {
    display: block !important;
    -webkit-animation-name: fade-in;
    -moz-animation-name: fade-in;
    animation-name: fade-in;
  }

  @keyframes fade-out {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  @keyframes fade-out {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  @keyframes fade-out {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  @keyframes fade-out {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  .fadeOut {
    -webkit-animation-name: fade-out;
    -moz-animation-name: fade-out;
    animation-name: fade-out;
  }

  .ea_path-title {
    display: flex;
    align-items: center;
    color: #333;
    font-size: 14px;
    font-weight: bold;

    .tag {
      margin-left: 5px;
    }
  }

  .single-path {
    color: #666;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;

    &:last-child {
      padding-bottom: 0;
      border-bottom: none;
    }
  }

  .e_close-bar {
    height: 52px;
    border-bottom: #e5e5e5 1px solid;
    padding: 0 15px;
  }

  .pnodata {
    img {
      margin: 0 auto;
      display: block;
    }

    p {
      text-align: center;
      color: #999;
    }
  }
}

.app-company-bar {
  position: fixed;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 -2px 4px 0 rgba(0, 0, 0, 0.1);
  width: 100%;
  padding: 10px 15px;
  font-size: 14px;
  line-height: 22px;
  z-index: 10;

  > .left {
    padding-right: 110px;

    > .logo-img {
      height: 22px;
      border-radius: 4px;
      vertical-align: middle;
    }

    > .title {
      display: inline-block;
      margin-left: 8px;
      vertical-align: middle;
    }

    > .tags {
      display: inline-block;
      margin-left: 4px;
      line-height: 20px;
    }

    .avatar-empty {
      width: 22px;
      height: 22px;
      float: left;
      text-align: center;
      border-radius: 4px;

      &::before {
        content: attr(first-letter);
        font-size: 14px;
        line-height: 22px;
        color: #fff;
        font-weight: bold;
      }

      &.color-1 {
        background-color: #9eb5de;
      }
    }
  }

  a {
    position: absolute;
    color: #128bed;
    text-decoration: none;
    display: block;
    top: 0;
    right: -15px;
    bottom: 0;
    padding: 10px 30px 10px 15px;
  }
}

.e_path {
  line-height: 28px;
  margin-bottom: 6px;
}

::v-deep .e_path > div {
  margin-top: 8px;

  &:first-child {
    margin-top: 0;
  }

  &::after {
    display: none !important;
  }
}

.e_path div.ea_path-title {
  color: #333;
  font-weight: bold;
  font-size: 12px;
}

.e_path a {
  word-break: break-all;
  color: #333;
}

.e_path a:hover {
  color: #128bed;
}

.e_lang-arrow {
  position: relative;
  top: 10px;
  border: red 0 solid;
  display: inline-block;
  height: 32px;
  padding: 0 5px;
  margin-left: 10px;
  margin-right: 10px;
  max-width: 160px;
}

.ea_text {
  position: relative;
  height: 12px;
  line-height: 12px;
  color: #128bed;
  text-align: center;
  font-size: 12px;
  width: 100%;
  margin-bottom: 7px;
  float: left;
  max-width: 150px;
  word-break: keep-all;
  text-overflow: ellipsis;
  overflow: hidden;
}

.ea_line {
  position: absolute;
  width: 100%;
  height: 2px;
  background-color: #ddd;
  top: 15px;
  left: 0;
}

.ea_arrow-wrap {
  position: absolute;
  top: 8px;
  font-size: 15px;
  color: #999;
  width: 15px;
  height: 100%;
  z-index: 99;
}

.ea_arrow-wrap.ea_left {
  left: -3px;
}

.ea_arrow-wrap.ea_right {
  right: -10px;
}

.e_path .ea_path-wrap {
  max-height: 200px;
  overflow-y: auto;
  float: left;
}

.e_path .ea_path-wrap > div {
  float: left;
  overflow: hidden;
}

.box-bottom-info {
  color: #999;
  text-align: center;
  left: 50%;
  transform: translate(-50%, 0);
}
