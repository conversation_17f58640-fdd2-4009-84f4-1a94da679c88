<template>
  <div class="wrap">
    <a href="javascript:;" :class="{ current: current == 'sh' }" @click="rule('sh')">上交所规则</a>
    <a href="javascript:;" :class="{ current: current == 'sz' }" @click="rule('sz')">深交所规则</a>
    <a href="javascript:;" :class="{ current: current == 'kj' }" @click="rule('kj')">企业会计准则</a>
  </div>
</template>
<script>
export default {
  name: 'glfRuleBts',
  data() {
    return {
      current: 'sh',
    };
  },
  methods: {
    rule(type) {
      this.current = type;
      this.$emit('ruleChange', type);
    },
  },
};
</script>

<style lang="less" scoped>
.wrap {
  position: absolute;
  top: 30px;
  left: 30px;
  width: 135px;
  z-index: 2;

  &.isFull {
    top: 30px;
  }

  a {
    border: 1px solid #d6d6d6;
    font-size: 10px;
    display: block;
    width: 135px;
    line-height: 30px;
    box-sizing: border-box;
    text-decoration: none;
    text-align: center;
    background: #fff;
    color: #666;
    height: 32px;
    margin-bottom: 10px;
    border-radius: 2px;
  }
  a.current {
    background: #128bed;
    border: 1px solid #128bed;
    color: #fff;
  }
}
</style>
