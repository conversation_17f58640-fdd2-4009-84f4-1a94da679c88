.toolbox {
  position: fixed;
  width: 44px;
  right: 15px;
  bottom: 50px;
  font-size: 18px;
  z-index: 15;
}

.glf-chart-container {
  position: absolute;
  inset: 0;
  overflow: hidden;
  background: url('../utils/images/shuiying6.png') repeat;
  background-size: 360px 280px;
  line-height: 1.2;

  > svg {
    user-select: none;
  }
}

#glf-chart {
  position: absolute;
  inset: 0;
  overflow: hidden;
  z-index: 1;
}

.pop-nodata {
  width: 328px;
  height: 122px;
  background-size: 328px 122px;
  margin-left: -10px;
}

.e_path {
  line-height: 28px;
  margin-bottom: 6px;
}

.e_path div.ea_path-title {
  color: #333;
  font-weight: bold;
  font-size: 12px;
}

.e_path a {
  word-break: keep-all;
  color: #333;
}

.e_path a:hover {
  color: #128bed;
}

.e_path span.ea_arrow {
  display: inline-block;
  min-width: 84px;
  height: 26px;
  background-size: 75px 8px;
  padding-bottom: 10px;
  font-size: 12px;
  color: #999;
  text-align: center;
  position: relative;
  top: -7px;
  margin-right: 6px;
  margin-left: 6px;
}

.e_path span.ea_arrow.ea_left {
  background-size: 75px 8px;
}

.e_path span.ea_t5 {
  display: inline-block;
  width: 105px;
  height: 26px;
  background-size: 100px 34px;
  font-size: 12px;
  color: #128bed;
  text-align: center;
  position: relative;
  top: -9px;
  margin-right: 6px;
  padding-bottom: 40px;
}

.e_path .ea_path-wrap {
  max-height: 200px;
  overflow-y: auto;
  float: left;
}

.e_path .ea_path-wrap > div {
  float: left;
  overflow: hidden;
}

.animated {
  -webkit-animation-fill-mode: both;
  -moz-animation-fill-mode: both;
  -ms-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-duration: 0.5s;
  -moz-animation-duration: 0.5s;
  -ms-animation-duration: 0.5s;
  animation-duration: 0.5s;
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.fadeIn {
  display: block !important;
  -webkit-animation-name: fade-in;
  -moz-animation-name: fade-in;
  animation-name: fade-in;
}

@keyframes fade-out {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@keyframes fade-out {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@keyframes fade-out {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@keyframes fade-out {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.fadeOut {
  -webkit-animation-name: fade-out;
  -moz-animation-name: fade-out;
  animation-name: fade-out;
}

/* buttons */
.container.buttons {
  position: absolute;
  top: 135px;
  height: 1px;
  width: 105px;
}

.container.buttons a {
  margin-top: 10px;
  width: 104px;
  height: 32px;
  font-size: 12px;
  padding: 0;
  text-align: center;
  line-height: 30px;
}

.container.buttons a.btn-white {
  border: #d6d6d6 1px solid;
  background: white;
  color: #666 !important;
}

/* search-box */
.m_search-box {
  width: 500px;
  height: 600px;
  border-radius: 5px;
  position: absolute;
  right: 80px;
  top: 125px;
  background: #fff;
  -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  -moz-box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  box-shadow: rgba(0, 0, 0, 0.2) 0 2px 4px 0;
  display: none;
  opacity: 0;
}

.m_search-box .ma_top-bar {
  position: relative;
}

.m_search-box .ma_top-bar .ma_bar-normal {
  font-size: 16px;
}

.m_search-box .ma_top-bar .ma_bar-all {
  font-size: 16px;
  position: relative;
  cursor: pointer;
  height: 51px;
  padding: 0 10px;
  display: inline-block;
}

.m_search-box .ma_top-bar .ma_bar-all.current {
  color: #128bed;
  border-bottom: 2px solid #128bed;
}

.m_search-box .ma_search-group {
  border-bottom: 1px solid #eee;
  padding: 0 15px;
  position: relative;
  height: 66px;
  width: 100%;
}

.ma_search-group a.clear-searchkey {
  top: 24px;
  left: 292px;
}

.ma_search-group input {
  position: relative;
  display: inline-block;
}

.ma_search-group input.ma_search-input {
  width: 300px;
  top: 15px;
}

.ma_search-group input.ea_check-input {
  top: 20px;
  left: 46px;
}

.ma_search-group span {
  position: relative;
  display: inline-block;
  color: #666;
  font-size: 14px;
  top: 18px;
  left: 55px;
}

.ma_search-group label {
  cursor: pointer;
  position: relative;
  right: -26px;
}

.ma_search-group label.active span {
  color: #128bed;
}

.m_search-box .ma_items-container {
  height: 480px;
  overflow-y: auto;
}

.ma_items-container .ma_item {
  position: relative;
}

.ma_item .ma_item-top {
  padding: 14px 15px;
  height: 60px;
  position: relative;
}

.ma_item .ma_item-top:hover {
  background: #f3f9fd;
}

.ma_item-top img {
  width: 32px;
  height: 32px;
  display: block;
  border-radius: 3px;
  float: left;
}

.ma_item-top .ma_plogo {
  width: 32px;
  height: 32px;
  line-height: 33px;
  border-radius: 3px;
  color: white;
  display: block;
  float: left;
  text-align: center;
}

.ma_item-top .ma_name {
  font-size: 14px;
  color: #333;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  height: 32px;
  line-height: 32px;
  margin-left: 10px;
  float: left;
}

.ma_item-top .ma_name:hover span {
  color: #128bed;
}

.ma_item-top .ma_tag {
  font-size: 12px;
  margin-top: 5px;
  margin-left: 5px;
}

.ma_item-top .ma_follow {
  display: inline-block;
  float: right;
  position: relative;
  top: 20px;
}

.ma_item-top .ma_arrow {
  font-size: 14px;
  color: #666;
  display: inline-block;
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translate(0, -50%);
  cursor: pointer;
}

.ma_item .ma_item-detail {
  padding: 10px 15px 0;
  background: #f3f9fd;
}

.ma_item .ma_item-detail > div {
  border-bottom: 1px #eee solid;
  padding-bottom: 10px;
}

.ma_item .ma_item-detail > div .detail-cell {
  display: inline-block;
  margin-right: 15px;
}

.ma_item .ma_item-detail > div .detail-cell span {
  font-size: 12px;
  color: #666;
}

.ma_item .ma_item-detail > div .detail-cell a {
  font-size: 12px;
  color: #128bed;
}

.ma_item .ma_item-path {
  background: #f3f9fd;
  padding: 10px 15px;
}

.ma_item.active .fa-fw,
.ma_item.active .ma_name {
  color: #128bed;
}

/* loading */
.load_data {
  top: 0;
  left: 0;
  right: 0;
  position: absolute;
  margin-top: 280px;
  text-align: center;
  z-index: 100;
}

.mao-loading {
  z-index: 9;
  background-size: 360px 280px;
}

.mao-loading img {
  width: auto;
}

/* hover pop */
.person-container {
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  width: 340px;
  padding: 15px;
  box-shadow: rgba(0, 0, 0, 0.2) 0 2px 4px 0;
  background-color: #fff;
  border-radius: 6px;
  display: none;
  position: absolute;
  left: 0;
  top: 280px;
}

.person-container .avatar {
  width: 50px;
  height: 50px;
  background-size: cover;
  float: left;
  border-radius: 8px;
}

.person-container .ma_plogo {
  color: white;
  text-align: center;
  line-height: 50px;
  font-size: 20px;
}

.person-container .loading {
  width: 340px;
  height: 120px;
  background-size: cover;
  margin: 0 0 0 -15px;
}

.person-container .content-container,
.person-container .summary-container {
  float: left;
  padding-left: 15px;
}

.person-container .content-container .row-item.margin-top-3,
.person-container .summary-container .row-item.margin-top-3 {
  margin-top: 3px;
}

.person-container .content-container .content,
.person-container .summary-container .content {
  color: #333;
  font-size: 14px;
}

.person-container .content-container a {
  color: #128bed;
  text-decoration: none;
}

.person-container .content-container .row-item,
.person-container .summary-container .row-item {
  margin-top: 7px;
}

.person-container .content-container .label-text,
.person-container .summary-container .label-text {
  color: #999;
  font-size: 14px;
}

.person-container .content-container .content,
.person-container .summary-container .content {
  color: #333;
  font-size: 14px;
}

.person-container .avatar-empty {
  width: 50px;
  height: 50px;
  float: left;
  border-radius: 8px;
  text-align: center;
}

.person-container .avatar-empty ::before {
  content: attr(first-letter);
  font-size: 28px;
  line-height: 50px;
  color: #fff;
  font-weight: bold;
}

.person-container .avatar-empty .color-1 {
  background-color: #9eb5de;
}

.person-container .peron-link {
  font-size: 16px;
  word-break: break-all;
}

.person-container .content-container {
  width: 260px;
}

.gap {
  margin: 11.5px -15px 7.5px;
  height: 5px;
  background: #f6f6f6;
}

.person-container .summary-container {
  float: unset;
  padding-left: 0;
}

.person-container .summary-container .col-item {
  width: 49%;
  display: inline-block;
}

.company-container {
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  width: 340px;
  padding: 15px;
  box-shadow: rgba(0, 0, 0, 0.2) 0 2px 4px 0;
  background-color: #fff;
  border-radius: 6px;
  display: none;
  position: absolute;
  left: 0;
  top: 280px;
}

.company-container .logo {
  width: 50px;
  height: 50px;
  background-size: cover;
  float: left;
  border-radius: 8px;
}

.company-container .content-container {
  float: left;
  min-height: 100px;
  padding-left: 15px;
  width: 260px;
}

.company-container .content-container .company-link {
  font-size: 16px;
  word-break: break-all;
}

.company-container .content-container .row-item.margin-top-3 {
  margin-top: 3px;
}

.app-tag.tag-green {
  color: #094;
  background-color: #ebfff4;
}

.app-tag {
  padding: 3px 6px;
  display: inline-block;
  font-size: 12px;
  margin-right: 5px;
  border-radius: 2px;
  margin-top: 5px;
}

.company-container .content-container .row-item {
  margin-top: 7px;
}

.company-container .content-container .label-text {
  color: #999;
  font-size: 14px;
  font-weight: unset;
}

.company-container .content-container .content {
  color: #333;
  font-size: 14px;
}

.company-container .content-container a {
  color: #128bed;
  text-decoration: none;
}

.box-bottom-info {
  text-align: center;
  color: #999;
  font-size: 12px;
  display: inline-block;
  width: 100%;
}

#search-box .e_path a {
  font-size: 12px;
}

#search-box .e_path span.ea_arrow {
  font-size: 12px;
}

#person-pop .e_path div.ea_path-title,
#company-pop .e_path div.ea_path-title {
  font-size: 14px;
}

#person-pop .e_path a,
#company-pop .e_path a {
  color: #666;
}

/* 路径长箭头 */
.e_lang-arrow {
  position: relative;
  top: 10px;
  border: red 0 solid;
  display: inline-block;
  height: 32px;
  padding: 0 5px;
  margin-left: 10px;
  margin-right: 10px;
  max-width: 160px;
}

.ea_text {
  position: relative;
  display: inline-block;
  height: 12px;
  line-height: 12px;
  color: #999;
  text-align: center;
  font-size: 12px;
  width: 100%;
  margin-bottom: 7px;
  float: left;
  max-width: 150px;
  word-break: keep-all;
  text-overflow: ellipsis;
  overflow: hidden;
}

.ea_line {
  position: absolute;
  width: 100%;
  height: 2px;
  background-color: #ddd;
  top: 15px;
  left: 0;
}

.ea_arrow-wrap {
  position: absolute;
  top: 8px;
  font-size: 15px;
  color: #999;
  width: 15px;
  height: 100%;
  z-index: 99;
}

.ea_arrow-wrap.ea_left {
  left: -3px;
}

.ea_arrow-wrap.ea_right {
  right: -10px;
}

.popnodata .content-container div {
  text-align: left;
}

.popnodata .content-container .row-item {
  float: left;
  width: 100%;
  text-align: left;
}

.popnodata .logo {
  background-image: url('https://image.qcc.com/logo/default.jpg');
}

.popnodata .person-logo {
  width: 50px;
  height: 50px;
  background-size: cover;
  float: left;
  border-radius: 8px;
}

.popnodata .content-container .content {
  color: #999;
}
