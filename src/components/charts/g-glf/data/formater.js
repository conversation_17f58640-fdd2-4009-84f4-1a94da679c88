/* eslint-disable consistent-return */
/* eslint-disable no-param-reassign */
import _ from 'lodash';
import moment from 'moment';

import settings from '../settings';
import operTypes from '../../utils/operType';

const formatGraph = (data) => {
  if (data.Children) {
    data.children = data.Children;
    data.children.forEach((t) => {
      formatGraph(t);
    });
  } else {
    return;
  }
  return data;
};

const setDepthInfo = (node, level, arr) => {
  if (node.children && node.children.length) {
    if (!arr[level]) {
      arr[level] = [];
    }
    arr[level] = arr[level].concat(node.children);
    node.children.forEach((t) => {
      setDepthInfo(t, level + 1, arr);
    });
  }
};
const groupGraph = (data) => {
  // 分组
  let ups = [];
  let downs = [];
  if (data.children) {
    const children = _.filter(data.children, (child) => settings.sortOrder.includes(child.NodeType));
    data.children = children;
    const halfLength = children.length % 2 === 0 ? children.length / 2 : (children.length + 1) / 2;
    ups = children.slice(0, halfLength);
    downs = children.slice(halfLength);
  }

  const top = {
    Id: data.Id,
    Name: data.Name,
    children: ups,
  };
  const down = {
    Id: data.Id,
    Name: data.Name,
    children: downs,
  };
  const depthUp = { 1: [], 2: [], 3: [] };
  const depthDown = { 1: [], 2: [], 3: [] };
  setDepthInfo(top, 1, depthUp);
  setDepthInfo(down, 1, depthDown);
  return { data, top, down, depthDown, depthUp };
};

export default {
  formatGraph,
  groupGraph,
};
