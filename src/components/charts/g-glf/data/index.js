import { graph } from '@/shared/services';

import formater from './formater';

const loadGraph = (params) => {
  return new Promise((resolve, reject) => {
    graph
      .glfGetNodes(params)
      .then((data) => {
        const detail = formater.formatGraph(data.Result);
        const detail1 = formater.groupGraph(detail);
        resolve(detail1);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const loadSearchNode = (params) => {
  return new Promise((resolve, reject) => {
    graph
      .glfSearchNode(params)
      .then((data) => {
        resolve(data);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const glfSearchAll = (params) => {
  return new Promise((resolve, reject) => {
    graph
      .glfSearchAll(params)
      .then((data) => {
        resolve(data);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const glfPersonOverviewBath = (params) => {
  return new Promise((resolve, reject) => {
    const { eid, pids } = params;
    const pidArrs = pids.split(',');
    const arr = [];
    pidArrs.forEach((pid, idx) => {
      graph
        .getPersonOverview({ eid, pid, isVIP: true })
        .then((data) => {
          arr.push(data.Result);
          if (idx === pidArrs.length - 1) {
            resolve(arr);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  });
};

const glfGetDetailsByIds = (params) => {
  return new Promise((resolve, reject) => {
    graph
      .glfGetDetailsByIds(params)
      .then((data) => {
        resolve(data);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export default {
  loadGraph,
  loadSearchNode,
  glfSearchAll,
  glfGetDetailsByIds,
  glfPersonOverviewBath,
};
