/* eslint-disable func-names */
/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-empty-function */
import resizeDetector from 'element-resize-detector';
import _ from 'lodash';
import moment from 'moment';

import dataLoader from './data';
import { actionTypes, gUiToolbox, gUiToolboxAction } from '../g-ui-toolbox';
import gUiFooter from '../g-ui-footer';
import gUiLoading from '../g-ui-loading';
import gUiNoData from '../g-ui-no-data';
import appPerson from '../g-app-person';
import appCompany from '../g-app-company';
import Chart from './chart/index';
import popoverHelper from '../utils/popoverHelper';
import chartSettings from './settings';
import globalUtils from '../utils/utils';
import glfRuleBts from './components/rule-bts';
import boxDetail from './components/glf-search-box';
import saveSvg from '../utils/save-svg';

const CONTAINER_SELECTOR = '#chart-container';

export default {
  name: 'g-glf',
  props: {
    actionTypes: {
      type: Object,
      default: () => actionTypes,
    },
    containerName: {
      type: String,
      default: 'glf-chart',
    },
    keyNo: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
  },

  components: {
    [gUiToolbox.name]: gUiToolbox,
    [gUiToolboxAction.name]: gUiToolboxAction,
    [gUiFooter.name]: gUiFooter,
    [gUiLoading.name]: gUiLoading,
    [gUiNoData.name]: gUiNoData,
    [glfRuleBts.name]: glfRuleBts,
    [boxDetail.name]: boxDetail,
  },

  data() {
    return {
      rule: 'sh',
      boxDetailNode: {},
      isInit: false,
      isSaving: false,
      isLoading: true,
      noData: false,
      // 操作栏
      isFullScreen: false,
      selectedEntity: {
        id: '',
        nodeType: undefined,
        parentEid: '',
      },
      isSearchOpened: false,
      isReportOpened: false,
      transform: {},
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.onRefresh();
      this.isFullScreen =
        document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement || document.msFullscreenElement;
      document.addEventListener('fullscreenchange', () => {
        this.isFullScreen = !!document.fullscreenElement;
      });
      document.addEventListener('webkitfullscreenchange', () => {
        this.isFullScreen = !!document.webkitFullscreenElement;
      });
      document.addEventListener('mozfullscreenchange', () => {
        this.isFullScreen = !!document.mozFullScreenElement;
      });
      document.addEventListener('MSFullscreenChange', () => {
        this.isFullScreen = !!document.msFullscreenElement;
      });
    });
    $(CONTAINER_SELECTOR).on('click', 'svg', (evt) => {
      $('.detail-popover').remove();
    });
  },

  methods: {
    cleanup() {
      if (this.chart) {
        this.chart.cleanup();
      }
    },
    ruleChange(rule) {
      this.rule = rule;
      this.onRefresh();
    },
    onRefresh() {
      this.isLoading = true;
      this.noData = false;
      this.cleanup();
      this.isReportOpened = false;
      this.isSearchOpened = false;
      this.chart = new Chart(`#${this.containerName}`);
      dataLoader
        .loadGraph({ keyNo: this.keyNo, rule: this.rule })
        .then((data) => {
          this.isLoading = false;
          this.chart.init();
          this.chart.render(data);
          this.chart.zoomInOrOut(false, true);
        })
        .catch(() => {
          this.isLoading = false;
          this.noData = true;
        })
        .finally(() => {
          this.isInit = true;
          if (!this.noData) {
            this.attachResizeDetector();
          }
        });
      this.chart.on('onScale', (data) => {
        this.transform = data;
      });

      this.chart.on('nodeClick', (data) => {
        this.boxDetailNode = data;
        this.isSearchOpened = true;
      });
      this.chart.on('onDataMouseOver', ({ data, size, position }) => {
        data.delayTimer = setTimeout(() => {
          const $instance = $(`#detail-popover-${data.Id}`);
          if ($instance.length > 0) {
            $instance.stop(true).fadeIn(0);
            return;
          }
          const vmData = {
            id: data.Id,
            name: data.Name,
            keyNo: data.Id,
            pathData: data.Path,
            rule: this.rule,
            type: 3,
          };
          vmData.eid = this.keyNo;
          popoverHelper.showPopover({
            component: data.Id && data.Id[0] === 'p' ? appPerson : appCompany,
            data: vmData,
            targetPosition: {
              left: this.isFullScreen ? position.left : position.left - (180 + 60),
              top: this.isFullScreen ? position.top : position.top - (42 + 42 + 40),
            },
            container: $(`.${this.containerName}`),
            identity: `detail-popover-${data.Id}`,
            targetSize: { width: size.width * this.transform.k, height: size.height * this.transform.k },
          });
        }, 400);
      });
      this.chart.on('onDataMouseOut', (data) => {
        if (data.delayTimer) {
          clearTimeout(data.delayTimer);
          delete data.delayTimer;
        }

        $(`#detail-popover-${data.Id}`).fadeOut(400, function () {
          $(this).remove();
        });
      });
    },

    onZoomIn() {
      if (this.chart) {
        this.chart.zoomInOrOut(true);
      }
    },
    onZoomOut() {
      if (this.chart) {
        this.chart.zoomInOrOut(false);
      }
    },
    onFullScreen() {
      const element = $(`.${this.containerName}`)[0];
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
      this.isFullScreen = true;
    },
    onExitFullScreen() {
      if (this.isFullScreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
        this.isFullScreen = false;
      }
    },
    onSearch() {
      // 获取搜索列表
      this.boxDetailNode = {};
      this.isSearchOpened = !this.isSearchOpened;
    },

    onSave() {
      // 保存过程中，不能再保存
      if (this.isSaving) {
        return;
      }

      this.isSaving = true;

      setTimeout(() => {
        const $svg = $(`#${this.containerName}>svg`);
        const $shadow = $('<div></div>')
          .css({
            width: 0,
            height: 0,
            position: 'fixed',
            top: -1000000,
            let: -1000000,
            zIndex: -1,
            overflow: 'hidden',
          })
          .appendTo('body');

        const $svgClone = $svg.clone().appendTo($shadow);
        const $g = $svgClone.children('g');
        $g.attr('transform', 'translate(0,0) scale(1)');
        const box = $g[0].getBBox();
        const contentSize = { width: box.width, height: box.height };
        const padding = { left: 80, right: 80, top: 30, bottom: 60 };
        const rect = {
          top: box.y - padding.top,
          left: box.x - padding.left,
          width: contentSize.width + padding.left + padding.right,
          height: contentSize.height + padding.top + padding.bottom,
        };

        // add water markter
        const n = Math.ceil(rect.width / 240.0);
        const m = Math.ceil(rect.height / 200.0);
        for (let i = 0; i < n; i++) {
          for (let j = 0; j < m; j++) {
            const x = rect.left + 240 * i;
            const y = rect.top + 200 * j;
            $svgClone.prepend(globalUtils.getWaterMarkLogo().clone().attr('transform', `translate(${x}, ${y})`));
          }
        }

        // add white bg
        const $masker = $('<rect></rect>').attr({
          x: rect.left,
          y: rect.top,
          width: rect.width,
          height: rect.height,
          fill: 'rgba(255, 255, 255, 1)',
        });
        $svgClone.prepend($masker);

        // add bottom text

        if (rect.width < 600) {
          const $textFooter = $('<text></text>')
            .attr({
              x: padding.left + contentSize.width / 2 + rect.left,
              y: rect.top + padding.top + contentSize.height + 20,
              'text-anchor': 'middle',
              fill: '#999',
              'font-size': '12px',
            })
            .text('以上数据是企查查大数据分析引擎基于公开信息挖掘的成果，仅供参考');
          $svgClone.append($textFooter);
          const $textFooter1 = $('<text></text>')
            .attr({
              x: padding.left + contentSize.width / 2 + rect.left,
              y: rect.top + padding.top + contentSize.height + 40,
              'text-anchor': 'middle',
              fill: '#999',
              'font-size': '12px',
            })
            .text('该成果不构成任何明示或暗示的观点或保证。');
          $svgClone.append($textFooter1);
        } else {
          const $textFooter = $('<text></text>')
            .attr({
              x: padding.left + contentSize.width / 2 + rect.left,
              y: rect.top + padding.top + contentSize.height + 40,
              'text-anchor': 'middle',
              fill: '#999',
              'font-size': '12px',
            })
            .text('以上数据是企查查大数据分析引擎基于公开信息挖掘的成果，仅供参考。该成果不构成任何明示或暗示的观点或保证。');
          $svgClone.append($textFooter);
        }
        const fileName = `${this.name}-关联方认定图-${moment().format('YYYY-MM-DD')}.png`;
        const option = {
          ...rect,
          scale: 2, // 移动端有大小限制
        };
        saveSvg
          .saveAsImage($svgClone[0], fileName, option)
          .then(() => {
            $shadow.remove();
            this.isSaving = false;
          })
          .catch(() => {
            $shadow.remove();
            this.$toasted.error('error');
            // eventBus.triggerToastr(err, 'error')
            this.isSaving = false;
          });
      }, 600);
    },
    showReport() {
      // 显示报告
    },
    attachResizeDetector() {
      this.resizeDetector = resizeDetector();
      this.resizeDetector.listenTo(
        $(`.${this.containerName}`)[0],
        _.debounce(() => {
          this.chart.sizeChange();
        }, 200)
      );
    },
  },
};
