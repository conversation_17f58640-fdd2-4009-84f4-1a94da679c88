<template>
  <div :class="['glf-chart-container', containerName]">
    <div :id="containerName"></div>
    <!-- 操作栏 -->
    <div class="toolbox" v-show="isInit && !noData">
      <g-ui-toolbox>
        <g-ui-toolbox-action :action-type="actionTypes.search" @click="onSearch"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.zoomIn" @click="onZoomIn"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.zoomOut" @click="onZoomOut"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.refresh" @click="onRefresh"></g-ui-toolbox-action>
        <g-ui-toolbox-action v-show="!isFullScreen" :action-type="actionTypes.fullScreen" @click="onFullScreen"></g-ui-toolbox-action>
        <g-ui-toolbox-action
          v-show="isFullScreen"
          :action-type="actionTypes.exitFullScreen"
          @click="onExitFullScreen"
        ></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.save" :is-busy="isSaving" @click="onSave"></g-ui-toolbox-action>
      </g-ui-toolbox>
    </div>
    <!-- 免责 -->
    <g-ui-footer></g-ui-footer>
    <!-- 加载 -->
    <g-ui-loading v-if="isLoading"></g-ui-loading>
    <!-- 暂无数据 -->
    <g-ui-no-data v-if="noData"></g-ui-no-data>
    <!-- 规则切换 -->
    <glf-rule-bts v-if="isInit" @ruleChange="ruleChange" />
    <!-- 筛选 -->
    <glf-search-box
      v-if="isSearchOpened"
      :visible="isSearchOpened"
      :key-no="keyNo"
      :box-data="boxDetailNode"
      :rule="rule"
      @close="isSearchOpened = false"
    />
  </div>
</template>
<script src="./component.js"></script>
<style lang="less" src="./style.less" scoped></style>
<style lang="less">
/* 流线 */
.link-flow-in {
  stroke-dasharray: 8;
  animation: dash-line 30s linear infinite;
}

.link-flow-out {
  stroke-dasharray: 8;
  animation: dash-line 20s linear infinite reverse;
}

@keyframes dash-line {
  100% {
    stroke-dashoffset: 1000;
  }
}
</style>
