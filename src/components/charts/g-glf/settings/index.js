export default {
  title: '关联方认定图谱',
  centerTextMax: 13, // the max count of one row
  centerWidth: 233, // center box width
  radius: 2,
  nodeSize: [290, 400],
  // nodeSize : [9,2.4],
  // size : [$("body").width(), $("body").height()],
  scaleRange: [1 * 0.3, 1 * 2.3],
  box: {
    keyNo: null,
    popNeedShow: false,
    popHideTime: null,
    inputChangeRequestCount: 1, // 用来解决这个bug： https://www.teambition.com/task/5ebbebd5c6b0fd00211b9b04

    // public
    rule: 'sh',
    companiesDetail: [],
    personsDetail: [],
    companyDetailRequest: null,
    personDetailRequest: null,
    searchRequest: null,
    searchRightCompaniesRequest: null,
    searchRightPersonsRequest: null,
    rightCompanies: [],
    rightPersons: [],

    // search box
    isLoading: false,
    showAllDetail: false,
    searchInput: '',
    show: false,
    title: null,
    count: null,
    type: null, // allCompany allPerson other
    items: null,
    followList: null,
    allCollection: null,
    currentCollection: null,
    currentPage: 1,
    nextPageLoading: false,
    noNextPage: false,
    currentNode: null,

    // company detail pop
    companyDetail: null,
    companyDetailLoading: false,
    companyDetailNoData: false,
    companyDetailNoDataName: null,

    // person detail pop
    personDetail: null,
    personDetailLoading: false,
    personDetailNoData: false,
    personDetailNoDataName: null,
  },
  boxSetting: {
    width: 290,
    borderColor: '#EEEEEE',
    top: {
      company: {
        height: 40,
        backgroundColor: '#EAF2FF',
        leftFontColor: '#128BED',
        leftFontSize: 14,
        rightFontColor: '#666666',
        rightFontSize: 14,
      },
      person: {
        height: 40,
        backgroundColor: '#F9F2EA',
        leftFontSize: 14,
        rightFontColor: '#666666',
        rightFontSize: 14,
      },
    },
    item: {
      height: 40,
      fontSize: 14,
      fontColor: '#333333',
      subTitleFontSize: 12,
      subTitleFontColor: '#999999',
    },
    bottom: {
      height: 40,
      fontSize: 12,
      fontColor: '#999999',
    },
  },
  // 排序优先级
  // 上：C母公司/控股股东 BE5直接或间接5%以上的企业 BP5直接或间接5%以上的自然人
  // 下：HE子公司 DJG董监高 HC历史母公司/控股股东
  sortOrder: [
    'C',
    'BE5',
    'BP5',
    'BP5-DJGED',
    'HE',
    'BP5-HE',
    'HE-BP10',
    'HE-BE10',
    'DJG',
    'C-DJG',
    'C-DJG-HE',
    'C-DJG-DJGED',
    'DJG-DJGED',
    'DJG-HE',
    'HC',
    'CP',
    'CE',
    'C-HE',
    'BP20-HE',
    'BP20',
    'DG-HE',
    'C-DG-HE',
    'BE20',
    'DG',
    'C-DG',
  ],
};
