/* eslint-disable radix */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-this-alias */
/* eslint-disable class-methods-use-this */
/* eslint-disable consistent-return */
/* eslint-disable func-names */
import { EventEmitter } from 'eventemitter3';
import * as d3 from 'd3';

import settings from '../settings/index';

export default class Chart extends EventEmitter {
  constructor(selector) {
    super();
    this.upLevelsInfo = [];
    this.downLevelsInfo = [];
    this.selector = selector;
    this.$selector = $(selector);
  }

  cleanup() {
    this.$selector.find('svg').remove();
  }

  init() {
    // remove first
    this.$selector.find('svg').remove();

    const width = this.$selector.width();
    const height = this.$selector.height();
    this.size = {
      width,
      height,
    };
    this.svg = d3.select(this.selector).append('svg').attr('width', width).attr('height', height).attr('cursor', 'move');

    this.root = this.svg.append('g').attr('id', 'root');

    // 创建一个树状图
    this.tree = d3
      .tree()
      .size([width, height])
      .nodeSize(settings.nodeSize)
      .separation(function (a, b) {
        return 1.2;
      });

    // create top g
    this.topContainer = this.root.append('g').attr('transform', `translate(${width / 2}, ${height / 2})`);
    this.topLinksContainer = this.topContainer.append('g').classed('links', true);
    this.topNodesContainer = this.topContainer.append('g').classed('nodes', true);

    // create down g
    this.downContainer = this.root.append('g').attr('transform', `translate(${width / 2}, ${height / 2})`);
    this.downLinksContainer = this.downContainer.append('g').classed('links', true);
    this.downNodesContainer = this.downContainer.append('g').classed('nodes', true);

    // only one direction
    this.oneDownContainer = this.root.append('g').attr('transform', `translate(${width / 2}, ${height / 8})`);
    this.oneDownLinksContainer = this.oneDownContainer.append('g').classed('links', true);
    this.oneDownNodesContainer = this.oneDownContainer.append('g').classed('nodes', true);

    // only one direction
    this.oneTopContainer = this.root.append('g').attr('transform', `translate(${width / 2}, ${height / 1.5})`);
    this.oneTopLinksContainer = this.oneTopContainer.append('g').classed('links', true);
    this.oneTopNodesContainer = this.oneTopContainer.append('g').classed('nodes', true);

    this.zoom = d3
      .zoom()
      .scaleExtent(settings.scaleRange)
      .on('zoom', () => {
        const transform = d3.zoomTransform(this.svg.node());
        this.emit('onScale', transform);
        this.root.attr('transform', transform);
      });

    this.svg.call(this.zoom);
    // .on('dblclick.zoom', null)
  }

  render(data) {
    this.data = data.data;
    this.top = data.top;
    this.down = data.down;
    this.depthUp = data.depthUp;
    this.depthDown = data.depthDown;
    let oneDirection = false;
    if (this.top.children && this.top.children.length) {
      oneDirection = this.down.children && this.down.children.length ? false : 'onlyTop';
      this.updateTree(true, this.top, oneDirection);
    }
    if (this.down.children && this.down.children.length) {
      oneDirection = this.top.children && this.top.children.length ? false : 'onlyDown';
      this.updateTree(false, this.down, oneDirection);
    }
  }

  updateTree(isTop, data, oneDirection) {
    let nodesContainer = isTop ? this.topNodesContainer : this.downNodesContainer;
    let linksContainer = isTop ? this.topLinksContainer : this.downLinksContainer;
    if (oneDirection === 'onlyTop') {
      nodesContainer = this.oneTopNodesContainer;
      linksContainer = this.oneTopLinksContainer;
    }
    if (oneDirection === 'onlyDown') {
      nodesContainer = this.oneDownNodesContainer;
      linksContainer = this.oneDownLinksContainer;
    }

    const rootNode = d3.hierarchy(data);
    const treeData = this.tree(rootNode);

    let nodes = rootNode.descendants();
    nodes = this.initBoxSize(nodes);

    this.shortenLine(treeData, isTop);
    this.prepareNodes(nodes, isTop);

    const links = rootNode.links();

    this.drawLinks(linksContainer, links, isTop);
    this.drawBoxes(nodesContainer, nodes);
    this.drawItems(nodesContainer, nodes);
    this.drawArrow(linksContainer, links, isTop);
  }

  drawArrow(linksContainer, links, isTop) {
    const arrow = linksContainer.append('g').selectAll('g').data(links).enter().append('g');
    arrow
      .append('polygon')
      .attr('points', (d) => {
        const baseNode = d.source.depth > d.target.depth ? d.source : d.target;
        const size = { width: 3, height: 10 };
        let startY = null;
        let startX = null;
        let p2y;
        let p3y;
        if (d.target.data.Direction === 'IN') {
          if (isTop) {
            startX = baseNode.y + baseNode.position.height + 20;
            startY = baseNode.x;
            startX += 33;
            p2y = startX - size.height;
            p3y = startX - size.height;
          } else {
            startX = baseNode.y;
            startY = baseNode.x;
            startX -= 50;
            p2y = startX + size.height;
            p3y = startX + size.height;
          }
        } else if (d.target.data.Direction === 'OUT') {
          if (isTop) {
            startX = baseNode.y + baseNode.position.height + 20;
            startY = baseNode.x;
            startX -= 15;

            p2y = startX + size.height;
            p3y = startX + size.height;
          } else {
            startX = baseNode.y;
            startY = baseNode.x;
            startX -= 5;
            p2y = startX - size.height;
            p3y = startX - size.height;
          }
        }
        const p1y = startX;
        const p1x = startY;
        const p2x = startY - size.width;
        const p3x = startY + size.width;

        return `${p1x},${p1y} ${p2x},${p2y} ${p3x},${p3y}`;
      })
      .attr('fill', (d) => this.getArrowColor(d))
      .attr('stroke-width', 0);

    arrow
      .append('rect')
      .attr('x', (d) => {
        const baseNode = d.source.depth > d.target.depth ? d.source : d.target;
        return baseNode.x - 2;
      })
      .attr('y', (d) => {
        let x;
        const baseNode = d.source.depth > d.target.depth ? d.source : d.target;
        if (isTop) {
          x = baseNode.y + baseNode.position.height + 20;
          x -= 10;
        } else {
          x = baseNode.y;
          x -= 40;
        }

        if (d.target.data.Direction === 'IN') {
          if (isTop) {
            x = baseNode.y + baseNode.position.height + 20;
            x -= 5;
          } else {
            x = baseNode.y;
            x -= 35;
          }
        } else if (d.target.data.Direction === 'OUT') {
          if (isTop) {
            x = baseNode.y + baseNode.position.height + 20;
            x += 7;
          } else {
            x = baseNode.y;
            x -= 45;
          }
        }

        return x;
      })
      .attr('width', 3)
      .attr('height', 20)
      .attr('fill', 'white');

    arrow
      .append('text')
      .attr('x', (d) => {
        const baseNode = d.source.depth > d.target.depth ? d.source : d.target;
        return baseNode.x;
      })
      .attr('y', (d) => {
        let x;
        const baseNode = d.source.depth > d.target.depth ? d.source : d.target;
        if (isTop) {
          x = baseNode.y + baseNode.position.height + 20;
          x += 10;
        } else {
          x = baseNode.y;
          x -= 30;
        }
        return x;
      })
      .attr('dx', -15)
      .attr('dy', (d) => {
        if (d.target.data.Direction === 'IN') {
          if (isTop) {
            return 0;
          }
          return 10;
        }
        if (d.target.data.Direction === 'OUT') {
          if (isTop) {
            return 10;
          }
          return 0;
        }
      })
      .attr('fill', (d) => this.getArrowColor(d))
      .text((d) => d.target.data.Operation);
  }

  getArrowColor(link) {
    if ((link.target.isPerson && link.target.data.Direction === 'IN') || (link.source.isPerson && link.target.data.Direction === 'OUT')) {
      return '#F04040';
    }
    return '#128bed';
  }

  drawItems(nodesContainer, nodes) {
    const self = this;
    const boxItems = this.initBoxItems(nodes);
    const item = nodesContainer.append('g').selectAll('g').data(boxItems).enter().append('g');

    item
      .append('rect')
      .attr('stroke', settings.boxSetting.borderColor)
      .attr('fill', (d) => {
        if (d.type === 'top') {
          return this.isPerson(d.node.data) ? '#F04040' : '#128BED';
        }
        if (d.type === 'item' || d.type === 'bottom') {
          return '#fff';
        }
      })
      .attr('cursor', 'move')
      .attr('border-radius', 0)
      .attr('stroke-width', 1)
      .attr('stroke-opacity', 1)
      .attr('fill-opacity', 0)
      .attr('width', function (d) {
        return settings.boxSetting.width;
      })
      .attr('height', settings.boxSetting.item.height)
      .attr('fill-opacity', 1)
      .attr('x', function (d) {
        return d.x - d.box.position.width / 2;
      })
      .attr('y', function (d) {
        return d.y;
      })
      .attr('rx', settings.radius)
      .attr('ry', 0)
      .attr('pointer-events', 'none');

    item
      .append('text')
      .attr('x', (d) => {
        return d.x - d.box.position.width / 2 + 15;
      })
      .attr('y', (d) => d.y + 25)
      .attr('font-size', (d) => {
        if (d.type === 'top') {
          return 14;
        }
        if (d.type === 'item' || d.type === 'bottom') {
          return 12;
        }
      })
      .attr('cursor', 'pointer')
      .attr('fill', (d) => {
        if (d.type === 'top') {
          return 'white';
        }
        if (d.type === 'item') {
          return '#333';
        }
        if (d.type === 'bottom') {
          return '#999';
        }
        return 'red';
      })
      .text((d) => {
        let name = d.name;
        let maxLength = 36;
        if (d.type === 'item') {
          maxLength = 38;
          const reg = new RegExp('[\u4E00-\u9FA5]+');
          if (!reg.test(name)) {
            maxLength = 32;
          }
        }
        let length = 0;
        let subLeng = 0;
        for (let i = 0; i < name.length; i++) {
          const charCode = name.charCodeAt(i);
          if (charCode > 0 && charCode <= 128) {
            length += 1;
          } else {
            length += 2;
          }
          if (length > maxLength) {
            subLeng = i;
            break;
          }
        }
        if (subLeng) {
          name = name.substring(0, subLeng);
          name += '...';
        }
        return name;
      })
      .on('mouseover', function (d) {
        // 交互
        self.highLightNode(d.node);
        if (d.Id) {
          d3.select(this).attr('fill', '#128bed');

          const $sender = $(window.event.target);
          let position = $sender.position();
          if (position.left === 0 && position.top === 0) {
            // 都是0的情况下，说明没有取到正确值，因此做个补充
            const rect = $sender[0].getBoundingClientRect();
            position = {
              left: rect.x,
              top: rect.y,
            };
          }
          position.left -= 10;

          let size = {
            width: $sender.width(),
            height: $sender.height(),
          };
          if (size.width === 0 && size.height === 0) {
            // 都是0的情况下，说明没有取到正确值，因此做个补充
            const rect = $sender[0].getBBox();
            size = {
              width: rect.width,
              height: rect.height,
            };
          }
          self.emit('onDataMouseOver', { data: d, size, position });
        }
      })
      .on('mouseout', function (d) {
        if (d.Id) {
          d3.select(this).attr('fill', '#333');
          self.emit('onDataMouseOut', d);
        }
        self.unHighLightNode(d);
      })
      .on('click', function (d) {
        self.emit('nodeClick', d.node);
      });

    item
      .append('text')
      .attr('x', (d) => {
        let padding = 0;
        if (d.count) {
          const count = `${d.count}`;
          padding = (count.length - 1) * 9;
        }
        return d.x - d.box.position.width / 2 + settings.boxSetting.width - 25 - padding;
      })
      .attr('y', (d) => d.y + 25)
      .attr('fill', '#fff')
      .text((d) => d.count);
  }

  initBoxItems(nodes) {
    const boxItems = [];
    nodes.forEach((t) => {
      if (t.data.Collection) {
        boxItems.push({
          node: t,
          name: t.data.Title,
          type: 'top',
          count: t.data.Total,
          index: 0,
          box: t,
          x: t.x,
          y: t.y,
        });
        t.data.Collection.forEach((tt, index) => {
          if (index < 5) {
            tt.node = t;
            tt.name = tt.Name;
            tt.index = index + 1;
            tt.type = 'item';
            tt.box = t;
            tt.x = t.x;
            tt.y = t.y + settings.boxSetting.item.height * (index + 1);
            boxItems.push(tt);
          }
        });
        let py = t.y + settings.boxSetting.item.height * (t.data.Total + 1);
        if (t.data.Total > 5) {
          py = t.y + settings.boxSetting.item.height * 6;
        }
        boxItems.push({
          node: t,
          name: this.isPerson(t.data) ? `查看全部${t.data.Total}位人员>` : `查看全部${t.data.Total}家企业>`,
          type: 'bottom',
          count: t.data.Total,
          index: 6,
          box: t,
          x: t.x,
          y: py,
        });
      }
    });
    return boxItems;
  }

  drawBoxes(nodesContainer, nodes) {
    const node = nodesContainer
      .append('g')
      .selectAll('g')
      .data(nodes)
      .enter()
      .append('g')
      .attr('class', 'node')
      .attr('id', (d) => {
        if (d.depth === 0) {
          return 'center';
        }
      })
      .attr('transform', (d) => {
        return `translate(${d.x}, ${d.y})`;
      });
    node
      .append('rect')
      .attr('stroke', (d) => {
        return d.depth === 0 ? '#128bed' : '#eee';
      })
      .attr('fill', function (d) {
        if (d.depth === 0) {
          return '#128bed';
        }
        return 'white';
      })
      .attr('fill-opacity', function (d) {
        // return 0;
        if (d.depth === 0) {
          return 1;
        }
        return 0;
      })
      .attr('stroke-width', 1)
      .attr('stroke-opacity', 1)
      .attr('y', 0)
      .attr('x', function (d) {
        return -(d.position.width / 2);
      })
      .attr('width', function (d) {
        return d.position.width;
      })
      .attr('height', function (d) {
        return d.position.height;
      })
      .attr('border-radius', settings.radius)
      .attr('rx', function (d) {
        return settings.radius;
      })
      .attr('ry', function (d) {
        return settings.radius;
      })
      .attr('pointer-events', 'visiblePoint')
      .on('mouseover', (d) => {
        // 交互
        this.highLightNode(d);
      })
      .on('mouseout', (d) => {
        this.unHighLightNode(d);
      })
      .on('click', (d) => {
        if (d.depth !== 0) {
          this.emit('nodeClick', d);
        }
      });
    const rows = nodes[0].position.rows;
    const str = nodes[0].data.Name;
    const strs = [];
    if (rows === 1) {
      strs.push({
        name: str.substr(0, str.length),
        row: 'last',
      });
    } else if (rows === 2) {
      strs.push({
        name: str.substr(0, settings.centerTextMax),
        row: 1,
      });
      strs.push({
        name: str.substr(settings.centerTextMax),
        row: 'last',
      });
    } else if (rows === 3) {
      strs.push({
        name: str.substr(0, settings.centerTextMax),
        row: 1,
      });
      strs.push({
        name: str.substr(settings.centerTextMax, settings.centerTextMax),
        row: 2,
      });
      strs.push({
        name: str.substr(settings.centerTextMax * 2),
        row: 'last',
      });
    } else if (rows === 4) {
      strs.push({
        name: str.substr(0, settings.centerTextMax),
        row: 1,
      });
      strs.push({
        name: str.substr(settings.centerTextMax, settings.centerTextMax),
        row: 2,
      });
      strs.push({
        name: str.substr(settings.centerTextMax * 2, settings.centerTextMax),
        row: 3,
      });
      strs.push({
        name: str.substr(settings.centerTextMax * 3),
        row: 'last',
      });
    } else if (rows === 5) {
      strs.push({
        name: str.substr(0, settings.centerTextMax),
        row: 1,
      });
      strs.push({
        name: str.substr(settings.centerTextMax, settings.centerTextMax),
        row: 2,
      });
      strs.push({
        name: str.substr(settings.centerTextMax * 2, settings.centerTextMax),
        row: 3,
      });
      strs.push({
        name: str.substr(settings.centerTextMax * 3, settings.centerTextMax),
        row: 4,
      });
      strs.push({
        name: str.substr(settings.centerTextMax * 4),
        row: 'last',
      });
    }
    const text = nodesContainer
      .selectAll('#center')
      .append('text')
      .attr('font-size', '16px')
      .attr('x', -settings.centerWidth / 2 + 12)
      .attr('y', 2);
    text
      .selectAll('tspan')
      .data(strs)
      .enter()
      .append('tspan')
      .attr('fill', '#fff')
      .attr('x', (d) => {
        let x = text.attr('x');
        if (d.row === 'last') {
          const blank = settings.centerWidth - d.name.length * 16;
          x = parseFloat(blank / 2) + parseFloat(x) - 12;
        }
        return x;
      })
      .attr('dy', 20)
      .text((d) => d.name);
  }

  drawLinks(linksContainer, links, isTop) {
    const self = this;
    function elbow(d) {
      const sourceX = d.source.x;
      let sourceY = d.source.y;
      const targetX = d.target.x;
      let targetY = d.target.y;

      let height = 50;
      if (isTop) {
        targetY += d.target.position.height;
        if (d.source.depth !== 0) {
          let goodHeight = 280;
          if (d.target.depth > 1) {
            goodHeight = 280 - (280 - self.upLevelsInfo[d.target.depth - 1]);
          }

          height = -height - (goodHeight - d.source.position.height);
        } else {
          height = -height;
        }
      } else {
        sourceY += d.source.position.height;
        if (d.source.depth !== 0) {
          height += 280 - d.source.position.height;
        }
      }

      return `M${sourceX},${sourceY}V${sourceY + height}H${targetX}V${targetY}`;
    }
    linksContainer
      .append('g')
      .selectAll('path')
      .attr('class', 'line-g')
      .data(links)
      .enter()
      .append('path')
      .attr('d', elbow)
      .attr('fill', 'none')
      .attr('stroke', '#d6d6d6')
      .attr('stoke-width', 0.5)
      .attr('storke-width', 1);
  }

  highLightNode(node) {
    d3.selectAll('.node rect')
      .attr('stroke', (dd) => {
        if (node.id === dd.id) {
          return dd.isPerson ? '#ffe4e7' : '#cfe7fa';
        }
      })
      .attr('stroke-width', (dd) => {
        return node.id === dd.id ? 6 : 1;
      })
      .attr('cursor', 'pointer');

    // 线运动效果
    d3.selectAll('path')
      .classed('link-flow-in', function (dd) {
        if (dd) {
          if (dd.source.id === node.id || dd.target.id === node.id) {
            return dd.target.data.Direction === 'IN';
          }
        }
      })
      .classed('link-flow-out', function (dd) {
        if (dd) {
          if (dd.source.id === node.id || dd.target.id === node.id) {
            return dd.target.data.Direction === 'OUT';
          }
        }
      })
      .attr('stroke', (dd) => {
        if (dd) {
          if (dd.source.id === node.id || dd.target.id === node.id) {
            return this.getArrowColor(dd);
          }
          return '#D6D6D6';
        }
      });
  }

  unHighLightNode(node) {
    d3.selectAll('path').attr('stroke', '#d6d6d6');
    d3.selectAll('.node rect')
      .attr('stroke', (d) => {
        return d.id === node.id ? '#eee' : '#128bed';
      })
      .attr('stroke-width', (d) => {
        return d.id === node.id ? 1 : 0;
      });

    d3.selectAll('path')
      .classed('link-flow-in', function () {
        return false;
      })
      .classed('link-flow-out', function () {
        return false;
      });
  }

  prepareNodes(nodes, isTop) {
    nodes.forEach((t, index) => {
      t.id = index ? (isTop ? 'up' : 'down') + index : 0;
      t.isPerson = +t.depth ? this.isPerson(t.data) : false;
      if (+t.depth) {
        t.y = isTop ? t.y - 250 : t.y - 200;
      }
      if (isTop) {
        if (!t.depth) {
          t.y = -t.y;
        } else {
          t.y = -t.y - t.position.height;
        }
      }
      // 设置每层信息（用于确认每层box最大高度，避免线过长，优化显示体验）
      if (isTop) {
        if (!this.upLevelsInfo[t.depth]) {
          this.upLevelsInfo[t.depth] = 0;
        }
        const h = this.upLevelsInfo[t.depth];
        if (t.position.height > h) {
          this.upLevelsInfo[t.depth] = t.position.height;
        }
        if (t.depth > 1) {
          t.y += 280 - this.upLevelsInfo[t.depth - 1];
        }
      } else {
        if (!this.downLevelsInfo[t.depth]) {
          this.downLevelsInfo[t.depth] = 0;
        }
        const h = this.downLevelsInfo[t.depth];
        if (t.position.height > h) {
          this.downLevelsInfo[t.depth] = t.position.height;
        }
      }
    });
  }

  // 子节点只有一个线变短
  shortenLine(node, isTop) {
    if (node.children && node.children.length) {
      if (node.children.length === 1) {
        const deltHeight = 80;
        let pHeight = 0;
        if (node.depth !== 0 && node.position) {
          const parentBoxHeight = node.position.height;
          pHeight = 280 - parentBoxHeight;
        }
        if (isTop && this.depthUp[node.depth] && this.depthUp[node.depth].length === 1) {
          node.children[0].y = node.children[0].y + deltHeight + pHeight - 100; // 原先是-200导致陕西三秦科技发展（集团）公司企业第二层之间的连线消失了
        }
        if (!isTop && this.depthDown[node.depth] && this.depthDown[node.depth].length === 1) {
          node.children[0].y = node.children[0].y - deltHeight - pHeight + 40;
        }
      }
      node.children.forEach((t) => {
        this.shortenLine(t, isTop);
      });
    } else {
      return false;
    }
  }

  initBoxSize(nodes) {
    nodes.forEach((t) => {
      t.position = {};
      if (t.depth) {
        t.position.width = settings.boxSetting.width;
        t.position.height = this.getBoxHeight(t.data.Collection, t.data);
      } else {
        t.position.width = settings.centerWidth;
        t.position.height = this.getCenterHeight(t);
      }
    });
    return nodes;
  }

  getCenterHeight(t) {
    const length = t.data.Name.length;
    let rows = parseInt(length / settings.centerTextMax);
    const rowsPlus = length % settings.centerTextMax;
    if (rowsPlus) {
      rows += 1;
    }
    t.position.rows = rows;
    return rows * 19 + 14;
  }

  getBoxHeight(collection, nodeData) {
    let height = 0;
    const topHeight = this.isPerson(nodeData) ? settings.boxSetting.top.person.height : settings.boxSetting.top.company.height;
    if (nodeData.Total) {
      if (nodeData.Total <= 5) {
        height = topHeight + settings.boxSetting.item.height * (nodeData.Total + 1);
      } else {
        height = topHeight + settings.boxSetting.item.height * 5 + settings.boxSetting.bottom.height;
      }
    } else {
      height = topHeight;
    }
    return height;
  }

  isPerson(node) {
    return node.Type === 'Person';
  }

  zoomInOrOut(isEnlarge, isDefault = false) {
    const self = this;
    const transform = d3.zoomTransform(this.svg.node());
    const x = transform.x;
    const y = transform.y;
    const k = transform.k;
    let newK;
    if (isEnlarge) {
      newK = k * 1.3;
    } else {
      newK = k * 0.8;
    }
    if (newK <= settings.scaleRange[0]) {
      newK = settings.scaleRange[0];
    }
    if (newK >= settings.scaleRange[1]) {
      newK = settings.scaleRange[1];
    }

    if (isDefault) {
      newK = 0.7;
    }

    const center = { x: this.size.width / 2, y: this.size.height / 2 };
    const translate0 = { x: (center.x - x) / k, y: (center.y - y) / k };
    const l = { x: translate0.x * newK + x, y: translate0.y * newK + y };
    const newX = x + center.x - l.x;
    const newY = y + center.y - l.y;
    transform.x = newX;
    transform.y = newY;
    transform.k = newK;
    this.root
      .transition()
      .duration(500)
      .attr('transform', transform)
      .on('end', function () {
        self.emit('onScale', d3.zoomTransform(self.svg.node()));
        // d3.select(this).call(this.zoom.transform, transform)
      });
  }

  sizeChange() {
    this.size = {
      width: this.$selector.width(),
      height: this.$selector.height(),
    };
    this.svg.attr('width', this.size.width).attr('height', this.size.height);
    this.topContainer.attr('transform', `translate(${this.size.width / 2}, ${this.size.height / 2})`);
    this.downContainer.attr('transform', `translate(${this.size.width / 2}, ${this.size.height / 2})`);
  }
}
