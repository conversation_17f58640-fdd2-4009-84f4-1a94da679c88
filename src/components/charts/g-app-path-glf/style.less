.e_path {
  display: inline-block;
  margin-top: 8px;
  margin-bottom: 0;

  >div {
    position: relative;

    >span {
      display: inline-block;
      line-height: 28px;
    }

    &:last-child {
      &::after {
        display: none;
      }
    }

    &::after {
      content: '';
      width: 100%;
      height: 1px;
      background-color: #eee;
      position: absolute;
      bottom: -5px;
      left: 0;
    }
  }
}

.e_path div.ea_path-title {
  color: #333;
  font-weight: bold;
  font-size: 12px;
}

.e_path a {
  word-break: break-all;
  color: #333;
  font-size: 12px;
}

.e_path a:hover {
  color: #128bed;
}

.e_path span.ea_arrow {
  display: inline-block;
  min-width: 84px;
  height: 26px;
  background-size: 75px 8px;
  padding-bottom: 10px;
  font-size: 12px;
  color: #999;
  text-align: center;
  position: relative;
  top: -7px;
  margin-right: 6px;
  margin-left: 6px;
}

.e_path span.ea_arrow.ea_left {
  background-size: 75px 8px;
}

.e_path span.ea_t5 {
  display: inline-block;
  width: 105px;
  height: 26px;
  background-size: 100px 34px;
  font-size: 12px;
  color: #128bed;
  text-align: center;
  position: relative;
  top: -9px;
  margin-right: 6px;
  padding-bottom: 40px;
}

.e_path .ea_path-wrap {
  max-height: 200px;
  overflow-y: auto;
  float: left;
}

.e_path .ea_path-wrap>div {
  float: left;
  overflow: hidden;
}

/* 路径长箭头 */
.e_lang-arrow {
  position: relative;
  border: red 0 solid;
  display: inline-block;
  padding: 0 5px;
  margin-left: 10px;
  margin-right: 10px;
  max-width: 160px;
}

.ea_text {
  font-size: 12px;
  color: #128bed;
  text-align: center;
  width: 100%;
  max-width: 150px;
  word-break: keep-all;
  text-overflow: ellipsis;
  overflow: hidden;

  &.ea_text2 {
    height: 12px;
    line-height: 12px;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, 0);
  }

  &.ea_text1 {
    top: 8px;
    position: relative;
  }
}

.ea_line {
  position: absolute;
  width: 100%;
  height: 1px;
  background-color: #ddd;
  top: 50%;
  transform: translate(0, -50%);
  left: 0;
}

.ea_arrow-wrap {
  position: absolute;
  top: 8px;
  font-size: 15px;
  color: #999;
  width: 15px;
  height: 100%;
  z-index: 99;
}

.ea_arrow-wrap.ea_left {
  left: -6px;
  top: 10px;
}

.ea_arrow-wrap.ea_right {
  right: -6px;
  top: 10px;
}

.triangle-left {
  width: 0;
  height: 0;
  border-top: 4px solid transparent;
  border-right: 8px solid #bbb;
  border-bottom: 4px solid transparent;
}

.triangle-right {
  width: 0;
  height: 0;
  border-top: 4px solid transparent;
  border-left: 8px solid #bbb;
  border-bottom: 4px solid transparent;
}
