<template>
  <div class="e_path">
    <div v-for="(onePath, index) in paths" :key="index">
      <span v-for="(path, idx) in onePath" :key="idx">
        <q-entity-link v-if="path.Id" :coy-obj="{ KeyNo: path.Id, Name: path.Name }"></q-entity-link>
        <span v-else>
          <span class="e_lang-arrow">
            <span class="ea_text ea_text2">{{ path.Operation }}</span>
            <span class="ea_text ea_text1">{{ path.Reason }}</span>
            <span class="ea_line" />

            <i
              v-if="(onePath[0].Id === keyNo && path.Direction === 'IN') || (onePath[0].Id !== keyNo && path.Direction === 'OUT')"
              class="ea_arrow-wrap ea_left triangle-left"
            ></i>
            <i v-else class="ea_arrow-wrap ea_right triangle-right"></i>
          </span>
        </span>
      </span>
    </div>
  </div>
</template>
<script src="./component.js"></script>
<style lang="less" src="./style.less" scoped></style>
