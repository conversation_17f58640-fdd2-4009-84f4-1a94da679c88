/* eslint-disable radix */
/* eslint-disable no-param-reassign */
import * as companyUtil from '@/utils/firm';

import utils from '../chart/utils';
import Adjust from './adjustSus';
import settings from '../settings';

const formatGraph = () => {
  const nodeData = {};
  return nodeData;
};

const formatActualController = (data) => {
  // data.ControllerData.PathType = ['A', 'B'];
  data.Name = data.CompanyName;
  data.Paths = [data.ControllerData];
  utils.generateUUID(data);
  const nodes = [];
  const links = [];
  let xsortCount = 1;
  let maxlevel = 1;
  const rootNode = {
    uuid: data.KeyNo,
    keyNo: data.KeyNo,
    name: data.Name,
    org: 1,
    level: 0,
  };
  nodes.push(rootNode);
  data.Paths.forEach((vo, index) => {
    const mainCount = vo.MainCount || 1;
    const node = {
      keyNo: vo.KeyNo,
      name: vo.Name,
      org: vo.Org,
      image: vo.ImageUrl,
      dataType: vo.DataType,
      syr: true,
      tags: vo.Tags,
      xsort: xsortCount,
    };
    if (vo.HasImage && vo.KeyNo && vo.KeyNo[0] === 'p' && !node.image) {
      node.image = companyUtil.getLogoByKeyNo(vo.KeyNo, vo.HasImage);
      // node.image = logoHelper.getLogoByKeyNo(vo.KeyNo, vo.HasImage);
    }
    // 表决权部分
    const rsTags = [];
    let isKzr = false;
    if (vo.IsActual) {
      rsTags.push('实际控制人');
      isKzr = true;
    }
    if (vo.ControlPercent && vo.ControlPercent !== '0%' && vo.ControlPercent !== '0.0%' && vo.ControlPercent !== 0) {
      rsTags.push(`表决权：${vo.ControlPercent}`);
      vo.showControlPercent = true;
    }
    if (vo.PercentTotal && vo.PercentTotal !== '0%') {
      rsTags.push(`受益股份：${vo.PercentTotal}`);
    }
    node.kzr = isKzr;
    node.rsTags = rsTags;

    nodes.push(node);
    for (let i = 0; i < vo.Paths.length; i++) {
      if (!data.hasReverse) {
        vo.Paths[i].reverse();
      }
      if (i < settings.pathSusLimit) {
        for (let j = 0; j < vo.Paths[i].length; j++) {
          const v = vo.Paths[i][j];
          const level = parseInt(v.Level);
          if (level > maxlevel) {
            maxlevel = level;
          }
          if (j === 0) {
            // 头节点 等于 node 节点
            node.level = level;
            node.uuid = v.uuid;
            node.org = v.Org;
          }
          if (!utils.nodeExist(nodes, v)) {
            nodes.push({
              uuid: v.uuid,
              keyNo: v.KeyNo,
              name: v.Name,
              org: v.Org,
              level,
              tags: v.Tags,

              xsort: xsortCount,
            });
          }
          // 如果最后一个节点就连接rootNode,否则连接下一个点
          const targetNode = j === vo.Paths[i].length - 1 ? rootNode : vo.Paths[i][j + 1];
          let linkName = v.Percent;
          if (v.DataType === 5) {
            linkName = `执行事务合伙人 ${v.Percent}`;
            data.hasGp = true;
          }
          const link = {
            source: v.uuid,
            target: targetNode.uuid,
            name: linkName,
            isRed: level === 1,
          };
          if (!utils.linkExist(links, link)) {
            // if (link.source === node.uuid && link.target === rootNode.uuid) {
            //   link.syr = true
            // }
            // 在排序排好的情况下，第一条就是控制路径
            if (index === 0 && i < mainCount) {
              link.syr = true;
            }
            links.push(link);
          }
        }
      }
    }
    xsortCount += 2;
  });
  nodes.forEach((node) => {
    if (node.tags?.length > 0) {
      node.tags.forEach((vo) => {
        node.listTagType = vo.Type;
        if (vo.Type === 2) {
          node.listTag = 'A股';
        } else if (vo.Type === 1) {
          node.listTag = '新三板';
        } else if (vo.Type === 401 && !node.listTag) {
          node.listTag = '港股';
        } else if (vo.Type === 402) {
          node.listTag = vo.Name;
        }
      });
    }
  });

  new Adjust(nodes, links, maxlevel, xsortCount).adjust();
  data.hasReverse = true;
  return {
    name: data.Name,
    keyNo: data.KeyNo,
    nodes,
    links,
    paths: data.Paths,
    controllerData: data.ControllerData,
    actualControl: data.ActualControl,
    hasGp: data.hasGp,
  };
};

export default {
  formatGraph,
  formatActualController,
};
