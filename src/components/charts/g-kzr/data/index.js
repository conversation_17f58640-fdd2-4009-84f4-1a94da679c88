/* eslint-disable no-underscore-dangle */
import { graph } from '@/shared/services';

import formater from './formater';

const loadActualControllerData = (eid, ename) => {
  return new Promise((resolve, reject) => {
    graph
      .getSuspectedActualControllerV2({ keyNo: eid })
      .then((res) => {
        let data = res.Result;
        data.KeyNo = data._id;
        const ac = [];
        if (data.Names && data.Names.length) {
          data.Names.forEach((item) => {
            if (+item.IsActual) {
              ac.push(item);
            }
          });
        }
        data.ControllerData = ac[0];
        if (!data || (data && !data.ControllerData)) {
          const temp = {
            KeyNo: eid,
            CompanyName: ename,
            ControllerData: {
              KeyNo: 'p?',
              Name: '?',
              Percent: '?',
              PercentTotal: '?',
              Paths: [[{ name: '?', KeyNo: '', Percent: '?' }]],
            },
            IsListed: data.IsListed || false,
            ActualControl: (data && data.ActualControl) || {},
          };
          data = temp;
        }
        if (data && data.ControllerData) {
          const detail = formater.formatActualController(data);
          detail.isListed = data.IsListed || false;
          resolve(detail);
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export default {
  loadActualControllerData,
};
