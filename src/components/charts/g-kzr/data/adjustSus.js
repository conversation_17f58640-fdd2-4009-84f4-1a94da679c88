export default class adjust {
  constructor(nodes, links, maxlevel, xsortCount) {
    this.nodes = nodes;
    this.links = links;
    this.maxlevel = maxlevel;
    this.xsortCount = xsortCount;
  }

  adjust = () => {
    let matrix = [];
    this.nodes.forEach((node) => {
      if (node.syr) {
        node.level = this.maxlevel;
      }
      if (!matrix[node.level]) {
        matrix[node.level] = [];
      }
      matrix[node.level].push(node);
    });

    // level 整个一层都没节点，后面层往前移动
    let levelEmpty = 0;
    for (let i = 0; i < matrix.length; i++) {
      if (!matrix[i] || matrix[i].length === 0) {
        levelEmpty++;
      } else if (levelEmpty) {
        for (let j = 0; j < matrix[i].length; j++) {
          matrix[i][j].level -= levelEmpty;
        }
      }
    }

    // 去除空元素
    matrix = matrix.filter((d) => d);

    // 路径只有1条的时候居中
    for (let i = 0; i < matrix.length; i++) {
      if (matrix[i].length === 1) {
        matrix[i][0].xsort = 0;
      }
    }

    // xsort 调整
    for (let i = matrix.length - 1; i > 0; i--) {
      for (let j = 0; j < matrix[i].length; j++) {
        const v = matrix[i][j];
        const upLevelNodes = this.upLevelNodes(v);
        const avgSort = this.getAvgSort(upLevelNodes);
        if (avgSort > 0) {
          v.xsort = avgSort;
        }
        // console.info(v)
        // console.info(upLevelNodes)
        // console.info(avgSort)
      }
      matrix[i].sort((a, b) => {
        return a.xsort - b.xsort;
      });
    }

    // 避免节点重复
    for (let i = 1; i < matrix.length; i++) {
      for (let j = 1; j < matrix[i].length; j++) {
        const v = matrix[i][j];
        const prev = matrix[i][j - 1];
        // 与上个节点xsort相差1或0就重合
        if (v.xsort - prev.xsort <= 1) {
          // 上上节点的xsort
          let pre2vSort = 0;
          if (j >= 2) {
            pre2vSort = matrix[i][j - 2].xsort;
          }
          if (prev.xsort > 1 && (!pre2vSort || prev.xsort - pre2vSort > 2)) {
            v.xsort = prev.xsort + 1;
            prev.xsort--;
            if (this.isNodeLinked(v, prev)) {
              v.xsort++;
            }
          } else {
            v.xsort = prev.xsort + 2;
          }
        }
      }
    }

    // 只有两排的时候优化
    let xCount = 0;
    for (let i = 1; i < matrix.length; i++) {
      if (matrix[i].length > xCount) {
        xCount = matrix[i].length;
      }
    }
    if (xCount === 2 && matrix.length > 3 && matrix.length < 5 && !this.isNodeLinked(matrix[0][0], matrix[matrix.length - 1][0])) {
      for (let i = 1; i < matrix.length; i++) {
        if (i + 1 < matrix.length && !this.isNodeLinked(matrix[i - 1][0], matrix[i + 1][0])) {
          matrix[i][0].xsort = 0;
          if (matrix[i][1]) {
            matrix[i][1].xsort = 5; // （临时）
          }
        }
      }
    }

    // 线重合处理
    // if (this.isNodeLinked(matrix[0][0], matrix[matrix.length - 1][0])) {
    //   // 遍历中间层数
    //   // console.info(111)
    //   for (let i = 1; i < matrix.length - 1; i++) {
    //     let v
    //     if (matrix[i].length === 1) {
    //       v = matrix[i][0]
    //       v.xsort += 2
    //     } else if (matrix[i].length % 2 === 1) {
    //       v = matrix[i][(matrix[i].length - 1) / 2]
    //       v.xsort += 1
    //       this.rightMoveSort(matrix[i], (matrix[i].length - 1) / 2, 1)
    //     }
    //   }
    // }
    // 线重合处理
    const topNode = matrix[matrix.length - 1][0];
    const topTargetNodes = this.targetNodes(topNode);
    for (let i = 0; i < topTargetNodes.length - 1; i++) {
      if (topTargetNodes[i].xsort === topNode.xsort && topTargetNodes[i + 1].xsort === topNode.xsort) {
        if (topTargetNodes[i + 1] === matrix[0][0]) {
          topTargetNodes[i].xsort -= 1;
        } else {
          topTargetNodes[i + 1].xsort += 2;
        }
      }
    }

    // 折线处理
    if (matrix.length === 3) {
      matrix[1].forEach((node) => {
        this.links.forEach((link) => {
          if (link.target === node.uuid && node.xsort !== 0) {
            link.taxiUp = true;
          }
          if (link.source === node.uuid && node.xsort !== 0) {
            link.taxiDown = true;
          }
        });
      });
    }
    // 附加折线（临时），之后通过重新计算xsort处理
    if (xCount === 2 && matrix.length === 4) {
      matrix[1].forEach((node) => {
        this.links.forEach((link) => {
          if (link.source === node.uuid && node.xsort !== 0) {
            link.taxiDown = true;
          }
        });
      });
    }
  };

  // 获取上层链接的节点
  upLevelNodes = (node) => {
    const ns = [];
    this.links.forEach((link) => {
      if (link.target === node.uuid) {
        const targetNode = this.nodes.find((n) => {
          return n.uuid === link.source;
        });
        if (targetNode.level > node.level) {
          ns.push(targetNode);
        }
      }
    });
    return ns;
  };

  // 获取下层链接的节点
  downLevelNodes = (node) => {
    const ns = [];
    this.links.forEach((link) => {
      if (link.source === node.uuid) {
        const sourceNode = this.nodes.find((n) => {
          return n.uuid === link.target;
        });
        if (sourceNode.level && sourceNode.level < node.level) {
          ns.push(sourceNode);
        }
      }
    });
    return ns;
  };

  // 获取下层链接的节点
  targetNodes = (node) => {
    const ns = [];
    this.links.forEach((link) => {
      if (link.source === node.uuid) {
        const sourceNode = this.nodes.find((n) => {
          return n.uuid === link.target;
        });
        ns.push(sourceNode);
      }
    });
    return ns;
  };

  getAvgSort = (nodes) => {
    if (nodes.length <= 1) {
      return 0;
    }
    let xsortTotal = 0;
    nodes.forEach((node) => {
      xsortTotal += node.xsort;
    });
    return Math.floor(xsortTotal / nodes.length);
  };

  rightMoveSort = (nodes, benginIndex, offSort) => {
    for (let i = 0; i < nodes.length; i++) {
      if (i > benginIndex) {
        nodes[i].xsort += offSort;
      }
    }
  };

  isNodeLinked = (node1, node2) => {
    let isLinked = false;
    this.links.forEach((link) => {
      if ((link.source === node1.uuid && link.target === node2.uuid) || (link.target === node1.uuid && link.source === node2.uuid)) {
        isLinked = true;
      }
    });
    return isLinked;
  };
}
