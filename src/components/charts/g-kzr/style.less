.g-kzr-container {
  position: absolute;
  inset: 0;
  overflow: hidden;
  background: url('../utils/images/shuiying6.png') repeat;
  background-size: 360px 280px;
  line-height: 1.2;

  .app-nheader ::v-deep {
    position: relative;
    z-index: 30;

    .app-nheader-wrap {
      height: 58px;
    }
  }

  .foo {
    color: red;
  }

  .charts-iframe-mask {
    position: absolute;
    inset: 0;

    a {
      display: block;
      width: 100%;
      height: 100%;
    }
  }
}

.toolbox {
  position: fixed;
  width: 46px;
  right: 20px;
  bottom: 80px;
  font-size: 18px;
  z-index: 20;
}

#kzr-chart,
#kzr-tupu-chart {
  position: absolute;
  inset: 0;
  overflow: hidden;
}
