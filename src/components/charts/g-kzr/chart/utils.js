/* eslint-disable no-bitwise */
/* eslint-disable func-names */
const isCompany = (data) => {
  const org = data.org || data.org === 0 ? data.org : data.Org;
  if (org === 2) {
    return false;
  }
  if (data.keyNo[0] === 'p') {
    return false;
  }
  if ((!org || org === -1) && data.name && data.name.length < 5) {
    return false;
  }
  return true;
};
const getTagProp = (data, rsTags) => {
  let color;
  let yposSpan;
  let nodeHeight;
  if (rsTags.indexOf('实际控制人') !== -1) {
    color = '#FD485D';
  } else if (rsTags.indexOf('最终受益人') !== -1) {
    color = '#F9AD14';
  } else {
    color = '#128bed';
  }
  if (rsTags.length === 3) {
    yposSpan = 60;
    nodeHeight = 60;
  } else if (rsTags.length === 2) {
    yposSpan = 54;
    nodeHeight = 46;
  } else {
    yposSpan = 45;
    nodeHeight = 30;
  }
  if (isCompany(data)) {
    yposSpan -= 4;
  }
  return {
    color,
    yposSpan,
    nodeHeight,
  };
};

const nodeExist = (nodes, node) => {
  let exist = false;
  nodes.forEach((vo) => {
    if (vo.uuid === node.uuid) {
      exist = true;
    }
  });
  return exist;
};

const linkExist = (links, link) => {
  let exist = false;
  links.forEach((vo) => {
    if (vo.source === link.source && vo.target === link.target) {
      exist = true;
    }
  });
  return exist;
};

const generateUUID = (data) => {
  const UUID = function () {
    let d = new Date().getTime();
    if (window.performance && typeof window.performance.now === 'function') {
      d += performance.now(); // use high-precision timer if available
    }
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (d + Math.random() * 16) % 16 | 0;
      d = Math.floor(d / 16);
      return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
    });
    return uuid;
  };
  const cacheUUID = {};
  data.Paths.forEach((vo) => {
    for (let i = 0; i < vo.Paths.length; i++) {
      for (let j = vo.Paths[i].length - 1; j >= 0; j--) {
        const v = vo.Paths[i][j];
        const key = v.KeyNo || v.Name;
        if (!cacheUUID[key]) {
          cacheUUID[key] = UUID();
        }
        v.uuid = cacheUUID[key];
      }
    }
  });
};

const nullDataPaths = (companyKeyNo, companyName) => {
  const nodes = [
    {
      name: companyName,
      keyNo: companyKeyNo,
      uuid: companyKeyNo,
      org: 1,
      level: 0,
    },
    {
      name: '?',
      keyNo: '',
      uuid: '0',
      level: 1,
      org: 2,
      xsort: 0,
    },
  ];
  const links = [
    {
      source: '0',
      target: companyKeyNo,
      name: ['?%'],
    },
  ];
  return {
    nodes,
    links,
  };
};

const isChinese = (str) => {
  const reg = new RegExp('[\u4E00-\u9FA5]+');
  return reg.test(str);
};

const wrapText = (str) => {
  if (isChinese(str)) {
    if (str.length > 26) {
      str = `${str.substr(0, 25)}…`;
    }
    return str.replace(/(.{13})(?=.)/g, '$1\n');
  }
  if (str.length > 25) {
    str = `${str.substr(0, 25)}…`;
  }
  return str;
};

export default {
  getTagProp,
  isCompany,
  nodeExist,
  linkExist,
  generateUUID,
  nullDataPaths,
  isChinese,
  wrapText,
};
