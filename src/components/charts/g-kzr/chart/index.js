/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-param-reassign */
/* eslint-disable consistent-return */
/* eslint-disable no-underscore-dangle */
/* eslint-disable func-names */
/* eslint-disable @typescript-eslint/no-this-alias */

import { EventEmitter } from 'eventemitter3';

import chartSettings from '../settings';
import utils from './utils';
import globalUtils from '../../utils/utils';

const cytoscape = window.cytoscape;
export default class Chart extends EventEmitter {
  constructor(selector) {
    super();
    this.selector = selector;
    this.$selector = $(selector);
    this.elements = {
      nodes: [],
      edges: [],
    };
  }

  init(data) {
    this.data = data;
    // 计算节点位置
    this.calcPos();
    // 转换节点数据
    this.transData();
    // 绘制图谱
    this.drawChart();
  }

  // 绘制图谱f
  drawChart() {
    const self = this;
    this.cy = cytoscape({
      container: this.$selector[0],
      elements: this.elements,
      minZoom: 0.4,
      maxZoom: 2.5,
      motionBlur: false,
      textureOnViewport: false,
      wheelSensitivity: 0.1,
      layout: {
        name: 'preset',
        componentSpacing: 40,
        nestingFactor: 12,
        padding: 10,
        edgeElasticity: 800,
      },
      // 图谱样式配置
      style: [
        {
          selector: 'node',
          style: {
            color: '#333',
            'background-color': '#fff',
            'border-color': '#ccc',
            'font-size': 12,
            'font-family': 'Microsoft YaHei',
            'text-wrap': 'wrap',
            'text-halign': 'center',
            'text-valign': 'center',
            'overlay-color': '#fff',
            'overlay-opacity': 0,
            'background-opacity': 1,
            'border-width': 1,
            'border-opacity': 1,
          },
        },
        {
          selector: '.rootnode',
          style: {
            shape: 'square',
            width: (ele) => {
              const label = ele.data('name');
              return label.length * 12 + 20;
            },
            height: chartSettings.rootnodeHeight,
            color: '#fff',
            'border-color': '#128bed',
            'background-color': '#128bed',
            label: (ele) => {
              const label = ele.data('name');
              return label;
            },
          },
        },
        {
          selector: '.enode',
          style: {
            shape: 'square',
            width: chartSettings.enodeWidth,
            height: chartSettings.enodeHeight,
            color: '#333',
            'border-color': '#128bed',
            'line-height': 1.4,
            label: (ele) => {
              return utils.wrapText(ele.data('name'));
            },
            'text-wrap': 'wrap',
            'text-max-width': chartSettings.enodeWidth,
            'background-fit': 'contain',
            'background-offset-y': 3,
            'background-image': 'none',
          },
        },
        {
          selector: '.pnode',
          style: {
            shape: 'ellipse',
            width: chartSettings.pnodeWidth,
            height: chartSettings.pnodeHeight,
            color: '#fff',
            'border-color': '#FD485D',
            'background-fit': 'cover',
            'background-color': '#FD485D',
            'background-image': (ele) => {
              const image = ele.data('image');
              if (image) {
                return image;
              }
              return 'none';
            },
            'text-margin-y': (ele) => {
              if (ele.data('image')) {
                return 12;
              }
            },
            'border-width': (ele) => {
              if (ele.data('image')) {
                return 3;
              }
              return 1;
            },
            'text-background-color': '#000',
            'text-background-shape': 'roundrectangle',
            'text-background-opacity': function (ele) {
              if (ele.data('image')) {
                return 0.3;
              }
              return 0;
            },
            label: (ele) => {
              let label = ele.data('name');
              label = label.replace('（普通合伙）', '');
              return label.replace('(有限合伙)', '');
            },
          },
        },
        {
          selector: '.tag',
          style: {
            shape: 'polygon',
            'shape-polygon-points': (ele) => {
              if (ele.data('height') > 30) {
                return [-1, -1, 1, -1, 1, 0.8, 0.06, 0.8, 0, 1, -0.06, 0.8, -1, 0.8];
              }
              return [-1, -1, 1, -1, 1, 0.7, 0.06, 0.7, 0, 1, -0.06, 0.7, -1, 0.7];
            },
            width: chartSettings.tagWidth,
            color: '#fff',
            'text-margin-y': -2,
            'line-height': 1.4,
            'border-width': 0,
            'font-size': 12,
            'background-color': (ele) => {
              return ele.data('color');
            },
            height: (ele) => {
              return ele.data('height');
            },
            label: (ele) => {
              const label = ele.data('name');
              return label.replace(/,/g, '\n');
            },
          },
        },
        {
          selector: '.list-tag',
          style: {
            shape: 'square', // round-rectangle
            'z-index': 999,
            width: (ele) => {
              return ele.data('width');
            },
            height: chartSettings.listTagHeight,
            'font-size': 10,
            // 'text-margin-y': -1,
            'font-family': 'system-ui',
            color: (ele) => {
              return ele.data('color');
            },
            'border-color': (ele) => {
              return ele.data('color');
            },
            'background-color': (ele) => {
              return ele.data('background');
            },
            label: (ele) => {
              return ele.data('name');
            },
          },
        },

        {
          selector: '.enode:active',
          style: {
            color: '#000',
          },
        },
        {
          selector: '.enode.hover',
          style: {
            color: '#000',
          },
        },
        {
          selector: 'edge',
          style: {
            color: '#128bed',
            width: 0.5,
            'line-style': 'solid',
            'curve-style': 'bezier',
            'control-point-step-size': 20,
            'target-arrow-shape': 'triangle',
            'text-background-color': '#fff',
            'text-background-opacity': 0.8,
            'text-background-padding': 1,
            'target-arrow-color': '#128bed',
            'arrow-scale': 0.8,
            'line-color': '#aaaaaa',
            // 'edge-text-rotation': 'autorotate',
            'text-opacity': 1,
            'font-size': 12,
            'overlay-color': '#fff',
            'overlay-opacity': 0,
            'font-family': 'Microsoft YaHei',
            label: (ele) => {
              return ele.data('name');
            },
          },
        },
        {
          selector: '.taxi',
          style: {
            'curve-style': 'taxi',
            'taxi-direction': 'auto',
            'taxi-turn': 800,
            'taxi-turn-min-distance': 5,
          },
        },
        {
          selector: '.taxi-up',
          style: {
            label: '',
            'target-label': (ele) => {
              return ele.data('name');
            },
            'target-text-offset': 70,
            'curve-style': 'taxi',
            'taxi-direction': 'horizontal',
            'taxi-turn': 800,
            'taxi-turn-min-distance': 5,
          },
        },
        {
          selector: '.taxi-down',
          style: {
            label: '',
            'source-label': (ele) => {
              return ele.data('name');
            },
            'source-text-offset': 70,
            'curve-style': 'taxi',
            'taxi-direction': 'vertical',
            'taxi-turn': 800,
            'taxi-turn-min-distance': 5,
          },
        },
        {
          selector: '.syredge',
          style: {
            color: (ele) => {
              const isRed = ele.data('isRed');
              return isRed ? '#FD485D' : '#128bed';
            },
            'target-arrow-color': (ele) => {
              const isRed = ele.data('isRed');
              return isRed ? '#FD485D' : '#128bed';
            },
            'line-color': (ele) => {
              const isRed = ele.data('isRed');
              return isRed ? '#FD485D' : '#999';
            },
          },
        },
        {
          selector: 'node.active',
          style: {
            color: '#000',
          },
        },
        {
          selector: 'edge.active',
          style: {
            color: '#000',
          },
        },
        {
          selector: '.disable',
          style: {
            'z-index': 1,
            opacity: 0.2,
          },
        },
      ],
    });
    this.cy.ready(() => {
      this.cy.zoom({
        level: chartSettings.defaultScale,
      });
      // this.cy.$('.kzr').successors('edge').addClass('kzredge')
      this.cy.center();
      if (this.cy.elements().boundingBox().h > this.cy.height() - 125) {
        this.cy.fit(80);
        // this.cy.panBy({ x: 0, y: 20 })
      }
      this.emit('firstScale', this.cy.zoom());
      this.setListTag();
    });
    this.cy.on('click', '.enode,.pnode', (evt) => {
      const node = evt.target;
      if (!node._private.selectable) return;
      this.cy.$('eles').addClass('disable');
      node.removeClass('disable');
      node.successors().removeClass('disable');
      if (node.hasClass('tagnode')) {
        this.cy.$(`#${node._private.data.id}_tag`).removeClass('disable');
      }
      if (node.data('listTag')) {
        this.cy.$(`#${node._private.data.id}_listtag`).removeClass('disable');
      }
      node.successors().forEach((successorsNode) => {
        if (successorsNode.data('listTag')) {
          this.cy.$(`#${successorsNode._private.data.id}_listtag`).removeClass('disable');
        }
      });
    });
    this.cy.on('mouseover', 'node', (evt) => {
      const node = evt.target;
      node.addClass('hover');
      const position = node.renderedBoundingBox();
      const org = node._private.data.org;
      if (!(org === 860)) {
        this.emit('nodeHover', {
          data: node._private.data,
          position: {
            left: position.x1,
            right: position.x2,
            top: position.y1,
            bottom: position.y2,
          },
          size: {
            width: position.w,
            height: position.h,
          },
        });
      }
    });
    this.cy.on('mouseout', 'node', (evt) => {
      const node = evt.target;
      evt.target.removeClass('hover');
      this.emit('nodeOut', node._private.data);
    });
    this.cy.on('click', (evt) => {
      if (evt.target === this.cy) {
        this.cy.$('eles').removeClass('disable');
        this.emit('blankClick');
      }
    });
    this.cy.on('drag', '.tagnode', (evt) => {
      const node = evt.target;
      const tagProp = utils.getTagProp(node._private.data, node._private.data.rsTags);
      this.cy.$(`#${node._private.data.id}_tag`).position({
        x: node._private.position.x,
        y: node._private.position.y - tagProp.yposSpan,
      });
    });

    this.cy.on('drag', '.enode', (evt) => {
      const node = evt.target;
      if (node.data('listTag')) {
        const listTag = this.cy.$(`#${node._private.data.id}_listtag`);
        listTag.position({
          x: node._private.position.x + listTag.data('offSetX'),
          y: node._private.position.y + listTag.data('offSetY'),
        });
      }
    });
    this.cy.on('zoom', () => {
      this.emit('onZoom', this.cy.zoom());
    });
  }

  // 计算节点位置
  calcPos() {
    const width = this.$selector.width();
    const height = this.$selector.height();
    this.size = {
      width,
      height,
    };
    let maxLevel = 0;
    let xsortCount = 0;
    this.data.nodes.forEach((dataNode) => {
      if (dataNode.level > maxLevel) {
        maxLevel = dataNode.level;
      }
      if (dataNode.xsort > xsortCount) {
        xsortCount = dataNode.xsort;
      }
    });
    let xGap = chartSettings.xGap + chartSettings.enodeWidth / 2;
    let yGap = chartSettings.yGap;
    if (xsortCount < 8 && xsortCount >= 5) {
      if (this.data.hasGp) {
        xGap = 160;
      } else {
        xGap = 120;
      }
    } else if (xsortCount < 5) {
      xGap = 200;
    }
    if (maxLevel > 4) {
      yGap = 100;
    } else if (maxLevel <= 4 && maxLevel >= 2) {
      yGap = 110;
    } else if (maxLevel < 2) {
      yGap = 250;
    }
    const xCenter = 0;
    const xStart = xCenter - ((xsortCount + (xsortCount % 2)) / 2) * xGap;
    const yStart = height / 2 - chartSettings.bottom;
    this.data.nodes.forEach((dataNode) => {
      if (dataNode.xsort) {
        dataNode.x = xStart + dataNode.xsort * xGap;
      } else {
        dataNode.x = xCenter;
      }
      dataNode.y = yStart - dataNode.level * yGap;
    });
  }

  // 转换节点数据
  transData() {
    this.data.nodes.forEach((dataNode) => {
      const classes = [];
      if (dataNode.level === 0) {
        classes.push('rootnode');
      } else if (utils.isCompany(dataNode)) {
        classes.push('enode');
      } else {
        classes.push('pnode');
      }
      const newNode = {
        data: {
          ...dataNode,
          id: dataNode.uuid,
        },
        position: { x: dataNode.x, y: dataNode.y },
        classes,
      };

      if (dataNode.rsTags || dataNode.level === 0) {
        // newNode.grabbable = false
      }
      if (dataNode.rsTags) {
        newNode.classes.push('tagnode');
        const tagProp = utils.getTagProp(dataNode, dataNode.rsTags);
        const tagNode = {
          grabbable: false,
          selectable: false,
          data: {
            id: `${dataNode.uuid}_tag`,
            name: dataNode.rsTags.toString(),
            height: tagProp.nodeHeight,
            color: tagProp.color,
          },
          position: { x: dataNode.x, y: dataNode.y - tagProp.yposSpan },
          classes: ['tag'],
        };
        this.elements.nodes.push(tagNode);
      }
      if (dataNode.kzr) {
        newNode.classes.push('kzr');
      }
      this.elements.nodes.push(newNode);
    });
    this.data.links.forEach((link) => {
      const edge = {
        data: link,
      };
      if (link.taxiUp) {
        edge.classes = ['taxi-up'];
      } else if (link.taxiDown) {
        edge.classes = ['taxi-down'];
      } else {
        edge.classes = [];
      }
      if (link.syr) {
        edge.classes.push('syredge');
      }
      this.elements.edges.push(edge);
    });
  }

  zoomInOrOut(zoomIn) {
    let scale = this.cy.zoom();
    if (zoomIn) {
      scale += chartSettings.scaleRate;
    } else {
      scale -= chartSettings.scaleRate;
    }
    this.cy.zoom({
      level: scale, // the zoom level
    });
  }

  saveImg(companyName) {
    const imgdata = this.cy.png({ full: true, bg: 'rgba(255, 255, 255, 0)', scale: 1.5 });
    globalUtils.downloadImage(imgdata, companyName, '实际控制人');
  }

  sizeChange() {
    const newWidth = this.$selector.width();
    const newHeight = this.$selector.height();
    if (!this.cy) return;
    if (this.size.width === newWidth && this.size.height === newHeight) return;
    this.size = {
      width: newWidth,
      height: newHeight,
    };
    this.cy.center();
  }

  setListTag() {
    this.cy.$('.enode').forEach((node) => {
      const listTag = node.data('listTag');
      if (listTag) {
        const width = listTag.length * 9 + 8;
        const offSetX = chartSettings.enodeWidth / 2 - width / 2 - 5;
        const offSetY = -chartSettings.enodeHeight / 2;
        let color = '#F9AD14';
        let background = '#FEF3DC';
        if (node.data('listTagType') === 402) {
          color = '#6F77D1';
          background = '#EAEBF8';
        }
        this.cy.add({
          classes: ['list-tag'],
          pannable: true,
          position: { x: node.position().x + offSetX, y: node.position().y + offSetY },
          data: {
            id: `${node.data('id')}_listtag`,
            name: listTag,
            width,
            offSetX,
            offSetY,
            color,
            background,
          },
        });
      }
    });
  }

  // 左侧弹出详情时挡住图谱的话图谱右移
  moveByCard(flag) {
    if (flag) {
      const offX = 350 - this.cy.nodes().renderedBoundingBox().x1;
      if (offX > 0) {
        this.cardOffX = offX;
        this.cy.animate(
          {
            panBy: { x: this.cardOffX, y: 0 },
          },
          {
            duration: 200,
          }
        );
        // this.cy.panBy({ x: this.cardOffX, y: 0 })
      }
    } else if (this.cardOffX) {
      this.cy.animate(
        {
          panBy: { x: -this.cardOffX, y: 0 },
        },
        {
          duration: 200,
        }
      );
      // this.cy.panBy({ x: -this.cardOffX, y: 0 })
      this.cardOffX = 0;
    }
  }

  // iframe 时使用
  delayZoom() {
    this.cy.zoomingEnabled(false);
    setTimeout(() => {
      this.cy.zoomingEnabled(true);
    }, 2000);
  }

  cleanup() {
    if (this.cy) {
      this.cy.destroy();
    }
  }
}
