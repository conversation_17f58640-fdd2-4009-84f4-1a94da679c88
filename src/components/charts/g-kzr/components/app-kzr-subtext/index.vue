<template>
  <div class="app-sustext" :class="{ iframe: iframe, isFull: isFullScreen }">
    <div v-if="actualControl && !isListed" class="kzr">
      <span>
        上市企业公示实际控制人（官方公示）：
        <span v-if="actualControl.PersonList">
          <span v-for="(v, index) in actualControl.PersonList" :key="index">
            <span v-if="index != 0">，</span>

            <q-entity-link :coy-obj="{ KeyNo: v.KeyNo, Name: v.Name }"></q-entity-link>
          </span>
        </span>
        <span v-else>?</span>
      </span>
      <span class="m-l-lg" v-if="controllerData.showControlPercent"
        >表决权：<span class="text-danger">{{ controllerData.ControlPercent }}</span>
      </span>
    </div>
    <div v-else-if="controllerData" class="kzr">
      <template v-if="controllerData.Name">
        <span
          >实际控制人<a-popover placement="bottom">
            <div slot="content" style="width: 260px">
              企业的实际控制人，是企查查根据企业股权数据或上市公司公告得出，仅供参考；企业在公司章程或股东协议中或许有其他安排，在使用该数据前建议跟企业做进一步核实。<br />当前仅展示最多20条控制路径。
            </div>
            <q-icon type="icon-a-shuomingxian" class="icon-zhushi"></q-icon> </a-popover
          >（大数据分析）：
          <q-entity-link :coy-obj="{ KeyNo: controllerData.KeyNo, Name: controllerData.Name }"></q-entity-link>
        </span>
        <span class="m-l-lg"
          >表决权：<span class="text-danger" v-if="controllerData.PercentTotal">{{ controllerData.PercentTotal }}</span>
          <span class="text-danger" v-else>?</span>
        </span>
        <span class="m-l-lg" v-if="controllerData.Job"
          >担任职务：<span class="text-danger">{{ controllerData.Job }}</span></span
        >
      </template>
      <template v-if="!controllerData.Name">
        <span
          >实际控制人<a-popover placement="bottom">
            <div slot="content" style="width: 260px">
              企业的实际控制人，是企查查根据企业股权数据或上市公司公告得出，仅供参考；企业在公司章程或股东协议中或许有其他安排，在使用该数据前建议跟企业做进一步核实。<br />当前仅展示最多20条控制路径。
            </div>
            <app-icon type="icon-a-shuomingxian" class="icon-zhushi"></app-icon> </a-popover
          >（大数据分析）：?</span
        >
      </template>
    </div>
  </div>
</template>
<script>
/* eslint-disable @typescript-eslint/no-empty-function */

export default {
  name: 'gAppKzrSustext',
  props: {
    iframe: {
      type: Boolean,
    },
    controllerData: {
      type: Object,
      default: () => {},
    },
    actualControl: {
      type: Object,
      default: () => {},
    },
    isFullScreen: {
      type: Boolean,
      default: false,
    },
    isListed: {
      type: Boolean,
      default: false,
    },
  },
};
</script>
<style lang="less" scoped>
.app-sustext {
  position: absolute;
  right: 20px;
  top: 0;
  font-size: 14px;
  color: #999;
  background: #f5faff;
  border-radius: 2px;
  padding: 10px;
  z-index: 2;

  .kzr {
    text-align: right;
    line-height: 1.8;

    .icon-zhushi {
      font-size: 14px;
      position: relative;
      top: 1px;
      margin: 0 5px;
      color: #bbb;
    }
  }
}
.m-l-lg {
  margin-left: 30px;
}
.text-danger {
  color: #fe5151;
}
a.text-primary {
  color: #128bed;
  text-decoration: none;
}
.app-sustext.iframe {
  top: 0;
  right: 0;
  font-size: 13px;
  width: 100%;
}
.app-sustext.iframe .kzr {
  text-align: center;
}
</style>
