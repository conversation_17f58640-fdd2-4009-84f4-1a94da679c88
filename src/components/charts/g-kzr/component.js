/* eslint-disable no-param-reassign */
import resizeDetector from 'element-resize-detector';
import _ from 'lodash';

import dataLoader from './data';
import { actionTypes, gUiToolbox, gUiToolboxAction } from '../g-ui-toolbox';
import gUiFooter from '../g-ui-footer';
import gUiLoading from '../g-ui-loading';
import gUiNoData from '../g-ui-no-data';
import appPerson from '../g-app-person';
import appCompany from '../g-app-company';
import appSustext from './components/app-kzr-subtext';
import Chart from './chart/index';
import utils from './chart/utils';
import popoverHelper from '../utils/popoverHelper';

export default {
  name: 'g-kzr',
  props: {
    actionTypes: {
      type: Object,
      default: () => actionTypes,
    },
    containerName: {
      type: String,
      default: 'kzr-chart',
    },
    keyNo: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
    iframe: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    [gUiFooter.name]: gUiFooter,
    [gUiLoading.name]: gUiLoading,
    [gUiNoData.name]: gUiNoData,
    [gUiToolbox.name]: gUiToolbox,
    [gUiToolboxAction.name]: gUiToolboxAction,
    [appSustext.name]: appSustext,
  },
  data() {
    return {
      isInit: false,
      isLoading: true,
      noData: false,
      scale: 1,
      paths: [],
      // 操作栏
      isFullScreen: false,
      isSaving: false,
      controllerData: null,
      actualControl: null,
      isListed: false,
    };
  },
  mounted() {
    this.cleanup();
    this.$nextTick(() => {
      this.attachResizeDetector();
      this.onRefresh();
      this.isFullScreen =
        document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement || document.msFullscreenElement;
      this.$nextTick(() => {
        document.addEventListener('fullscreenchange', () => {
          this.isFullScreen = !!document.fullscreenElement;
        });
        document.addEventListener('webkitfullscreenchange', () => {
          this.isFullScreen = !!document.webkitFullscreenElement;
        });
        document.addEventListener('mozfullscreenchange', () => {
          this.isFullScreen = !!document.mozFullScreenElement;
        });
        document.addEventListener('MSFullscreenChange', () => {
          this.isFullScreen = !!document.msFullscreenElement;
        });
      });
    });
  },
  methods: {
    cleanup() {
      this.isInit = false;
      if (this.chart) {
        this.chart.cleanup();
      }
    },
    onRefresh() {
      this.cleanup();
      this.isLoading = true;
      this.noData = false;
      this.chart = new Chart(`#${this.containerName}`);
      dataLoader
        .loadActualControllerData(this.keyNo, this.name)
        .then((data) => {
          this.isInit = true;
          this.isLoading = false;
          this.isListed = data.isListed;
          this.paths = data.paths;
          this.controllerData = data.controllerData;
          this.actualControl = data.actualControl;
          if (this.controllerData.Paths.length === 0) {
            this.chart.init(utils.nullDataPaths(this.keyNo, this.name));
          } else {
            this.chart.init(data);
          }
        })
        .catch((err) => {
          this.isLoading = false;
          this.noData = true;
          if (err.statusCode === 201) {
            this.isInit = true;
            this.isLoading = false;
            this.chart.init(utils.nullDataPaths(this.keyNo, this.name));
            this.controllerData = { Name: '' };
          }
        });
      this.chart.on('nodeHover', ({ data, position, size }) => {
        if (!this.iframe && data.keyNo && data.keyNo.length > 2) {
          data.delayTimer = setTimeout(() => {
            const pathData = this.paths.find((path) => {
              return path.KeyNo === data.keyNo;
            });
            popoverHelper.showPopover({
              component: data.keyNo && data.keyNo[0] === 'p' ? appPerson : appCompany,
              data: {
                hasKeyNo: true,
                id: data.keyNo,
                keyNo: data.keyNo,
                name: data.name,
                eid: this.keyNo,
                ename: this.name,
                rsTags: data.rsTags,
                pathData,
                org: data.org,
                type: 2,
                tips: '当前仅展示最多50条控制路径。',
              },
              container: $(`.${this.containerName}`),
              targetPosition: position,
              targetSize: { width: size.width * this.scale, height: size.height * this.scale },
              identity: `detail-popover-${data.id}`,
              containerSize: {
                width: $(`.${this.containerName}`).width(),
                height: $(`.${this.containerName}`).height(),
              },
            });
          }, 400);
        }
      });
      this.chart.on('nodeOut', (data) => {
        if (!this.iframe) {
          if (data.delayTimer) {
            clearTimeout(data.delayTimer);
            delete data.delayTimer;
          }
          $(`#detail-popover-${data.id}`).fadeOut(400, function () {
            $(this).remove();
          });
        }
      });
      this.chart.on('onZoom', (scale) => {
        this.scale = scale;
      });
    },
    onZoomIn() {
      this.chart.zoomInOrOut(true);
    },
    onZoomOut() {
      this.chart.zoomInOrOut(false);
    },
    onFullScreen() {
      const element = $('.g-kzr-container')[0];
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
    },
    onExitFullScreen() {
      if (this.isFullScreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      }
    },
    onSave() {
      if (this.chart) {
        this.chart.saveImg(this.name);
      }
    },
    onMouseenter() {
      if (this.iframe && this.isInit) {
        this.chart.delayZoom();
      }
    },
    attachResizeDetector() {
      this.resizeDetector = resizeDetector();
      this.resizeDetector.listenTo(
        $('.g-kzr-container')[0],
        _.debounce(() => {
          if (this.chart) {
            this.chart.sizeChange();
          }
        }, 200)
      );
    },
  },
};
