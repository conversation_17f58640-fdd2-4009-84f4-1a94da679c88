/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable func-names */
/* eslint-disable no-underscore-dangle */
import { EventEmitter } from 'eventemitter3';
import _ from 'lodash';

import chartSettings from '../settings';
import utils from './utils';
import globalUtils from '../../utils/utils';

const cytoscape = window.cytoscape;
const automove = window.cytoscapeAutomove;
cytoscape.use(automove);

export default class Chart extends EventEmitter {
  constructor(selector) {
    super();
    this.selector = selector;
    this.$selector = $(selector);
    this.elements = {
      nodes: [],
      edges: [],
    };
    this.specialOrg = [13, 20, 21]; // 投资机构、集团、企业族群
  }

  init(data, isVip) {
    this.data = data;
    this.isVip = isVip;
    // 计算节点位置
    this.calcPos();
    // 转换节点数据
    this.transData();
    // 绘制图谱
    this.drawChart();
  }

  // 绘制图谱
  drawChart() {
    this.cy = cytoscape({
      container: this.$selector[0],
      elements: this.elements,
      minZoom: 0.4,
      maxZoom: 2.5,
      motionBlur: false,
      textureOnViewport: false,
      wheelSensitivity: 0.1,
      layout: {
        name: 'preset',
        componentSpacing: 40,
        nestingFactor: 12,
        padding: 10,
        edgeElasticity: 800,
      },
      // 图谱样式配置
      style: [
        {
          selector: 'node',
          style: {
            color: '#333',
            'background-color': '#fff',
            'border-color': '#ccc',
            'font-size': 14,
            'font-family': 'Microsoft YaHei',
            'text-wrap': 'wrap',
            'text-halign': 'center',
            'text-valign': 'center',
            'overlay-color': '#fff',
            'overlay-opacity': 0,
            'background-opacity': 1,
            'border-width': 1,
            'z-index': 6,
            'border-opacity': 1,
          },
        },
        {
          selector: '.rootnode',
          style: {
            shape: 'square',
            width: (ele) => {
              const label = ele.data('name');
              return label.length * 16 + 20;
            },
            'font-size': 16,
            height: chartSettings.rootnodeHeight,
            color: '#fff',
            'border-color': '#128bed',
            'background-color': '#128bed',
            label: (ele) => {
              const label = ele.data('name');
              return label;
            },
            'text-margin-y': 1,
            'border-width': 1,
          },
        },
        {
          selector: '.enode',
          style: {
            shape: 'square',
            width: chartSettings.enodeWidth,
            height: (ele) => {
              let offset = 0;
              if (ele.data('publicStatus')) {
                offset += 30;
              }
              if (ele.data('name').length > 12 && this.specialOrg.includes(ele.data('org'))) {
                offset += 20;
              }
              return offset + chartSettings.enodeHeight;
            },
            color: '#333',
            'border-color': '#128bed',
            'line-height': 1.6,
            label: (ele) => {
              return utils.wrapText(ele.data('name'));
            },
            'text-margin-y': (ele) => {
              const publicStatus = ele.data('publicStatus');
              const otherTag = ele.data('otherTag');
              // let org = ele.data('org')
              // let orgTag = this.specialTag.includes(org)
              let offset = 0;
              if (publicStatus && otherTag) {
                offset = -20;
              } else if (publicStatus) {
                offset = -13;
              } else if (otherTag) {
                offset = -10;
              }
              return offset;
            },
            // 'text-wrap': 'wrap',
            // 'text-max-width': chartSettings.enodeWidth - 10,
            'background-fit': 'contain',
            'background-offset-y': 0,
            'background-image': (ele) => {
              const image = ele.data('image');
              if (image) {
                return image;
              }
              return 'none';
            },
          },
        },
        {
          selector: '.pnode',
          style: {
            shape: 'ellipse',
            width: chartSettings.pnodeWidth,
            height: chartSettings.pnodeHeight,
            color: '#fff',
            'border-color': '#FD485D',
            'background-fit': 'contain',
            'background-color': '#FD485D',
            'background-image': (ele) => {
              const image = ele.data('image');
              if (image) {
                return image;
              }
              return 'none';
            },
            'text-margin-y': (ele) => {
              if (ele.data('image')) {
                return 12;
              }
              return 0;
            },
            'border-width': (ele) => {
              if (ele.data('image')) {
                return 3;
              }
              return 1;
            },
            'text-background-color': '#000',
            'text-background-shape': 'roundrectangle',
            'text-background-opacity': function (ele) {
              if (ele.data('image')) {
                return 0.3;
              }
              return 0;
            },
            label: (ele) => {
              let label = ele.data('name');
              label = label.replace('（普通合伙）', '');
              return label.replace('(有限合伙)', '');
            },
          },
        },
        {
          selector: '.tag',
          style: {
            shape: 'polygon',
            'shape-polygon-points': (ele) => {
              if (ele.data('height') > 50) {
                return [-1, -1, 1, -1, 1, 0.86, 0.06, 0.86, 0, 1, -0.06, 0.86, -1, 0.86];
              }
              if (ele.data('height') > 30) {
                return [-1, -1, 1, -1, 1, 0.8, 0.06, 0.8, 0, 1, -0.06, 0.8, -1, 0.8];
              }
              return [-1, -1, 1, -1, 1, 0.7, 0.06, 0.7, 0, 1, -0.06, 0.7, -1, 0.7];
            },
            width: (ele) => {
              return ele.data('width');
            },
            color: '#fff',
            'text-margin-y': -1,
            'line-height': 1.4,
            'border-width': 0,
            'font-size': 12,
            'background-color': (ele) => {
              return ele.data('color');
            },
            height: (ele) => {
              return ele.data('height');
            },
            label: (ele) => {
              const label = ele.data('name');
              return label.replace(/,/g, '\n');
            },
          },
        },
        {
          selector: '.enode:active',
          style: {
            color: '#000',
          },
        },
        {
          selector: '.enode.hover',
          style: {
            color: '#000',
          },
        },
        {
          selector: '.list-tag',
          style: {
            shape: 'square', // round-rectangle
            'z-index': 999999,
            width: (ele) => {
              return ele.data('width');
            },
            height: chartSettings.listTagHeight,
            'font-size': 12,
            // 'text-margin-y': -1,
            'font-family': 'system-ui',
            color: (ele) => {
              return ele.data('color');
            },
            'border-color': (ele) => {
              return ele.data('background');
            },
            'background-color': (ele) => {
              return ele.data('background');
            },
            label: (ele) => {
              return ele.data('name');
            },
          },
        },
        {
          selector: '.public-status',
          style: {
            shape: 'square',
            height: chartSettings.publicHeight,
            width: chartSettings.enodeWidth - 2,
            'font-size': 12,
            'text-margin-y': 1,
            'font-family': 'system-ui',
            color: (ele) => {
              return ele.data('color');
            },
            'border-color': (ele) => {
              return ele.data('background');
            },
            'background-color': (ele) => {
              return ele.data('background');
            },
            label: (ele) => {
              return ele.data('name');
            },
          },
        },
        {
          selector: '.other-tag',
          style: {
            shape: 'square',
            height: chartSettings.listTagHeight,
            width: (ele) => {
              return ele.data('width');
            },
            'font-size': 12,
            'text-margin-y': 1,
            'font-family': 'system-ui',
            color: (ele) => {
              return ele.data('color');
            },
            'border-color': (ele) => {
              return ele.data('background');
            },
            'background-color': (ele) => {
              return ele.data('background');
            },
            label: (ele) => {
              return ele.data('name');
            },
          },
        },
        {
          selector: '.enode.tagnode',
          style: {
            'border-color': '#FD485D',
            'border-width': 1,
          },
        },
        {
          selector: 'edge',
          style: {
            color: '#128bed',
            width: 0.5,
            'line-style': 'solid',
            'curve-style': 'bezier',
            'control-point-step-size': 20,
            'target-arrow-shape': (ele) => {
              const org = ele.data('sourceOrg');
              return this.specialOrg.includes(org) ? '' : 'triangle';
            },
            'text-background-color': '#fff',
            'text-background-opacity': 1,
            'text-background-padding': 2,
            'target-arrow-color': '#128bed',
            'arrow-scale': 0.8,
            'line-color': '#aaaaaa',
            // 'edge-text-rotation': 'autorotate',
            'text-opacity': 1,
            'font-size': 12,
            'overlay-color': '#fff',
            'overlay-opacity': 0,
            'font-family': 'Microsoft YaHei',
            'z-index': (ele) => {
              return ele.data('zIndex');
            },
            label: (ele) => {
              return ele.data('name');
            },
          },
        },
        // {
        //   selector: '.syredge',
        //   style: {
        //     color: '#FD485D',
        //     'target-arrow-color': '#FD485D',
        //     'line-color': '#FD485D'
        //   }
        // },
        {
          selector: 'node.active',
          style: {
            color: '#000',
          },
        },
        {
          selector: 'edge.active',
          style: {
            color: '#000',
          },
        },
        {
          selector: '.disable',
          style: {
            'z-index': 1,
            opacity: 0.2,
          },
        },
      ],
    });
    this.cy.ready(() => {
      this.cy.zoom({
        level: chartSettings.defaultScale,
      });
      // this.cy.$('.kzr').successors('edge').addClass('kzredge')
      this.cy.center();
      if (this.cy.elements().boundingBox().h > this.cy.height() - 125 || this.cy.elements().boundingBox().w > this.cy.width() - 80) {
        this.cy.fit(120);
        // if (!initialZoom) {
        //   initialZoom = this.cy.zoom()
        // }
        // this.cy.panBy({ x: 0, y: 20 })
      }
      this.emit('firstScale', this.cy.zoom());

      this.repos(); // 处理线重合
      this.setListTag();
      this.setPublicStatus();
      this.setOtherTag();
    });
    let dragFlag = false;
    this.cy.on('tap', '.enode,.pnode', (evt) => {
      const node = evt.target;
      if (!node._private.selectable) return;
      this.cy.$('eles').addClass('disable');
      node.removeClass('disable');
      node.successors().removeClass('disable');
      if (node.hasClass('tagnode')) {
        this.cy.$(`#${node._private.data.id}_tag`).removeClass('disable');
      }
      if (node.data('tags')) {
        _.forEach(node.data('tags'), (tag, i) => {
          this.cy.$(`#${node._private.data.id}_listtag${i}`).removeClass('disable');
        });
      }
      if (node.data('publicStatus')) {
        this.cy.$(`#${node._private.data.id}_publictag`).removeClass('disable');
      }
      if (node.data('otherTag')) {
        this.cy.$(`#${node._private.data.id}_othertag`).removeClass('disable');
      }
      node.successors().forEach((successorsNode) => {
        if (successorsNode.data('tags')) {
          _.forEach(successorsNode.data('tags'), (tag, i) => {
            this.cy.$(`#${successorsNode._private.data.id}_listtag${i}`).removeClass('disable');
          });
        }
        if (successorsNode.data('publicStatus')) {
          this.cy.$(`#${successorsNode._private.data.id}_publictag`).removeClass('disable');
        }
        if (successorsNode.data('otherTag')) {
          this.cy.$(`#${successorsNode._private.data.id}_othertag`).removeClass('disable');
        }
      });
      // this.emit('nodeClick', node._private.data)
    });
    this.cy.on('mouseover', '.enode,.pnode', (evt) => {
      const node = evt.target;
      const position = node.renderedBoundingBox();
      const org = node._private.data.org;
      if (!(org === 860)) {
        this.emit('onDataNodeMouseover', {
          data: node._private.data,
          position: {
            left: position.x1,
            right: position.x2,
            top: position.y1,
            bottom: position.y2,
          },
          size: {
            width: position.w,
            height: position.h,
          },
        });
      }
      evt.target.addClass('hover');
    });
    this.cy.on('mouseout', '.enode,.pnode', (evt) => {
      const node = evt.target;
      this.emit('onDataNodeMouseout', { data: node._private.data });
      evt.target.removeClass('hover');
    });
    this.cy.on('click', (evt) => {
      if (evt.target === this.cy) {
        this.cy.$('eles').removeClass('disable');
        this.emit('blankClick');
      }
    });
    this.cy.on('drag', '.tagnode', (evt) => {
      const node = evt.target;
      const tagProp = utils.getTagProp(node._private.data, node._private.data.rsTags);
      this.cy.$(`#${node._private.data.id}_tag`).position({
        x: node._private.position.x,
        y: node._private.position.y - tagProp.yposSpan,
      });
    });
    this.cy.on('tapstart', '.enode,.pnode', (evt) => {
      dragFlag = true;
    });
    this.cy.on('tapdrag', '.enode,.pnode', (evt) => {
      if (dragFlag) {
        const node = evt.target;
        this.emit('onDataNodeMouseout', { data: node._private.data });
        this.cy.$('eles').addClass('disable');
        node.removeClass('disable');
        node.successors().removeClass('disable');
        if (node.hasClass('tagnode')) {
          this.cy.$(`#${node._private.data.id}_tag`).removeClass('disable');
        }
        if (node.data('tags')) {
          _.forEach(node.data('tags'), (tag, i) => {
            this.cy.$(`#${node._private.data.id}_listtag${i}`).removeClass('disable');
          });
        }
        if (node.data('publicStatus')) {
          this.cy.$(`#${node._private.data.id}_publictag`).removeClass('disable');
        }
        if (node.data('otherTag')) {
          this.cy.$(`#${node._private.data.id}_othertag`).removeClass('disable');
        }
        node.successors().forEach((successorsNode) => {
          if (successorsNode.data('tags')) {
            _.forEach(successorsNode.data('tags'), (tag, i) => {
              this.cy.$(`#${successorsNode._private.data.id}_listtag${i}`).removeClass('disable');
            });
          }
          if (successorsNode.data('publicStatus')) {
            this.cy.$(`#${successorsNode._private.data.id}_publictag`).removeClass('disable');
          }
          if (successorsNode.data('otherTag')) {
            this.cy.$(`#${successorsNode._private.data.id}_othertag`).removeClass('disable');
          }
        });
      }
    });
    this.cy.on('tapend', '.enode,.pnode', (evt) => {
      dragFlag = false;
      this.cy.$('eles').removeClass('disable');
    });
    this.cy.on('zoom', (evt) => {
      this.emit('onZoom', this.cy.zoom());
    });
  }

  // 计算节点位置
  calcPos() {
    const width = this.$selector.width();
    const height = this.$selector.height();
    this.size = {
      width,
      height,
    };
    let maxLevel = 0;
    let xsortCount = 0;
    this.data.nodes.forEach((dataNode) => {
      if (dataNode.level > maxLevel) {
        maxLevel = dataNode.level;
      }
      if (dataNode.xsort > xsortCount) {
        xsortCount = dataNode.xsort;
      }
    });
    let xGap = chartSettings.xGap;
    const yGap = chartSettings.yGap;
    const heightObj = {};
    let prevHeight = height / 2 - chartSettings.bottom;
    for (let i = 0; i <= maxLevel; i++) {
      const filterNodes = _.filter(this.data.nodes, (n) => {
        return n.level === i && n.publicHeight;
      });
      heightObj[i] = prevHeight;
      prevHeight -= (filterNodes.length > 0 ? 15 : 0) + chartSettings.enodeHeight + yGap;
    }
    if (xsortCount < 8) {
      xGap = 110;
    }
    // if (maxLevel > 4) {
    //   yGap = 100
    // } else if (maxLevel <= 4 && maxLevel >= 2) {
    //   yGap = 110
    // } else if (maxLevel < 2) {
    //   yGap = 250
    // }
    const xCenter = 0;
    const xStart = xCenter - ((xsortCount + (xsortCount % 2)) / 2) * xGap;
    // let yStart = height / 2 - chartSettings.bottom
    this.data.nodes.forEach((dataNode) => {
      if (dataNode.xsort) {
        dataNode.x = xStart + dataNode.xsort * xGap;
      } else {
        dataNode.x = xCenter;
      }
      dataNode.y = heightObj[dataNode.level];
      // dataNode.y = yStart - dataNode.level * yGap
    });
  }

  // 转换节点数据
  transData() {
    this.data.nodes.forEach((dataNode) => {
      const classes = [];
      if (dataNode.level === 0) {
        classes.push('rootnode');
      } else if (utils.isPerson(dataNode)) {
        classes.push('pnode');
      } else {
        classes.push('enode');
      }
      const newNode = {
        data: {
          ...dataNode,
          id: dataNode.uuid,
        },
        position: { x: dataNode.x, y: dataNode.y },
        classes,
      };

      if (dataNode.rsTags || dataNode.level === 0) {
        // newNode.grabbable = false
      }
      if (dataNode.level === 0) {
        newNode.selectable = false;
      }
      if (dataNode.rsTags) {
        newNode.classes.push('tagnode');
        const tagProp = utils.getTagProp(dataNode, dataNode.rsTags);
        let offsetY = dataNode.y - tagProp.yposSpan;
        const name = dataNode.name;
        if (name.length > 12 && this.specialOrg.includes(dataNode.org)) {
          offsetY -= 10;
        }
        const tagNode = {
          grabbable: false,
          selectable: false,
          data: {
            id: `${dataNode.uuid}_tag`,
            name: dataNode.rsTags.toString(),
            height: tagProp.nodeHeight,
            width: tagProp.nodeWidth,
            color: tagProp.color,
          },
          position: { x: dataNode.x, y: offsetY },
          classes: ['tag'],
        };
        this.elements.nodes.push(tagNode);
      }
      if (dataNode.kzr) {
        newNode.classes.push('kzr');
      }
      this.elements.nodes.push(newNode);
    });
    this.data.links.forEach((link) => {
      const edge = {
        data: link,
      };
      if (link.syr) {
        edge.classes = ['syredge'];
      }
      this.elements.edges.push(edge);
    });
  }

  zoomInOrOut(zoomIn) {
    let scale = this.cy.zoom();
    if (zoomIn) {
      scale += chartSettings.scaleRate;
    } else {
      scale -= chartSettings.scaleRate;
    }
    this.cy.zoom({
      level: scale, // the zoom level
    });
  }

  saveImg(companyName, title) {
    const imgdata = this.cy.png({ full: true, bg: 'rgba(255, 255, 255, 0)', scale: 1.5 });
    globalUtils.downloadImage(imgdata, companyName, title);
  }

  sizeChange() {
    const newWidth = this.$selector.width();
    const newHeight = this.$selector.height();
    if (!this.cy) return;
    if (this.size.width === newWidth && this.size.height === newHeight) return;
    this.size = {
      width: newWidth,
      height: newHeight,
    };
    this.cy.center();
  }

  // 左侧弹出详情时挡住图谱的话图谱右移
  moveByCard(flag) {
    if (flag) {
      const offX = 350 - this.cy.nodes().renderedBoundingBox().x1;
      if (offX > 0) {
        this.cardOffX = offX;
        this.cy.animate(
          {
            panBy: { x: this.cardOffX, y: 0 },
          },
          {
            duration: 200,
          }
        );
        // this.cy.panBy({ x: this.cardOffX, y: 0 })
      }
    } else if (this.cardOffX) {
      this.cy.animate(
        {
          panBy: { x: -this.cardOffX, y: 0 },
        },
        {
          duration: 200,
        }
      );
      // this.cy.panBy({ x: -this.cardOffX, y: 0 })
      this.cardOffX = 0;
    }
  }

  setListTag() {
    this.cy.$('.enode').forEach((node) => {
      const listTag = node.data('listTag');
      const publicStatus = node.data('publicStatus');
      if (!_.isEmpty(listTag)) {
        let prevWidth = 0;
        const gap = 5;
        _.forEach(listTag, (tag, i) => {
          const width = tag.name.length * 12 + 10;
          const offSetX = chartSettings.enodeWidth / 2 - width / 2 - (prevWidth + gap);
          const offSetY = -chartSettings.enodeHeight / 2 - (publicStatus ? chartSettings.publicHeight / 2 : 0);
          const listTagNode = this.cy.add({
            classes: ['list-tag'],
            pannable: true,
            position: { x: node.position().x + offSetX, y: node.position().y + offSetY },
            data: {
              id: `${node.data('id')}_listtag${i}`,
              name: tag.name,
              width,
              offSetX,
              offSetY,
              color: tag.color,
              // parent: node.data('id'),
              background: tag.bg,
            },
          });
          this.cy.automove({
            nodesMatching: listTagNode,
            reposition: 'drag',
            dragWith: node,
          });
          prevWidth += width + gap;
        });
      }
    });
  }

  setPublicStatus() {
    this.cy.$('.enode').forEach((node) => {
      const publicTag = node.data('publicStatus');
      if (publicTag) {
        const offSetY = chartSettings.enodeHeight / 2 - 1;
        const newTag = this.cy.add({
          classes: ['public-status'],
          pannable: true,
          position: { x: node.position().x, y: node.position().y + offSetY },
          data: {
            id: `${node.data('id')}_publictag`,
            name: publicTag,
            color: '#128BED',
            background: '#E9F3FF',
          },
        });
        this.cy.automove({
          nodesMatching: newTag,
          reposition: 'drag',
          dragWith: node,
        });
      }
    });
  }

  // 设置集团 投资机构 企业族群tag
  setOtherTag() {
    this.cy.$('.enode').forEach((node) => {
      const otherTag = node.data('otherTag');
      if (otherTag) {
        let offSetY = chartSettings.listTagHeight / 2 + 5;
        const name = node.data('name');
        if (name.length > 12) {
          offSetY += 10;
        }
        const newTag = this.cy.add({
          classes: ['other-tag'],
          pannable: true,
          position: { x: node.position().x, y: node.position().y + offSetY },
          data: {
            id: `${node.data('id')}_othertag`,
            name: otherTag,
            width: otherTag.length * 12 + 10,
            color: '#128BED',
            background: '#E9F3FF',
          },
        });
        this.cy.automove({
          nodesMatching: newTag,
          reposition: 'drag',
          dragWith: node,
        });
      }
    });
  }

  // 处理线重合
  repos() {
    this.cy.$('node').forEach((node) => {
      const lineNodes = node.neighborhood('node').filter((n) => {
        // 一条线上的点
        return n.position().x === node.position().x;
      });
      const upNodes = lineNodes.filter((n) => {
        // 上面的点
        return n.position().y < node.position().y;
      });
      const downNodes = lineNodes.filter((n) => {
        // 下面的点
        return n.position().y > node.position().y;
      });
      if (upNodes.length < 1 || downNodes.length < 1) return;
      let isRe = false;
      for (let i = 0; i < upNodes.length; i++) {
        for (let j = 0; j < downNodes.length; j++) {
          if (upNodes[i].edgesTo(downNodes[j]).length > 0) {
            // 上面的点如果有直接连到面的点，那么线重合
            isRe = true;
            break;
          }
        }
      }
      if (isRe) {
        node.position('x', node.position().x + 25);
      }
    });
  }

  // iframe 时使用
  delayZoom() {
    this.cy.zoomingEnabled(false);
    setTimeout(() => {
      this.cy.zoomingEnabled(true);
    }, 2000);
  }

  cleanup() {
    if (this.cy) {
      this.cy.destroy();
    }
  }
}
