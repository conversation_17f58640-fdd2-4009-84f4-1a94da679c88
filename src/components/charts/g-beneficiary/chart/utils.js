/* eslint-disable no-bitwise */
import moment from 'moment';
import _ from 'lodash';

import chartSettings from '../settings';

const getTagProp = (data, rsTags) => {
  let color;
  let yposSpan;
  let nodeHeight;
  if (rsTags.length > 1) {
    color = '#FD485D';
  } else {
    color = '#128bed';
  }
  // if (rsTags.indexOf('实际控制人') !== -1) {
  //   color = '#FD485D'
  // } else if (rsTags.indexOf('最终受益人') !== -1) {
  //   color = '#F9AD14'
  // } else {
  //   color = '#128bed'
  // }
  if (rsTags.length === 3) {
    yposSpan = 61;
    nodeHeight = 60;
  } else if (rsTags.length === 2) {
    yposSpan = 74;
    nodeHeight = 56;
  } else {
    yposSpan = 62;
    nodeHeight = 36;
  }
  let longerText = '';
  let tagWidth = 0;
  _.forEach(rsTags, (tag) => {
    longerText = tag.length > longerText.length ? tag : longerText;
  });
  const reg = /^[\u4e00-\u9fa5]+$/g;
  if (reg.test(longerText.trim())) {
    tagWidth = longerText.length * 12 + 20 > chartSettings.tagWidth ? longerText.length * 12 + 20 : chartSettings.tagWidth;
  } else {
    tagWidth = longerText.length * 12;
  }
  return {
    color,
    yposSpan,
    nodeHeight,
    nodeWidth: tagWidth,
  };
};

const isCompany = (data) => {
  const org = data.org || data.org === 0 ? data.org : data.Org;
  if (org === 2) {
    return false;
  }
  if (data.keyNo && data.keyNo[0] === 'p') {
    return false;
  }
  if ((!org || org === -1) && data.name && data.name.length < 5) {
    return false;
  }
  return true;
};

const isPerson = (data) => {
  const org = data.org || data.org === 0 ? data.org : data.Org;
  if (Math.abs(org) === 2) {
    return true;
  }
  if (data.keyNo[0] === 'p') {
    return true;
  }
  return false;
};

const nodeExist = (nodes, node) => {
  let exist = false;
  nodes.forEach((vo) => {
    if (vo.uuid === node.uuid) {
      exist = true;
    }
  });
  return exist;
};

const linkExist = (links, link) => {
  let exist = false;
  links.forEach((vo) => {
    if (vo.source === link.source && vo.target === link.target) {
      exist = true;
    }
  });
  return exist;
};

const generateUUID = (data) => {
  const UUID = function () {
    let d = new Date().getTime();
    if (window.performance && typeof window.performance.now === 'function') {
      d += performance.now(); // use high-precision timer if available
    }
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (d + Math.random() * 16) % 16 | 0;
      d = Math.floor(d / 16);
      return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
    });
    return uuid;
  };
  const cacheUUID = {};
  data.Paths.forEach((vo, index) => {
    for (let i = 0; i < vo.Paths.length; i++) {
      for (let j = vo.Paths[i].length - 1; j >= 0; j--) {
        const v = vo.Paths[i][j];
        const key = v.KeyNo || v.Name;
        if (!cacheUUID[key]) {
          cacheUUID[key] = UUID();
        }
        v.uuid = cacheUUID[key];
      }
    }
  });
};

const nullDataPaths = (companyKeyNo, companyName) => {
  const nodes = [
    {
      name: companyName,
      keyNo: companyKeyNo,
      uuid: companyKeyNo,
      org: 1,
      level: 0,
    },
    {
      name: '?',
      keyNo: '',
      uuid: '0',
      level: 1,
      org: 2,
      xsort: 0,
    },
  ];
  const links = [
    {
      source: '0',
      target: companyKeyNo,
      name: ['?%'],
    },
  ];
  return {
    nodes,
    links,
  };
};

const jump = (data) => {
  if (data.keyNo) {
    window.open(`/firm_${data.keyNo}`);
  }
};

const isChinese = (str) => {
  const reg = new RegExp('[\u4E00-\u9FA5]+');
  return reg.test(str);
};

const wrapText = (str) => {
  if (isChinese(str)) {
    if (str.length > 24) {
      str = `${str.substr(0, 23)}…`;
    }
    return str.replace(/(.{12})(?=.)/g, '$1\n');
  }
  if (str.length > 40) {
    str = `${str.substr(0, 39)}…`;
  }
  return str.replace(/(.{20})(?=.)/g, '$1\n');
};

export default {
  getTagProp,
  isCompany,
  isPerson,
  nodeExist,
  linkExist,
  generateUUID,
  isChinese,
  wrapText,
  nullDataPaths,
  jump,
};
