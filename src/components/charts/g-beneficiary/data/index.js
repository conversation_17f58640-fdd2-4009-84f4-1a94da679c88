import { graph } from '@/shared/services';

import formatter from './formatter';

const loadBeneficiaryData = (eid, type) => {
  return new Promise((resolve, reject) => {
    graph
      .getBeneficiaryData({
        keyNo: eid,
        type,
        isVIP: true,
      })
      .then((data) => {
        const detail = formatter.formatBeneficiaryData(data.Result);
        resolve(detail);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export default {
  loadBeneficiaryData,
};
