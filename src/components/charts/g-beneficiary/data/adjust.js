export default class adjust {
  constructor(nodes, links, maxlevel, xsortCount) {
    this.nodes = nodes;
    this.links = links;
    this.maxlevel = maxlevel;
    this.xsortCount = xsortCount;
  }

  adjust = () => {
    let matrix = [];
    this.nodes.forEach((node) => {
      if (node.syr) {
        node.level = this.maxlevel;
      }
      if (!matrix[node.level]) {
        matrix[node.level] = [];
      }
      matrix[node.level].push(node);
    });

    // level 整个一层都没节点，后面层往前移动
    let levelEmpty = 0;
    for (let i = 0; i < matrix.length; i++) {
      if (!matrix[i] || matrix[i].length === 0) {
        levelEmpty++;
      } else if (levelEmpty) {
        for (let j = 0; j < matrix[i].length; j++) {
          matrix[i][j].level -= levelEmpty;
        }
      }
    }

    // 去除空元素
    matrix = matrix.filter((d) => d);

    // 路径只有1条的时候居中
    if (this.xsortCount < 5) {
      for (let i = 0; i < matrix.length; i++) {
        if (matrix[i].length === 1) {
          matrix[i][0].xsort = 0;
        }
      }
    }

    // xsort 调整
    for (let i = matrix.length - 1; i > 0; i--) {
      for (let j = 0; j < matrix[i].length; j++) {
        const v = matrix[i][j];
        const upLevelNodes = this.upLevelNodes(v);
        const avgSort = this.getAvgSort(upLevelNodes);
        if (avgSort > 0) {
          v.xsort = avgSort;
        }
      }
      matrix[i].sort((a, b) => {
        return a.xsort - b.xsort;
      });
    }

    // 避免节点重复
    for (let i = 1; i < matrix.length; i++) {
      for (let j = 1; j < matrix[i].length; j++) {
        const v = matrix[i][j];
        const prev = matrix[i][j - 1];
        // 与上个节点xsort相差1或0就重合
        if (v.xsort - prev.xsort <= 1) {
          // 上上节点的xsort
          let pre2vSort = 0;
          if (j >= 2) {
            pre2vSort = matrix[i][j - 2].xsort;
          }
          if (prev.xsort > 1 && (!pre2vSort || prev.xsort - pre2vSort > 2)) {
            v.xsort = prev.xsort + 1;
            prev.xsort--;
            if (this.isNodeLinked(v, prev)) {
              v.xsort++;
            }
          } else {
            v.xsort = prev.xsort + 2;
          }
        }
      }
    }

    // 顶层调整xsort
    const topNodes = matrix[matrix.length - 1];
    // for (let i = Math.floor(topNodes.length / 2); i < topNodes.length; i++) {
    for (let i = 0; i < topNodes.length; i++) {
      const v = topNodes[i];
      const downLevelNodes = this.downLevelNodes(v);
      const avgSort = this.getAvgSort(downLevelNodes);
      const offSort = avgSort - v.xsort;
      if (avgSort > 0 && offSort > 0 && (offSort <= 2 || i === topNodes.length - 1)) {
        v.xsort = avgSort;
        this.rightMoveSort(topNodes, i, offSort);
      }
    }
  };

  // 获取上层链接的节点
  upLevelNodes = (node) => {
    const ns = [];
    this.links.forEach((link) => {
      if (link.target === node.uuid) {
        const targetNode = this.nodes.find((n) => {
          return n.uuid === link.source;
        });
        if (targetNode.level > node.level) {
          ns.push(targetNode);
        }
      }
    });
    return ns;
  };

  // 获取下层链接的节点
  downLevelNodes = (node) => {
    const ns = [];
    this.links.forEach((link) => {
      if (link.source === node.uuid) {
        const sourceNode = this.nodes.find((n) => {
          return n.uuid === link.target;
        });
        if (sourceNode.level && sourceNode.level < node.level) {
          ns.push(sourceNode);
        }
      }
    });
    return ns;
  };

  getAvgSort = (nodes) => {
    if (nodes.length < 1) {
      return 0;
    }
    let xsortTotal = 0;
    nodes.forEach((node) => {
      xsortTotal += node.xsort;
    });
    return Math.floor(xsortTotal / nodes.length);
  };

  rightMoveSort = (nodes, benginIndex, offSort) => {
    for (let i = 0; i < nodes.length; i++) {
      if (i > benginIndex) {
        nodes[i].xsort += offSort;
      }
    }
  };

  isNodeLinked = (node1, node2) => {
    let isLinked = false;
    this.links.forEach((link) => {
      if ((link.source === node1.uuid && link.target === node2.uuid) || (link.target === node1.uuid && link.source === node2.uuid)) {
        isLinked = true;
      }
    });
    return isLinked;
  };
}
