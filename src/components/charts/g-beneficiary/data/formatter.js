import _ from 'lodash';

import * as companyUtil from '@/utils/firm';

import utils from '../chart/utils';
import Adjust from './adjust';
import settings from '../settings';
// import logoHelper from '../../../../../utils/logo-helper';

const formatGraph = () => {
  const nodeData = {};
  return nodeData;
};
const formatBeneficiaryData = (data) => {
  // 每个节点增加id
  utils.generateUUID(data);
  const nodes = [];
  const links = [];
  let xsortCount = 1;
  let maxlevel = 1;
  const rootNode = {
    uuid: data.KeyNo,
    keyNo: data.KeyNo,
    name: data.Name,
    org: 1,
    level: 0,
  };
  nodes.push(rootNode);
  // 对path进行排序
  const tempPath = _.sortBy(data.Paths, (path) => {
    let percent = path.PercentTotal || '';
    _.forEach(path.PathType, (pt) => {
      if (pt === 'A') {
        percent = '100';
      }
    });
    return +percent.split('%')[0];
  });
  data.Paths = tempPath.reverse();
  data.Paths.forEach((vo, index) => {
    if (vo.Paths && vo.Paths.length > 0) {
      const node = {
        keyNo: vo.KeyNo,
        name: vo.Name,
        org: vo.Org,
        image: vo.ImageUrl,
        dataType: vo.DataType,
        syr: true,
        tags: vo.Tags,
        xsort: xsortCount,
      };
      if (vo.HasImage && vo.KeyNo && vo.KeyNo[0] === 'p' && !node.image) {
        node.image = companyUtil.getLogoByKeyNo(vo.KeyNo, vo.HasImage);
      }
      if (vo.PathType && vo.PathType.length > 0) {
        const rsTags = [];
        let isKzr = false;
        // 自然人直接持股部分
        if (+vo.Org === 860) {
          rsTags.push(`总持股比例：${vo.PercentTotal}`);
        } else {
          vo.PathType.forEach((pt) => {
            if (pt === 'A') {
              rsTags.push('实际控制人');
              isKzr = true;
            } else if (pt === 'B') {
              if (vo.PathType.includes('U')) {
                let syrText = '';
                if (vo.PercentTotal) {
                  const percent = vo.PercentTotal.split('%') && vo.PercentTotal.split('%')[0];
                  syrText = +percent > 0 ? `最终受益人：${vo.PercentTotal}` : '最终受益人';
                } else {
                  syrText = '最终受益人';
                }
                const text = syrText;
                rsTags.push(text);
              } else {
                rsTags.push(`最终受益股份：${vo.PercentTotal}`);
              }
            }
            //  else if (pt === 'U') {
            //   rsTags.push('最终受益人')
            // }
          });
        }
        node.kzr = isKzr;
        node.rsTags = rsTags;
      }
      nodes.push(node);
      for (let i = 0; i < vo.Paths.length; i++) {
        if (!data.hasReverse) {
          vo.Paths[i].reverse();
        }
        if (i < settings.pathLimit) {
          for (let j = 0; j < vo.Paths[i].length; j++) {
            const v = vo.Paths[i][j];
            const level = parseInt(v.Level, 10);
            if (level > maxlevel) {
              maxlevel = level;
            }
            if (j === 0) {
              // 头节点 等于 node 节点
              node.level = level;
              node.uuid = v.uuid;
            }
            if (!utils.nodeExist(nodes, v)) {
              nodes.push({
                uuid: v.uuid,
                keyNo: v.KeyNo,
                name: v.Name,
                org: v.Org,
                level,
                tags: v.Tags,
                xsort: xsortCount,
              });
            }
            // 如果最后一个节点就连接rootNode,否则连接下一个点
            const targetNode = j === vo.Paths[i].length - 1 ? rootNode : vo.Paths[i][j + 1];

            const link = {
              source: v.uuid,
              target: targetNode.uuid,
              sourceOrg: v.Org,
              name: v.Percent,
            };
            if (+v.Org === 860) {
              link.name = v.PercentTotal;
            }
            if (v.DataType === 5) {
              link.name = `执行事务合伙人 ${v.Percent}`;
              link.zIndex = 10;
            } else if (node.dataType === 6) {
              link.name = '法定代表人';
              link.zIndex = 10;
            }
            if (!utils.linkExist(links, link)) {
              // if (link.source === node.uuid && link.target === rootNode.uuid) {
              //   link.syr = true
              // }
              // 在排序排好的情况下，第一条就是控制路径
              if (index === 0 && i === 0) {
                link.syr = true;
              }
              links.push(link);
            }
          }
        }
      }
      xsortCount += 2;
    }
  });
  const listTags = [1, 2, 7, 401, 501, 301];
  nodes.forEach((node) => {
    const listTag = [];
    let publicStatus = '';
    if (node.tags?.length > 0) {
      node.tags.forEach((vo) => {
        node.listTagType = vo.Type;
        // 上市或融资进度标签
        if (vo.Type === 3) {
          publicStatus = `融资轮次：${vo.Name}`;
        } else if (listTags.includes(vo.Type)) {
          publicStatus = `${vo.Name}: ${vo.ShortName} ${vo.DataExtend}`;
          if (!_.find(listTag, { name: '上市' })) {
            listTag.push({
              name: '上市',
              color: '#EC9662',
              bg: '#FFF4ED',
            });
          }
        } else if (vo.Type === 402) {
          listTag.push({
            name: vo.Name,
            color: '#6F77D1',
            bg: '#EAEBF8',
          });
        }
      });
    }
    node.publicStatus = publicStatus;
    node.listTag = listTag;
  });

  const specialTag = [13, 20, 21];
  nodes.forEach((node) => {
    let otherTag = '';
    if (specialTag.includes(node.org)) {
      if (+node.org === 13) {
        otherTag = '投资机构';
      } else if (+node.org === 20) {
        otherTag = '集团';
      } else if (+node.org === 21) {
        otherTag = '企业族群';
      }
    }
    node.otherTag = otherTag;
  });

  new Adjust(nodes, links, maxlevel, xsortCount).adjust();
  data.hasReverse = true;
  return {
    name: data.Name,
    keyNo: data.KeyNo,
    nodes,
    links,
    paths: data.Paths,
  };
};

export default {
  formatGraph,
  formatBeneficiaryData,
};
