.charts-beneficiary {
  position: absolute;
  inset: 0;
  overflow: hidden;
  background: url("./images/shuiying6.png") repeat;
  background-size: 360px 280px;
  line-height: 1.2;

  .foo {
    color: red
  }

  .switch-wrap {
    z-index: 999;
    position: fixed;
    bottom: 45px;
    left: 45px;
    display: inline-block;
    padding: 10px;
    background: rgba(255, 255, 255, 0.9);

    .switch-btn {
      width: 104px;
      padding: 8px 0;
      background: rgba(255, 255, 255, 1);
      border-radius: 2px;
      border: 1px solid rgba(214, 214, 214, 1);
      font-size: 12px;
      color: rgba(102, 102, 102, 1);
      text-align: center;
      margin-bottom: 10px;
      cursor: pointer;

      &:last-child {
        margin-bottom: 0;
      }

      &.active {
        background: rgba(18, 139, 237, 1);
        border-color: rgba(18, 139, 237, 1);
        color: #fff;
      }

      &.disable {
        background: #f6f6f6;
        color: #999;
      }
    }
  }
}

.app-beneficiary.top-n {
  top: 0;
}

.toolbox {
  position: fixed;
  width: 46px;
  right: 20px;
  bottom: 50px;
  font-size: 18px;
  z-index: 20;
}

#beneficiary-person, #beneficiary-org {
  position: absolute;
  inset: 0;
  overflow: hidden;
  z-index: 1;
}
