<template>
  <div :class="['app-beneficiary charts-beneficiary', containerName]" id="company-chart-container">
    <div :id="containerName"></div>
    <!-- 操作栏 -->
    <div class="toolbox" v-show="isInit">
      <g-ui-toolbox>
        <g-ui-toolbox-action :action-type="actionTypes.zoomIn" @click="onZoomIn"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.zoomOut" @click="onZoomOut"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.refresh" @click="onRefresh"></g-ui-toolbox-action>
        <g-ui-toolbox-action v-show="!isFullScreen" :action-type="actionTypes.fullScreen" @click="onFullScreen"></g-ui-toolbox-action>
        <g-ui-toolbox-action
          v-show="isFullScreen"
          :action-type="actionTypes.exitFullScreen"
          @click="onExitFullScreen"
        ></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.save" :is-busy="isSaving" @click="onSave"></g-ui-toolbox-action>
      </g-ui-toolbox>
    </div>
    <!-- 免责 -->
    <g-ui-footer></g-ui-footer>
    <!-- 加载 -->
    <g-ui-loading v-if="isLoading"></g-ui-loading>
    <!-- 暂无数据 -->
    <g-ui-no-data v-if="noData"></g-ui-no-data>
  </div>
</template>

<script src="./component.js"></script>

<style lang="less" scoped src="./style.less"></style>
