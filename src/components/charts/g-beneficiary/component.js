/* eslint-disable func-names */
/* eslint-disable no-param-reassign */
import resizeDetector from 'element-resize-detector';
import _ from 'lodash';

import dataLoader from './data';
import { actionTypes, gUiToolbox, gUiToolboxAction } from '../g-ui-toolbox';
import gUiFooter from '../g-ui-footer';
import gUiLoading from '../g-ui-loading';
import gUiNoData from '../g-ui-no-data';
import appPerson from '../g-app-person';
import appCompany from '../g-app-company';
import Chart from './chart/index';
import popoverHelper from '../utils/popoverHelper';
import chartSettings from './settings';

export default {
  name: 'g-beneficiary',
  components: {
    [gUiToolbox.name]: gUiToolbox,
    [gUiToolboxAction.name]: gUiToolboxAction,
    [gUiFooter.name]: gUiFooter,
    [gUiLoading.name]: gUiLoading,
    [gUiNoData.name]: gUiNoData,
  },
  props: {
    actionTypes: {
      type: Object,
      default: () => actionTypes,
    },
    containerName: {
      type: String,
      default: 'beneficiary-person',
    },
    keyNo: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
    isPerson: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isInit: false,
      isLoading: true,
      noData: false,
      scale: 1,
      paths: [],
      // 操作栏
      isFullScreen: false,
      isSaving: false,
    };
  },
  mounted() {
    this.cleanup();
    this.$nextTick(() => {
      this.attachResizeDetector();
      this.onRefresh();
      this.isFullScreen =
        document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement || document.msFullscreenElement;
      this.$nextTick(() => {
        document.addEventListener('fullscreenchange', () => {
          this.isFullScreen = !!document.fullscreenElement;
        });
        document.addEventListener('webkitfullscreenchange', () => {
          this.isFullScreen = !!document.webkitFullscreenElement;
        });
        document.addEventListener('mozfullscreenchange', () => {
          this.isFullScreen = !!document.mozFullScreenElement;
        });
        document.addEventListener('MSFullscreenChange', () => {
          this.isFullScreen = !!document.msFullscreenElement;
        });
      });
    });
  },
  methods: {
    cleanup() {
      this.isInit = false;
      if (this.chart) {
        this.chart.cleanup();
      }
    },
    onRefresh() {
      this.cleanup();
      this.isLoading = true;
      this.chart = new Chart(`#${this.containerName}`);
      dataLoader
        .loadBeneficiaryData(this.keyNo, this.isPerson ? 1 : 2)
        .then((data) => {
          this.noData = false;
          this.isInit = true;
          this.isLoading = false;
          this.paths = data.paths;
          this.chart.init(data);
        })
        .catch(() => {
          this.isLoading = false;
          this.noData = true;
        });
      this.chart.on('onDataNodeMouseover', ({ data, position, size }) => {
        data.delayTimer = setTimeout(() => {
          const pathData = this.paths.find((path) => {
            return path.KeyNo === data.keyNo;
          });
          popoverHelper.showPopover({
            component: data.keyNo && data.keyNo[0] === 'p' ? appPerson : appCompany,
            data: {
              hasKeyNo: true,
              id: data.keyNo,
              keyNo: data.keyNo,
              name: data.name,
              eid: this.keyNo,
              ename: this.name,
              rsTags: data.rsTags,
              pathData,
              org: data.org,
              type: 2,
            },
            container: $(`.${this.containerName}`),
            targetPosition: position,
            targetSize: { width: size.width * this.scale, height: size.height * this.scale },
            identity: `detail-popover-${data.id}`,
            containerSize: {
              width: $(`.${this.containerName}`).width(),
              height: $(`.${this.containerName}`).height(),
            },
          });
        }, chartSettings.fadeDuration);
      });
      this.chart.on('onDataNodeMouseout', ({ data }) => {
        if (data.delayTimer) {
          clearTimeout(data.delayTimer);
          delete data.delayTimer;
        }
        $(`#detail-popover-${data.id}`).fadeOut(chartSettings.fadeDuration, function () {
          $(this).remove();
        });
      });
      this.chart.on('onZoom', (scale) => {
        this.scale = scale;
      });
    },
    onZoomIn() {
      this.chart.zoomInOrOut(true);
    },
    onZoomOut() {
      this.chart.zoomInOrOut(false);
    },
    onFullScreen() {
      const element = $('.charts-beneficiary')[0];
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
      this.isFullScreen = true;
    },
    onExitFullScreen() {
      if (this.isFullScreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
        this.isFullScreen = false;
      }
    },
    onSave() {
      this.chart.saveImg(this.name, this.isPerson ? '企业受益股东' : '企业股权分布');
    },
    attachResizeDetector() {
      this.resizeDetector = resizeDetector();
      this.resizeDetector.listenTo(
        $('.charts-beneficiary')[0],
        _.debounce(() => {
          this.chart.sizeChange();
        }, 200)
      );
    },
  },
};
