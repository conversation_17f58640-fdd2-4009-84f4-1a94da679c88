/* eslint-disable no-unused-expressions */
/* eslint-disable func-names */
/* eslint-disable no-param-reassign */
import resizeDetector from 'element-resize-detector';
import _ from 'lodash';

import dataLoader from './data';
import { actionTypes, gUiToolbox, gUiToolboxAction } from '../g-ui-toolbox';
import gUiFooter from '../g-ui-footer';
import gUiLoading from '../g-ui-loading';
import gUiNoData from '../g-ui-no-data';
import appPerson from '../g-app-person';
import appCompany from '../g-app-company';
import appFilter from '../g-app-filter';
import appSearch from '../g-ui-search';
import Chart from './chart/index';
import popoverHelper from '../utils/popoverHelper';

const CONTAINER_SELECTOR = 'group-staff';
const CONTAINER_PATH = 'group-staff-path';

export default {
  name: 'g-group-staff',
  components: {
    [gUiToolbox.name]: gUiToolbox,
    [gUiToolboxAction.name]: gUiToolboxAction,
    [gUiFooter.name]: gUiFooter,
    [gUiLoading.name]: gUiLoading,
    [gUiNoData.name]: gUiNoData,
    [appFilter.name]: appFilter,
    [appSearch.name]: appSearch,
  },
  props: {
    actionTypes: {
      type: Object,
      default: () => actionTypes,
    },
    keyNo: {
      type: String,
      defalut: '',
    },
    name: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      scale: 1,
      loaded: false,
      groupId: '',
      companyName: '',
      isInit: false,
      isLoading: true,
      noData: false,
      pathNoData: false,
      // 操作栏
      isFullScreen: false,
      isSaving: false,
      showFilter: false,
      position: '',
      typeList: [
        {
          key: 'position',
          title: '职务类型',
          tips: '',
          list: [
            { key: '', value: '职务不限', selected: true },
            { key: 'LEGAL', value: '法定代表人', selected: false },
            { key: 'EMPLOY', value: '高管', selected: false },
            { key: 'CORESTAFF', value: '核心人员', selected: false },
          ],
        },
      ],
      showSearch: false,
      initialList: [], // 避免多次调search 条件为空时调接口
      searchList: [],
      searchLoading: true,
      searchNoData: false,
      showPath: false,
    };
  },
  mounted() {
    this.groupId = this.keyNo;
    this.companyName = this.name;
    this.loaded = true;
    this.onRefresh();
    this.isFullScreen = document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement;
    this.$nextTick(() => {
      document.addEventListener('fullscreenchange', () => {
        this.isFullScreen = document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement;
      });
      document.addEventListener('webkitfullscreenchange', () => {
        this.isFullScreen = document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement;
      });
      document.addEventListener('mozfullscreenchange', () => {
        this.isFullScreen = document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement;
      });
      document.addEventListener('MSFullscreenChange', () => {
        this.isFullScreen =
          document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement || document.msFullscreenElement;
      });
    });
  },
  methods: {
    clean() {
      this.isInit = false;
      this.isLoading = true;
      this.noData = false;
      this.showSearch = false;
      this.initFilter();
      this.showFilter = false;
      $('.detail-popover').remove();
      $(`#${CONTAINER_SELECTOR}`).empty();
      $(`#${CONTAINER_PATH}`).empty();
    },
    // options
    onRefresh() {
      this.clean();
      this.chart = new Chart(CONTAINER_SELECTOR);
      dataLoader
        .getGroupStaffGraph({
          groupId: this.groupId,
        })
        .then((res) => {
          if (!res.fetchAll && !this.iframe) {
            // this.$toasted.error('图谱节点过多，暂无法显示，您可直接搜索想查找的企业/人员');
          }
          if (res.data && !_.isEmpty(res.data)) {
            setTimeout(() => {
              this.chart.init(res);
              this.chart.domUpdate();
              if (this.iframe) {
                this.chart.zoomInOrOut(0.5);
              }
            });
          } else {
            this.noData = true;
          }
        })
        .finally(() => {
          this.isLoading = false;
          this.isInit = true;
        })
        .catch(() => {
          this.noData = true;
        });
      this.chart.on('clickCy', () => {
        if (this.showSearch || this.showFilter) {
          this.showFilter = false;
          this.showSearch = false;
        } else {
          this.chart.cancelHighLight();
          this.initFilter();
        }
      });
      this.chart.on('nodeHover', ({ data, size, position }) => {
        if (this.iframe) {
          return;
        }
        data.delayTimer = setTimeout(() => {
          const $instance = $(`#detail-popover-${data.id}`);
          if ($instance.length > 0) {
            $instance.stop(true).fadeIn(0);
            return;
          }
          popoverHelper.showPopover({
            component: data.keyNo && data.keyNo[0] === 'p' ? appPerson : appCompany,
            data: {
              hasKeyNo: true,
              id: data.keyNo,
              keyNo: data.keyNo,
              name: data.name,
              eid: this.groupId,
              ename: this.companyName,
              rsTags: data.rsTags,
              org: data.org,
              type: 1,
            },
            notTreeGraph: true,
            container: $(`#${CONTAINER_SELECTOR}`),
            identity: `detail-popover-${data.id}`,
            targetPosition: position,
            targetSize: { width: size.width * this.scale, height: size.height * this.scale },
          });
        }, 400);
      });

      this.chart.on('nodeOut', (data) => {
        if (this.iframe) {
          return;
        }
        if (data && data.delayTimer) {
          clearTimeout(data.delayTimer);
          delete data.delayTimer;
        }

        $(`#detail-popover-${data.id}`).fadeOut(400, function () {
          $(this).remove();
        });
      });

      this.chart.on('onZoom', (zoom) => {
        this.scale = zoom;
      });
    },
    initFilter() {
      this.position = '';
      this.typeList = [
        {
          key: 'position',
          width: '60',
          title: '职务类型',
          tips: '',
          list: [
            { key: '', value: '职务不限', selected: true },
            { key: 'LEGAL', value: '法定代表人', selected: false },
            { key: 'EMPLOY', value: '高管', selected: false },
            { key: 'CORESTAFF', value: '核心人员', selected: false },
          ],
        },
      ];
    },
    onShowFilter() {
      this.showSearch = false;
      this.showFilter = !this.showFilter;
    },
    handlerFilter(list) {
      let key = '';
      let value = '';
      list.forEach((li) => {
        key = li.key;
        li.list.forEach((val) => {
          if (val.selected) {
            value = val.key;
          }
        });
      });
      this[key] = value;
      this.chart.handlePositionFilter(value);
    },
    onSearch() {
      this.showFilter = false;
      this.showSearch = !this.showSearch;
      if (this.showSearch) {
        // 调搜索接口
        this.handleSearch();
      }
    },
    handleSearch(searchKey = '') {
      this.searchNoData = false;
      // if (!searchKey && this.initialList.length) {
      //   this.searchList = this.initialList
      //   this.searchLoading = false
      //   return
      // }
      this.searchLoading = true;
      dataLoader
        .searchStaffGraph({
          groupId: this.groupId,
          searchKey,
        })
        .then((res) => {
          if (!searchKey && !this.initialList.length) {
            this.initialList = res;
          }
          this.searchList = res;
          if (!this.searchList.length) {
            this.searchNoData = true;
          }
        })
        .finally(() => {
          this.searchLoading = false;
        })
        .catch(() => {
          this.searchNoData = false;
        });
    },
    handlePath(item) {
      this.showSearch = false;
      this.showFilter = false;
      if (!item.keyNo) {
        this.$toasted.error('无效');
        return;
      }
      $('.detail-popover').remove();
      $(`#${CONTAINER_PATH}`).empty();
      this.showPath = true;
      this.isLoading = true;
      this.pathChart = new Chart(CONTAINER_PATH);
      dataLoader
        .getSearchResultGraph({
          groupId: this.groupId,
          memberId: item.keyNo,
        })
        .then((res) => {
          if (res.data && !_.isEmpty(res.data)) {
            this.pathChart.init(res);
            this.pathChart.domUpdate();
          } else {
            this.noData = true;
          }
        })
        .finally(() => {
          this.isLoading = false;
        });
      this.pathChart.on('clickCy', () => {
        this.pathChart.cancelHighLight();
      });
      this.pathChart.on('nodeHover', ({ data, size, position }) => {
        data.delayTimer = setTimeout(() => {
          const $instance = $(`#detail-popover-${data.id}`);
          if ($instance.length > 0) {
            $instance.stop(true).fadeIn(0);
            return;
          }
          popoverHelper.showPopover({
            component: data.keyNo && data.keyNo[0] === 'p' ? appPerson : appCompany,
            data: {
              hasKeyNo: true,
              id: data.keyNo,
              keyNo: data.keyNo,
              name: data.name,
              eid: this.groupId,
              ename: this.companyName,
              rsTags: data.rsTags,
              org: data.org,
              type: 1,
            },
            container: $(`#${CONTAINER_PATH}`),
            identity: `detail-popover-${data.id}`,
            targetPosition: position,
            targetSize: { width: size.width * this.scale, height: size.height * this.scale },
          });
        }, 400);
      });

      this.pathChart.on('nodeOut', (data) => {
        if (data && data.delayTimer) {
          clearTimeout(data.delayTimer);
          delete data.delayTimer;
        }

        $(`#detail-popover-${data.id}`).fadeOut(400, function () {
          $(this).remove();
        });
      });
    },
    hidePath() {
      this.isLoading = false;
      this.showPath = false;
      this.noData = false;
      this.pathChart.cancelHighLight();
    },
    onZoomIn() {
      this.chart && this.chart.zoomInOrOut(true);
    },
    onZoomOut() {
      this.chart && this.chart.zoomInOrOut(false);
    },
    onFullScreen() {
      const element = $('.group-staff-container')[0];
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
    },
    onExitFullScreen() {
      if (this.isFullScreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      }
    },
    onSave() {
      this.chart && this.chart.saveImg(this.companyName);
    },
    onMouseenter() {
      if (this.iframe && this.isInit) {
        this.chart && this.chart.delayZoom();
      }
    },
    attachResizeDetector() {
      this.resizeDetector = resizeDetector();
      this.resizeDetector.listenTo(
        $(`.${this.containerName}`)[0],
        _.debounce(() => {
          this.chart && this.chart.sizeChange();
        }, 200)
      );
    },
  },
};
