
.group-staff-container {
  position: absolute;
  inset: 0;
  overflow: hidden;
  background: url("../utils/images/shuiying6.png") repeat;
  background-size: 360px 280px;
  line-height: 1.2;

  &.iframe-index {
    z-index: 1;
    height: 417px;
  }

  >svg {
    user-select: none;
  }

  .toolbox {
    position: fixed;
    width: 46px;
    right: 20px;
    bottom: 80px;
    font-size: 18px;
    z-index: 20;
  }

  .filter-modal {
    position: fixed;
    right: 78px;
    bottom: 100px;
    width: 502px;
    background: #FFF;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    border: 1px solid #EEE;
    box-sizing: border-box;
    z-index: 2;
  }

  .chart-legend-wrap {
    background-color: #fff;
    height: 30px;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 20;

    .chart-tips {
      overflow: hidden;
      display: inline-block;
      margin: 0 auto;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);

      >div {
        float: left;
        line-height: 30px;
        margin-right: 50px;
        color: #333;
        font-size: 14px;
        position: relative;

        &.tips {
          padding-left: 16px;

          &::before {
            content: '';
            display: block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translate(0, -50%);
          }
        }

        &.current-tips {
          &::before {
            background-color: #FA0;
          }
        }

        &.ent-tips {
          &::before {
            background-color: #128BED;
          }
        }

        &.person-tips {
          &::before {
            background-color: #F04040;
          }
        }

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .chart-footer {
    bottom: 30px;
  }

  #group-staff, #group-staff-path, .group-main {
    position: absolute;
    inset: 0;
  }

  .group-path-container {
    position: absolute;
    inset: 0;
    z-index: 999;
    overflow: hidden;
    background: url("../utils/images/shuiying6.png") repeat;
    background-size: 360px 280px;
    line-height: 1.2;

    .back {
      color: #128bed;
      position: absolute;
      top: 0;
      line-height: 42px;
      padding-left: 32px;
      cursor: pointer;
      z-index: 1000;

      &.full-screen {
        top: 0;
      }
    }
  }
}
