/* eslint-disable no-unused-expressions */
/* eslint-disable class-methods-use-this */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-this-alias */
/* eslint-disable func-names */
import { EventEmitter } from 'eventemitter3';
import _ from 'lodash';
import * as d3 from 'd3';

import * as companyUtil from '@/utils/firm';

import globalUtils from '../../utils/utils';

const cytoscape = window.cytoscape;

const _COLOR = {
  node: { person: '#F04040', company: '#128BED', current: '#FFAA00' },
  border: { person: '#F04040', company: '#128BED', current: '#FFAA00' },
  line: { position: '#128BED', employ: '#4ea2f0', legal: '#4ea2f0' },
};

const chartSetting = { scaleRate: 0.2 };

export default class Chart extends EventEmitter {
  constructor(selector) {
    super();
    this.selector = selector;
    this.$selector = $(`#${selector}`);
    this.elements = {
      nodes: [],
      edges: [],
    };
    this._rootNode = null;
  }

  init({ data }) {
    this.data = data;
    const width = this.$selector.width();
    const height = this.$selector.height();
    this.size = {
      width,
      height,
    };
  }

  domUpdate() {
    this.getD3Position(this.data);
    for (let i = 0, n = Math.ceil(Math.log(this.simulation.alphaMin()) / Math.log(1 - this.simulation.alphaDecay())); i < n; ++i) {
      this.simulation.tick();
    }
    this.drawChart(this.transformData(this.data));
  }

  getD3Position(graphs) {
    // 获取用来计算位置的节点和关系
    let layoutNodes = [];
    let layoutLinks = [];
    _.forEach(graphs, (graph) => {
      const rootNode = d3.hierarchy(graph.tree);
      const tempNodes = rootNode.descendants();
      layoutNodes = layoutNodes.concat(tempNodes);
      const tempLinks = rootNode.links();
      if (tempLinks.length) {
        layoutLinks = layoutLinks.concat(tempLinks);
      }
    });
    this.layoutLinks = layoutLinks;
    this.layoutNodes = layoutNodes;
    let strength = -600;
    let distanceMax = 330;
    let distance = 130;
    let colideRadius = 35;
    const distanceMin = 400;
    const nodeLength = layoutNodes.length;
    if (nodeLength < 50) {
      strength = -800;
      distanceMax = 350;
      distance = 130;
      colideRadius = 35;
    } else if (nodeLength < 100) {
      strength = -800;
      distanceMax = 350;
      distance = 130;
      colideRadius = 35;
    } else if (nodeLength < 150) {
      strength = -900;
      distanceMax = 450;
    } else if (nodeLength < 200) {
      strength = -1000;
      distanceMax = 500;
    } else if (nodeLength >= 200) {
      strength = -1600;
      distanceMax = 500;
      distance = 100;
      colideRadius = 35;
    }
    this.simulation = d3
      .forceSimulation(layoutNodes)
      .force('charge', d3.forceManyBody().strength(strength).distanceMax(distanceMax).distanceMin(distanceMin))
      .force('link', d3.forceLink(layoutLinks).distance(distance))
      .force('center', d3.forceCenter(this.size.width / 2, this.size.height / 2))
      .force(
        'collide',
        d3.forceCollide().radius(function () {
          return colideRadius;
        })
      )
      .stop();
  }

  transformData(graphs) {
    const els = {
      nodes: [],
      edges: [],
    };

    _.forEach(graphs, (graph) => {
      if (graph.relationships && graph.relationships.length) {
        graph.relationships.forEach((link) => {
          els.edges.push({
            data: {
              id: link.id,
              type: link.type,
              properties: link.properties,
              label: this.getLinkLabel(link.properties, link.type),
              sourceNode: link.sourceNode,
              targetNode: link.targetNode,
              source: link.startNode,
              target: link.endNode,
            },
            classes: 'edgeShow',
          });
        });
      }
      this.layoutNodes.forEach((node) => {
        const nodeProperties = this.getNodeProperties(node.data.properties, node.data.labels?.[0]);
        els.nodes.push({
          data: {
            id: node.data.id,
            type: node.data.labels?.[0],
            properties: node.data.properties,
            hasImage: node.data.properties.hasImage,
            isMain: node.data.properties.isMain,
            keyNo: node.data.properties.keyNo,
            name: node.data.properties.name,
            color: nodeProperties.color,
            borderColor: nodeProperties.borderColor,
            size: nodeProperties.size,
            d3x: node.x,
            d3y: node.y,
          },
        });
        if (node.data.properties.isMain) {
          this._rootNode = node;
        }
      });
    });
    return els;
  }

  drawChart(elements) {
    const self = this;
    let _isFoucs = false;
    this.cy = cytoscape({
      container: document.getElementById(this.selector),
      textureOnViewport: false,
      wheelSensitivity: 0.1,
      elements,
      minZoom: 0.4,
      maxZoom: 2.5,
      zoom: 1,
      layout: {
        name: 'preset',
        componentSpacing: 40,
        nestingFactor: 12,
        padding: 10,
        edgeElasticity: 800,
      },
      style: [
        {
          selector: 'node',
          style: {
            shape: 'ellipse',
            width(ele) {
              return ele.data('size');
            },
            height(ele) {
              return ele.data('size');
            },
            'background-color': function (ele) {
              return ele.data('color');
            },
            'background-fit': 'cover',
            'background-image': function (ele) {
              const hasImage = ele.data('hasImage');
              const keyNo = ele.data('keyNo');
              const type = ele.data('type');
              if (hasImage && type === 'Person') {
                return companyUtil.getLogoByKeyNo(keyNo, hasImage);
              }
              return 'none';
            },
            // 'background-image-containment': 'over',
            'border-color': function (ele) {
              return ele.data('borderColor');
            },
            'border-width': function (ele) {
              if (ele.data('hasImage') && ele.data('type') === 'Person') {
                return 3;
              }
              return 1;
            },
            'border-opacity': 1,
            label(ele) {
              const label = ele.data('name') || '';
              const length = label.length;

              if (length <= 5) {
                // 4 5 4排列
                return label;
              }
              if (length >= 5 && length <= 9) {
                return `${label.substring(0, length - 5)}\n${label.substring(length - 5, length)}`;
              }
              if (length >= 9 && length <= 13) {
                return `${label.substring(0, 4)}\n${label.substring(4, 9)}\n${label.substring(9, 13)}`;
              }
              return `${label.substring(0, 4)}\n${label.substring(4, 9)}\n${label.substring(9, 12)}..`;
            },
            'z-index-compare': 'manual',
            'z-index': 20,
            color: '#fff',
            padding(ele) {
              if (ele.data('type') === 'Company') {
                return 6;
              }
              return 0;
            },
            'line-height': 1.3,
            'font-size': 12,
            'font-family': 'microsoft yahei',
            'text-wrap': 'wrap',
            'text-max-width': 60,
            'text-halign': 'center',
            'text-valign': 'center',
            'overlay-color': '#fff',
            'overlay-opacity': 0,
            'background-opacity': 1,
            'text-background-color': '#000',
            'text-background-shape': 'roundrectangle',
            'text-background-opacity': function (ele) {
              if (ele.data('hasImage') && ele.data('type') === 'Person') {
                return 0.3;
              }
              return 0;
            },
            'text-background-padding': 0,
            'text-margin-y': function (ele) {
              // 当前节点有图片
              if (ele.data('type') === 'Person' && ele.data('isMain') && ele.data('hasImage')) {
                return 23;
              }
              // 有图片
              if (ele.data('hasImage') && ele.data('type') === 'Person') {
                return 16;
              }
              if (ele.data('type') === 'Company') {
                return 2;
              }
              return 2;
            },
          },
        },
        {
          selector: 'edge',
          style: {
            color() {
              return '#999';
            },
            'line-style': function () {
              return 'solid';
            },
            'curve-style': 'bezier',
            'control-point-step-size': 20,
            'target-arrow-shape': 'triangle',
            'target-arrow-color': function () {
              return _COLOR.line.position;
            },
            'arrow-scale': 0.8,
            'line-color': '#999',
            'text-opacity': 1,
            'text-background-color': '#fff',
            'text-background-opacity': 0.8,
            'font-size': 12,
            'background-color': function () {
              return '#ccc';
            },
            width: '0.6',
            'overlay-color': '#fff',
            'overlay-opacity': 0,
            'font-family': 'microsoft yahei',
          },
        },
        {
          selector: '.hidetext',
          style: {
            'text-opacity': 0,
          },
        },
        {
          selector: '.edgeShow',
          style: {
            color: '#999',
            'text-opacity': 1,
            'font-weight': 'normal',
            label(ele) {
              return ele.data('label') || '';
            },
            'font-size': 10,
          },
        },
        {
          selector: '.dull',
          style: {
            'z-index': 1,
            opacity: 0.2,
          },
        },
        {
          selector: '.nodeActive',
          style: {
            'border-width': 10,
            'border-opacity': 0.5,
          },
        },
        {
          selector: '.edgeActive',
          style: {
            color: _COLOR.line.position,
            'arrow-scale': 0.8,
            width: 1.5,
            'text-opacity': 1,
            'font-size': 12,
            'text-background-color': '#fff',
            'text-background-opacity': 0.8,
            'text-background-padding': 0,
            'source-text-margin-y': 20,
            'target-text-margin-y': 20,
            'z-index-compare': 'manual',
            'z-index': 1,
            'line-color': _COLOR.line.position,
            'target-arrow-color': _COLOR.line.position,
            label(ele) {
              return ele.data('label') || '';
            },
          },
        },
        {
          selector: '.lineFixed', // 加载完成后，加载该类，修复线有锯齿的问题
          style: {
            'overlay-opacity': 0,
          },
        },
      ],
    });
    this.cy.on('click', 'node', (evt) => {
      if (+evt.target._private.style['z-index'].value === 20) {
        _isFoucs = true;
        const node = evt.target;
        this.highLight([node._private.data.id]);
        this.emit('nodeClick', node._private.data);
        if (node.hasClass('nodeActive')) {
          node.removeClass('nodeActive');
          this.cy.collection('edge').removeClass('edgeActive');
        } else {
          this.cy.collection('node').addClass('nodeDull');
          this.cy.collection('node').removeClass('nodeActive');
          this.cy.collection('edge').removeClass('edgeActive');
          node.addClass('nodeActive');
          node.neighborhood('edge').removeClass('opacity');
          node.neighborhood('edge').addClass('edgeActive');
          node.neighborhood('edge').connectedNodes().removeClass('opacity');
        }
      } else {
        _isFoucs = false;
        self.emit('clickCy');
      }
    });
    let startX = 0;
    let startY = 0;

    this.cy.on('mousedown', (evt) => {
      if (evt.target._private.group === 'nodes') {
        startX = window.event.clientX;
        startY = window.event.clientY;
      }
    });

    this.cy.on('mousemove', (evt) => {
      if (evt.target && evt.target._private && evt.target._private.group === 'nodes' && startX && startY) {
        const endX = window.event.clientX;
        const endY = window.event.clientY;
        if ((endX - startX) * (endX - startX) + (endY - startY) * (endY - startY) >= 1) {
          if (evt.target._private.group === 'nodes' && evt.target._private.data.id) {
            this.emit('nodeOut', evt.target._private.data);
          }
        }
      }
    });

    this.cy.on('mouseup', (evt) => {
      startX = 0;
      startY = 0;
    });

    // 点击
    this.cy.on('tap', function (evt) {
      const node = evt.target._private;
      if (node.group !== 'nodes') {
        _isFoucs = false;
        self.emit('clickCy');
        self.emit('hideDetail');
      }
    });

    this.cy.on('mouseover', 'node', (evt) => {
      const node = evt.target;
      node.addClass('hover');
      const position = node.renderedBoundingBox();
      this.emit('nodeHover', {
        data: node._private.data,
        position: {
          left: position.x1,
          right: position.x2,
          top: position.y1,
          bottom: position.y2,
        },
        size: {
          width: position.w,
          height: position.h,
        },
      });
    });
    this.cy.on('mouseout', 'node', (evt) => {
      const node = evt.target;
      evt.target.removeClass('hover');
      this.emit('nodeOut', node._private.data);
    });

    this.cy.nodes().positions((node) => {
      return {
        x: node._private.data.d3x,
        y: node._private.data.d3y,
      };
    });
    this.cy.on('zoom', () => {
      if (this.cy.zoom() < 0.5) {
        this.cy.collection('node').addClass('hidetext');
        this.cy.collection('edge').addClass('hidetext');
      } else {
        this.cy.collection('node').removeClass('hidetext');
        this.cy.collection('edge').removeClass('hidetext');
      }

      // 加载完成后，加载该类，修复线有锯齿的问题
      setTimeout(() => {
        this.cy.collection('edge').removeClass('lineFixed');
        this.cy.collection('edge').addClass('lineFixed');
      }, 200);
      this.emit('onZoom', this.cy.zoom());
    });

    this.cy.on('pan', () => {
      // 加载完成后，加载该类，修复线有锯齿的问题
      setTimeout(() => {
        this.cy.collection('edge').removeClass('lineFixed');
        this.cy.collection('edge').addClass('lineFixed');
      }, 200);
    });

    this.cy.ready(() => {
      this.cy
        .zoom({
          level: 1,
        })
        .center();
      setTimeout(() => {
        this.cy.collection('edge').addClass('lineFixed');
      }, 5000);
    });
  }

  highLight(nodeIds, isFiltered = false) {
    this.isFiltered = isFiltered;
    this.cy.collection('node').removeClass('nodeActive');
    this.cy.collection('edge').removeClass('edgeActive');
    this.cy.collection('node').addClass('dull');
    this.cy.collection('edge').addClass('dull');
    for (let i = 0; i < nodeIds.length; i++) {
      const nodeId = nodeIds[i];
      this.cy.nodes((node) => {
        const nodeData = node._private.data;
        if (nodeData.id === nodeId) {
          node.removeClass('dull');
          node.neighborhood('edge').removeClass('dull');
          node.neighborhood('edge').addClass('edgeActive');
          node.neighborhood('edge').connectedNodes().removeClass('dull');
        }
      });
    }
  }

  cancelHighLight() {
    this.isFiltered = false;
    this.cy.collection('node').removeClass('nodeActive');
    this.cy.collection('edge').removeClass('edgeActive');
    this.cy.collection('node').removeClass('dull');
    this.cy.collection('edge').removeClass('dull');
  }

  getNodeProperties(properties, type) {
    const obj = {
      color: _COLOR.node.company,
      borderColor: _COLOR.border.company,
      size: 45,
    };
    if (properties.isMain) {
      obj.color = _COLOR.node.current;
      obj.borderColor = _COLOR.border.current;
      if (type === 'Person' && properties.hasImage) {
        obj.size = 80;
      } else if (type === 'Company') {
        obj.size = 60;
      }
    } else if (type === 'Person') {
      obj.color = _COLOR.node.person;
      obj.borderColor = _COLOR.border.person;
      if (properties.hasImage) {
        obj.size = 60;
      }
    } else if (type === 'Company') {
      obj.size = 56;
    }
    return obj;
  }

  getLinkLabel(properties, type) {
    const role = properties.role;
    if (type === 'LEGAL') {
      return role || '法定代表人';
    }
    return role || '';
  }

  handlePositionFilter(type) {
    if (type) {
      // 取消高亮
      this.cancelHighLight();
      this.cy.collection('edge').addClass('dull');
      this.cy.collection('node').addClass('dull');
      this.cy.edges(function (ele) {
        const edgeType = ele.data('type');
        if (edgeType === type) {
          ele.removeClass('dull');
          ele.addClass('edgeActive');
          ele.connectedNodes().removeClass('dull');
        }
      });
    } else {
      this.cancelHighLight();
    }
  }

  cleanup() {
    this.cy && this.cy.destroy();
  }

  saveImg(companyName) {
    const imgdata = this.cy.png({
      full: true,
      bg: 'rgba(255, 255, 255, 0)',
      scale: 1,
      maxWidth: 4000,
      maxHeight: 4000,
    });
    return globalUtils.downloadImage(imgdata, companyName, '集团人员图谱');
  }

  zoomInOrOut(zoomIn) {
    if (_.isNumber(zoomIn)) {
      this.cy.zoom({
        level: zoomIn, // the zoom level
      });
      this.cy.center();
      return;
    }
    let scale = this.cy.zoom();
    if (zoomIn) {
      scale += chartSetting.scaleRate;
    } else {
      scale -= chartSetting.scaleRate;
    }
    this.cy.zoom({
      level: scale, // the zoom level
    });
  }

  // iframe 时使用
  delayZoom() {
    this.cy.zoomingEnabled(false);
    setTimeout(() => {
      this.cy.zoomingEnabled(true);
    }, 2000);
  }

  sizeChange() {
    const newWidth = this.$selector.width();
    const newHeight = this.$selector.height();
    if (!this.cy) return;
    if (this.size.width === newWidth && this.size.height === newHeight) return;
    this.size = {
      width: newWidth,
      height: newHeight,
    };
    this.cy.center();
  }
}
