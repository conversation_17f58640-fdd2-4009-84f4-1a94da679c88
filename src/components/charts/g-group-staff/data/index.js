import _ from 'lodash';

import { graph } from '@/shared/services';

const staffWorker = new Worker(new URL('./groupStaff.chartworker', import.meta.url));

const getGroupStaffGraph = (params) => {
  return new Promise((resolve, reject) => {
    graph
      .getStaffGraph(params)
      .then((res) => {
        const fetchAll = res.Result?.fetchAll;
        const graph1 = _.get(res, 'Result.results.[0].data', []);
        if (!_.isEmpty(graph1)) {
          staffWorker.postMessage({
            event: 'initGraph',
            payload: { data: graph1, groupId: params.groupId },
          });
          staffWorker.onmessage = (e) => {
            if (e.data.event === 'finishInitGraph') {
              resolve({
                data: e.data.payload,
                fetchAll,
              });
            }
          };
        } else {
          resolve({});
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const getSearchResultGraph = (params) => {
  return new Promise((resolve, reject) => {
    graph
      .getSearchResultGraph(params)
      .then((res) => {
        const graph1 = _.get(res, 'Result.results.[0].data', []);
        if (!_.isEmpty(graph1)) {
          staffWorker.postMessage({
            event: 'initGraph',
            payload: { data: graph1, groupId: params.groupId },
          });
          staffWorker.onmessage = (e) => {
            if (e.data.event === 'finishInitGraph') {
              resolve({
                data: e.data.payload,
              });
            }
          };
        } else {
          resolve({});
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const searchStaffGraph = (params) => {
  return new Promise((resolve, reject) => {
    graph
      .searchStaffGraph(params)
      .then((res) => {
        const list = _.get(res, 'Result.list', []);
        const formatList = [];
        list.forEach((i) => {
          formatList.push({
            id: i.id,
            type: i.labels[0] === 'Person' ? 'Person' : 'Company',
            keyNo: i.properties.keyNo,
            name: i.properties.name,
            hasImage: i.properties.hasImage,
          });
        });
        resolve(formatList);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export default {
  getGroupStaffGraph,
  getSearchResultGraph,
  searchStaffGraph,
};
