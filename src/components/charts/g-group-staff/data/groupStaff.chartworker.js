import _ from 'lodash';

class GroupStaff {
  constructor() {
    this.reset();
  }

  init({ groupId, data }) {
    this.data = data;
    this.groupId = groupId;
    this._rootNode = null;
    this.relatedHasMainC = false;
    this.mainCKeyNo = '';
  }

  reset() {}

  format() {
    // 存在多棵树的情况 循环data数据进行处理
    const dataArr = [];
    _.forEach(this.data, (data, index) => {
      const res = this.formatGraph(data.graph, index);
      dataArr.push(res);
    });
    self.postMessage({
      event: 'finishInitGraph',
      payload: dataArr,
    });
  }

  formatGraph(data) {
    // 找到主公司节点
    let tempRoot = _.find(data.nodes, (o) => o.properties?.isMain);
    if (!tempRoot) {
      tempRoot = data.nodes[0];
    }
    _.forEach(data.relationships, (link) => {
      link.sourceNode = this.getGraphNode(link.startNode, data.nodes);
      link.targetNode = this.getGraphNode(link.endNode, data.nodes);
    });
    const treeData = tempRoot;
    this.DFS([treeData], data.relationships, 1);
    data.tree = treeData;
    return data;
  }

  // 深度遍历 形成children格式
  DFS(nodes, links, level) {
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      node.level = level;
      level++;
      const nextNodes = this.getNextNodes(node.id, links);
      if (nextNodes.length) {
        node.children = nextNodes;
        nextNodes.forEach((n) => {
          n.level = level;
        });
        this.DFS(nextNodes, links, level);
      }
    }
  }

  getGraphNode = (nodeId, nodes) => {
    return _.find(nodes, (o) => o.id === nodeId);
  };

  getNextNodes = (nodeId, links) => {
    let nextNodes = [];
    _.forEach(links, (link) => {
      if (nodeId === link.startNode && !link.targetNode.level) {
        nextNodes.push(link.targetNode);
      } else if (nodeId === link.endNode && !link.sourceNode.level) {
        nextNodes.push(link.sourceNode);
      }
    });
    nextNodes = _.uniqBy(nextNodes, 'id');
    return nextNodes;
  };
}

const formatter = new GroupStaff();

self.addEventListener('message', function (e) {
  if (e.data.event === 'initGraph') {
    formatter.init({ data: e.data.payload.data, groupId: e.data.payload.groupId });
    formatter.format();
  }
});
