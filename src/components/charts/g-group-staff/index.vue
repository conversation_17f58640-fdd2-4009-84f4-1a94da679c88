<template>
  <div :class="['group-staff-container']">
    <div class="group-main">
      <div id="group-staff"></div>
      <!-- 加载 -->
      <g-ui-loading v-if="isLoading"></g-ui-loading>
      <!-- 暂无数据 -->
      <g-ui-no-data v-if="noData || pathNoData"></g-ui-no-data>
      <!-- 免责 -->
      <g-ui-footer></g-ui-footer>
      <!-- 图例 -->
      <div class="chart-legend-wrap">
        <div class="chart-tips">
          <div class="tips current-tips">主公司</div>
          <div class="tips ent-tips">企业</div>
          <div class="tips person-tips">人员</div>
          <div class="position">
            <img alt="任职" src="./images/icon-arrow.svg" />
            任职
          </div>
        </div>
      </div>
      <template v-if="isInit">
        <transition name="slide-fade">
          <div class="group-path-container" v-show="showPath">
            <div id="group-staff-path"></div>
            <div class="back container" :class="[isFullScreen && 'full-screen']" @click="hidePath">
              <img alt="返回" src="./images/ic_reply.png" />
              {{ companyName || '' }}
            </div>
          </div>
        </transition>
        <!-- toolbox -->
        <div class="toolbox">
          <g-ui-toolbox>
            <g-ui-toolbox-action :action-type="actionTypes.filter2" :is-active="showFilter" @click="onShowFilter"></g-ui-toolbox-action>
            <g-ui-toolbox-action :action-type="actionTypes.search" :is-active="showSearch" @click="onSearch"></g-ui-toolbox-action>
            <g-ui-toolbox-action :action-type="actionTypes.zoomIn" @click="onZoomIn"></g-ui-toolbox-action>
            <g-ui-toolbox-action :action-type="actionTypes.zoomOut" @click="onZoomOut"></g-ui-toolbox-action>
            <g-ui-toolbox-action :action-type="actionTypes.refresh" @click="onRefresh()"></g-ui-toolbox-action>
            <g-ui-toolbox-action v-show="!isFullScreen" :action-type="actionTypes.fullScreen" @click="onFullScreen"> </g-ui-toolbox-action>
            <g-ui-toolbox-action v-show="isFullScreen" :action-type="actionTypes.exitFullScreen" @click="onExitFullScreen">
            </g-ui-toolbox-action>
            <g-ui-toolbox-action :action-type="actionTypes.save" :is-busy="isSaving" @click="onSave"></g-ui-toolbox-action>
          </g-ui-toolbox>
        </div>
        <!-- 筛选 -->
        <transition name="fade">
          <div v-show="showFilter" class="filter-modal">
            <g-app-filter title="筛选" :type-list="typeList" @chooseFilter="handlerFilter" @close="showFilter = false"></g-app-filter>
          </div>
        </transition>
        <!-- 搜索 -->
        <transition name="fade">
          <div v-if="showSearch" class="filter-modal">
            <g-ui-search
              :is-visible="showSearch"
              :search-loading="searchLoading"
              :search-no-data="searchNoData"
              :search-list="searchList"
              ref="search"
              @choose="handlePath"
              @search="handleSearch"
              @close="showSearch = false"
            ></g-ui-search>
          </div>
        </transition>
      </template>
    </div>
  </div>
</template>
<style lang="less" scoped src="./style.less"></style>
<style lang="less">
.slide-fade-enter-active {
  transition: all 0.3s ease;
}
.slide-fade-leave-active {
  transition: all 0.3s ease;
}
.slide-fade-enter, .slide-fade-leave-to
/* .slide-fade-leave-active for below version 2.1.8 */ {
  transform: translateX(1200px);
}
</style>
<script src="./component.js"></script>
