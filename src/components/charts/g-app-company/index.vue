<template>
  <div class="company-container">
    <transition name="fade">
      <div class="wrap" v-if="isLoaded">
        <div>
          <q-entity-avatar class="logo" :key-no="keyNo" :src="image" :has-image="!!image" :name="name" :size="40"></q-entity-avatar>
          <div class="content-container">
            <q-entity-link
              style="font-size: 14px; line-height: 22px"
              class="entity-text"
              :coy-obj="{ KeyNo: keyNo, Name: name }"
            ></q-entity-link>
            <template v-if="type !== 'risk'">
              <div class="row-item tag-container" v-if="!specialOrg.includes(org)">
                <q-tag :type="tag.font" :key="tag.name" v-for="tag in tags">{{ tag.name }}</q-tag>
              </div>
              <div class="row-item tag-container" v-else-if="tagName">
                <q-tag type="primary">{{ tagName }}</q-tag>
              </div>
            </template>
            <div class="row-item to-analysis" v-else>
              <q-link :to="`/firm/${keyNo}/susong#summarytext`">查看企业风险分析 <q-icon type="icon-a-xianduanyou"></q-icon></q-link>
            </div>
            <template v-if="!specialOrg.includes(org)">
              <div class="row-item">
                <span class="label-text">{{ operTypeName || '法定代表人' }}：</span>
                <span class="content">
                  <q-entity-link :coy-obj="{ KeyNo: operId, Name: operName }"></q-entity-link>
                </span>
              </div>
              <div class="row-item">
                <span class="label-text">注册资本：</span>
                <span class="content">{{ registCapi || '-' }}</span>
              </div>
              <div class="row-item">
                <span class="label-text">成立日期：</span>
                <span class="content">{{ startDate || '-' }}</span>
              </div>
            </template>
            <template v-else-if="+org === 13">
              <div class="row-item">
                <span class="label-text">成立日期：</span>
                <span class="content">{{ startDate || '-' }}</span>
              </div>
              <div class="row-item">
                <span class="label-text">所属地：</span>
                <span class="content">{{ region || '-' }}</span>
              </div>
            </template>
            <template v-else-if="+org === 20 || +org === 21">
              <div class="row-item">
                <span class="label-text">成员企业：</span>
                <span class="content">{{ memberCount || '-' }}</span>
              </div>
              <div class="row-item">
                <span class="label-text">主公司：</span>
                <span class="content">
                  <q-entity-link :coy-obj="{ KeyNo: mainNo, Name: mainName }"></q-entity-link>
                </span>
              </div>
            </template>
            <div class="row-item row-item-btn" v-if="type === 'risk'">
              <q-link :to="`/firm/${id}/tupu#riskChart`"> <div class="btn">查看风险图谱</div></q-link>
            </div>
          </div>
          <div style="clear: both"></div>
        </div>
        <template v-if="+type === 3 && pathData && pathData.length">
          <div class="gap"></div>
          <div class="path">
            <div class="path-wrap">
              <span class="path-title">关联方认定详情</span>
              <q-tag type="primary" v-if="rule === 'sh'">上交所</q-tag>
              <q-tag type="warning" v-if="rule === 'sz'">深交所</q-tag>
              <q-tag type="pl" v-if="rule === 'kj'">会计准则</q-tag>
            </div>
            <g-app-path-glf :paths="pathData" :key-no="eid" />
          </div>
        </template>
        <template v-else-if="+type === 2">
          <g-app-path v-if="rsTags && !specialOrg.includes(org)" :path-data="pathData" :ename="ename" :eid="eid" :tips="tips"></g-app-path>
        </template>
      </div>
    </transition>
    <div v-if="!isLoaded">
      <div class="loading"></div>
    </div>
  </div>
</template>

<script src="./component.js"></script>

<style lang="less" src="./style.less" scoped></style>
