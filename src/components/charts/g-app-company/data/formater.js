/* eslint-disable consistent-return */
/**
 * Created by <PERSON> on - 2019/07/05.
 */

import moment from 'moment';
import _ from 'lodash';

import operTypes from '../../utils/operType';

const notShowTag = [622, 908];
const formatEnterpriseOverview = (data) => {
  // 公司ID
  // id: '',
  // 公司名称
  // name: '',
  // 公司logo
  // image: '',
  // 公司标签
  // tags: [{
  //   name: 'xxxx',
  //   style: 'style-1'
  // }],
  // 法定代表人
  // operName: '',
  // 法定代表人
  // operId: '',
  // 注册资本
  // registCapi: '',
  // 成立日期
  // startDate: '',

  let startDate = '';
  if (data.StartDate) {
    const d = moment(+data.StartDate * 1000);
    startDate = d.format('YYYY-MM-DD');
  }
  const nodeData = {
    id: data.Id,
    name: data.Name,
    image: data.Image,
    registCapi: data.RegistCapi,
    startDate,
    region: data.Region,
    memberCount: data.MemberCount,
    mainName: data.MainName,
    mainNo: data.MainNo,
  };

  if (data.Oper) {
    nodeData.operType = data.Oper.OperType;
    nodeData.operTypeName = operTypes.getOperTypeName(data.Oper.OperType);
    nodeData.operName = data.Oper.Name;
    nodeData.operId = data.Oper.KeyNo;
  }

  nodeData.tags = [];

  if (data.Tags) {
    _.map(data.Tags, (tag) => {
      let tagName = tag.Name;
      if (tag.Type === 1 || tag.Type === 2 || tag.Type === 7 || tag.Type === 301 || tag.Type === 401) {
        tagName = `${tag.Name} | ${tag.ShortName} ${tag.DataExtend}`;
      } else if (tag.Type === 502) {
        tagName = tag.DataExtend;
      } else if (tag.Type === 8 || tag.Type === 9) {
        tagName = `${tag.Name} | ${tag.ShortName} ${tag.DataExtend} 已退市`;
      } else if (notShowTag.indexOf(+tag.Type) > -1) {
        // 屏蔽国有企业
        return false;
      }
      if (tagName) {
        nodeData.tags.push({
          type: +tag.Type,
          name: tagName,
        });
      }
    });
  }

  return nodeData;
};

export default {
  formatEnterpriseOverview,
};
