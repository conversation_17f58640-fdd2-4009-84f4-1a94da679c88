import { graph } from '@/shared/services';

import formater from './formater';

const loadCompanyDetail = (eid, org) => {
  return new Promise((resolve, reject) => {
    graph
      .getEnterpriseOverview({
        eid,
        org,
      })
      .then((data) => {
        if (data.Result) {
          const detail = formater.formatEnterpriseOverview(data.Result);
          resolve(detail);
        } else {
          resolve({
            code: 500,
          });
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export default {
  loadCompanyDetail,
};
