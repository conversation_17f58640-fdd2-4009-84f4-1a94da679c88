/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-empty-function */
/**
 * Created by <PERSON> on - 2019/06/28.
 */
import dataLoader from './data';
import defaultLogo from './images/default.jpg';
import getTypes from '../utils/tag-type';
import appPath from '../g-app-path';
import appPathGlf from '../g-app-path-glf';

export default {
  name: 'g-app-company',
  data() {
    return {
      isLoaded: false,
      // 公司ID
      id: '',
      // 公司名称
      name: '',
      // 公司logo
      image: '',
      // 公司标签
      tags: [],
      // 法定代表人
      operName: '',

      operTypeName: '',
      // 法定代表人
      operId: '',
      // 注册资本
      registCapi: '',
      // 成立日期
      startDate: '',
      // 公司名
      ename: '',
      // 公司编号
      eid: '',
      // 受益人标签
      rsTags: '',
      // 路径信息
      pathData: '',
      // 默认logo
      defaultLogo,
      // 省份
      region: '',
      mainName: '',
      mainNo: '',
      memberCount: '',
      // org
      org: 0,
      specialOrg: [13, 20, 21],
      type: 1,
      tips: '',
    };
  },
  components: {
    [appPath.name]: appPath,
    [appPathGlf.name]: appPathGlf,
  },
  mounted() {
    this.keyNo = this.id;
    if (this.keyNo) {
      dataLoader
        .loadCompanyDetail(this.keyNo, this.org)
        .then((data) => {
          if (data.code === 500) {
            // this.$toasted.error('查无数据');
          } else if (data.name) {
            this.isLoaded = true;
            this.name = data.name;
            this.image = data.image;
            this.tags = data.tags || [];
            data.tags.forEach((tag) => {
              tag.font = getTypes.getType(tag.type, tag.name);
            });
            this.operTypeName = data.operTypeName;
            this.operName = data.operName;
            this.operId = data.operId;
            this.registCapi = data.registCapi;
            this.startDate = data.startDate;
            this.region = data.region;
            this.mainName = data.mainName;
            this.mainNo = data.mainNo;
            this.memberCount = data.memberCount;
            if (this.org === 13) {
              this.tagName = '投资机构';
            } else if (this.org === 20) {
              this.tagName = '集团';
            } else if (this.org === 21) {
              this.tagName = '企业族群';
            }
          } else {
            this.isLoaded = true;
          }
        })
        .catch(() => {});
    } else {
      this.rsTags = null;
      this.pathData = null;
      this.isLoaded = true;
    }
  },
  methods: {
    getTagType(status) {
      let type = 'success';
      if (['注销', '吊销', '停业', '撤销', '清算', '无效'].includes(status)) {
        type = 'danger';
      } else if (['筹建', '迁入', '迁出'].includes(status)) {
        type = 'warning';
      }
      return type;
    },
  },
};
