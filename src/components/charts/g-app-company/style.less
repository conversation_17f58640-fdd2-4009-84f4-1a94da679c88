
.company-container {
  font-family: "Microsoft YaHei", Arial, sans-serif;
  width: 360px;
  box-shadow: rgba(0, 0, 0, 0.2) 0 2px 4px 0;
  background-color: #fff;
  border-radius: 6px;

  .wrap {
    padding: 16px;
  }

  .loading {
    width: 360px;
    height: 152px;
    background: url('./images/loading.png') center center;
    background-size: cover;
    margin-top: 0;
    margin-right: 0;
    margin-bottom: 0;
  }

  .logo {
    float: left;
  }

  .special-tag {
    color: #128BED;
    background-color: #E9F3FF;
    margin-top: 8px;
    font-size: 12px;
    display: inline-block;
    padding: 3px 6px;
  }

  .to-analysis {
    font-size: 13px;

    a {
      color: #ff7220 !important;
    }

    cursor: pointer;
  }

  .content-container {
    float: left;
    min-height: 65px;
    padding-left: 12px;
    width: 260px;

    .entity-link {
      font-size: 14px !important;
    }


    a {
      color: #128BED;
      text-decoration: none;

      &:hover {
        color: #3071a9;
      }
    }

    .company-link {
      font-size: 16px;
      word-break: break-all;
    }

    .row-item {
      padding-top: 4px;
      display: flex;
      flex-wrap: wrap;

      &.tag-container {
        padding-top: 0;
        margin-bottom: 4px;

        >div {
          margin-top: 4px;
        }
      }

      &.row-item-btn {
        padding-top: 16px;
      }

      .btn {
        font-size: 12px;
        line-height: 18px;
        padding: 4px 16px;
        text-align: center;
        background-color: #128BED;
        color: #fff;
        border-radius: 2px;
      }
    }

    .label-text {
      color: #999;
      font-size: 13px;
      font-weight: unset;
      flex-shrink: 0;
    }

    .content {
      color: #666;
      font-size: 14px;
      flex: 1;
    }
  }

  .gap {
    margin-top: 16px;
    height: 1px;
    background: #eee;
  }

  .path {
    line-height: 28px;
    max-height: 150px;
    overflow: auto;
    padding-top: 12px;

    .path-wrap {
      display: flex;
      align-items: center;
    }
  }

  .path-title {
    font-weight: normal;
    font-size: 13px;
    color: #333;
    margin-right: 8px;
  }
}
