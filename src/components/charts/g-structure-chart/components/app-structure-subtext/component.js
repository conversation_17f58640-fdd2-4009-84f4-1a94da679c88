/* eslint-disable @typescript-eslint/no-empty-function */
export default {
  name: 'structure-chart-subtext',
  props: {
    data: {
      type: Object,
      default: () => {},
    },
    hasPledge: {
      type: Boolean,
      default: false,
    },
    keyNo: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
    iframe: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    toKzr() {
      if (this.iframe) {
        this.$tabs.open(`/firm/${this.keyNo}/tupu#kzr`);
      }
    },
  },
};
