
.app-structure-subtext {
  height: 70px;

  &.special {
    >div {
      top: 156px;
    }
  }

  >div {
    width: 100%;
    background-color: #F5FAFF;
    margin: 0 auto;
    overflow: hidden;
    height: 70px;
    padding: 0 20px;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, 0);
    z-index: 2;
  }

  .title {
    float: left;
    line-height: 70px;
    margin-right: 30px;

    .icon-zhushi {
      font-size: 14px;
      color: #bbb;
      position: relative;
      top: 0;
      margin-left: 5px;
    }
  }

  .main {
    float: left;
    padding-top: 10px;

    .app-auto-logo {
      float: left;
      margin-right: 10px;
    }

    .name-panel {
      float: left;
      font-size: 14px;
      line-height: 24px;

      &.name-panel-single {
        line-height: 50px;
      }

      .name {
        font-size: 16px;
      }

      a.name {
        color: #128bed;
        font-size: 16px;
      }

      .percent {
        margin-top: 2px;
        color: #999;

        span {
          color: #FF722D;
        }
      }
    }
  }

  .tips {
    float: right;
    line-height: 70px;
    font-size: 14px;
    color: #FF722D;
  }
}
