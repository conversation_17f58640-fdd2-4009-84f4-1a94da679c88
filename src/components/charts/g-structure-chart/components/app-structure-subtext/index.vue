<template>
  <div :class="['app-structure-subtext']">
    <div class="app-structure-subtext-wrap">
      <span class="title"
        >{{ data.isSH && !data.isListed ? '上市企业公示实际控制人（官方公示）：' : '实际控制人'
        }}<a-popover v-if="!(data.isSH && !data.isListed)" placement="bottom">
          <div slot="content" style="width: 260px">
            企业的实际控制人，是企查查根据企业股权数据或上市公司公告得出，仅供参考；企业在公司章程或股东协议中或许有其他安排，在使用该数据前建议跟企业做进一步核实。
          </div>
          <q-icon type="icon-a-shuomingxian" class="icon-zhushi"></q-icon> </a-popover
      ></span>
      <div class="main">
        <div :class="['name-panel', data.isSH && !data.isListed && 'name-panel-single']">
          <template v-for="(item, index) in data.list">
            <span v-if="index !== 0" :key="`option-${index}`">，</span>
            <q-entity-link :key="`name-${index}`" :coy-obj="{ KeyNo: item.KeyNo, Name: item.Name }"></q-entity-link>
            <div :key="`percent-${index}`" class="percent" v-if="!(data.isSH && !data.isListed)">
              总股权比例为：<span>{{ item.PercentTotal || '-' }}</span>
            </div>
          </template>
        </div>
      </div>
      <a class="tips" @click="toKzr"> 查看控制链 > </a>
    </div>
  </div>
</template>
<script src="./component.js"></script>
<style lang="less" scoped src="./style.less"></style>
