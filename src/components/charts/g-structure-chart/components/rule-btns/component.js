import _ from 'lodash';

import appChartSelect from '../../../g-app-select';

export default {
  name: 'structure-chart-rule-btns',
  components: {
    [appChartSelect.name]: appChartSelect,
  },
  props: {
    dateList: {
      type: Array,
      default: () => [],
    },
    hasIpoPartners: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      ruleType: [
        { type: 'gd', value: '股东信息' },
        { type: 'invest', value: '对外投资' },
        { type: 'hisgd', value: '历史股东信息' },
      ],
      chosenType: 'gd',
      // 股东筛选信息
      gdTypeList: [
        { key: '', value: '全部类型' },
        { key: '自然人股东', value: '自然人股东' },
        { key: '企业法人', value: '企业法人' },
        // { key: '合伙企业', value: '合伙企业' },
        // { key: '其他投资者', value: '其他投资者' }
      ],
      gdType: { key: '', value: '全部类型' },
      // 股东持股比例
      gdPercentList: [
        { key: '', value: '持股比例不限' },
        { key: '5', value: '5%以上' },
        { key: '25', value: '25%以上' }, // (超过25%的为最终受益人)
        { key: '50', value: '50%以上' },
        { key: '90', value: '90%以上' },
      ],
      gdPercent: { key: '', value: '持股比例不限' },
      // 对外投资-是否控股
      kgList: [
        { key: '', value: '控股类型不限' },
        { key: '1', value: '控股' },
        { key: '2', value: '非控股' },
      ],
      isKg: { key: '', value: '控股类型不限' },
      // 对外投资比例
      investPercentList: [
        { key: '-1', value: '持股比例不限' },
        { key: '100', value: '100%' },
        { key: '66.66', value: '66.66以上' },
        { key: '50', value: '50%以上' },
        { key: '20', value: '20%以上' },
        { key: '5', value: '5%以上' },
        { key: '0', value: '不到5%' },
      ],
      investPercent: { key: '-1', value: '持股比例不限' },
      // 历史股东列表
      hisList: [{ key: '', value: '不限' }],
      date: { key: '', value: '不限' },
    };
  },
  watch: {
    dateList(item) {
      this.hisList = [{ key: '', value: '不限' }];
      _.forEach(item, (val) => {
        this.hisList.push({ key: val, value: val });
      });
    },
  },
  methods: {
    switchType(item) {
      if (this.chosenType !== item) {
        this.chosenType = item;
        if (item === 'gd') {
          $('.invest-filter').slideUp();
          $('.hisgd-filter').slideUp();
          $('.gd-filter').slideDown();
        } else if (item === 'invest') {
          $('.gd-filter').slideUp();
          $('.invest-filter').slideDown();
          $('.hisgd-filter').slideUp();
        } else if (item === 'hisgd') {
          $('.gd-filter').slideUp();
          $('.invest-filter').slideUp();
          $('.hisgd-filter').slideDown();
        }
        this.$emit('switchType', this.chosenType);
      }
    },
    choose(item) {
      this[item.type] = item.item;
      if (this.chosenType === 'gd') {
        this.$emit('filterGd', { type: this.gdType.key, percent: this.gdPercent.key });
      } else if (this.chosenType === 'invest') {
        this.$emit('filterInvest', { type: this.isKg.key, percent: this.investPercent.key });
      } else if (this.chosenType === 'hisgd') {
        this.$emit('filterHis', { date: this.date.key });
      }
    },
    closeSelect() {
      if (this.chosenType === 'gd') {
        this.$refs.gdType.showSelect = false;
        this.$refs.gdPercent.showSelect = false;
      } else if (this.chosenType === 'invest') {
        this.$refs.kg.showSelect = false;
        this.$refs.investPercent.showSelect = false;
      } else if (this.chosenType === 'hisgd') {
        this.$refs.date.showSelect = false;
      }
    },
    initSelect(needEmit = true) {
      if (this.chosenType === 'gd') {
        this.gdType = { key: '', value: '全部类型' };
        this.gdPercent = { key: '', value: '持股比例不限' };
        if (needEmit) {
          this.$emit('filterGd', { type: this.gdType.key, percent: this.gdPercent.key });
        }
      } else if (this.chosenType === 'invest') {
        this.isKg = { key: '', value: '控股类型不限' };
        this.investPercent = { key: '-1', value: '持股比例不限' };
        if (needEmit) {
          this.$emit('filterInvest', { type: this.isKg.key, percent: this.investPercent.key });
        }
      } else if (this.chosenType === 'hisgd') {
        this.date = { key: '', value: '不限' };
        if (needEmit) {
          this.$emit('filterHis', { date: this.date.key });
        }
      }
    },
  },
};
