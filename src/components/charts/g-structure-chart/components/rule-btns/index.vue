<template>
  <div class="structure-rule-btns">
    <div class="left">
      <div :class="['rule-btn', chosenType === 'gd' && 'active']" @click="switchType('gd')">股东信息</div>

      <div :class="['rule-btn', chosenType === 'invest' && 'active']" @click="switchType('invest')">对外投资</div>

      <div :class="['rule-btn', chosenType === 'hisgd' && 'active']" @click="switchType('hisgd')">历史股东信息</div>

      <div class="filter-panel gd-filter" v-show="chosenType === 'gd'">
        <g-app-select
          v-show="!hasIpoPartners"
          ref="gdType"
          :list="gdTypeList"
          type="gdType"
          @choose="choose"
          :chosen="gdType"
          @closeSelect="closeSelect"
        ></g-app-select>
        <g-app-select
          ref="gdPercent"
          :list="gdPercentList"
          type="gdPercent"
          @choose="choose"
          :chosen="gdPercent"
          @closeSelect="closeSelect"
        ></g-app-select>
      </div>
      <div class="filter-panel invest-filter" v-show="chosenType === 'invest'">
        <g-app-select ref="kg" :list="kgList" type="isKg" @choose="choose" :chosen="isKg" @closeSelect="closeSelect"></g-app-select>
        <g-app-select
          ref="investPercent"
          :list="investPercentList"
          type="investPercent"
          @choose="choose"
          :chosen="investPercent"
          @closeSelect="closeSelect"
        ></g-app-select>
      </div>
      <div class="filter-panel hisgd-filter" v-show="chosenType === 'hisgd'">
        <g-app-select ref="date" :list="hisList" type="date" @choose="choose" :chosen="date" @closeSelect="closeSelect"></g-app-select>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped src="./style.less"></style>
<script src="./component.js"></script>
