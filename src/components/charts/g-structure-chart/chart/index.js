/* eslint-disable class-methods-use-this */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable func-names */
/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-this-alias */
import { EventEmitter } from 'eventemitter3';
import _ from 'lodash';
import * as d3 from 'd3';

import { deviceInfo } from '../../utils/device-info';
import chartSettings from '../settings';
import measure from '../../utils/measure';

export default class Chart extends EventEmitter {
  constructor(selector) {
    super();
    this.selector = selector;
    this.$selector = $(selector);
    this.totalHeight = 0;
    this.isWindows = !deviceInfo.isMacOS();
    this.isIE = deviceInfo.isIE();

    this.stratify = {
      nodeId: (d) => {
        return d.data.id;
      },
      linkId: (d) => {
        return d.target.data.id;
      },
    };
  }

  init() {
    // remove first
    this.$selector.find('svg').remove();
    const width = this.$selector.width();
    const height = this.$selector.height();
    this.size = {
      width,
      height,
    };
    this.svg = d3.select(this.selector).append('svg').attr('width', '100%').attr('cursor', 'move');

    // prepare root g
    this.root = this.svg.append('g').attr('class', 'rootG');
    this.transform = {
      k: 1,
      x: 0,
      y: 0,
    };

    // create g
    this.container = this.root
      .append('g')
      .attr('transform', `translate(${(this.size.width - chartSettings.divWidth) / 2}, ${chartSettings.padding.v})`);
    this.linksContainer = this.container.append('g').classed('links', true);
    this.nodesContainer = this.container.append('g').classed('nodes', true);
    let zoom = null;
    zoom = d3
      .zoom()
      .scaleExtent(chartSettings.scaleRange)
      .on('zoom', () => {
        const transform = d3.zoomTransform(this.svg.node());
        this.root.attr('transform', transform);
        this.emit('onScale', { oldTransform: this.transform, newTransform: { ...transform } });
        this.transform = { ...transform };
      });

    this.scaleStep = (chartSettings.scaleRange[1] - chartSettings.scaleRange[0]) / (1.0 * chartSettings.scaleLevel);

    this.zoom = zoom;
    this.tree = d3.tree();
  }

  render({ data, rule }) {
    this.rule = rule;
    this.data = data;
    this.draw({ data: this.data });
  }

  draw({ source, data }) {
    let i = 0;
    this.rootNode = d3.hierarchy(data.tree).eachBefore((d) => {
      d.index = i++;
    });
    this.tree(this.rootNode);
    this.nodes = this.rootNode.descendants();
    this.links = this.rootNode.links();
    this.update(source);
  }

  update(source) {
    const self = this;
    // 需要prepare一下内容
    this.prepareNodes(this.nodes);
    if (!source) {
      source = this.rootNode;
    }
    // 动态设置滚动条高度0this.rule === 'gd' ? 150 : 80
    const offsetHeight = this.rule === 'gd' ? 150 : 80;
    this.svg.attr('height', this.totalHeight + chartSettings.padding.v * 2 + offsetHeight);

    const node = this.nodesContainer.selectAll('g.node').data(this.nodes, self.stratify.nodeId);

    const nodeEnter = node
      .enter()
      .append('g')
      .classed('node', true)
      .attr('cursor', 'pointer')
      .attr('transform', () => {
        let offsetX = 0;
        let offsetY = 0;
        if (source.depth !== 0) {
          offsetX = source.depth * 20;
          offsetY = source.data.settings.offsetY;
        }
        return `translate(${offsetX}, ${offsetY})`;
      });

    nodeEnter
      .append('rect')
      .attr('class', (d) => `bg-rect-${d.data.id}`)
      .attr('stroke-width', 0.5)
      .attr('stroke', '#d8d8d8')
      .attr('fill', '#fff')
      .attr('width', (d) => d.data.settings.width)
      .attr('height', (d) => d.data.settings.height);

    nodeEnter
      .append('rect')
      .attr('fill', (d) => d.data.settings.borderColor)
      .attr('width', 5)
      .attr('height', (d) => d.data.settings.height);
    // name
    nodeEnter
      .append('text')
      .text((d) => d.data.nameText || d.data.name)
      .attr('class', 'name')
      .attr('fill', (d) => {
        return d.data.keyNo ? '#128bed' : '#333';
      })
      .style('font-size', '14px')
      .attr('transform', (d) => {
        return `translate(${chartSettings.padding.l}, ${d.data.settings.offsetName})`;
      });
    const kgText = nodeEnter
      .filter((d) => +d.data.kg === 1)
      .append('g')
      .attr('class', 'kg')
      .attr('transform', (d) => {
        return `translate(${chartSettings.padding.l + d.data.settings.nameTextSize.width + 8}, ${chartSettings.padding.v})`;
      });

    kgText
      .append('rect')
      .attr('height', (d) => d.data.settings.tagTextHeight)
      .attr('rx', 2)
      .attr('ry', 2)
      .attr('width', (d) => d.data.settings.kgSize.width + 8)
      .attr('fill', '#E5F2FD');
    kgText
      .append('text')
      .text('控股')
      .attr('fill', '#128BED')
      .style('font-size', 12)
      .attr('transform', (d) => {
        return `translate(4, ${d.data.settings.kgSize.height + (deviceInfo.isMacOS() ? 1 : -2)})`;
      });

    // tags
    nodeEnter.each(function (d) {
      const tagG = d3
        .select(this)
        .filter((d) => d.data.tags.length > 0)
        .append('g')
        .attr('class', 'node-tag')
        .attr('transform', `translate(${chartSettings.padding.l}, ${d.data.settings.offsetTag})`);
      if (d.data.tags.length > 0) {
        let offset = 0;
        _.forEach(d.data.tags, (tag) => {
          const singleTag = tagG.append('g').attr('class', 'g');
          singleTag
            .append('rect')
            .attr('height', d.data.settings.tagTextHeight)
            .attr('rx', 2)
            .attr('ry', 2)
            .attr('width', tag.size.width + 8 + (self.isIE ? 10 : 0))
            .attr('fill', tag.color.bg)
            .attr('transform', `translate(${offset}, 0)`);
          singleTag
            .append('text')
            .text(tag.text)
            .attr('fill', tag.color.color)
            .style('font-size', 12)
            .attr('transform', `translate(${offset + 4}, ${tag.size.height + (self.isWindows ? -2 : 1)})`);
          offset += 6 + tag.size.width + 8 + (self.isIE ? 10 : 0);
        });
      }
    });
    // desc
    const descG = nodeEnter
      .filter((d) => d.depth > 0)
      .append('g')
      .attr('class', 'node-desc')
      .attr('transform', (d) => `translate(${chartSettings.padding.l}, ${d.data.settings.offsetDesc})`);

    const percentG = descG
      // .filter(d => d.data.percent)
      .append('g')
      .attr('class', 'percent');
    percentG.append('text').text('持股比例：').attr('fill', '#808080').style('font-size', chartSettings.textSize.tag);

    percentG
      .append('text')
      .text((d) => {
        return d.data.percent === '0%' || d.data.percent === '0.00%' || !d.data.percent ? '-' : d.data.percent;
      })
      .attr('fill', (d) => (d.data.percent === '0%' || d.data.percent === '0.00%' || !d.data.percent ? '#999' : '#FF8900'))
      .style('font-size', chartSettings.textSize.tag)
      .attr('transform', (d) => `translate(${d.data.settings.percentTitleSize.width + (this.isIE ? 5 : 0)}, 0)`);

    const amountG = descG
      // .filter(d => d.data.capi || d.data.stockNum)
      .append('g')
      .attr('class', 'amount')
      .attr('transform', (d) => {
        const offsetX = (d.data.settings.width - chartSettings.padding.l) / 2;
        return `translate(${offsetX}, 0)`;
      });
    amountG
      .append('text')
      .text((d) => {
        if (d.data.stockNum) {
          return '持股数：';
        }
        return '认缴金额：';
      })
      .attr('fill', '#808080')
      .style('font-size', chartSettings.textSize.tag);

    amountG
      .append('text')
      .text((d) => d.data.capi || d.data.stockNum || '-')
      .attr('fill', (d) => (d.data.capi || d.data.stockNum ? '#FF8900' : '#999'))
      .style('font-size', chartSettings.textSize.tag)
      .attr('transform', (d) => `translate(${d.data.settings.amountTitleSize.width + (this.isIE ? 5 : 0)}, 0)`);

    nodeEnter
      .append('rect')
      .attr('class', 'node-handle-rect')
      .attr('fill', 'transparent')
      .attr('width', (d) => d.data.settings.width)
      .attr('height', (d) => d.data.settings.height)
      .on('mouseover', (d) => {
        d3.selectAll(`rect.bg-rect-${d.data.id}`).attr('fill', '#F5F9FF');
        if (!d.data.keyNo) {
          return;
        }
        const $sender = $(window.event.target);
        let position = $sender.position();
        if (position.left === 0 && position.top === 0) {
          // 都是0的情况下，说明没有取到正确值，因此做个补充
          const rect = $sender[0].getBoundingClientRect();
          position = {
            left: rect.x,
            top: rect.y,
          };
        }

        let size = {
          width: $sender.width(),
          height: $sender.height(),
        };
        if (size.width === 0 && size.height === 0) {
          // 都是0的情况下，说明没有取到正确值，因此做个补充
          const rect = $sender[0].getBBox();
          size = {
            width: rect.width,
            height: rect.height,
          };
        }
        this.emit('onNodeMouseover', {
          eid: (d.parent && d.parent.data.keyNo) || '',
          data: d.data,
          position,
          size,
        });
      })
      .on('mouseout', (d) => {
        d3.selectAll(`rect.bg-rect-${d.data.id}`).attr('fill', '#fff');
        if (!d.data.keyNo) {
          return;
        }
        this.emit('onNodeMouseout', {
          data: d.data,
        });
      })
      .on('click', (d) => {
        this.emit('toDetail', d);
      });

    // circle
    const nodeCircle = nodeEnter
      .filter((d) => (d.data.hasChildren && d.data.detailList.length) || (!d.data.dataloaded && d.data.hasChildren))
      .append('g')
      .attr('class', 'circle-g')
      .attr('transform', (d) => `translate(20, ${d.data.settings.height / 2})`);

    nodeCircle
      .append('circle')
      .attr('r', 7)
      .attr('stroke', '#d8d8d8')
      .attr('fill', '#fff')
      .attr('stroke-width', 1)
      .style('font-size', '14px');

    nodeCircle
      .append('line')
      .attr('class', 'horizontal-line')
      .attr('stroke', '#d8d8d8')
      .attr('stroke-width', 1)
      .attr('x1', 4)
      .attr('y1', 0)
      .attr('x2', -4)
      .attr('y2', 0);

    nodeCircle
      .append('line')
      .attr('class', 'vertical-line')
      .attr('stroke', '#d8d8d8')
      .attr('stroke-width', 1)
      .attr('x1', 0)
      .attr('y1', 4)
      .attr('x2', 0)
      .attr('y2', -4)
      .attr('visibility', (d) => {
        return d.data.isFolded ? 'visible' : 'hidden';
      });
    nodeCircle
      .append('circle')
      .attr('class', 'handle-circle')
      .attr('cursor', 'pointer')
      .attr('r', 8)
      .attr('fill', 'transparent')
      .on('click', (d) => {
        if (d.data.dataloaded) {
          if (d.data.children && d.data.hasChildren) {
            d.data.children = null;
            d.data.isFolded = true;
            this.draw({ source: d, data: this.data });
          } else if (_.isEmpty(d.data.children) && d.data.hasChildren) {
            d.data.children = d.data.detailList;
            d.data.isFolded = false;
            this.draw({ source: d, data: this.data });
          }
        } else if (d.data.hasChildren) {
          this.emit('requireData', { d, data: this.data });
        }
      });

    const updateNode = node
      .merge(nodeEnter)
      .transition()
      .duration(chartSettings.duration)
      .ease(d3.easeQuad)
      .attr('transform', (d) => {
        const offsetX = d.depth * 20;
        const offsetY = d.data.settings.offsetY;
        return `translate(${offsetX}, ${offsetY})`;
      });

    updateNode.selectAll('.circle-g').attr('visibility', (d) => {
      if (d.depth === 0) {
        return 'visible';
      }
      if (d.data.hasChildren && !d.data.dataloaded) {
        return 'visible';
      }
      if (d.data.dataloaded && d.data.detailList && d.data.detailList.length) {
        return 'visible';
      }
      return 'hidden';
    });

    node.selectAll('.vertical-line').attr('visibility', (d) => {
      if (!d.data.dataloaded && d.data.hasChildren) {
        return 'visible';
      }
      if (d.data.dataloaded && _.isEmpty(d.data.children) && d.data.detailList.length) {
        return 'visible';
      }
      return 'hidden';
    });

    node
      .exit()
      .transition()
      .duration(chartSettings.duration)
      .ease(d3.easeQuad)
      .attr('transform', () => {
        let offsetX = 0;
        let offsetY = 0;
        if (source.depth !== 0) {
          offsetX = source.depth * 20;
          offsetY = source.data.settings.offsetY;
        }
        return `translate(${offsetX}, ${offsetY})`;
      })
      .remove();

    const link = this.linksContainer.selectAll('g.link').data(this.links, self.stratify.linkId);

    const linkEnter = link.enter().append('g').classed('link', true);

    linkEnter
      .append('path')
      .attr('class', 'link-path')
      .attr('fill', 'none')
      .attr('stroke', chartSettings.borderColor.path)
      .attr('stroke-width', 1)
      .attr('d', () => {
        return this.horizontalPolyline([
          { x: source.depth * 20 + 2, y: source.data.settings.offsetY + source.data.settings.height / 2 },
          { x: source.depth * 20 + 2, y: source.data.settings.offsetY + source.data.settings.height / 2 },
          { x: source.depth * 20 + 2, y: source.data.settings.offsetY + source.data.settings.height / 2 },
        ]);
      });

    const updateLink = link.merge(linkEnter).transition().duration(chartSettings.duration).ease(d3.easeQuad);

    updateLink.selectAll('.link-path').attr('d', (d) => {
      return this.horizontalPolyline([
        { x: d.source.depth * 20 + 2, y: d.source.data.settings.offsetY + d.source.data.settings.height / 2 },
        { x: d.source.depth * 20 + 2, y: d.target.data.settings.offsetY + d.target.data.settings.height / 2 },
        { x: d.target.depth * 20 + 2, y: d.target.data.settings.offsetY + d.target.data.settings.height / 2 },
      ]);
    });

    const exitLink = link.exit().transition().duration(chartSettings.duration).ease(d3.easeQuad).remove();

    exitLink.selectAll('.link-path').attr('d', () => {
      return this.horizontalPolyline([
        { x: source.depth * 20 + 2, y: source.data.settings.offsetY + source.data.settings.height / 2 },
        { x: source.depth * 20 + 2, y: source.data.settings.offsetY + source.data.settings.height / 2 },
        { x: source.depth * 20 + 2, y: source.data.settings.offsetY + source.data.settings.height / 2 },
      ]);
    });

    this.emit('finishDraw');
  }

  prepareNodes(beforeSortedNodes) {
    this.totalHeight = 0;
    const windowsOffset = this.isWindows ? -4 : 0;
    const nodes = _.sortBy(beforeSortedNodes, (d) => d.index);
    const percentTitleSize = measure.getTotal('持股比例：', chartSettings.textSize.tag);
    let amountTitleSize = measure.getTotal('认缴金额：', chartSettings.textSize.tag);
    const temp = amountTitleSize;
    const kgSize = measure.getTotal('控股', chartSettings.textSize.tag);
    const singleTextWidth = measure.getTotal('测', chartSettings.textSize.name);
    let offsetY = 0;
    _.forEach(nodes, (node) => {
      if (node.data.stockNum) {
        amountTitleSize = measure.getTotal('持股数：', chartSettings.textSize.tag);
      } else {
        amountTitleSize = temp;
      }
      const settings = {};
      settings.width = chartSettings.divWidth - node.depth * 20;
      settings.height = 36;
      if (node.data.tags.length) {
        settings.height = 85;
      } else if (!node.data.tags.length) {
        settings.height = 57;
      }
      if (node.depth === 0) {
        settings.height = 36;
      }
      this.totalHeight += settings.height + 8;
      settings.offsetY = offsetY;
      offsetY += settings.height + 8;
      settings.borderColor = node.data.isPerson ? chartSettings.borderColor.person : chartSettings.borderColor.ent;
      settings.nameTextSize = measure.getTotal(node.data.name, chartSettings.textSize.name);
      const maxLength = chartSettings.divWidth - chartSettings.padding.l * 2.5;
      const maxTextLength = Math.floor(maxLength / singleTextWidth.width);
      if (settings.nameTextSize.width > maxLength) {
        node.data.nameText = `${node.data.name.slice(0, maxTextLength - 1)}...`;
      }
      if (node.data.nameText) {
        settings.nameTextSize = measure.getTotal(node.data.nameText, chartSettings.textSize.name);
      }
      settings.tagTextHeight = 20;
      settings.percentTitleSize = percentTitleSize;
      settings.amountTitleSize = amountTitleSize;
      settings.offsetName = chartSettings.padding.v + settings.nameTextSize.height + windowsOffset;
      settings.offsetTag = chartSettings.padding.v * 2 + settings.nameTextSize.height + 1 + windowsOffset + (this.isWindows ? 2 : 0);
      settings.offsetDesc = settings.nameTextSize.height + chartSettings.padding.v * 2 + percentTitleSize.height + windowsOffset * 1.5;
      settings.kgSize = kgSize;
      if (node.data.tags.length) {
        settings.offsetDesc =
          settings.nameTextSize.height +
          chartSettings.padding.v * 3 +
          percentTitleSize.height +
          settings.tagTextHeight +
          windowsOffset * 1.5;
      }
      _.forEach(node.data.tags, (tag) => {
        tag.color = this.getColor(tag.type);
        tag.size = measure.getTotal(tag.text, chartSettings.textSize.tag);
      });
      node.data.settings = settings;
    });
  }

  horizontalPolyline([p1, p2, p3]) {
    const path = d3.path();
    path.moveTo(p1.x, p1.y);
    path.lineTo(p2.x, p2.y);
    path.lineTo(p3.x, p3.y);
    return path;
  }

  getColor(type) {
    let color = '#128BED';
    let bg = '#E5F2FD';
    /* eslint-disable */
    switch (type) {
      case 'large_partner':
        color = '#F04040'
        bg = '#FFECEC'
        break
      case 'kzr':
        color = '#FF8900'
        bg = '#FFEEDB'
        break
      case 'listing_status':
      case 'financing_status':
        color = '#EC9662'
        bg = '#FFF4ED'
        break
    }
    return {
      color, bg
    }
    /* eslint-disable */
  }
}
