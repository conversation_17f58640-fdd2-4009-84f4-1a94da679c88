/* eslint-disable */
import _ from 'lodash'
import tagTypes from '../../utils/tags'
import uuid from 'uuid'


let exitGd = []
let exitTz = []
class FormatGuquan {
  constructor() {
    this.reset()
  }

  init({ keyNo, data, level = 3, exitKeyNos }) {
    this.data = data
    this.level = level
    this.rootData = {
      id: 'root',
      KeyNo: keyNo,
      Name: data.gd?.Name || data.gd?.CompanyName || data.touzi?.Name || data.touzi?.CompanyName || data.CompanyName,
      isRoot: true,
      depth: 0,
      isFolded: false,
      hasIpoPartners: data.gd?.HasIpoPartners,
      hasPledgeGraph: data.gd?.HasPledgeGraph
    }
    this.exitKeyNos = exitKeyNos
    this.entity = 0
    if (!this.rootData.Name) {
      if (data.syr && data.syr.CompanyName) {
        this.rootData.Name = data.syr.CompanyName
      } else if (data.kzr && data.kzr.CompanyName) {
        this.rootData.Name = data.kzr.CompanyName
      } else if (data.kg && data.kg.CompanyName) {
        this.rootData.Name = data.kg.CompanyName
      }
    }
  }

  reset() {
    this.data = {}
    this.rootData = {}
    this.syrKeyNos = []
    this.kzrKeyNos = []
    this.kgKeyNos = []
    this.bigPartnerIds = []
  }

  formatGd() {
    this.filterLevelTotal = [0, 0, 0, 0, 0, 0]
    this.bigPartnerIds = this.data.bigPartnerIds
    this.syrObj = {}
    this.syrKeyNos = this.data?.syr?.Names?.forEach(vo => {
      if (vo.KeyNo) {
        this.syrObj[vo.KeyNo] = vo.PercentTotal
      } else if (vo.Name) {
        this.syrObj[vo.Name] = vo.PercentTotal
      }
    })
    let ctlData = {
      isListed: this.data?.kzr?.IsListed || false,
      isSH: false,
      list: []
    }
    if (this.data?.kzr?.ControllerData) {
      ctlData.list = [this.data?.kzr?.ControllerData]
    }
    if (!this.data?.kzr?.IsListed && this.data?.kzr?.ActualControl) {
      ctlData.isSH = true
      ctlData.list = this.data.kzr.ActualControl.PersonList
    }
    this.kzrKeyNos = _.map(ctlData.list, o => o.KeyNo)
    // this.setLevel({ ...this.rootData, DetailList: this.data?.gd?.EquityShareDetail }, true)
    let gdTree = this.transTree({ ...this.rootData, DetailList: this.data?.gd?.EquityShareDetail }, 0, 0, true, false, this.exitKeyNos)
    let detail = { ...this.rootData, tree: gdTree, syr: this.syrObj, kzr: this.kzrKeyNos, kzrData: ctlData }
    self.postMessage({
      event: 'finishInitGd',
      payload: detail
    })
  }

  formatInvest() {
    this.filterLevelTotal = [0, 0, 0, 0, 0, 0]
    this.kgKeyNos = _.map(this.data?.kg?.Names, 'KeyNo')
    // this.setLevel({ ...this.rootData, DetailList: this.data?.touzi?.EquityShareDetail }, false)
    let investTree = this.transTree({ ...this.rootData, DetailList: this.data?.touzi?.EquityShareDetail }, 0, 0, false, false, this.exitKeyNos)
    let detail = { ...this.rootData, tree: investTree }
    self.postMessage({
      event: 'finishInitInvest',
      payload: detail
    })
  }

  formatHis(res) {
    this.filterLevelTotal = [0, 0, 0, 0, 0, 0]
    let act = {}
    if (res.ChangeDetail?.ActualController) {
      act = JSON.parse(res.ChangeDetail.ActualController)
      this.kzrKeyNos = [act.KeyNo]
    }
    if (res.ChangeDetail?.BenifitList) {
      this.syrObj = {}
      let be = JSON.parse(res.ChangeDetail.BenifitList)
      be.forEach(vo => {
        this.syrObj[vo.KeyNo] = vo.PercentTotal || '0%'
      })
    }
    let gdTree = this.transTree({ ...this.rootData, DetailList: this.data?.EquityShareDetail }, 0, 0, true, true, this.exitKeyNos)
    let detail = { ...this.rootData, tree: gdTree, syr: this.syrObj, kzr: this.kzrKeyNos, kzrData: act, dateList: res.ChangeDateList }
    self.postMessage({
      event: 'finishInitHis',
      payload: detail
    })
  }

  setLevel(_rootNode, isGd) {
    let nodes = []
    nodes.push(_rootNode)
    while (nodes.length) {
      let nextNodes = []
      for (let i = 0; i < nodes.length; i++) {
        let node = nodes[i]
        if (isGd && !_.find(exitGd, o => o.keyNo === node.KeyNo)) {
          exitGd.push({
            keyNo: node.KeyNo,
            level: node.Level || 0
          })
        } else if (!isGd && !_.find(exitTz, o => o.keyNo === node.KeyNo)) {
          exitTz.push({
            keyNo: node.KeyNo,
            level: node.Level || 0
          })
        }
        nextNodes = nextNodes.concat(node.DetailList)
      }
      nodes = nextNodes
    }
  }

  transTree(currentData, depth, index, isGudong = false, isHistory = false, exitKeyNos = []) {
    this.entity += 1
    this.filterLevelTotal[depth]++
    let node = {
      id: uuid(),
      keyNo: currentData.KeyNo,
      name: currentData.Name,
      depth: depth,
      isRoot: currentData.isRoot || false,
      org: currentData.Org,
      entity: this.entity,
      amount: '',
      Level: currentData.Level || 0,
      hasChildren: +currentData.DetailCount > 0 || depth === 0,
      children: null,
      // public: '',
      public: '',
      // financing: '',
      financing: '',
      detailList: [],
      _detailList: [],
      isFolded: depth > 0,
      tags: [],
      labelList: [],
      shortStatus: currentData.ShortStatus,
      percent: currentData.Percent,
      stockType: currentData.StockType,
      dataloaded: false,
      isGudong,
      exitKeyNos: _.cloneDeep(exitKeyNos),
      isPerson: Math.abs(currentData.Org) === 2 || (currentData.KeyNo && currentData.KeyNo[0] === 'p')
    }
    node.capi = currentData.ShouldCapi
    node.stockNum = currentData.StockRightNum
    // tags 按照（地区-是否大股东-实际控制人-最终受益人-融资轮次/上市信息）
    let listingTags = []
    let financingTags = []
    let areaTag = []
    if (!_.isEmpty(currentData.Tags)) {
      _.each(currentData.Tags, tag => {
        let module = tagTypes[tag.Type] && tagTypes[tag.Type].module
        if (module) {
          tag.module = module
          if (module === 'listing_status') {
            listingTags.push(tag)
          } else if (module === 'financing_status') {
            financingTags.push(tag)
          } else if (module === 'area') {
            areaTag.push(tag)
          }
        }
      })
    }
    // 企业地区
    if (!_.isEmpty(areaTag)) {
      _.each(areaTag, tag => {
        node.tags.push({
          type: tag.module,
          text: tag.Name
        })
      })
    }
    if (this.bigPartnerIds.indexOf(node.keyNo || node.name) > -1 && node.percent && node.percent !== '0%' && node.percent !== '0.00%' && isGudong && node.depth === 1 && this.level > 1) {
      node.tags.push({
        type: 'large_partner',
        text: '大股东'
      })
    }
    if (this.kzrKeyNos.indexOf(node.keyNo) > -1 && node.keyNo && node.depth === 1 && this.level > 1) {
      node.kzr = true
      node.tags.push({
        type: 'kzr',
        text: '实际控制人'
      })
    }
    if (this.syrObj && (this.syrObj[node.keyNo] || (!node.keyNo && this.syrObj[node.name])) && node.depth === 1 && this.level > 1) {
      let percent = this.syrObj[node.keyNo] || this.syrObj[node.name]
      if (percent.split('%') && +percent.split('%')[0] && +percent.split('%')[0] > 0) {
        node.syr = this.syrObj[node.keyNo] || this.syrObj[node.name]
      }
      node.tags.push({
        type: 'syr',
        text: '最终受益人'
      })
    }
    // 融资轮次
    if (!_.isEmpty(financingTags) && _.isEmpty(listingTags)) {
      let tempFinancingTag = financingTags[0]
      let text = `${tempFinancingTag.Name}`
      node.financing = text
      node.tags.push({
        type: 'financing_status',
        text
      })
    }
    // 上市状态
    if (!_.isEmpty(listingTags)) {
      node.isPublic = true
      let tempListingTag = listingTags[0]
      let text = `${tempListingTag.Name}|${tempListingTag.ShortName} ${tempListingTag.DataExtend}`
      node.public = text
      node.tags.push({
        type: 'listing_status',
        text
      })
    }
    if (node.keyNo) {
      node.kg = this.kgKeyNos.indexOf(node.keyNo) > -1 ? 1 : 2
    }
    // 控股比例
    if (currentData.Percent || currentData.PercentTotal) {
      node.percent = currentData.Percent || currentData.PercentTotal
    }
    if (!node.hasChildren) {
      node.dataloaded = true
    }
    if (node.hasChildren && currentData.DetailList?.length > 0) {
      currentData.DetailList.forEach((v, index) => {
        node.dataloaded = true
        v.exitKeyNos = _.cloneDeep(node.exitKeyNos)
        if (v.KeyNo && v.exitKeyNos.indexOf(v.KeyNo) > -1) {
          v.DetailList = []
          v.DetailCount = 0
        } else {
          v.exitKeyNos.push(v.KeyNo)
        }
        let obj = this.transTree(v, depth + 1, index, isGudong, isHistory, v.exitKeyNos)// 处理重复股东 在exitGd/tz中一致的不处理 keyNo_level keyNo一致 level不一致的情况不显示加号
        node.detailList.push(obj)
        node._detailList.push(obj)
        if (node.depth < 1) {
          node.children = node.detailList
        }
      })
    }
    return node
  }

  filterGd({ data, item, type, isFolded }) {
    this.filterLevelTotal = [0, 0, 0, 0, 0, 0]
    this.filterData(data.tree, item, type, isFolded)
    return data
  }

  filterData(currentData, item, type) {
    this.filterLevelTotal[currentData.depth]++
    currentData.detailList = _.filter(currentData._detailList, d => {
      let nodePercent = d.percent?.split('%')[0]
      if (type === 'gd') {
        return this.filterGudong({ nodePercent, nodeType: d.stockType }, { filterType: item.type, filterPercent: item.percent })
      } else {
        return this.filterInvest({ nodePercent, nodeType: d.kg }, { filterType: item.type, filterPercent: item.percent })
      }
    })
    if (currentData.detailList.length) {
      currentData.detailList.forEach(v => {
        this.filterData(v, item, type)
      })
    }
  }

  foldData(currentData, isFolded, isHandleFold = false) {
    if (currentData.depth === 0) {
      currentData.children = currentData.detailList
    }
    if (currentData.detailList.length) {
      if (isFolded) {
        // 收起
        currentData.isFolded = currentData.depth > 0 ? isFolded : false
      } else {
        // 展开
        currentData.isFolded = isFolded
      }
      if (!currentData.isFolded) {
        currentData.children = currentData.detailList
      } else {
        currentData.children = null
      }
      currentData.detailList.forEach(v => {
        this.foldData(v, isFolded, isHandleFold)
      })
    } else {
      currentData.children = currentData.detailList || null
    }
  }

  fold(data, isFolded) {
    this.foldData(data.tree, isFolded, true)
    self.postMessage({
      event: 'finishFold',
      payload: data
    })
  }

  filterInvest({ nodePercent, nodeType }, { filterPercent, filterType }) {
    if ((+filterPercent > -1) && (+filterType > 0)) {
      if (+filterPercent === 0 && nodePercent) {
        if (+nodePercent >= 0 && +nodePercent < 5) {
          return true
        }
      } else {
        if (+filterType === +nodeType && +nodePercent >= +filterPercent && nodePercent) {
          return true
        }
      }
    } else if (+filterType > 0) {
      if (+filterType === +nodeType) {
        return true
      }
    } else if ((filterPercent > -1)) {
      if (+filterPercent === 0 && nodePercent) {
        if (+nodePercent >= 0 && +nodePercent < 5) {
          return true
        }
      } else {
        if (+nodePercent >= +filterPercent && nodePercent) {
          return true
        }
      }
    } else {
      return true
    }
  }

  filterGudong({ nodePercent, nodeType }, { filterPercent, filterType }) {
    if (+filterPercent && filterType) {
      if (+nodePercent && filterType === nodeType && +nodePercent >= +filterPercent) {
        return true
      }
    } else if (filterType) {
      if (filterType === nodeType) {
        return true
      }
    } else if (+filterPercent) {
      if (+nodePercent && (+nodePercent >= +filterPercent)) {
        return true
      }
    } else {
      return true
    }
  }
}


let formatter = new FormatGuquan()

self.addEventListener('message', function (e) {
  if (e.data.event === 'initGd') {
    formatter.init(e.data.payload)
    formatter.formatGd()
  }
  else if (e.data.event === 'initInvest') {
    formatter.init(e.data.payload)
    formatter.formatInvest()
  } else if (e.data.event === 'initHis') {
    let { keyNo } = e.data.payload
    let tempData = e.data.payload.data.ChangeDetail.EquityShareDetail
    formatter.init({ keyNo, data: tempData })
    formatter.formatHis(e.data.payload.data)
  } else if (e.data.event === 'filterGd') {
    let { isFolded } = e.data.payload
    let res = formatter.filterGd(e.data.payload)
    formatter.foldData(res.tree, isFolded, false)
    self.postMessage({
      event: 'finishFilterGd',
      payload: res
    })
  } else if (e.data.event === 'foldNodes') {
    let { data, isFolded } = e.data.payload
    formatter.fold(data, isFolded)
  }
})

/* eslint-disable */
