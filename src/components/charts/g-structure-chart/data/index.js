/* eslint-disable array-callback-return */
/* eslint-disable consistent-return */
/* eslint-disable @typescript-eslint/no-unused-vars */
import _ from 'lodash';

import { graph } from '@/shared/services';

const structureWorker = new Worker(new URL('./formatter.chartworker', import.meta.url));

const getHolderData = ({ keyNo, level = 3 }) => {
  return new Promise((resolve, reject) => {
    Promise.all([
      graph.getOwnershipStructure({ keyNo, level }),
      graph.getUltimateBeneficiaryNoPath({ keyNo, level, dataTypes: '0,1,2,3,4,5' }),
      graph.getSuspectedActualControllerNoPathV2({ keyNo }),
    ])
      .then(([gdData, syrData, kzrData]) => {
        let returnData = {
          gd: gdData.Result || {},
          syr: syrData.Result || {},
          kzr: kzrData?.Result || {},
          bigPartnerIds: [],
        };
        if (gdData.Result?.EquityShareDetail?.length && level === 3 && !['t', 'h', 's', 'l'].includes(keyNo[0])) {
          graph
            .getPartnerList({ keyNo, type: 'Priority', pageSize: gdData.Result?.EquityShareDetail?.length })
            .then((partnerData) => {
              const bigPartnerIds = [];
              if (partnerData.Result && partnerData.Result.length) {
                partnerData.Result.map((item) => {
                  if (item.Tags.indexOf('大股东') > -1 && (item.KeyNo || item.StockName)) {
                    bigPartnerIds.push(item.KeyNo || item.StockName);
                  }
                });
              }
              returnData = {
                gd: gdData.Result || {},
                syr: syrData.Result || {},
                kzr: kzrData?.Result || {},
                bigPartnerIds,
              };
              resolve(returnData);
            })
            .catch();
        } else {
          resolve(returnData);
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const getHolders = ({ keyNo, level = 3, exitKeyNos }) => {
  return new Promise((resolve, reject) => {
    getHolderData({ keyNo, level })
      .then((res) => {
        if (_.isEmpty(res.gd)) {
          resolve({});
          return;
        }
        structureWorker.postMessage({
          event: 'initGd',
          payload: { keyNo, data: res, level, exitKeyNos },
        });
        structureWorker.onmessage = (e) => {
          if (e.data.event === 'finishInitGd') {
            resolve(e.data.payload);
          }
        };
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const getInvestData = ({ keyNo, level = 3 }) => {
  return new Promise((resolve, reject) => {
    Promise.all([graph.getEquityInvestment({ keyNo, level }), graph.getHoldingCompany({ keyNo, pageSize: 1000, includePaths: false })])
      .then(([touziData, kgData]) => {
        resolve({
          touzi: touziData.Result || {},
          kg: kgData?.Result || {},
        });
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const getInvest = ({ keyNo, level = 3, exitKeyNos }) => {
  return new Promise((resolve, reject) => {
    getInvestData({ keyNo, level })
      .then((res) => {
        if (_.isEmpty(res.touzi)) {
          resolve({});
          return;
        }
        structureWorker.postMessage({
          event: 'initInvest',
          payload: { keyNo, data: res, level, exitKeyNos },
        });
        structureWorker.onmessage = (e) => {
          if (e.data.event === 'finishInitInvest') {
            resolve(e.data.payload);
          }
        };
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const getHisHolders = ({ keyNo, changeDate, exitKeyNos }) => {
  return new Promise((resolve, reject) => {
    graph
      .getHisHolders({ keyNo, changeDate })
      .then((res) => {
        if (res && !_.isEmpty(res) && res.Result && !_.isEmpty(res.Result)) {
          structureWorker.postMessage({
            event: 'initHis',
            payload: { keyNo, data: res.Result, exitKeyNos },
          });
          structureWorker.onmessage = (e) => {
            if (e.data.event === 'finishInitHis') {
              resolve(e.data.payload);
            }
          };
        } else {
          resolve({});
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};
const loadData = (params) => {
  return new Promise((resolve, reject) => {
    if (params.rule === 'gd') {
      return getHolders({
        keyNo: params.keyNo,
        level: params.level || 3,
        exitKeyNos: params.exitKeyNos,
      }).then((res) => resolve(res));
    }
    if (params.rule === 'invest') {
      return getInvest({ keyNo: params.keyNo, level: params.level || 3, exitKeyNos: params.exitKeyNos }).then((res) => resolve(res));
    }
    if (params.rule === 'hisgd') {
      return getHisHolders({
        keyNo: params.keyNo,
        changeDate: params.changeDate,
        exitKeyNos: params.exitKeyNos,
      }).then((res) => resolve(res));
    }
  });
};

export default {
  loadData,
  getHolders,
  getInvest,
  getHisHolders,
};
