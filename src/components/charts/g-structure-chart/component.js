/* eslint-disable func-names */
/* eslint-disable no-underscore-dangle */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-empty-function */
/* eslint-disable consistent-return */
/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-expressions */
import moment from 'moment';
import _ from 'lodash';

import dataLoader from './data';
import { actionTypes, gUiToolbox, gUiToolboxAction } from '../g-ui-toolbox';
import gUiFooter from '../g-ui-footer';
import gUiLoading from '../g-ui-loading';
import gUiNoData from '../g-ui-no-data';
import appPerson from '../g-app-person';
import appCompany from '../g-app-company';
import Chart from './chart';
import popoverHelper from '../utils/popoverHelper';
import globalUtils from '../utils/utils';
import saveSvg from '../utils/save-svg';
import ruleBtns from './components/rule-btns';
import appStructureSubtext from './components/app-structure-subtext';

const structureWorker = new Worker(new URL('./data/formatter.chartworker', import.meta.url));

export default {
  name: 'g-structure-chart',
  props: {
    actionTypes: {
      type: Object,
      default: () => actionTypes,
    },
    containerName: {
      type: String,
      default: 'structure-chart',
    },
    keyNo: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
    iframe: {
      type: Boolean,
      default: false,
    },
    isPerson: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    [gUiToolbox.name]: gUiToolbox,
    [gUiToolboxAction.name]: gUiToolboxAction,
    [gUiFooter.name]: gUiFooter,
    [gUiLoading.name]: gUiLoading,
    [gUiNoData.name]: gUiNoData,
    [ruleBtns.name]: ruleBtns,
    [appStructureSubtext.name]: appStructureSubtext,
  },
  data() {
    return {
      isFullScreen: false,
      isInit: false,
      isFilterOpened: false,
      isLoading: true,
      noData: false,
      transform: {},
      defaultScale: 1,
      isSaving: false,
      isFolding: false,
      rule: 'gd',
      data: {},
      expendData: {},
      kzr: {},
      dateList: [],
      changeDate: '',
      // baogao
      showReport: false,
      reportDetail: [],
      isFolded: true, // 是否已收起
      hasIpoPartners: false, // 是否有ipopartners 有则没有股东类型筛选
      isMousedown: false,
      mouseStartY: 0,
      hasPledgeGraph: false,
    };
  },
  mounted() {
    $(`.${this.containerName}`).on('click', '.chart-wrap', (event) => {
      if (event.target.localName !== 'circle') {
        this.$refs.rules.closeSelect();
      }
    });
    this.isFullScreen = !!(document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement);
    this.$nextTick(() => {
      document.addEventListener('fullscreenchange', () => {
        this.isFullScreen = !!document.fullscreenElement;
      });
      document.addEventListener('webkitfullscreenchange', () => {
        this.isFullScreen = !!document.webkitFullscreenElement;
      });
      document.addEventListener('mozfullscreenchange', () => {
        this.isFullScreen = !!document.mozFullScreenElement;
      });
      document.addEventListener('MSFullscreenChange', () => {
        this.isFullScreen = !!document.msFullscreenElement;
      });
    });

    this.onRefresh();
  },
  methods: {
    onRefresh() {
      if (this.$refs.rules) {
        this.$refs.rules.closeSelect();
        this.$refs.rules.initSelect(false);
      }
      this.isLoading = true;
      this.noData = false;
      this.chart = new Chart(`#${this.containerName}`);
      this.chart.init();
      this.isFolded = true;
      const exitKeyNos = [this.keyNo];
      dataLoader
        .loadData({ keyNo: this.keyNo, rule: this.rule, changeDate: this.changeDate, exitKeyNos })
        .then((res) => {
          if (_.isEmpty(res)) {
            this.isLoading = false;
            this.noData = true;
          }
          if (res.dateList) {
            this.dateList = res.dateList;
          }
          this.hasIpoPartners = res.hasIpoPartners;
          this.hasPledgeGraph = res.hasPledgeGraph;
          this.kzr = res.kzrData || {};
          this.isInit = true;
          this.isLoading = false;
          this.data = res;
          this.initData = res;
          this.chart.render({ data: res, rule: this.rule });
        })
        .catch(() => {
          // this.$toasted.error('失败！请重试！')
        });

      this.chart.on('toDetail', (d) => {
        if (d.data.keyNo) {
          const isPerson = d.data.keyNo[0] === 'p' || Math.abs(d.data.org) === 2;
          this.$router.push({
            path: `${isPerson ? '/pl' : '/firm'}/${d.data.keyNo}`,
          });
        }
      });
      this.chart.on('onNodeMouseover', ({ eid, data, position, size }) => {
        data.delayTimer = setTimeout(() => {
          const $instance = $(`#detail-popover-${data.id}`);
          if ($instance.length > 0) {
            $instance.stop(true).fadeIn(0);
            return;
          }
          const vmData = { id: data.keyNo, name: data.name, keyNo: data.keyNo };
          if (data.keyNo && data.keyNo[0] === 'p') {
            vmData.eid = eid;
          }
          if (data.org === 6) {
            return;
          }
          popoverHelper.showPopover({
            component: (data.keyNo && data.keyNo[0] === 'p') || Math.abs(data.org) === 2 ? appPerson : appCompany,
            isLeft: false,
            data: vmData,
            targetPosition: {
              left: this.isFullScreen ? position.left : position.left - (180 + 60),
              top: this.isFullScreen ? position.top : position.top - (42 + 42 + 40),
            },
            container: $(`.${this.containerName}`),
            identity: `detail-popover-${data.id}`,
            targetSize: { width: size.width, height: size.height },
          });
        }, 400);
      });
      this.chart.on('onNodeMouseout', ({ data }) => {
        if (data.delayTimer) {
          clearTimeout(data.delayTimer);
          delete data.delayTimer;
        }
        $(`#detail-popover-${data.id}`).fadeOut(400, function () {
          $(this).remove();
        });
      });
      this.chart.on('finishDraw', () => {
        if (this.isFolding) {
          this.isFolding = false;
        }
      });
      this.chart.on('requireData', ({ d, data }) => {
        if (d.data.keyNo) {
          this.getData({ d, data });
        }
      });
    },
    getData({ d, data }) {
      const exitKeyNos = d.data?.exitKeyNos || [];
      dataLoader
        .loadData({ keyNo: d.data.keyNo, rule: this.rule, changeDate: this.changeDate, level: 1, exitKeyNos })
        .then((res) => {
          if (this.rule === 'gd' && (this.filterGdType || this.filterGdPercent)) {
            structureWorker.postMessage({
              event: 'filterGd',
              payload: {
                data: res,
                item: { type: this.filterGdType, percent: this.filterGdPercent },
                type: 'gd',
                isFolded: this.isFolded,
              },
            });
            structureWorker.onmessage = (e) => {
              if (e.data.event === 'finishFilterGd') {
                const returnData = e.data.payload;
                d.children = returnData.tree.children;
                d.data.children = returnData.tree.children;
                d.data.detailList = returnData.tree.children;
                d.data._detailList = returnData.tree.children;
                d.data.dataloaded = true;
                this.chart.draw({ source: d, data });
              }
            };
          } else if (this.rule === 'invest' && (this.filterInvestType || +this.filterInvestPercent >= 0)) {
            structureWorker.postMessage({
              event: 'filterGd',
              payload: {
                data: res,
                item: { type: this.filterInvestType, percent: this.filterInvestPercent },
                type: 'invest',
                isFolded: this.isFolded,
              },
            });
            structureWorker.onmessage = (e) => {
              if (e.data.event === 'finishFilterGd') {
                const returnData = e.data.payload;
                d.children = returnData.tree.children;
                d.data.children = returnData.tree.children;
                d.data.detailList = returnData.tree.children;
                d.data._detailList = returnData.tree.children;
                d.data.dataloaded = true;
                this.chart.draw({ source: d, data });
              }
            };
          } else {
            d.children = res.tree.children;
            d.data.children = res.tree.children;
            d.data.detailList = res.tree.children;
            d.data._detailList = res.tree.children;
            d.data.dataloaded = true;
            this.chart.draw({ source: d, data });
          }
        })
        .catch(() => {
          // this.$toasted.error('失败！请重试！')
        });
    },
    getReport() {
      // if (!this.isLogin) {
      //   uiService.showLoginDialog()
      //   return
      // }
      // this.reportDetail = []
      // Promise.all([
      //   service.getReport({ keyNo: this.companyKeyNo, goodsId: 18 }),
      //   service.getReport({ keyNo: this.companyKeyNo, goodsId: 140 })
      // ])
      //   .then(([res, res1]) => {
      //     if (!_.isEmpty(res.data)) {
      //       this.reportDetail.push(res.data)
      //     }
      //     if (!_.isEmpty(res1.data)) {
      //       this.reportDetail.push(res1.data)
      //     }
      //     if (this.reportDetail.length) {
      //       this.showReport = true
      //     } else {
      //       this.$toasted.error('该企业暂不支持股权穿透报告')
      //     }
      //   })
      //   .catch(err => {
      //     this.$toasted.error(err.message || '请重试！')
      //   })
    },
    onFullScreen() {
      const element = $(`.${this.containerName}`)[0];
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
    },
    onExitFullScreen() {
      if (this.isFullScreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      }
    },
    switchType(type) {
      this.rule = type;
      this.isFolded = true;
      this.onRefresh();
    },
    filterGd(item) {
      this.filterGdType = item.type;
      this.filterGdPercent = item.percent;
      structureWorker.postMessage({
        event: 'filterGd',
        payload: { data: this.initData, item, type: 'gd', isFolded: this.isFolded },
      });
      structureWorker.onmessage = (e) => {
        if (e.data.event === 'finishFilterGd') {
          this.data = e.data.payload;
          this.chart.init();
          this.chart.render({ data: e.data.payload, rule: this.rule });
        }
      };
    },
    filterInvest(item) {
      this.filterInvestType = item.type;
      this.filterInvestPercent = item.percent;
      structureWorker.postMessage({
        event: 'filterGd',
        payload: { data: this.initData, item, type: 'invest', isFolded: this.isFolded },
      });
      structureWorker.onmessage = (e) => {
        if (e.data.event === 'finishFilterGd') {
          this.data = e.data.payload;
          this.chart.init();
          this.chart.render({ data: e.data.payload, rule: this.rule });
        }
      };
    },
    filterHis(item) {
      this.changeDate = item.date;
      this.onRefresh();
    },
    foldNodes() {
      if (this.noData) {
        return;
      }
      this.isFolded = !this.isFolded;
      this.isFolding = true;
      structureWorker.postMessage({
        event: 'foldNodes',
        payload: { data: this.data, isFolded: this.isFolded },
      });
      structureWorker.onmessage = (e) => {
        if (e.data.event === 'finishFold') {
          this.data = e.data.payload;
          this.chart.init();
          this.chart.render({ data: e.data.payload, rule: this.rule });
        }
      };
    },
    onSave() {
      // 保存过程中，不能再保存
      if (this.isSaving) {
        return;
      }

      this.onExitFullScreen();
      this.isSaving = true;

      setTimeout(() => {
        const $svg = $(`#${this.containerName}>svg`);
        const $shadow = $('<div></div>')
          .css({
            width: 0,
            height: 0,
            position: 'fixed',
            top: -1000000,
            let: -1000000,
            zIndex: -1,
            overflow: 'hidden',
          })
          .appendTo('body');

        const $svgClone = $svg.clone().appendTo($shadow);
        const $g = $svgClone.children('g');
        // 移除transparent内容 存图会将其变成黑色
        $g.find('.node-handle-rect').remove();
        $g.find('.handle-circle').remove();
        $g.attr('transform', 'translate(0,0) scale(1)');
        const box = $g[0].getBBox();
        // if (deviceInfo.isIE()) {
        //   box = this.chart.getSize()
        // }
        const contentSize = { width: box.width, height: box.height };
        const padding = { left: 80, right: 80, top: 30, bottom: 60 };
        const rect = {
          top: box.y - padding.top,
          left: box.x - padding.left,
          width: contentSize.width + padding.left + padding.right,
          height: contentSize.height + padding.top + padding.bottom,
        };
        // add water markter
        const n = Math.ceil(rect.width / 240.0);
        const m = Math.ceil(rect.height / 200.0);
        for (let i = 0; i < n; i++) {
          for (let j = 0; j < m; j++) {
            const x = rect.left + 240 * i;
            const y = rect.top + 200 * j;
            $svgClone.prepend(globalUtils.getWaterMarkLogo().clone().attr('transform', `translate(${x}, ${y})`));
          }
        }

        // add white bg
        const $masker = $('<rect></rect>').attr({
          x: rect.left,
          y: rect.top,
          width: rect.width,
          height: rect.height,
          fill: 'rgba(255, 255, 255, 1)',
        });
        $svgClone.prepend($masker);

        // add bottom text
        if (rect.width < 600) {
          const $textFooter = $('<text></text>')
            .attr({
              x: padding.left + contentSize.width / 2 + rect.left,
              y: rect.top + padding.top + contentSize.height + 20,
              'text-anchor': 'middle',
              fill: '#999',
              'font-size': '12px',
            })
            .text('以上数据是企查查大数据分析引擎基于公开信息挖掘的成果，仅供参考');
          $svgClone.append($textFooter);
          const $textFooter1 = $('<text></text>')
            .attr({
              x: padding.left + contentSize.width / 2 + rect.left,
              y: rect.top + padding.top + contentSize.height + 40,
              'text-anchor': 'middle',
              fill: '#999',
              'font-size': '12px',
            })
            .text('该成果不构成任何明示或暗示的观点或保证。');
          $svgClone.append($textFooter1);
        } else {
          const $textFooter = $('<text></text>')
            .attr({
              x: padding.left + contentSize.width / 2 + rect.left,
              y: rect.top + padding.top + contentSize.height + 40,
              'text-anchor': 'middle',
              fill: '#999',
              'font-size': '12px',
            })
            .text('以上数据是企查查大数据分析引擎基于公开信息挖掘的成果，仅供参考。该成果不构成任何明示或暗示的观点或保证。');
          $svgClone.append($textFooter);
        }

        const fileName = `${this.name}-股权结构图-${moment().format('YYYY-MM-DD')}.png`;

        const option = {
          ...rect,
          scale: 1,
          chartType: 'equity_chart',
        };
        saveSvg
          .saveAsImage($svgClone[0], fileName, option)
          .then(() => {
            $shadow.remove();
            this.isSaving = false;
          })
          .catch(() => {
            $shadow.remove();
            this.$toasted.error('由于浏览器大小限制，暂无法为您保存该图片，请筛选隐藏部分节点后重试。');
            // eventBus.triggerToastr(err, 'error')
            this.isSaving = false;
          });
      }, 50);
    },
    onChartTouchStart(event) {
      this.isMousedown = true;
      this.mouseStartY = event.offsetY;
    },
    onChartTouchMove(event) {
      if (this.isMousedown) {
        const mouseEndY = event.offsetY;
        const mouseStarAndEndX = this.mouseStartY - mouseEndY;
        const scrollTop = this.$refs.chartWrap.scrollTop;
        if (mouseStarAndEndX !== 0) {
          const $scrollTop = scrollTop + mouseStarAndEndX;
          if ($scrollTop > 0) {
            this.$refs.chartWrap.scrollTop = $scrollTop;
          }
        }
      }
    },
    onChartTouchEnd(event) {
      this.isMousedown = false;
      this.mouseStartY = 0;
    },
  },
};
