<template>
  <div class="structure-chart-container" :class="containerName">
    <div class="toolbox" v-show="isInit && !noData">
      <g-ui-toolbox>
        <g-ui-toolbox-action
          :action-type="actionTypes.expand"
          :is-busy="isFolding"
          v-if="isFolded"
          @click="foldNodes"
        ></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.fold" :is-busy="isFolding" @click="foldNodes" v-else></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.refresh" @click="onRefresh()"></g-ui-toolbox-action>
        <g-ui-toolbox-action v-if="!isFullScreen && !iframe" :action-type="actionTypes.fullScreen" @click="onFullScreen">
        </g-ui-toolbox-action>
        <g-ui-toolbox-action v-else-if="isFullScreen && !iframe" :action-type="actionTypes.exitFullScreen" @click="onExitFullScreen">
        </g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.save" :is-busy="isSaving" @click="onSave"></g-ui-toolbox-action>
      </g-ui-toolbox>
    </div>
    <structure-chart-rule-btns
      ref="rules"
      v-show="isInit"
      :has-ipo-partners="hasIpoPartners"
      :date-list="dateList"
      @filterHis="filterHis"
      @filterInvest="filterInvest"
      @switchType="switchType"
      @filterGd="filterGd"
    ></structure-chart-rule-btns>
    <!-- 免责 -->
    <g-ui-footer></g-ui-footer>
    <!-- 加载 -->
    <g-ui-loading v-if="isLoading"></g-ui-loading>
    <!-- 暂无数据 -->
    <g-ui-no-data v-if="noData"></g-ui-no-data>
    <structure-chart-subtext
      :has-pledge="hasPledgeGraph"
      :data="kzr"
      :keyNo="keyNo"
      :name="name"
      :iframe="iframe"
      v-if="rule === 'gd' && kzr.list && kzr.list.length"
    ></structure-chart-subtext>
    <div
      class="chart-wrap"
      :class="[isFullScreen && 'no-padding-subtext']"
      ref="chartWrap"
      @mousedown="onChartTouchStart"
      @mousemove="onChartTouchMove"
      @mouseup="onChartTouchEnd"
    >
      <div :id="containerName"></div>
    </div>
  </div>
</template>
<style lang="less" scoped src="./style.less"></style>
<script src="./component.js"></script>
