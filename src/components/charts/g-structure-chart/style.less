.structure-chart-container {
  user-select: none;
  position: absolute;
  inset: 0;
  overflow: hidden;
  background: url("../utils/images/shuiying6.png") repeat;
  background-size: 360px 280px;
  line-height: 1.2;

  .structure-main-wrap {
    height: 100%;
    width: 915px;
    padding-top: 70px;
    margin: 0 auto;
  }

  .show-pledge {
    height: 50px;
    background-color: rgba(255, 255, 255, 0.9);
    width: 100%;
    position: fixed;
    top: 106px;
    text-align: center;
    cursor: pointer;
    padding-bottom: 10px;
    z-index: 22;

    > a {
      background-color: #F6FAFF;
      color: #128BED;
      font-size: 14px;
      line-height: 40px;
      width: 100%;
      display: block;
      height: 40px;
    }
  }

  .chart-wrap {
    height: 100%;
    overflow-y: auto;
    width: 100%;
    position: relative;

    &.has-pledgeGraph {
      padding-top: 155px;
    }
  }

  .no-padding-subtext {
    padding-top: 0;

    ::v-deep.app-structure-subtext-wrap {
      top: 0;
    }
  }

  .chart-footer {
    bottom: 0;
    padding: 10px 0 6px;
    background: #fff;
  }

  #structure-chart {
    height: 100%;
    width: 915px;
    margin: 0 auto;
    position: relative;

    ::-webkit-scrollbar {
      display: none;
      width: 0;
    }

    ::-webkit-scrollbar-track {
      background-color: none;
    }
  }

  .toolbox {
    position: absolute;
    width: 46px;
    right: 20px;
    bottom: 50px;
    font-size: 18px;
    z-index: 20;
  }
}
