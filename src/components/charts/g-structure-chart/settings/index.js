const defaultScale = 0.8;
export default {
  divWidth: 500, // 每个rect的宽度
  debug: false,
  // 默认缩放
  defaultScale,
  // fade效果
  fadeDuration: 400,
  // 缩放范围
  scaleRange: [defaultScale * 0.1, defaultScale * 3],
  // 节点大小，y无效
  nodeSize: [210, 0],
  horizontalNodeSize: [150, 10],
  // 两层之间最小距离
  gap: 110,
  gap1: 145,
  // 横向排版下的最小距离
  gap2: 300,
  // 同级节点距离比
  nodeSeparation: 1.6,
  // 动画持续时间
  duration: 290,
  textSize: {
    name: 14,
    tag: 12,
  },
  borderColor: {
    person: '#FF7777',
    ent: '#359CEF',
    path: '#d8d8d8',
  },
  padding: {
    l: 35,
    v: 8,
  },
};
