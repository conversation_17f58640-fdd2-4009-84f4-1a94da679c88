/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable func-names */
import * as d3 from 'd3';

import chartSettings from '../settings';

const curveCardinalGenerator = d3.line().curve(d3.curveCardinal);
const curveCardinal = ([p1, p2, p3, p4, p5]) => {
  const path = curveCardinalGenerator([
    [p1.x, p1.y],
    [(p3.x + p4.x) / 2, (p3.y + p4.y) / 2],
    [p5.x, p5.y],
  ]);

  return path;
};

const horizontalGenerator = d3
  .linkHorizontal()
  .x(function (d) {
    return d.x;
  })
  .y(function (d) {
    return d.y;
  });

const horizontal = ([p1, p2, p3, p4, p5]) => {
  const pathLine = d3.path();
  pathLine.moveTo(p1.x, p1.y);
  pathLine.lineTo(p2.x, p2.y);

  const path = horizontalGenerator({
    source: p2,
    target: p5,
  });

  return pathLine + path;
};

const horizontalPolyline = ([p1, p2, p3, p4, p5]) => {
  const path = d3.path();
  path.moveTo(p1.x, p1.y);
  path.lineTo(p3.x, p3.y);
  path.lineTo(p4.x, p4.y);
  path.lineTo(p5.x, p5.y);
  return path;
};

const createDefault = ([p1, p2, p3, p4, p5]) => {
  return horizontalPolyline([p1, p2, p3, p4, p5]);
};

export default {
  createDefault,
  curveCardinal,
  horizontal,
  horizontalPolyline,
};
