/* eslint-disable consistent-return */
/* eslint-disable radix */
/* eslint-disable no-use-before-define */
/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable max-len */
import _ from 'lodash';
import uuid from 'uuid';

import settings from '../settings';

const SVG_NS = 'http://www.w3.org/2000/svg';
const createRichNameText = (data) => {
  let ele;
  if (data.richNames) {
    ele = document.createElementNS(SVG_NS, 'text');
    ele.setAttribute('stroke-width', 0);
    ele.setAttribute('style', 'font-family:"微软雅黑", Microsoft YaHei", Arial;');
    for (let i = 0; i < data.richNames.length; ++i) {
      const o = data.richNames[i];
      const tspan = document.createElementNS(SVG_NS, 'tspan');
      tspan.textContent = o.text;
      _.chain(o.props)
        .keys()
        .each((key) => {
          tspan.setAttribute(key, o.props[key]);
        })
        .value();
      // tspan.setAttribute('style', 'font-family:"微软雅黑", Microsoft YaHei", Arial;')
      ele.appendChild(tspan);
    }
  } else {
    ele = document.createElementNS(SVG_NS, 'text');
    ele.setAttribute('stroke-width', 0);
    ele.setAttribute('style', 'font-family:"微软雅黑", Microsoft YaHei", Arial;');
    const tspan = document.createElementNS(SVG_NS, 'tspan');
    tspan.textContent = data.name;
    ele.appendChild(tspan);
  }

  ele.setAttribute('font-size', data.settings.fontSize);
  ele.setAttribute('text-anchor', 'middle');
  ele.setAttribute('dominant-baseline', 'middle');
  return ele;
};

const createText = (text, fontSize = 12) => {
  const ele = document.createElementNS(SVG_NS, 'text');
  ele.setAttribute('stroke-width', 0);
  ele.setAttribute('style', 'font-family:"微软雅黑", Microsoft YaHei", Arial;');
  ele.setAttribute('font-size', fontSize);
  ele.setAttribute('text-anchor', 'middle');
  ele.setAttribute('dominant-baseline', 'middle');

  const tspan = document.createElementNS(SVG_NS, 'tspan');
  tspan.textContent = text;
  ele.appendChild(tspan);
  return ele;
};

const createVipIcon = () => {
  const ele = document.createElementNS(SVG_NS, 'g');
  ele.setAttribute('fill', 'none');
  ele.setAttribute('fill-rule', 'evenodd');
  ele.setAttribute('stroke', 'none');
  ele.setAttribute('stroke-width', 1);

  const path = document.createElementNS(SVG_NS, 'path');
  path.setAttribute('fill', '#D2A874');
  ele.setAttribute('stroke-width', 0);
  path.setAttribute(
    'd',
    'M0,0 L2.32660748,0 L4.23960454,4.69068223 L7.43989711,0 L9.81564448,0 C7.01709513,4.17013148 5.33744494,6.60501711 4.77669393,7.30465689 C4.52863339,7.61415794 4.34106153,7.7441411 4.23960454,7.81617264 C3.57138685,8.29058799 3.2412286,7.8460073 2.62126134,6.49090541 C2.20794983,5.58750415 1.33419605,3.42386901 0,0 Z M10.2413648,0 L12.4265449,0 L11.1997238,7.99331529 L9.01667231,7.99331529 L10.2413648,0 Z M13.4843707,0 L17.8029256,0 C19.3109988,0.388426746 20.0427152,1.31703298 19.9980749,2.7858187 C19.9731602,3.60558041 19.6620749,4.50877056 18.9914285,5.07542255 C18.3874678,5.58572952 17.8029256,5.80476916 17.3026484,5.86664542 C15.6170743,5.86664542 14.7379194,5.86664542 14.6651836,5.86664542 L14.3769944,7.99837119 L12.2902927,7.99837119 L12.9593841,3.8314651 L17.0068434,3.8314651 C17.4832854,3.61252781 17.7486461,3.38604467 17.8029256,3.1520157 C17.9639694,2.45766684 17.6484587,2.0977979 16.6330869,2.0977979 C15.1415065,2.0977979 13.9576257,2.0977979 13.0814444,2.0977979 L13.4843707,0 Z'
  );
  ele.appendChild(path);
  return ele;
};

const createEmptyIcon = () => {
  return document.createElementNS(SVG_NS, 'g');
};

const createRect = ({ x, y, width, height, fill }) => {
  const SVG_NS = 'http://www.w3.org/2000/svg';
  const masker = document.createElementNS(SVG_NS, 'rect');
  masker.setAttribute('x', x);
  masker.setAttribute('y', y);
  masker.setAttribute('width', width);
  masker.setAttribute('height', height);
  masker.setAttribute('fill', fill);
  return masker;
};

const preparePropertyNameList = (entity) => {
  entity.richNames = [];
  entity.richNames.push({
    text: entity.name,
    props: {},
  });
};

const prepareCompanyNameList = (entity) => {
  entity.richNames = [];
  // 公司名称
  const name = entity.name;
  // 股比
  const proportionOfShares = entity.proportionOfShares;
  // 关联机构/产品
  const product = entity.product;
  // 地区名
  const location = entity.location;
  // 上市信息
  const quotedCompanyInfo = entity.quotedCompanyInfo; // || '上市信息';

  if (name) {
    entity.richNames.push({
      text: name,
      props: {
        fill: '#333',
      },
    });
  }

  if (proportionOfShares) {
    entity.richNames.push({
      text: ' [',
      props: {
        fill: '#999',
      },
    });

    entity.richNames.push({
      text: proportionOfShares,
      props: {
        fill: '#F9AD14',
      },
    });

    entity.richNames.push({
      text: ']',
      props: {
        fill: '#999',
      },
    });
  }

  if (product) {
    entity.richNames.push({
      text: ' [',
      props: {
        fill: '#999',
      },
    });

    entity.richNames.push({
      text: product,
      props: {
        fill: '#999',
      },
    });

    entity.richNames.push({
      text: ']',
      props: {
        fill: '#999',
      },
    });
  }

  if (location) {
    entity.richNames.push({
      text: ' [',
      props: {
        fill: '#999',
      },
    });

    entity.richNames.push({
      text: location,
      props: {
        fill: '#6F77D1',
      },
    });

    entity.richNames.push({
      text: ']',
      props: {
        fill: '#999',
      },
    });
  }

  if (quotedCompanyInfo) {
    entity.richNames.push({
      text: ' [',
      props: {
        fill: '#999',
      },
    });

    entity.richNames.push({
      text: quotedCompanyInfo,
      props: {
        fill: '#EC9662',
      },
    });

    entity.richNames.push({
      text: ']',
      props: {
        fill: '#999',
      },
    });
  }
};

const preparePersonNameList = (entity) => {
  entity.richNames = [];
  // 公司名称
  const name = entity.name;
  // 股比
  const proportionOfShares = entity.proportionOfShares;
  // 职位
  const jobTitle = entity.jobTitle;

  if (name) {
    entity.richNames.push({
      text: name,
      props: {
        fill: '#333',
      },
    });
  }

  if (proportionOfShares) {
    entity.richNames.push({
      text: ' [',
      props: {
        fill: '#999',
      },
    });

    entity.richNames.push({
      text: proportionOfShares,
      props: {
        fill: '#F9AD14',
      },
    });

    entity.richNames.push({
      text: ']',
      props: {
        fill: '#999',
      },
    });
  }

  if (jobTitle) {
    entity.richNames.push({
      text: ' [',
      props: {
        fill: '#999',
      },
    });

    entity.richNames.push({
      text: jobTitle,
      props: {
        fill: '#999',
      },
    });

    entity.richNames.push({
      text: ']',
      props: {
        fill: '#999',
      },
    });
  }
};

const prepareOperationMoreOrLessNameList = (entity) => {
  entity.richNames = [];

  if (entity.parent && entity.parent.restChildren && entity.parent.restChildren.length > 0) {
    entity.richNames.push({
      text: `${
        entity.parent.totalChildrenNum > settings.moreOrLessSettings.maxNumForSearch
          ? settings.moreOrLessSettings.textForSearch
          : settings.moreOrLessSettings.textForMore
      }(${entity.parent.restChildren.length})`,
      props: {
        fill: '#999',
      },
    });
  } else {
    entity.richNames.push({
      text: settings.moreOrLessSettings.textForLess,
      props: {
        fill: '#999',
      },
    });
  }
};

const prepareNameList = (entity) => {
  if (isPropertyNode(entity)) {
    preparePropertyNameList(entity);
  } else if (isCompanyDataNode(entity)) {
    prepareCompanyNameList(entity);
  } else if (isPersonDataNode(entity)) {
    preparePersonNameList(entity);
  } else if (isOperationMoreOrLessNode(entity)) {
    prepareOperationMoreOrLessNameList(entity);
  }
};

const createOperationMoreOrLessNode = (text) => {
  return {
    id: uuid(),
    nodeId: uuid(),
    nodeType: 1,
    name: text || settings.moreOrLessSettings.textForMore,
  };
};

const patchNode = (node) => {
  if (node) {
    // node identity
    node.nodeId = uuid();
    // children count
    node.totalChildrenNum = 0;

    if (_.isArray(node.children)) {
      node.totalChildrenNum = node.children.length;
      _.forEach(node.children, (o) => {
        o.parent = node;
        patchNode(o);
      });
      if (node.parent && isPropertyNode(node)) {
        // split children to rest children
        if (node.children.length > settings.moreOrLessSettings.maxNumForMore) {
          const text =
            node.children.length > settings.moreOrLessSettings.maxNumForSearch
              ? settings.moreOrLessSettings.textForSearch
              : settings.moreOrLessSettings.textForMore;
          node.restChildren = _.takeRight(node.children, node.children.length - settings.moreOrLessSettings.maxNumForMore);
          node.children = _.take(node.children, settings.moreOrLessSettings.maxNumForMore);
          const moreOrLessNodeData = createOperationMoreOrLessNode(text);
          moreOrLessNodeData.parent = node;
          node.children.push(moreOrLessNodeData);
        } else {
          node.restChildren = [];
        }
      }
    } else if (!_.isUndefined(node.children)) {
      // for bad data, if children is not array
      delete node.children;
    }
  }
};

const createSettings0 = (isLeft) => {
  return {
    isLeft,
    x: 0,
    y: 0,
    width: 0,
    height: 0,
    maxSliblings: 0,
    textSize: {
      width: 0,
      height: 0,
    },
    topLeft: {
      x: 0,
      y: 0,
    },
    topCenter: {
      x: 0,
      y: 0,
    },
    topRight: {
      x: 0,
      y: 0,
    },
    rightCenter: {
      x: 0,
      y: 0,
    },
    bottomRight: {
      x: 0,
      y: 0,
    },
    bottomCenter: {
      x: 0,
      y: 0,
    },
    bottomLeft: {
      x: 0,
      y: 0,
    },
    leftCenter: {
      x: 0,
      y: 0,
    },
    pointsForLinkIn: [
      { x: 0, y: 0 },
      { x: 0, y: 0 },
      { x: 0, y: 0 },
      { x: 0, y: 0 },
      { x: 0, y: 0 },
    ],
  };
};

const isPropertyNode = (node) => {
  const nodeType = +node.nodeType;
  return nodeType > 100 && nodeType < 300;
};

const isCompanyDataNode = (node) => {
  return +parseInt(node.nodeType) === 999;
};

const isPersonDataNode = (node) => {
  return +parseInt(node.nodeType) === 998;
};

const isDataNode = (node) => {
  return isCompanyDataNode(node) || isPersonDataNode(node);
};

const isOperationMoreOrLessNode = (node) => {
  return +node.nodeType === 1;
};

const plusIsOpen = (d) => {
  if (isDataNode(d.data)) {
    // D3: 当data.children.length === 0 会导致d.children === null, 所以要加后面的补充条件
    return d.children || (d.data.isLoaded && d.data.children);
  }
  if (isPropertyNode(d.data)) {
    return d.children;
  }
  if (isOperationMoreOrLessNode(d.data)) {
    return d.parent.data.totalChildrenNum > settings.moreOrLessSettings.maxNumForMore && d.parent.data.restChildren.length === 0;
  }
};

const isLinkFlowInEnabled = (isLeft, d) => {
  if (!settings.defaultLineSettings.enableFlowAnimation) {
    return false;
  }
  if (isLeft) {
    if (isPropertyNode(d.source.data)) {
      if (d.source.data.settings.arrowInOrOut === 'out') {
        return true;
      }
    }

    if (isPropertyNode(d.target.data)) {
      if (d.target.data.settings.arrowInOrOut === 'out') {
        return true;
      }
    }
  } else {
    if (isPropertyNode(d.source.data)) {
      if (d.source.data.settings.arrowInOrOut === 'in') {
        return true;
      }
    }
    if (isPropertyNode(d.target.data)) {
      if (d.target.data.settings.arrowInOrOut === 'in') {
        return true;
      }
    }
  }
  return false;
};

const isLinkFlowOutEnabled = (isLeft, d) => {
  if (!settings.defaultLineSettings.enableFlowAnimation) {
    return false;
  }
  if (isLeft) {
    if (isPropertyNode(d.source.data)) {
      if (d.source.data.settings.arrowInOrOut === 'in') {
        return true;
      }
    }

    if (isPropertyNode(d.target.data)) {
      if (d.target.data.settings.arrowInOrOut === 'in') {
        return true;
      }
    }
  } else {
    if (isPropertyNode(d.source.data)) {
      if (d.source.data.settings.arrowInOrOut === 'out') {
        return true;
      }
    }
    if (isPropertyNode(d.target.data)) {
      if (d.target.data.settings.arrowInOrOut === 'out') {
        return true;
      }
    }
  }

  return false;
};

const getLogo = () => {
  return $(`<g fill="none" fill-rule="evenodd" stroke="none" stroke-width="1">
  <g>
      <rect width="554" height="554" x="0" y="0" fill="#128BED" rx="100.362319"/>
      <path fill="#fff" d="M391.186578,446.965109 C358.566067,468.923566 319.279651,481.73913 277,481.73913 C163.925701,481.73913 72.2608696,390.074299 72.2608696,277 C72.2608696,163.925701 163.925701,72.2608696 277,72.2608696 C390.074299,72.2608696 481.73913,163.925701 481.73913,277 C481.73913,319.479161 468.802332,358.936772 446.653644,391.647923 L418.339755,363.334034 C433.728014,338.195255 442.597826,308.633455 442.597826,277 C442.597826,185.542846 368.457154,111.402174 277,111.402174 C185.542846,111.402174 111.402174,185.542846 111.402174,277 C111.402174,368.457154 185.542846,442.597826 277,442.597826 C322.63996,442.597826 363.967561,424.134427 393.920996,394.269438 L421.598102,421.946544 C421.585433,421.959183 421.572762,421.971821 421.560089,421.984457 L453.424066,453.848433 L425.746984,481.525515 L391.186578,446.965109 Z M365.534204,365.882646 C342.845608,388.482851 311.554244,402.452899 277,402.452899 C207.714277,402.452899 151.547101,346.285723 151.547101,277 C151.547101,207.714277 207.714277,151.547101 277,151.547101 C311.725291,151.547101 343.15532,165.655798 365.870505,188.453608 L336.064342,218.259771 C320.977266,203.089969 300.08529,193.699275 277,193.699275 C230.99428,193.699275 193.699275,230.99428 193.699275,277 C193.699275,323.00572 230.99428,360.300725 277,360.300725 C299.914242,360.300725 320.667553,351.048674 335.72803,336.076472 L365.534204,365.882646 Z M312.308792,312.657234 C303.241924,321.636051 290.768527,327.181159 277,327.181159 C249.285711,327.181159 226.818841,304.714289 226.818841,277 C226.818841,249.285711 249.285711,226.818841 277,226.818841 C290.939575,226.818841 303.551641,232.50258 312.645127,241.678986 L291.354512,262.969601 C287.710113,259.241544 282.625289,256.927536 277,256.927536 C265.914284,256.927536 256.927536,265.914284 256.927536,277 C256.927536,288.085716 265.914284,297.072464 277,297.072464 C282.454239,297.072464 287.40038,294.897042 291.018092,291.366534 L312.308792,312.657234 Z"/>
      <path fill="#313131" d="M1573.708,498.511 C1573.197,501.157 1570.866,503.156 1568.064,503.156 C1565.263,503.156 1562.932,501.157 1562.421,498.511 L1562.204,498.511 L1562.204,456.606 C1562.204,451.865 1561.819,448.452 1561.051,446.366 C1560.283,444.282 1558.888,442.602 1556.869,441.329 C1554.848,440.056 1552.477,439.419 1549.755,439.419 C1544.837,439.419 1540.753,441.055 1537.503,444.325 C1534.253,447.596 1532.629,452.831 1532.629,460.031 L1532.629,498.511 L1532.052,498.511 C1531.544,501.157 1529.226,503.156 1526.439,503.156 C1523.653,503.156 1521.335,501.157 1520.827,498.511 L1520.773,498.511 L1520.773,498.156 C1520.742,497.915 1520.721,497.671 1520.721,497.422 C1520.721,497.172 1520.742,496.928 1520.773,496.687 L1520.773,455.224 C1520.773,449.955 1519.807,446.004 1517.875,443.37 C1515.942,440.736 1512.78,439.419 1508.39,439.419 C1505.052,439.419 1501.967,440.297 1499.135,442.053 C1496.303,443.809 1494.25,446.377 1492.976,449.758 C1491.702,453.139 1491.066,458.012 1491.066,464.377 L1491.066,498.511 L1490.458,498.511 C1489.949,501.157 1487.624,503.156 1484.83,503.156 C1481.663,503.156 1479.096,500.589 1479.096,497.422 C1479.096,497.031 1479.136,496.65 1479.21,496.281 L1479.21,433.571 C1479.136,433.215 1479.096,432.847 1479.096,432.469 C1479.096,432.091 1479.136,431.722 1479.21,431.366 L1479.21,430.726 L1479.39,430.726 C1480.115,428.614 1482.113,427.094 1484.471,427.094 C1486.828,427.094 1488.826,428.614 1489.551,430.726 L1489.815,430.726 L1489.815,431.898 C1489.835,432.086 1489.846,432.276 1489.846,432.469 C1489.846,432.661 1489.835,432.851 1489.815,433.039 L1489.815,440.538 C1492.01,437.114 1494.93,434.359 1498.575,432.273 C1502.22,430.189 1506.369,429.145 1511.024,429.145 C1516.205,429.145 1520.454,430.222 1523.77,432.372 C1527.085,434.524 1529.423,437.532 1530.785,441.394 C1536.318,433.228 1543.519,429.145 1552.39,429.145 C1559.327,429.145 1564.663,431.067 1568.396,434.908 C1572.128,438.75 1573.995,444.666 1573.995,452.655 L1573.995,498.511 L1573.708,498.511 Z M1441.663,497.963 C1436.503,500.816 1430.871,502.244 1424.768,502.244 C1414.976,502.244 1407.06,499.105 1401.023,492.827 C1394.985,486.549 1391.966,477.505 1391.966,465.695 C1391.966,452.744 1395.566,443.151 1402.768,436.916 C1408.784,431.736 1416.117,429.145 1424.768,429.145 C1434.385,429.145 1442.245,432.296 1448.349,438.596 C1454.452,444.896 1457.505,453.6 1457.505,464.707 C1457.505,473.707 1456.155,480.786 1453.454,485.945 C1450.753,491.104 1446.823,495.11 1441.663,497.963 Z M1439.424,445.642 C1435.494,441.187 1430.608,438.957 1424.768,438.957 C1418.84,438.957 1413.922,441.175 1410.014,445.609 C1406.105,450.044 1404.152,456.738 1404.152,465.695 C1404.152,474.651 1406.105,481.358 1410.014,485.813 C1413.922,490.27 1418.84,492.497 1424.768,492.497 C1430.652,492.497 1435.548,490.258 1439.457,485.78 C1443.365,481.302 1445.319,474.476 1445.319,465.3 C1445.319,456.651 1443.353,450.098 1439.424,445.642 Z M1369.057,479.017 C1367.606,486.224 1364.454,491.935 1359.592,496.145 C1354.345,500.689 1347.901,502.961 1340.26,502.961 C1330.686,502.961 1322.991,499.833 1317.173,493.577 C1311.354,487.321 1308.446,478.354 1308.446,466.675 C1308.446,459.125 1309.697,452.517 1312.2,446.853 C1314.703,441.19 1318.512,436.942 1323.628,434.11 C1328.743,431.279 1334.309,429.863 1340.326,429.863 C1347.922,429.863 1354.136,431.784 1358.967,435.625 C1363.797,439.467 1366.892,444.922 1368.254,451.99 L1367.837,452.054 C1367.839,452.109 1367.846,452.163 1367.846,452.219 C1367.846,455.394 1365.278,457.969 1362.111,457.969 C1359.028,457.969 1356.52,455.526 1356.389,452.466 C1355.25,448.414 1353.421,445.311 1350.898,443.165 C1348.109,440.795 1344.739,439.609 1340.787,439.609 C1334.814,439.609 1329.962,441.749 1326.23,446.03 C1322.497,450.311 1320.631,457.083 1320.631,466.346 C1320.631,475.741 1322.431,482.569 1326.033,486.827 C1329.632,491.086 1334.332,493.214 1340.128,493.214 C1344.782,493.214 1348.669,491.789 1351.787,488.934 C1354.904,486.081 1356.88,481.69 1357.715,475.763 L1358.021,475.803 C1358.685,473.361 1360.912,471.562 1363.565,471.562 C1366.726,471.562 1369.289,474.114 1369.312,477.27 L1369.374,477.278 C1369.349,477.432 1369.319,477.581 1369.293,477.734 C1369.261,478.177 1369.184,478.608 1369.057,479.017 Z M1273.143,504.562 C1269.976,504.562 1267.408,501.995 1267.408,498.828 C1267.408,495.661 1269.976,493.094 1273.143,493.094 C1276.31,493.094 1278.877,495.661 1278.877,498.828 C1278.877,501.995 1276.31,504.562 1273.143,504.562 Z M1238.613,501.381 C1238.1,504.03 1235.77,506.031 1232.971,506.031 C1230.171,506.031 1227.842,504.03 1227.328,501.381 L1226.85,501.381 C1225.62,498.922 1224.83,496.046 1224.479,492.753 C1220.088,496.486 1215.861,499.12 1211.799,500.656 C1207.737,502.193 1203.378,502.961 1198.724,502.961 C1191.04,502.961 1185.133,501.084 1181.006,497.33 C1176.878,493.577 1174.814,488.781 1174.814,482.941 C1174.814,479.517 1175.593,476.389 1177.153,473.557 C1178.711,470.725 1180.753,468.453 1183.278,466.741 C1185.803,465.029 1188.647,463.735 1191.808,462.856 C1194.135,462.241 1197.648,461.649 1202.347,461.078 C1211.92,459.937 1218.968,458.575 1223.491,456.995 C1223.534,455.371 1223.557,454.339 1223.557,453.9 C1223.557,449.071 1222.437,445.668 1220.198,443.692 C1217.168,441.015 1212.666,439.675 1206.695,439.675 C1201.117,439.675 1197.001,440.653 1194.344,442.606 C1191.687,444.559 1189.722,448.017 1188.449,452.978 L1187.732,452.88 C1186.749,454.629 1184.882,455.813 1182.736,455.813 C1180.044,455.813 1177.791,453.949 1177.175,451.44 L1176.856,451.397 C1176.912,451.135 1176.971,450.877 1177.03,450.62 C1177.012,450.436 1177.002,450.251 1177.002,450.062 C1177.002,448.728 1177.459,447.504 1178.22,446.528 C1179.214,443.744 1180.492,441.359 1182.06,439.379 C1184.474,436.328 1187.965,433.979 1192.533,432.332 C1197.099,430.686 1202.391,429.863 1208.407,429.863 C1214.379,429.863 1219.231,430.565 1222.964,431.97 C1226.696,433.376 1229.441,435.142 1231.198,437.272 C1232.953,439.401 1234.183,442.09 1234.886,445.338 C1235.282,447.359 1235.479,451.002 1235.479,456.27 L1235.479,472.075 C1235.479,483.096 1235.731,490.065 1236.237,492.984 C1236.426,494.08 1236.708,495.154 1237.037,496.216 C1238.077,497.256 1238.721,498.694 1238.721,500.281 C1238.721,500.308 1238.717,500.333 1238.717,500.359 C1238.889,500.7 1239.046,501.044 1239.234,501.381 L1238.613,501.381 Z M1223.491,466.28 C1219.187,468.037 1212.732,469.53 1204.126,470.758 C1199.251,471.461 1195.804,472.251 1193.784,473.129 C1191.764,474.008 1190.205,475.292 1189.108,476.982 C1188.01,478.672 1187.461,480.549 1187.461,482.612 C1187.461,485.773 1188.657,488.407 1191.051,490.515 C1193.444,492.622 1196.946,493.676 1201.557,493.676 C1206.123,493.676 1210.186,492.677 1213.743,490.679 C1217.299,488.682 1219.911,485.949 1221.581,482.48 C1222.854,479.803 1223.491,475.852 1223.491,470.627 L1223.491,466.28 Z M1146.143,503.156 C1143.101,503.156 1140.62,500.787 1140.427,497.794 L1140.404,497.794 L1140.404,458.496 C1140.404,452.568 1139.119,448.255 1136.55,445.555 C1133.982,442.855 1130.348,441.505 1125.649,441.505 C1122.136,441.505 1118.832,442.417 1115.736,444.238 C1112.64,446.06 1110.433,448.53 1109.116,451.647 C1107.799,454.764 1107.14,459.066 1107.14,464.554 L1107.14,497.794 L1106.639,497.794 C1106.446,500.787 1103.958,503.156 1100.908,503.156 C1097.733,503.156 1095.158,500.589 1095.158,497.422 C1095.158,497.013 1095.203,496.616 1095.284,496.232 L1095.284,408.924 C1095.203,408.54 1095.158,408.143 1095.158,407.734 C1095.158,407.326 1095.203,406.929 1095.284,406.544 L1095.284,406.273 L1095.354,406.273 C1096.003,403.815 1098.24,402 1100.908,402 C1103.577,402 1105.814,403.815 1106.463,406.273 L1107.14,406.273 L1107.14,440.912 C1112.673,434.503 1119.655,431.298 1128.086,431.298 C1133.267,431.298 1137.769,432.318 1141.589,434.36 C1145.41,436.401 1148.143,439.223 1149.79,442.822 C1151.437,446.422 1152.26,451.647 1152.26,458.496 L1152.26,497.794 L1151.858,497.794 C1151.666,500.787 1149.184,503.156 1146.143,503.156 Z M1072.708,479.033 C1071.256,486.232 1068.104,491.938 1063.247,496.145 C1057.999,500.689 1051.555,502.961 1043.914,502.961 C1034.341,502.961 1026.645,499.833 1020.827,493.577 C1015.008,487.321 1012.1,478.354 1012.1,466.675 C1012.1,459.125 1013.351,452.517 1015.854,446.853 C1018.357,441.19 1022.166,436.942 1027.283,434.11 C1032.398,431.279 1037.963,429.863 1043.98,429.863 C1051.576,429.863 1057.79,431.784 1062.621,435.625 C1067.451,439.467 1070.547,444.922 1071.908,451.99 L1071.494,452.054 C1071.495,452.109 1071.502,452.163 1071.502,452.219 C1071.502,455.394 1068.935,457.969 1065.768,457.969 C1062.687,457.969 1060.181,455.531 1060.046,452.476 C1058.907,448.419 1057.078,445.313 1054.552,443.165 C1051.763,440.795 1048.393,439.609 1044.441,439.609 C1038.469,439.609 1033.616,441.749 1029.884,446.03 C1026.151,450.311 1024.285,457.083 1024.285,466.346 C1024.285,475.741 1026.086,482.569 1029.687,486.827 C1033.287,491.086 1037.986,493.214 1043.783,493.214 C1048.437,493.214 1052.323,491.789 1055.441,488.934 C1058.559,486.081 1060.535,481.69 1061.369,475.763 L1061.678,475.803 C1062.341,473.361 1064.568,471.562 1067.221,471.562 C1070.382,471.562 1072.945,474.114 1072.969,477.27 L1073.028,477.278 C1073.004,477.427 1072.975,477.571 1072.95,477.719 C1072.918,478.173 1072.84,478.614 1072.708,479.033 Z M989.613,501.381 C989.1,504.03 986.77,506.031 983.971,506.031 C981.171,506.031 978.842,504.03 978.329,501.381 L977.862,501.381 C976.633,498.922 975.842,496.046 975.491,492.753 C971.099,496.486 966.873,499.12 962.811,500.656 C958.749,502.193 954.391,502.961 949.737,502.961 C942.052,502.961 936.145,501.084 932.018,497.33 C927.89,493.577 925.826,488.781 925.826,482.941 C925.826,479.517 926.606,476.389 928.165,473.557 C929.723,470.725 931.765,468.453 934.291,466.741 C936.815,465.029 939.659,463.735 942.82,462.856 C945.147,462.241 948.66,461.649 953.359,461.078 C962.932,459.937 969.98,458.575 974.503,456.995 C974.546,455.371 974.569,454.339 974.569,453.9 C974.569,449.071 973.449,445.668 971.21,443.692 C968.18,441.015 963.678,439.675 957.707,439.675 C952.13,439.675 948.013,440.653 945.356,442.606 C942.699,444.559 940.734,448.017 939.461,452.978 L938.735,452.879 C937.755,454.629 935.892,455.813 933.752,455.813 C931.068,455.813 928.822,453.951 928.206,451.443 L927.868,451.397 C927.929,451.112 927.993,450.83 928.058,450.551 C928.044,450.389 928.033,450.227 928.033,450.062 C928.033,448.746 928.478,447.538 929.218,446.568 C930.214,443.767 931.497,441.369 933.072,439.379 C935.486,436.328 938.978,433.979 943.545,432.332 C948.112,430.686 953.403,429.863 959.419,429.863 C965.391,429.863 970.243,430.565 973.976,431.97 C977.708,433.376 980.453,435.142 982.21,437.272 C983.966,439.401 985.195,442.09 985.898,445.338 C986.294,447.359 986.491,451.002 986.491,456.27 L986.491,472.075 C986.491,483.096 986.743,490.065 987.249,492.984 C987.439,494.087 987.724,495.167 988.055,496.236 C989.084,497.275 989.721,498.704 989.721,500.281 C989.721,500.3 989.718,500.318 989.718,500.337 C989.893,500.685 990.054,501.037 990.246,501.381 L989.613,501.381 Z M974.503,466.28 C970.199,468.037 963.744,469.53 955.138,470.758 C950.264,471.461 946.816,472.251 944.796,473.129 C942.776,474.008 941.217,475.292 940.12,476.982 C939.022,478.672 938.473,480.549 938.473,482.612 C938.473,485.773 939.669,488.407 942.063,490.515 C944.456,492.622 947.958,493.676 952.569,493.676 C957.136,493.676 961.198,492.677 964.755,490.679 C968.311,488.682 970.924,485.949 972.593,482.48 C973.866,479.803 974.503,475.852 974.503,470.627 L974.503,466.28 Z M897.158,503.156 C894.109,503.156 891.62,500.787 891.427,497.794 L891.416,497.794 L891.416,497.57 C891.414,497.52 891.408,497.472 891.408,497.422 C891.408,497.371 891.414,497.323 891.416,497.273 L891.416,458.496 C891.416,452.568 890.131,448.255 887.562,445.555 C884.994,442.855 881.36,441.505 876.661,441.505 C873.148,441.505 869.844,442.417 866.748,444.238 C863.652,446.06 861.446,448.53 860.128,451.647 C858.811,454.764 858.152,459.066 858.152,464.554 L858.152,497.794 L857.702,497.794 C857.509,500.787 855.02,503.156 851.971,503.156 C848.795,503.156 846.221,500.589 846.221,497.422 C846.221,497.12 846.251,496.826 846.296,496.538 L846.296,408.619 C846.251,408.33 846.221,408.036 846.221,407.734 C846.221,407.433 846.251,407.139 846.296,406.85 L846.296,406.273 L846.416,406.273 C847.065,403.815 849.302,402 851.971,402 C854.639,402 856.876,403.815 857.525,406.273 L858.152,406.273 L858.152,440.912 C863.685,434.503 870.667,431.298 879.099,431.298 C884.279,431.298 888.781,432.318 892.601,434.36 C896.422,436.401 899.155,439.223 900.802,442.822 C902.449,446.422 903.272,451.647 903.272,458.496 L903.272,497.794 L902.889,497.794 C902.696,500.787 900.208,503.156 897.158,503.156 Z M823.771,478.794 C822.341,486.107 819.171,491.891 814.259,496.145 C809.011,500.689 802.567,502.961 794.926,502.961 C785.353,502.961 777.657,499.833 771.839,493.577 C766.02,487.321 763.112,478.354 763.112,466.675 C763.112,459.125 764.363,452.517 766.866,446.853 C769.369,441.19 773.178,436.942 778.295,434.11 C783.41,431.279 788.975,429.863 794.992,429.863 C802.589,429.863 808.802,431.784 813.633,435.625 C818.463,439.467 821.559,444.922 822.92,451.99 L822.525,452.051 C822.526,452.107 822.533,452.162 822.533,452.219 C822.533,455.394 819.966,457.969 816.799,457.969 C813.746,457.969 811.258,455.575 811.082,452.559 C809.944,448.461 808.107,445.328 805.564,443.165 C802.775,440.795 799.405,439.609 795.453,439.609 C789.481,439.609 784.628,441.749 780.896,446.03 C777.164,450.311 775.298,457.083 775.298,466.346 C775.298,475.741 777.097,482.569 780.699,486.827 C784.299,491.086 788.998,493.214 794.794,493.214 C799.448,493.214 803.335,491.789 806.453,488.934 C809.571,486.081 811.547,481.69 812.382,475.763 L812.708,475.806 C813.369,473.362 815.59,471.562 818.236,471.562 C821.389,471.562 823.945,474.113 823.968,477.268 L824.04,477.278 C824.011,477.459 823.976,477.635 823.945,477.814 C823.916,478.151 823.856,478.477 823.771,478.794 Z M741.44,498.125 C741.44,501.283 738.865,503.844 735.69,503.844 C732.514,503.844 729.939,501.283 729.939,498.125 C729.939,498.013 729.95,497.904 729.956,497.794 L729.741,497.794 L729.741,432.878 L729.942,432.878 C729.942,432.861 729.939,432.845 729.939,432.828 C729.939,429.661 732.514,427.094 735.69,427.094 C738.865,427.094 741.44,429.661 741.44,432.828 C741.44,432.845 741.437,432.861 741.437,432.878 L741.597,432.878 L741.597,497.794 L741.423,497.794 C741.429,497.904 741.44,498.013 741.44,498.125 Z M735.69,421.344 C732.514,421.344 729.939,418.783 729.939,415.625 C729.939,412.467 732.514,409.906 735.69,409.906 C738.865,409.906 741.44,412.467 741.44,415.625 C741.44,418.783 738.865,421.344 735.69,421.344 Z M707.721,528.266 C707.721,528.669 707.678,529.062 707.599,529.441 L707.599,529.618 L707.553,529.618 C706.943,532.131 704.678,534 701.971,534 C699.263,534 696.999,532.131 696.388,529.618 L695.742,529.618 L695.742,495.374 C693.898,497.965 691.318,500.116 688.003,501.827 C684.687,503.54 681.163,504.396 677.431,504.396 C669.132,504.396 661.985,501.081 655.991,494.452 C649.997,487.823 647,478.735 647,467.188 C647,460.164 648.219,453.864 650.656,448.288 C653.093,442.713 656.627,438.487 661.26,435.611 C665.893,432.736 670.976,431.298 676.509,431.298 C685.159,431.298 691.965,434.942 696.928,442.229 L696.928,432.878 L696.96,432.878 C696.95,432.743 696.94,432.607 696.94,432.469 C696.94,429.5 699.353,427.094 702.33,427.094 C705.307,427.094 707.721,429.5 707.721,432.469 C707.721,432.857 707.677,433.235 707.599,433.6 L707.599,527.09 C707.678,527.47 707.721,527.863 707.721,528.266 Z M691.165,447.695 C687.322,442.998 682.81,440.649 677.629,440.649 C672.491,440.649 668.132,442.833 664.554,447.201 C660.974,451.571 659.185,458.387 659.185,467.649 C659.185,476.649 661.073,483.399 664.85,487.899 C668.626,492.4 673.15,494.649 678.419,494.649 C683.468,494.649 687.816,492.509 691.461,488.229 C695.105,483.948 696.928,477.44 696.928,468.703 C696.928,459.396 695.006,452.393 691.165,447.695 Z"/>
      <path fill="#313131" d="M1556.143,100.24 L1556.143,100.747 L1498.46,100.747 L1561.2,155.19 L1560.933,155.5 C1562.134,157.229 1562.844,159.327 1562.844,161.594 C1562.844,167.497 1558.066,172.281 1552.172,172.281 C1550.459,172.281 1548.845,171.867 1547.41,171.148 L1546.977,171.65 L1464.641,100.747 L1442.516,100.747 L1442.516,146.644 L1488.978,146.644 C1510.155,146.644 1528.171,164.37 1528.171,185.578 L1528.171,241.921 C1528.171,263.762 1510.155,281.172 1488.978,281.172 L1378.67,281.172 C1357.493,281.172 1339.794,263.762 1339.794,241.921 L1339.794,185.578 C1339.794,164.37 1357.493,146.644 1378.67,146.644 L1421.023,146.644 L1421.023,100.747 L1404.588,100.747 C1378.67,136.198 1357.177,149.493 1307.871,171.65 L1307.837,171.575 C1306.658,172.024 1305.384,172.281 1304.047,172.281 C1298.153,172.281 1293.375,167.497 1293.375,161.594 C1293.375,157.445 1295.74,153.857 1299.188,152.086 L1299.021,151.709 C1339.161,134.299 1354.965,126.702 1377.406,100.747 L1303.446,100.747 L1303.446,100.251 C1297.833,99.937 1293.375,95.293 1293.375,89.594 C1293.375,83.691 1298.153,78.906 1304.047,78.906 C1304.921,78.906 1305.767,79.023 1306.58,79.222 L1421.023,79.222 L1421.023,60.863 L1421.573,60.863 C1422.438,55.831 1426.799,52 1432.063,52 C1437.948,52 1442.719,56.785 1442.719,62.687 C1442.719,63.393 1442.646,64.082 1442.516,64.75 L1442.516,79.222 L1552.795,79.222 C1553.608,79.023 1554.454,78.906 1555.328,78.906 C1561.222,78.906 1566,83.691 1566,89.594 C1566,95.221 1561.655,99.822 1556.143,100.24 Z M1378.67,168.168 C1368.872,168.168 1361.286,176.082 1361.286,185.578 L1361.286,198.872 C1361.286,198.872 1415.58,198.872 1457.738,198.872 L1479.875,198.872 L1479.875,199.379 C1484.819,200.312 1488.562,204.652 1488.562,209.875 C1488.562,215.098 1484.819,219.438 1479.875,220.371 L1479.875,220.397 L1479.703,220.397 C1479.113,220.498 1478.51,220.562 1477.891,220.562 C1477.272,220.562 1476.668,220.498 1476.078,220.397 L1457.637,220.397 C1415.493,220.397 1361.286,220.397 1361.286,220.397 L1361.286,241.921 C1361.286,251.734 1368.872,259.647 1378.67,259.647 L1488.978,259.647 C1497.966,259.647 1505.087,252.987 1506.204,244.313 L1505.937,244.313 L1505.937,220.397 L1505.937,198.872 L1505.937,184.156 L1506.3,184.156 C1505.587,175.305 1498.282,168.168 1488.978,168.168 L1378.67,168.168 Z M1232.234,321.844 C1231.542,321.844 1230.866,321.772 1230.21,321.646 L1230.21,322.005 L981.148,322.005 L981.148,321.835 C981.088,321.836 981.029,321.844 980.969,321.844 C975.084,321.844 970.312,317.066 970.312,311.172 C970.312,305.278 975.084,300.5 980.969,300.5 C981.029,300.5 981.088,300.508 981.148,300.509 L981.148,300.481 L1230.21,300.481 L1230.21,300.698 C1230.866,300.572 1231.542,300.5 1232.234,300.5 C1238.128,300.5 1242.906,305.278 1242.906,311.172 C1242.906,317.066 1238.128,321.844 1232.234,321.844 Z M1233.055,100.24 L1233.055,100.747 L1175.372,100.747 L1238.112,155.19 L1237.842,155.503 C1239.042,157.232 1239.75,159.329 1239.75,161.594 C1239.75,167.497 1234.972,172.281 1229.078,172.281 C1227.366,172.281 1225.755,171.868 1224.321,171.15 L1223.889,171.65 L1141.553,100.747 L1119.428,100.747 L1119.428,146.644 L1165.89,146.644 C1187.067,146.644 1205.083,164.37 1205.083,185.578 L1205.083,241.921 C1205.083,263.762 1187.067,281.172 1165.89,281.172 L1055.582,281.172 C1034.406,281.172 1016.706,263.762 1016.706,241.921 L1016.706,185.578 C1016.706,164.37 1034.406,146.644 1055.582,146.644 L1097.935,146.644 L1097.935,100.747 L1081.5,100.747 C1055.582,136.198 1034.089,149.493 984.783,171.65 L984.75,171.576 C983.573,172.025 982.302,172.281 980.969,172.281 C975.084,172.281 970.312,167.497 970.312,161.594 C970.312,157.451 972.667,153.867 976.103,152.093 L975.933,151.709 C1016.074,134.299 1031.877,126.702 1054.318,100.747 L980.358,100.747 L980.358,100.25 C974.759,99.931 970.312,95.29 970.312,89.594 C970.312,83.691 975.084,78.906 980.969,78.906 C981.842,78.906 982.686,79.023 983.498,79.222 L1097.935,79.222 L1097.935,60.863 L1098.479,60.863 C1099.345,55.831 1103.713,52 1108.984,52 C1114.878,52 1119.656,56.785 1119.656,62.687 C1119.656,63.441 1119.576,64.176 1119.428,64.886 L1119.428,79.222 L1229.701,79.222 C1230.515,79.023 1231.36,78.906 1232.234,78.906 C1238.128,78.906 1242.906,83.691 1242.906,89.594 C1242.906,95.219 1238.564,99.819 1233.055,100.24 Z M1055.582,168.168 C1045.784,168.168 1038.198,176.082 1038.198,185.578 L1038.198,198.872 C1038.198,198.872 1092.492,198.872 1134.65,198.872 L1156.781,198.872 L1156.781,199.379 C1161.726,200.312 1165.469,204.652 1165.469,209.875 C1165.469,215.098 1161.726,219.438 1156.781,220.371 L1156.781,220.397 L1156.61,220.397 C1156.019,220.498 1155.416,220.562 1154.797,220.562 C1154.178,220.562 1153.575,220.498 1152.984,220.397 L1134.549,220.397 C1092.405,220.397 1038.198,220.397 1038.198,220.397 L1038.198,241.921 C1038.198,251.734 1045.784,259.647 1055.582,259.647 L1165.89,259.647 C1174.878,259.647 1181.999,252.987 1183.116,244.313 L1182.844,244.313 L1182.844,220.397 L1182.844,198.872 L1182.844,184.156 L1183.212,184.156 C1182.499,175.305 1175.194,168.168 1165.89,168.168 L1055.582,168.168 Z M909.734,321.844 C909.139,321.844 908.558,321.783 907.989,321.689 L659.417,321.689 C658.848,321.783 658.268,321.844 657.672,321.844 C651.778,321.844 647,317.066 647,311.172 C647,305.809 650.959,301.384 656.112,300.627 L656.112,300.164 L691.037,300.164 L691.037,204.412 L691.506,204.412 C692.56,199.62 696.819,196.031 701.922,196.031 C707.024,196.031 711.284,199.62 712.338,204.412 L712.846,204.412 L712.846,300.164 L770.686,300.164 L770.686,108.026 L771.276,108.026 C772.262,103.144 776.569,99.469 781.734,99.469 C787.628,99.469 792.406,104.254 792.406,110.156 C792.406,110.908 792.327,111.641 792.179,112.349 L792.179,196.973 L861.325,196.973 C861.906,196.875 862.5,196.812 863.109,196.812 C863.719,196.812 864.312,196.875 864.894,196.973 L865.507,196.973 L865.507,197.093 C870.245,198.184 873.781,202.424 873.781,207.5 C873.781,212.576 870.245,216.816 865.507,217.907 L865.507,218.498 L792.179,218.498 L792.179,300.164 L908.651,300.164 L908.651,300.555 C909.007,300.519 909.369,300.5 909.734,300.5 C915.628,300.5 920.406,305.278 920.406,311.172 C920.406,317.066 915.628,321.844 909.734,321.844 Z M909.734,161.219 C907.478,161.219 905.388,160.513 903.664,159.316 L903.278,159.622 L851.442,95.048 C841.328,82.071 832.162,74.157 811.934,74.157 L754.883,74.157 C734.971,74.157 725.489,82.071 715.374,95.048 L667.074,155.587 C666.468,156.715 665.661,157.713 664.709,158.552 L663.855,159.622 L663.598,159.418 C661.903,160.554 659.865,161.219 657.672,161.219 C651.778,161.219 647,156.434 647,150.531 C647,149.213 647.25,147.956 647.686,146.789 L647.104,146.327 L698.623,81.754 C712.53,64.345 726.437,52.633 754.883,52.633 L811.934,52.633 C840.696,52.633 854.287,64.345 868.194,81.754 L917.773,143.516 C917.947,143.717 918.112,143.925 918.272,144.138 L920.029,146.327 L919.659,146.621 C920.136,147.833 920.406,149.15 920.406,150.531 C920.406,156.434 915.628,161.219 909.734,161.219 Z M1304.047,300.5 C1304.111,300.5 1304.172,300.509 1304.236,300.51 L1304.236,300.481 L1553.298,300.481 L1553.298,300.699 C1553.956,300.572 1554.633,300.5 1555.328,300.5 C1561.222,300.5 1566,305.278 1566,311.172 C1566,317.066 1561.222,321.844 1555.328,321.844 C1554.633,321.844 1553.956,321.772 1553.298,321.645 L1553.298,322.005 L1304.236,322.005 L1304.236,321.834 C1304.172,321.835 1304.111,321.844 1304.047,321.844 C1298.153,321.844 1293.375,317.066 1293.375,311.172 C1293.375,305.278 1298.153,300.5 1304.047,300.5 Z"/>
  </g>
</g>`);
};

const getWaterMarkLogo = () => {
  return $(`<g id="logo水印">
                <rect id="bg" fill="#FFFFFF" x="0" y="0" width="360" height="280"></rect>
                <g id="logo_svg" transform="translate(180.222087, 139.658965) rotate(-30.000000) translate(-180.222087, -139.658965) translate(58.722087, 101.658965)" fill="#F5FAFF">
                    <path d="M62.2318841,2.13162821e-14 C69.8358045,1.99194645e-14 76,6.16419547 76,13.7681159 L76,62.2318841 C76,69.8358045 69.8358045,76 62.2318841,76 L13.7681159,76 C6.16419547,76 2.22474938e-14,69.8358045 2.13162821e-14,62.2318841 L2.13162821e-14,13.7681159 C2.03850704e-14,6.16419547 6.16419547,2.27130996e-14 13.7681159,2.13162821e-14 L62.2318841,2.13162821e-14 Z M38,9.91304348 C22.4880022,9.91304348 9.91304348,22.4880022 9.91304348,38 C9.91304348,53.5119978 22.4880022,66.0869565 38,66.0869565 C43.8000996,66.0869565 49.1895744,64.3288629 53.6645917,61.3165084 L53.6645917,61.3165084 L58.4057235,66.0576519 L62.2025794,62.2607959 L57.8310435,57.8890435 L57.8366223,57.8843041 L54.0397333,54.0874744 C49.9305954,58.184494 44.2610926,60.7173913 38,60.7173913 C25.4535312,60.7173913 15.2826087,50.5464688 15.2826087,38 C15.2826087,25.4535312 25.4535312,15.2826087 38,15.2826087 C50.5464688,15.2826087 60.7173913,25.4535312 60.7173913,38 C60.7173913,42.3396515 59.5005694,46.395099 57.3895052,49.8437628 L57.3895052,49.8437628 L61.2735988,53.7281492 C64.3121607,49.2406449 66.0869565,43.8275853 66.0869565,38 C66.0869565,22.4880022 53.5119978,9.91304348 38,9.91304348 Z M38,20.7898551 C28.4950994,20.7898551 20.7898551,28.4950994 20.7898551,38 C20.7898551,47.5049006 28.4950994,55.2101449 38,55.2101449 C42.5973498,55.2101449 46.773673,53.4075159 49.8607134,50.4705142 L49.8607134,50.4705142 L50.1440435,50.1920435 L46.0567274,46.10418 C43.9906549,48.1582303 41.1435586,49.4275362 38,49.4275362 C31.688746,49.4275362 26.5724638,44.311254 26.5724638,38 C26.5724638,31.688746 31.688746,26.5724638 38,26.5724638 C41.167199,26.5724638 44.0334599,27.8609325 46.1032069,29.9422938 L46.1032069,29.9422938 L50.1918673,25.8530882 C47.0756858,22.7254458 42.7638836,20.7898551 38,20.7898551 Z M38,31.115942 C34.1980398,31.115942 31.115942,34.1980398 31.115942,38 C31.115942,41.8019602 34.1980398,44.884058 38,44.884058 C39.7844805,44.884058 41.4103774,44.2050813 42.6334402,43.0913786 L42.6334402,43.0913786 L42.8430435,42.8910435 L39.9225961,39.9713123 C39.4263447,40.4553757 38.7480021,40.7536232 38,40.7536232 C36.4792159,40.7536232 35.2463768,39.5207841 35.2463768,38 C35.2463768,36.4792159 36.4792159,35.2463768 38,35.2463768 C38.7718093,35.2463768 39.4694545,35.5639116 39.969423,36.0754688 L39.969423,36.0754688 L42.8903798,33.1549566 C41.6428653,31.8958419 39.9125103,31.115942 38,31.115942 Z" id="logo"></path>
                    <path d="M242.954488,70.3540227 C242.873456,70.7749773 242.503813,71.093 242.05948,71.093 C241.615306,71.093 241.245664,70.7749773 241.164631,70.3540227 L241.13022,70.3540227 L241.13022,63.6873182 C241.13022,62.9330682 241.069168,62.3900909 240.947381,62.0582273 C240.825593,61.7266818 240.604379,61.4594091 240.284212,61.2568864 C239.963728,61.0543636 239.587742,60.9530227 239.156096,60.9530227 C238.376215,60.9530227 237.728586,61.2132955 237.213212,61.7335227 C236.697837,62.2539091 236.440308,63.08675 236.440308,64.2322045 L236.440308,70.3540227 L236.348809,70.3540227 C236.268252,70.7749773 235.90067,71.093 235.458717,71.093 C235.016921,71.093 234.64934,70.7749773 234.568783,70.3540227 L234.56022,70.3540227 L234.56022,70.2975455 C234.555304,70.2592045 234.551974,70.2203864 234.551974,70.1807727 C234.551974,70.141 234.555304,70.1021818 234.56022,70.0638409 L234.56022,63.4674545 C234.56022,62.6292045 234.407035,62.0006364 234.100664,61.5815909 C233.794135,61.1625455 233.292715,60.9530227 232.596562,60.9530227 C232.067232,60.9530227 231.578023,61.0927045 231.128933,61.3720682 C230.679843,61.6514318 230.354285,62.0599773 230.152258,62.5978636 C229.950231,63.13575 229.849376,63.911 229.849376,64.9236136 L229.849376,70.3540227 L229.752961,70.3540227 C229.672245,70.7749773 229.303554,71.093 228.86049,71.093 C228.358277,71.093 227.95121,70.6846136 227.95121,70.1807727 C227.95121,70.1185682 227.957553,70.0579545 227.969288,69.99925 L227.969288,60.0226591 C227.957553,59.9660227 227.95121,59.9074773 227.95121,59.8473409 C227.95121,59.7872045 227.957553,59.7285 227.969288,59.6718636 L227.969288,59.5700455 L227.997832,59.5700455 C228.1128,59.2340455 228.429637,58.9922273 228.803561,58.9922273 C229.177327,58.9922273 229.494163,59.2340455 229.609132,59.5700455 L229.650996,59.5700455 L229.650996,59.7565 C229.654167,59.7864091 229.655912,59.8166364 229.655912,59.8473409 C229.655912,59.8778864 229.654167,59.9081136 229.650996,59.9380227 L229.650996,61.1310455 C229.999072,60.5863182 230.462117,60.1480227 231.04013,59.8161591 C231.618142,59.4846136 232.276078,59.3185227 233.014254,59.3185227 C233.835841,59.3185227 234.509634,59.4898636 235.035475,59.8319091 C235.561157,60.1742727 235.93191,60.6528182 236.147892,61.2672273 C237.025298,59.9680909 238.16721,59.3185227 239.573946,59.3185227 C240.673994,59.3185227 241.52016,59.6242955 242.112128,60.2353636 C242.703937,60.8465909 243,61.7877727 243,63.05875 L243,70.3540227 L242.954488,70.3540227 Z M222.015201,70.2668409 C221.196944,70.7207273 220.303839,70.9479091 219.336044,70.9479091 C217.783259,70.9479091 216.527964,70.4485227 215.570635,69.44975 C214.613148,68.4509773 214.134404,67.0121591 214.134404,65.1332955 C214.134404,63.0729091 214.705281,61.54675 215.847352,60.5548182 C216.801351,59.7307273 217.964195,59.3185227 219.336044,59.3185227 C220.861078,59.3185227 222.107492,59.8198182 223.075446,60.8220909 C224.043241,61.8243636 224.527376,63.2090909 224.527376,64.9761136 C224.527376,66.4079318 224.313297,67.5341364 223.884981,68.3548864 C223.456665,69.1756364 222.833458,69.8129545 222.015201,70.2668409 Z M221.660147,61.9430455 C221.03694,61.2342955 220.262133,60.8795227 219.336044,60.8795227 C218.396,60.8795227 217.616119,61.2323864 216.9964,61.9377955 C216.376523,62.6433636 216.066822,63.7083182 216.066822,65.1332955 C216.066822,66.5581136 216.376523,67.6251364 216.9964,68.3338864 C217.616119,69.0429545 218.396,69.39725 219.336044,69.39725 C220.26911,69.39725 221.045503,69.0410455 221.66538,68.3286364 C222.285099,67.6162273 222.594958,66.5302727 222.594958,65.0704545 C222.594958,63.6944773 222.283196,62.6519545 221.660147,61.9430455 Z M210.501566,67.2527045 C210.271471,68.3992727 209.771636,69.3078409 209.000635,69.9776136 C208.168582,70.7005227 207.146713,71.0619773 205.935027,71.0619773 C204.416811,71.0619773 203.196562,70.5643409 202.273962,69.5690682 C201.351203,68.5737955 200.890061,67.1472273 200.890061,65.2892045 C200.890061,64.0880682 201.088441,63.0367955 201.485359,62.1357045 C201.882277,61.2347727 202.486296,60.5589545 203.297575,60.1084091 C204.108696,59.6580227 204.991335,59.43275 205.945493,59.43275 C207.150043,59.43275 208.13544,59.7383636 208.901525,60.3494318 C209.667451,60.9606591 210.158247,61.8285 210.374229,62.9529545 L210.308102,62.9631364 C210.308419,62.9718864 210.309529,62.9804773 210.309529,62.9893864 C210.309529,63.4945 209.902304,63.9041591 209.400091,63.9041591 C208.911198,63.9041591 208.513487,63.5155 208.492714,63.0286818 C208.312094,62.3840455 208.022057,61.8903864 207.621968,61.5489773 C207.179697,61.1719318 206.645293,60.98325 206.018597,60.98325 C205.071417,60.98325 204.302002,61.3237045 203.710193,62.0047727 C203.118225,62.6858409 202.822321,63.7632045 202.822321,65.2368636 C202.822321,66.7315227 203.107759,67.8177955 203.678953,68.4952045 C204.249671,69.1727727 204.994983,69.5113182 205.914094,69.5113182 C206.652111,69.5113182 207.2685,69.2846136 207.762943,68.8304091 C208.257227,68.3765227 208.570575,67.6779545 208.702987,66.7350227 L208.751511,66.7413864 C208.856806,66.3528864 209.209957,66.0666818 209.630661,66.0666818 C210.131923,66.0666818 210.538356,66.4726818 210.542003,66.9747727 L210.551835,66.9760455 C210.54787,67.0005455 210.543113,67.02425 210.53899,67.0485909 C210.533916,67.1190682 210.521705,67.1876364 210.501566,67.2527045 Z M195.29182,71.3166818 C194.789607,71.3166818 194.382382,70.9082955 194.382382,70.4044545 C194.382382,69.9006136 194.789607,69.4922273 195.29182,69.4922273 C195.794033,69.4922273 196.2011,69.9006136 196.2011,70.4044545 C196.2011,70.9082955 195.794033,71.3166818 195.29182,71.3166818 Z M189.81616,70.8106136 C189.73481,71.2320455 189.365326,71.5503864 188.921469,71.5503864 C188.477453,71.5503864 188.108128,71.2320455 188.026619,70.8106136 L187.95082,70.8106136 C187.75577,70.4194091 187.630494,69.9618636 187.574834,69.4379773 C186.878523,70.0318636 186.208218,70.4509091 185.564079,70.6952727 C184.919939,70.9397955 184.228702,71.0619773 183.490685,71.0619773 C182.272181,71.0619773 181.335467,70.7633636 180.68102,70.1661364 C180.026414,69.5690682 179.699112,68.8060682 179.699112,67.8769773 C179.699112,67.33225 179.822643,66.8346136 180.070023,66.3840682 C180.317086,65.9335227 180.6409,65.5720682 181.041307,65.2997045 C181.441713,65.0273409 181.892706,64.8214773 182.393968,64.6816364 C182.762976,64.5837955 183.320057,64.4896136 184.06521,64.3987727 C185.583266,64.21725 186.700916,64.0005682 187.41816,63.7492045 C187.424979,63.4908409 187.428626,63.3266591 187.428626,63.2568182 C187.428626,62.4885682 187.25102,61.9471818 186.895966,61.6328182 C186.415478,61.2069318 185.701565,60.99375 184.754702,60.99375 C183.87016,60.99375 183.217457,61.1493409 182.796119,61.4600455 C182.37478,61.77075 182.063176,62.3208864 181.861308,63.1101364 L181.747608,63.0945455 C181.591727,63.3727955 181.295664,63.5611591 180.955358,63.5611591 C180.528469,63.5611591 180.171195,63.2646136 180.073512,62.8654545 L180.022926,62.8586136 C180.031806,62.8169318 180.041162,62.7758864 180.050518,62.735 C180.047664,62.7057273 180.046078,62.6762955 180.046078,62.6462273 C180.046078,62.434 180.118548,62.2392727 180.239225,62.084 C180.39685,61.6410909 180.599511,61.2616591 180.84816,60.9466591 C181.230965,60.4612727 181.784557,60.0875682 182.508936,59.8255455 C183.232998,59.5636818 184.072187,59.43275 185.026186,59.43275 C185.973207,59.43275 186.742622,59.5444318 187.33459,59.7679545 C187.926399,59.9916364 188.361692,60.2725909 188.640312,60.6114545 C188.918614,60.9501591 189.113664,61.3779545 189.225144,61.8946818 C189.28794,62.2162045 189.31918,62.7957727 189.31918,63.6338636 L189.31918,66.1482955 C189.31918,67.9016364 189.359141,69.0103409 189.439381,69.4747273 C189.469352,69.6490909 189.514071,69.8199545 189.566243,69.9889091 C189.731163,70.1543636 189.833286,70.3831364 189.833286,70.6356136 C189.833286,70.6399091 189.832652,70.6438864 189.832652,70.6480227 C189.859927,70.7022727 189.884824,70.757 189.914636,70.8106136 L189.81616,70.8106136 Z M187.41816,65.2263636 C186.735645,65.5058864 185.712031,65.7434091 184.347318,65.9387727 C183.574256,66.0506136 183.027641,66.1762955 182.707316,66.3159773 C182.38699,66.4558182 182.139769,66.6600909 181.96581,66.9289545 C181.791693,67.1978182 181.704634,67.4964318 181.704634,67.8246364 C181.704634,68.3275227 181.894292,68.7465682 182.273925,69.0819318 C182.653399,69.4171364 183.208736,69.5848182 183.939934,69.5848182 C184.663996,69.5848182 185.308294,69.4258864 185.872352,69.1080227 C186.436252,68.7903182 186.850454,68.3555227 187.115278,67.8036364 C187.317146,67.37775 187.41816,66.7491818 187.41816,65.9179318 L187.41816,65.2263636 Z M175.152553,71.093 C174.670162,71.093 174.276733,70.7161136 174.246128,70.2399545 L174.24248,70.2399545 L174.24248,63.988 C174.24248,63.0449091 174.038709,62.35875 173.631325,61.9292045 C173.224099,61.4996591 172.647831,61.2848864 171.902678,61.2848864 C171.345597,61.2848864 170.821659,61.4299773 170.330705,61.7196818 C169.839751,62.0095455 169.489772,62.4025 169.280926,62.8983864 C169.07208,63.3942727 168.967578,64.0786818 168.967578,64.9517727 L168.967578,70.2399545 L168.888131,70.2399545 C168.857526,70.7161136 168.462986,71.093 167.979327,71.093 C167.475845,71.093 167.06751,70.6846136 167.06751,70.1807727 C167.06751,70.1157045 167.074645,70.0525455 167.08749,69.9914545 L167.08749,56.1015455 C167.074645,56.0404545 167.06751,55.9772955 167.06751,55.9122273 C167.06751,55.8473182 167.074645,55.7841591 167.08749,55.7229091 L167.08749,55.6797955 L167.098591,55.6797955 C167.201507,55.28875 167.556244,55 167.979327,55 C168.402569,55 168.757305,55.28875 168.860221,55.6797955 L168.967578,55.6797955 L168.967578,61.1905455 C169.844984,60.1709318 170.952168,59.6610455 172.28913,59.6610455 C173.110717,59.6610455 173.82463,59.8233182 174.430394,60.1481818 C175.036316,60.4728864 175.469707,60.9218409 175.730883,61.4944091 C175.992059,62.0671364 176.122568,62.8983864 176.122568,63.988 L176.122568,70.2399545 L176.05882,70.2399545 C176.028373,70.7161136 175.634786,71.093 175.152553,71.093 Z M163.507458,67.25525 C163.277204,68.4005455 162.77737,69.3083182 162.007162,69.9776136 C161.17495,70.7005227 160.153081,71.0619773 158.941395,71.0619773 C157.423338,71.0619773 156.20293,70.5643409 155.280329,69.5690682 C154.35757,68.5737955 153.896429,67.1472273 153.896429,65.2892045 C153.896429,64.0880682 154.094808,63.0367955 154.491726,62.1357045 C154.888644,61.2347727 155.492664,60.5589545 156.304102,60.1084091 C157.115223,59.6580227 157.997703,59.43275 158.951861,59.43275 C160.156411,59.43275 161.141808,59.7383636 161.907893,60.3494318 C162.673819,60.9606591 163.164773,61.8285 163.380596,62.9529545 L163.314946,62.9631364 C163.315104,62.9718864 163.316214,62.9804773 163.316214,62.9893864 C163.316214,63.4945 162.909147,63.9041591 162.406934,63.9041591 C161.918359,63.9041591 161.520965,63.5162955 161.499557,63.0302727 C161.318938,62.3848409 161.028901,61.8907045 160.628336,61.5489773 C160.186065,61.1719318 159.65166,60.98325 159.024965,60.98325 C158.077943,60.98325 157.30837,61.3237045 156.71656,62.0047727 C156.124593,62.6858409 155.828688,63.7632045 155.828688,65.2368636 C155.828688,66.7315227 156.114285,67.8177955 156.685321,68.4952045 C157.256198,69.1727727 158.001351,69.5113182 158.920621,69.5113182 C159.658638,69.5113182 160.274868,69.2846136 160.769311,68.8304091 C161.263753,68.3765227 161.577101,67.6779545 161.709354,66.7350227 L161.758355,66.7413864 C161.863491,66.3528864 162.216642,66.0666818 162.637346,66.0666818 C163.138608,66.0666818 163.545041,66.4726818 163.548847,66.9747727 L163.558203,66.9760455 C163.554397,66.99975 163.549798,67.0226591 163.545834,67.0462045 C163.540759,67.1184318 163.52839,67.1885909 163.507458,67.25525 Z M150.33051,70.8106136 C150.24916,71.2320455 149.879676,71.5503864 149.435819,71.5503864 C148.991804,71.5503864 148.622478,71.2320455 148.541128,70.8106136 L148.467073,70.8106136 C148.272182,70.4194091 148.146747,69.9618636 148.091087,69.4379773 C147.394617,70.0318636 146.724471,70.4509091 146.080332,70.6952727 C145.436192,70.9397955 144.745114,71.0619773 144.007097,71.0619773 C142.788434,71.0619773 141.85172,70.7633636 141.197273,70.1661364 C140.542667,69.5690682 140.215365,68.8060682 140.215365,67.8769773 C140.215365,67.33225 140.339055,66.8346136 140.586276,66.3840682 C140.833339,65.9335227 141.157153,65.5720682 141.557718,65.2997045 C141.957966,65.0273409 142.408959,64.8214773 142.910221,64.6816364 C143.279229,64.5837955 143.83631,64.4896136 144.581463,64.3987727 C146.099519,64.21725 147.217169,64.0005682 147.934413,63.7492045 C147.941232,63.4908409 147.944879,63.3266591 147.944879,63.2568182 C147.944879,62.4885682 147.767273,61.9471818 147.412219,61.6328182 C146.931731,61.2069318 146.217818,60.99375 145.270955,60.99375 C144.386572,60.99375 143.733711,61.1493409 143.312372,61.4600455 C142.891033,61.77075 142.579429,62.3208864 142.377561,63.1101364 L142.262434,63.0943864 C142.107029,63.3727955 141.8116,63.5611591 141.472245,63.5611591 C141.046625,63.5611591 140.690461,63.2649318 140.592778,62.8659318 L140.539179,62.8586136 C140.548852,62.8132727 140.559001,62.7684091 140.569308,62.7240227 C140.567088,62.69825 140.565344,62.6724773 140.565344,62.6462273 C140.565344,62.4368636 140.635911,62.2446818 140.753258,62.0903636 C140.9112,61.64475 141.114654,61.26325 141.364413,60.9466591 C141.747218,60.4612727 142.300968,60.0875682 143.025189,59.8255455 C143.74941,59.5636818 144.58844,59.43275 145.542439,59.43275 C146.48946,59.43275 147.258875,59.5444318 147.850843,59.7679545 C148.442652,59.9916364 148.877945,60.2725909 149.156565,60.6114545 C149.435026,60.9501591 149.629917,61.3779545 149.741397,61.8946818 C149.804193,62.2162045 149.835433,62.7957727 149.835433,63.6338636 L149.835433,66.1482955 C149.835433,67.9016364 149.875394,69.0103409 149.955634,69.4747273 C149.985764,69.6502045 150.030958,69.8220227 150.083447,69.9920909 C150.246623,70.1573864 150.347636,70.3847273 150.347636,70.6356136 C150.347636,70.6386364 150.34716,70.6415 150.34716,70.6445227 C150.374911,70.6998864 150.400442,70.7558864 150.430889,70.8106136 L150.33051,70.8106136 Z M147.934413,65.2263636 C147.251898,65.5058864 146.228284,65.7434091 144.863571,65.9387727 C144.090667,66.0506136 143.543894,66.1762955 143.223569,66.3159773 C142.903243,66.4558182 142.656022,66.6600909 142.482063,66.9289545 C142.307946,67.1978182 142.220887,67.4964318 142.220887,67.8246364 C142.220887,68.3275227 142.410545,68.7465682 142.790178,69.0819318 C143.169652,69.4171364 143.724989,69.5848182 144.456187,69.5848182 C145.180408,69.5848182 145.824547,69.4258864 146.388605,69.1080227 C146.952505,68.7903182 147.366866,68.3555227 147.631531,67.8036364 C147.833399,67.37775 147.934413,66.7491818 147.934413,65.9179318 L147.934413,65.2263636 Z M135.669282,71.093 C135.185781,71.093 134.791083,70.7161136 134.760478,70.2399545 L134.758733,70.2399545 L134.758733,70.2043182 C134.758416,70.1963636 134.757465,70.1887273 134.757465,70.1807727 C134.757465,70.1726591 134.758416,70.1650227 134.758733,70.1570682 L134.758733,63.988 C134.758733,63.0449091 134.554962,62.35875 134.147578,61.9292045 C133.740352,61.4996591 133.164084,61.2848864 132.418931,61.2848864 C131.86185,61.2848864 131.337912,61.4299773 130.846958,61.7196818 C130.356004,62.0095455 130.006183,62.4025 129.797179,62.8983864 C129.588333,63.3942727 129.483831,64.0786818 129.483831,64.9517727 L129.483831,70.2399545 L129.412471,70.2399545 C129.381866,70.7161136 128.987168,71.093 128.503667,71.093 C128.000027,71.093 127.59185,70.6846136 127.59185,70.1807727 C127.59185,70.1327273 127.596607,70.0859545 127.603743,70.0401364 L127.603743,56.0530227 C127.596607,56.0070455 127.59185,55.9602727 127.59185,55.9122273 C127.59185,55.8643409 127.596607,55.8175682 127.603743,55.7715909 L127.603743,55.6797955 L127.622773,55.6797955 C127.725689,55.28875 128.080425,55 128.503667,55 C128.92675,55 129.281487,55.28875 129.384403,55.6797955 L129.483831,55.6797955 L129.483831,61.1905455 C130.361237,60.1709318 131.468421,59.6610455 132.805542,59.6610455 C133.62697,59.6610455 134.340883,59.8233182 134.946647,60.1481818 C135.552569,60.4728864 135.98596,60.9218409 136.247136,61.4944091 C136.508312,62.0671364 136.638821,62.8983864 136.638821,63.988 L136.638821,70.2399545 L136.578086,70.2399545 C136.547481,70.7161136 136.152941,71.093 135.669282,71.093 Z M124.031798,67.2172273 C123.805033,68.3806591 123.302345,69.3008409 122.523415,69.9776136 C121.691203,70.7005227 120.669334,71.0619773 119.457648,71.0619773 C117.939591,71.0619773 116.719183,70.5643409 115.796583,69.5690682 C114.873823,68.5737955 114.412682,67.1472273 114.412682,65.2892045 C114.412682,64.0880682 114.611062,63.0367955 115.00798,62.1357045 C115.404898,61.2347727 116.008917,60.5589545 116.820355,60.1084091 C117.631476,59.6580227 118.513956,59.43275 119.468114,59.43275 C120.672822,59.43275 121.658061,59.7383636 122.424146,60.3494318 C123.190072,60.9606591 123.681026,61.8285 123.896849,62.9529545 L123.834212,62.9626591 C123.83437,62.9715682 123.83548,62.9803182 123.83548,62.9893864 C123.83548,63.4945 123.428413,63.9041591 122.9262,63.9041591 C122.442065,63.9041591 122.047526,63.5232955 122.019616,63.0434773 C121.839156,62.3915227 121.54785,61.8930909 121.144589,61.5489773 C120.702318,61.1719318 120.167914,60.98325 119.541218,60.98325 C118.594196,60.98325 117.824623,61.3237045 117.232814,62.0047727 C116.641005,62.6858409 116.3451,63.7632045 116.3451,65.2368636 C116.3451,66.7315227 116.63038,67.8177955 117.201574,68.4952045 C117.772451,69.1727727 118.517604,69.5113182 119.436715,69.5113182 C120.174732,69.5113182 120.791121,69.2846136 121.285564,68.8304091 C121.780006,68.3765227 122.093354,67.6779545 122.225766,66.7350227 L122.277462,66.7418636 C122.382281,66.3530455 122.734481,66.0666818 123.154075,66.0666818 C123.654068,66.0666818 124.059391,66.4725227 124.063038,66.9744545 L124.074456,66.9760455 C124.069857,67.0048409 124.064307,67.0328409 124.059391,67.0613182 C124.054792,67.1149318 124.045277,67.1667955 124.031798,67.2172273 Z M110.976003,70.2926136 C110.976003,70.7950227 110.567668,71.2024545 110.064186,71.2024545 C109.560546,71.2024545 109.15221,70.7950227 109.15221,70.2926136 C109.15221,70.2747955 109.153954,70.2574545 109.154906,70.2399545 L109.120812,70.2399545 L109.120812,59.9124091 L109.152686,59.9124091 C109.152686,59.9097045 109.15221,59.9071591 109.15221,59.9044545 C109.15221,59.4006136 109.560546,58.9922273 110.064186,58.9922273 C110.567668,58.9922273 110.976003,59.4006136 110.976003,59.9044545 C110.976003,59.9071591 110.975527,59.9097045 110.975527,59.9124091 L111.0009,59.9124091 L111.0009,70.2399545 L110.973307,70.2399545 C110.974259,70.2574545 110.976003,70.2747955 110.976003,70.2926136 Z M110.064186,58.0774545 C109.560546,58.0774545 109.15221,57.6700227 109.15221,57.1676136 C109.15221,56.6652045 109.560546,56.2577727 110.064186,56.2577727 C110.567668,56.2577727 110.976003,56.6652045 110.976003,57.1676136 C110.976003,57.6700227 110.567668,58.0774545 110.064186,58.0774545 Z M105.628948,75.0877727 C105.628948,75.1518864 105.62213,75.2144091 105.609602,75.2747045 L105.609602,75.3028636 L105.602307,75.3028636 C105.505576,75.7026591 105.146399,76 104.717131,76 C104.287705,76 103.928687,75.7026591 103.831796,75.3028636 L103.729356,75.3028636 L103.729356,69.8549545 C103.43694,70.2671591 103.027811,70.6093636 102.502129,70.8815682 C101.976288,71.1540909 101.417463,71.2902727 100.825654,71.2902727 C99.5096241,71.2902727 98.376275,70.7628864 97.425765,69.7082727 C96.475255,68.6536591 96,67.2078409 96,65.3708182 C96,64.2533636 96.1933052,63.2510909 96.5797572,62.364 C96.9662091,61.4770682 97.5266199,60.80475 98.2613067,60.3472045 C98.9959935,59.8898182 99.8020399,59.6610455 100.679446,59.6610455 C102.051136,59.6610455 103.130411,60.2407727 103.917428,61.4000682 L103.917428,59.9124091 L103.922502,59.9124091 C103.920917,59.8909318 103.919331,59.8692955 103.919331,59.8473409 C103.919331,59.375 104.301977,58.9922273 104.77406,58.9922273 C105.246144,58.9922273 105.628948,59.375 105.628948,59.8473409 C105.628948,59.9090682 105.621971,59.9692045 105.609602,60.0272727 L105.609602,74.9006818 C105.62213,74.9611364 105.628948,75.0236591 105.628948,75.0877727 Z M103.003549,62.2696591 C102.394138,61.5224091 101.678639,61.1487045 100.857052,61.1487045 C100.042284,61.1487045 99.3510472,61.4961591 98.783659,62.1910682 C98.2159537,62.8862955 97.9322596,63.9706591 97.9322596,65.4441591 C97.9322596,66.8759773 98.2316528,67.9498409 98.8305978,68.66575 C99.4293842,69.3818182 100.146786,69.7396136 100.982328,69.7396136 C101.782983,69.7396136 102.472475,69.3991591 103.050488,68.71825 C103.628342,68.0371818 103.917428,67.0018182 103.917428,65.6118409 C103.917428,64.1311818 103.612643,63.0170682 103.003549,62.2696591 Z" id="qichacha.com"></path>
                    <path d="M240.434035,7.6825244 L240.434035,7.76326735 L231.270033,7.76326735 L241.237432,16.4336586 L241.195014,16.4830281 C241.385815,16.7583823 241.498612,17.092502 241.498612,17.453536 C241.498612,18.393626 240.739539,19.1555082 239.803169,19.1555082 C239.531027,19.1555082 239.274614,19.0895761 239.046638,18.9750708 L238.977848,19.0550175 L225.897264,7.76326735 L222.382303,7.76326735 L222.382303,15.0726542 L229.763643,15.0726542 C233.127998,15.0726542 235.99017,17.8956316 235.99017,21.2731394 L235.99017,30.2461177 C235.99017,33.7244347 233.127998,36.4970871 229.763643,36.4970871 L212.239195,36.4970871 C208.87484,36.4970871 206.063029,33.7244347 206.063029,30.2461177 L206.063029,21.2731394 C206.063029,17.8956316 208.87484,15.0726542 212.239195,15.0726542 L218.967745,15.0726542 L218.967745,7.76326735 L216.356744,7.76326735 C212.239195,13.4090628 208.824638,15.5263754 200.991476,19.0550175 L200.986074,19.0430733 C200.798768,19.1145794 200.59637,19.1555082 200.383963,19.1555082 C199.447593,19.1555082 198.68852,18.393626 198.68852,17.453536 C198.68852,16.7927816 199.064244,16.22137 199.612022,15.939327 L199.585491,15.8792874 C205.962466,13.1066351 208.473221,11.8967649 212.038385,7.76326735 L200.288483,7.76326735 L200.288483,7.68427622 C199.396755,7.63426974 198.68852,6.89468343 198.68852,5.98708172 C198.68852,5.04699172 199.447593,4.28495028 200.383963,4.28495028 C200.522814,4.28495028 200.657217,4.30358327 200.786376,4.33527527 L218.967745,4.33527527 L218.967745,1.41148868 L219.055123,1.41148868 C219.192544,0.610110924 219.885369,0 220.721652,0 C221.656592,0 222.414553,0.762041444 222.414553,1.70197219 C222.414553,1.81440714 222.402955,1.92413474 222.382303,2.03051795 L222.382303,4.33527527 L239.902144,4.33527527 C240.031304,4.30358327 240.165706,4.28495028 240.304557,4.28495028 C241.240927,4.28495028 242,5.04699172 242,5.98708172 C242,6.88321698 241.309717,7.61595526 240.434035,7.6825244 Z M212.239195,18.500487 C210.682603,18.500487 209.477428,19.7608415 209.477428,21.2731394 L209.477428,23.3902928 C209.477428,23.3902928 218.103025,23.3902928 224.800596,23.3902928 L228.317465,23.3902928 L228.317465,23.4710357 C229.10291,23.6196219 229.697554,24.3107942 229.697554,25.14259 C229.697554,25.9743857 229.10291,26.665558 228.317465,26.8141442 L228.317465,26.8182848 L228.290139,26.8182848 C228.196407,26.8343697 228.100609,26.8445621 228.00227,26.8445621 C227.90393,26.8445621 227.807974,26.8343697 227.714242,26.8182848 L224.784551,26.8182848 C218.089203,26.8182848 209.477428,26.8182848 209.477428,26.8182848 L209.477428,30.2461177 C209.477428,31.8088998 210.682603,33.069095 212.239195,33.069095 L229.763643,33.069095 C231.191552,33.069095 232.322853,32.008448 232.500309,30.6270588 L232.457891,30.6270588 L232.457891,26.8182848 L232.457891,23.3902928 L232.457891,21.0466769 L232.51556,21.0466769 C232.402287,19.6370993 231.241754,18.500487 229.763643,18.500487 L212.239195,18.500487 Z M188.975151,42.9743597 C188.865214,42.9743597 188.757819,42.9628933 188.653602,42.942827 L188.653602,43 L149.085536,43 L149.085536,42.9729264 C149.076004,42.9730857 149.066631,42.9743597 149.057099,42.9743597 C148.122159,42.9743597 147.364039,42.2134331 147.364039,41.2747764 C147.364039,40.3361197 148.122159,39.5751931 149.057099,39.5751931 C149.066631,39.5751931 149.076004,39.5764671 149.085536,39.5766264 L149.085536,39.5721672 L188.653602,39.5721672 L188.653602,39.6067258 C188.757819,39.5866595 188.865214,39.5751931 188.975151,39.5751931 C189.911521,39.5751931 190.670594,40.3361197 190.670594,41.2747764 C190.670594,42.2134331 189.911521,42.9743597 188.975151,42.9743597 Z M189.105582,7.6825244 L189.105582,7.76326735 L179.94158,7.76326735 L189.908979,16.4336586 L189.866085,16.4835059 C190.056727,16.75886 190.169206,17.0928205 190.169206,17.453536 C190.169206,18.393626 189.410133,19.1555082 188.473763,19.1555082 C188.20178,19.1555082 187.945843,19.0897354 187.718026,18.9753893 L187.649395,19.0550175 L174.568812,7.76326735 L171.05385,7.76326735 L171.05385,15.0726542 L178.43519,15.0726542 C181.799545,15.0726542 184.661717,17.8956316 184.661717,21.2731394 L184.661717,30.2461177 C184.661717,33.7244347 181.799545,36.4970871 178.43519,36.4970871 L160.910742,36.4970871 C157.546546,36.4970871 154.734577,33.7244347 154.734577,30.2461177 L154.734577,21.2731394 C154.734577,17.8956316 157.546546,15.0726542 160.910742,15.0726542 L167.639293,15.0726542 L167.639293,7.76326735 L165.028292,7.76326735 C160.910742,13.4090628 157.496185,15.5263754 149.663023,19.0550175 L149.65778,19.0432325 C149.470792,19.1147386 149.268871,19.1555082 149.057099,19.1555082 C148.122159,19.1555082 147.364039,18.393626 147.364039,17.453536 C147.364039,16.7937372 147.738174,16.2229625 148.284046,15.9404418 L148.257038,15.8792874 C154.634172,13.1066351 157.144768,11.8967649 160.709933,7.76326735 L148.96003,7.76326735 L148.96003,7.68411696 C148.070527,7.6333142 147.364039,6.89420566 147.364039,5.98708172 C147.364039,5.04699172 148.122159,4.28495028 149.057099,4.28495028 C149.195791,4.28495028 149.329876,4.30358327 149.458877,4.33527527 L167.639293,4.33527527 L167.639293,1.41148868 L167.725717,1.41148868 C167.863297,0.610110924 168.557234,0 169.394629,0 C170.330999,0 171.090072,0.762041444 171.090072,1.70197219 C171.090072,1.82205144 171.077362,1.93910483 171.05385,2.05217681 L171.05385,4.33527527 L188.572738,4.33527527 C188.702057,4.30358327 188.8363,4.28495028 188.975151,4.28495028 C189.911521,4.28495028 190.670594,5.04699172 190.670594,5.98708172 C190.670594,6.88289846 189.980788,7.61547749 189.105582,7.6825244 Z M160.910742,18.500487 C159.35415,18.500487 158.148975,19.7608415 158.148975,21.2731394 L158.148975,23.3902928 C158.148975,23.3902928 166.774572,23.3902928 173.472144,23.3902928 L176.988059,23.3902928 L176.988059,23.4710357 C177.773663,23.6196219 178.368307,24.3107942 178.368307,25.14259 C178.368307,25.9743857 177.773663,26.665558 176.988059,26.8141442 L176.988059,26.8182848 L176.960892,26.8182848 C176.867001,26.8343697 176.771203,26.8445621 176.672864,26.8445621 C176.574524,26.8445621 176.478727,26.8343697 176.384836,26.8182848 L173.456098,26.8182848 C166.760751,26.8182848 158.148975,26.8182848 158.148975,26.8182848 L158.148975,30.2461177 C158.148975,31.8088998 159.35415,33.069095 160.910742,33.069095 L178.43519,33.069095 C179.863099,33.069095 180.9944,32.008448 181.171856,30.6270588 L181.128644,30.6270588 L181.128644,26.8182848 L181.128644,23.3902928 L181.128644,21.0466769 L181.187108,21.0466769 C181.073835,19.6370993 179.913301,18.500487 178.43519,18.500487 L160.910742,18.500487 Z M137.740113,42.9743597 C137.645587,42.9743597 137.553284,42.9646451 137.462888,42.949675 L97.9726681,42.949675 C97.882272,42.9646451 97.7901284,42.9743597 97.6954429,42.9743597 C96.7590729,42.9743597 96,42.2134331 96,41.2747764 C96,40.4206848 96.6289597,39.7159756 97.4476083,39.5954186 L97.4476083,39.5216829 L102.996085,39.5216829 L102.996085,24.2725727 L103.070594,24.2725727 C103.238041,23.5094165 103.914662,22.9378456 104.725367,22.9378456 C105.535913,22.9378456 106.212692,23.5094165 106.380139,24.2725727 L106.460844,24.2725727 L106.460844,39.5216829 L115.649789,39.5216829 L115.649789,8.92249403 L115.743521,8.92249403 C115.900165,8.14500472 116.584411,7.55973778 117.404966,7.55973778 C118.341336,7.55973778 119.100409,8.32177923 119.100409,9.26170997 C119.100409,9.38147071 119.087859,9.49820559 119.064346,9.61095906 L119.064346,23.087865 L130.049456,23.087865 C130.141758,23.0722579 130.236126,23.0622248 130.332877,23.0622248 C130.429787,23.0622248 130.523996,23.0722579 130.616457,23.087865 L130.713843,23.087865 L130.713843,23.1069758 C131.466561,23.2807244 132.02832,23.9559712 132.02832,24.7643562 C132.02832,25.5727412 131.466561,26.247988 130.713843,26.4217366 L130.713843,26.5158571 L119.064346,26.5158571 L119.064346,39.5216829 L137.568059,39.5216829 L137.568059,39.5839521 C137.624616,39.5782189 137.682126,39.5751931 137.740113,39.5751931 C138.676483,39.5751931 139.435556,40.3361197 139.435556,41.2747764 C139.435556,42.2134331 138.676483,42.9743597 137.740113,42.9743597 Z M137.740113,17.3938149 C137.381706,17.3938149 137.049671,17.28138 136.775782,17.0907502 L136.714459,17.1394826 L128.47936,6.85566564 C126.872566,4.7889965 125.416379,3.52864206 122.20279,3.52864206 L113.139193,3.52864206 C109.975806,3.52864206 108.469417,4.7889965 106.862464,6.85566564 L99.189123,16.4968834 C99.0928487,16.6765245 98.964642,16.8354623 98.8133993,16.9690784 L98.6777258,17.1394826 L98.6368966,17.1069943 C98.3676148,17.2879095 98.0438411,17.3938149 97.6954429,17.3938149 C96.7590729,17.3938149 96,16.6317735 96,15.6916835 C96,15.4817837 96.0397171,15.2815985 96.1089837,15.0957464 L96.0165223,15.02217 L104.20126,4.73851225 C106.410642,1.96601915 108.620024,0.100809244 113.139193,0.100809244 L122.20279,0.100809244 C126.772161,0.100809244 128.931341,1.96601915 131.140723,4.73851225 L139.017256,14.5745005 C139.044899,14.606511 139.071112,14.6396363 139.096531,14.6735579 L139.375663,15.02217 L139.316881,15.0689913 C139.392662,15.26201 139.435556,15.4717505 139.435556,15.6916835 C139.435556,16.6317735 138.676483,17.3938149 137.740113,17.3938149 Z M200.383963,39.5751931 C200.394131,39.5751931 200.403822,39.5766264 200.413989,39.5767856 L200.413989,39.5721672 L239.982054,39.5721672 L239.982054,39.6068851 C240.08659,39.5866595 240.194144,39.5751931 240.304557,39.5751931 C241.240927,39.5751931 242,40.3361197 242,41.2747764 C242,42.2134331 241.240927,42.9743597 240.304557,42.9743597 C240.194144,42.9743597 240.08659,42.9628933 239.982054,42.9426677 L239.982054,43 L200.413989,43 L200.413989,42.9727672 C200.403822,42.9729264 200.394131,42.9743597 200.383963,42.9743597 C199.447593,42.9743597 198.68852,42.2134331 198.68852,41.2747764 C198.68852,40.3361197 199.447593,39.5751931 200.383963,39.5751931 Z" id="企查查"></path>
                </g>
            </g>`);
};

export default {
  createRichNameText,
  createText,
  createRect,
  createVipIcon,
  createEmptyIcon,
  getLogo,
  getWaterMarkLogo,
  prepareNameList,
  createSettings0,
  patchNode,
  isPropertyNode,
  isCompanyDataNode,
  isPersonDataNode,
  isDataNode,
  isOperationMoreOrLessNode,
  plusIsOpen,
  isLinkFlowInEnabled,
  isLinkFlowOutEnabled,
};
