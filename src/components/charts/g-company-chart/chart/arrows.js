import * as d3 from 'd3';

import chartSettings from '../settings';

const left = () => {
  const path = d3.path();
  const p1 = [0, 0];
  const p2 = [chartSettings.defaultArrowSettings.width, -chartSettings.defaultArrowSettings.height / 2];
  const p3 = [chartSettings.defaultArrowSettings.width, chartSettings.defaultArrowSettings.height / 2];
  path.moveTo(...p1);
  path.lineTo(...p2);
  path.lineTo(...p3);
  path.closePath();
  return path;
};

const right = () => {
  const path = d3.path();
  const p1 = [0, 0];
  const p2 = [0, chartSettings.defaultArrowSettings.height / 2];
  const p3 = [chartSettings.defaultArrowSettings.width, 0];
  const p4 = [0, -chartSettings.defaultArrowSettings.height / 2];
  path.moveTo(...p1);
  path.lineTo(...p2);
  path.lineTo(...p3);
  path.lineTo(...p4);
  path.closePath();
  return path;
};

const empty = () => {
  const path = d3.path();
  const p1 = [0, 0];
  path.moveTo(...p1);
  return path;
};

export default {
  empty,
  left,
  right,
};
