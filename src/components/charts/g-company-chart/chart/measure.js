/* eslint-disable no-underscore-dangle */
import utils from './utils';

class Measure {
  constructor() {
    const $container = $('<div></div>').css({
      width: 0,
      height: 0,
      top: -200,
      position: 'fixed',
      zIndex: -1,
      overflow: 'hidden',
    });

    $('body').append($container);

    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', 0);
    svg.setAttribute('height', 0);
    $container.append(svg);
    this.svg = svg;

    this._cache = [];
  }

  getSize(data) {
    const key = data.nodeId;
    if (this._cache[key]) {
      return this._cache[key];
    }
    const ele = utils.createRichNameText(data);
    this.svg.appendChild(ele);
    const $ele = $(ele);
    const svgRect = $ele[0].getBBox();
    $ele.remove();
    const size = {
      width: svgRect.width,
      height: svgRect.height,
    };
    this._cache[key] = size;
    return size;
  }
}

export default new Measure();
