/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-this-alias */
/* eslint-disable no-underscore-dangle */
/* eslint-disable no-loop-func */
/* eslint-disable no-param-reassign */
/* eslint-disable no-multi-assign */
/* eslint-disable class-methods-use-this */
/* eslint-disable func-names */
import { EventEmitter } from 'eventemitter3';
import _ from 'lodash';
import resizeDetector from 'element-resize-detector';
import * as d3 from 'd3';

import chartSettings from '../settings';
import chartArrows from './arrows';
import chartLines from './lines';
import filerStore from '../data/filter';
// import { deviceInfo } from '../../../../../core/device-info';
import measure from './measure';
import utils from './utils';

export default class Chart extends EventEmitter {
  constructor(selector) {
    super();
    this.selector = selector;
    this.$selector = $(selector);

    this.stratify = {
      nodeId: (d) => {
        return d.data.nodeId;
      },
      linkId: (d) => {
        return d.target.data.nodeId;
      },
    };
  }

  init() {
    // remove first
    this.$selector.find('svg').remove();

    const width = this.$selector.width();
    const height = this.$selector.height();
    this.size = {
      width,
      height,
    };

    this.svg = d3.select(this.selector).append('svg').attr('width', width).attr('height', height).attr('cursor', 'move');

    // prepare root g
    this.root = this.svg.append('g');

    this.transform = {
      k: 1,
      x: 0,
      y: 0,
    };

    // create right g
    this.rightContainer = this.root.append('g').attr('transform', `translate(${this.size.width / 2}, ${this.size.height / 2})`);
    this.rightLinksContainer = this.rightContainer.append('g').classed('links', true);
    this.rightNodesContainer = this.rightContainer.append('g').classed('nodes', true);

    // create left g
    this.leftContainer = this.root.append('g').attr('transform', `translate(${this.size.width / 2}, ${this.size.height / 2})`);
    this.leftLinksContainer = this.leftContainer.append('g').classed('links', true);
    this.leftNodesContainer = this.leftContainer.append('g').classed('nodes', true);

    let zoom = null;
    zoom = d3
      .zoom()
      .scaleExtent(chartSettings.scaleRange)
      .on('zoom', () => {
        const transform = d3.zoomTransform(this.svg.node());
        this.root.attr('transform', transform);
        this.emit('onScale', { oldTransform: this.transform, newTransform: { ...transform } });
        this.transform = { ...transform };
      });

    this.scaleStep = (chartSettings.scaleRange[1] - chartSettings.scaleRange[0]) / (1.0 * chartSettings.scaleLevel);

    // enable zoom
    this.svg
      .call(zoom)
      // disable dbclick for zooming
      .on('dblclick.zoom', null);

    this.zoom = zoom;

    this.tree = d3
      .tree()
      .nodeSize(chartSettings.nodeSize)
      .separation((a, b) => {
        return a.parent === b.parent ? 1 : chartSettings.nodeSeparation;
      });

    this.attachResizeDetector();
  }

  attachResizeDetector() {
    this.resizeDetector = resizeDetector();
    this.resizeDetector.listenTo(
      this.$selector[0],
      _.debounce(() => {
        this.size = {
          width: this.$selector.width(),
          height: this.$selector.height(),
        };
        this.svg.attr('width', this.size.width).attr('height', this.size.height);
        this.leftContainer.attr('transform', `translate(${this.size.width / 2}, ${this.size.height / 2})`);
        this.rightContainer.attr('transform', `translate(${this.size.width / 2}, ${this.size.height / 2})`);
      }, 200)
    );
  }

  cleanup() {
    this.resizeDetector.removeAllListeners(this.$selector[0]);
  }

  render(data) {
    this.data = data;
    this.updateTree(true);
    this.updateTree(false);
  }

  refresh() {
    this.updateTree(true);
    this.updateTree(false);
  }

  prepareNodes(nodes, isLeft) {
    nodes.forEach((d) => {
      let globalSettings = chartSettings.nodeSettings[d.data.nodeType] || chartSettings.nodeSettings.default;
      if (d.depth === 0) {
        globalSettings = { ...chartSettings.nodeSettings.default };
        globalSettings.plusEnabled = false; // all nodes can be expand, except root node
      }

      d.data.settings = _.assignIn({}, globalSettings, {
        depth: d.depth,
        isLeft,
        x: 0,
        y: 0,
        width: 0,
        height: 0,
        maxSliblings: 0,
        textSize: { width: 0, height: 0 },
        topLeft: { x: 0, y: 0 },
        topCenter: { x: 0, y: 0 },
        topRight: { x: 0, y: 0 },
        rightCenter: { x: 0, y: 0 },
        bottomRight: { x: 0, y: 0 },
        bottomCenter: { x: 0, y: 0 },
        bottomLeft: { x: 0, y: 0 },
        leftCenter: { x: 0, y: 0 },
      });

      if (utils.isDataNode(d.data) && (!d.data.id || !d.data.nGraph)) {
        d.data.settings.plusEnabled = false;
      }

      // root node should not process richName
      utils.prepareNameList(d.data);
      d.data.settings.textSize = measure.getSize(d.data);

      // control position
      const x = d.x;
      const y = d.y;
      d.x = isLeft ? -y : y;
      d.y = d.data.settings.y = x;

      if (d.depth === 0) {
        d.x = isLeft ? -d.data.settings.width / 2 : d.data.settings.width / 2;
      }

      // control size
      d.data.settings.width = d.data.settings.textSize.width + chartSettings.padding.h * 2;
      if (d.depth !== 0 && d.data.settings.plusEnabled) {
        d.data.settings.width += d.data.settings.plusR * 2 + chartSettings.padding.h;
      }

      // vip icon
      // if (d.data.settings.vipOnly) {
      //   d.data.settings.width += chartSettings.padding.h + 20 // 20 is vip icon width
      // }

      d.data.settings.height = d.data.settings.textSize.height + chartSettings.padding.v * 2;
    });

    // process nodes x
    const maxDepth = _.maxBy(nodes, function (o) {
      return o.depth;
    }).depth;

    let startX =
      _.find(nodes, function (o) {
        return o.depth === 0;
      }).data.settings.width /
        2 +
      chartSettings.gap;

    for (let i = 1; i <= maxDepth; ++i) {
      const list = _.filter(nodes, function (o) {
        return o.depth === i;
      });

      const maxWidth = _.maxBy(list, function (o) {
        return o.data.settings.width;
      }).data.settings.width;

      _.each(list, function (o) {
        o.x = o.data.settings.x = isLeft
          ? -startX + (maxWidth - o.data.settings.width) / 2 - maxWidth / 2
          : startX - (maxWidth - o.data.settings.width) / 2 + maxWidth / 2;
        o.data.settings.maxSliblings = maxWidth;
      });
      startX += chartSettings.gap + maxWidth;
    }

    // process nodes edge
    _.each(nodes, function (o) {
      const halfWidth = o.data.settings.width / 2;
      const halfHeight = o.data.settings.height / 2;

      o.data.settings.topLeft = {
        x: o.data.settings.x - halfWidth,
        y: o.data.settings.y - halfHeight,
      };

      o.data.settings.topCenter = {
        x: o.data.settings.x,
        y: o.data.settings.y - halfHeight,
      };
      o.data.settings.topRight = {
        x: o.data.settings.x + halfWidth,
        y: o.data.settings.y - halfHeight,
      };
      o.data.settings.rightCenter = {
        x: o.data.settings.x + halfWidth,
        y: o.data.settings.y,
      };
      o.data.settings.bottomRight = {
        x: o.data.settings.x + halfWidth,
        y: o.data.settings.y + halfHeight,
      };
      o.data.settings.bottomCenter = {
        x: o.data.settings.x,
        y: o.data.settings.y + halfHeight,
      };
      o.data.settings.bottomLeft = {
        x: o.data.settings.x - halfWidth,
        y: o.data.settings.y + halfHeight,
      };

      o.data.settings.leftCenter = {
        x: o.data.settings.x - halfWidth,
        y: o.data.settings.y,
      };
    });
  }

  prepareLinks(links, isLeft) {
    links.forEach(function (d) {
      _.assignIn(d, chartSettings.defaultLineSettings);
      let p1;
      let p2;
      let p3;
      let p4;
      let p5;

      if (d.target.data.settings.isLeft) {
        p1 = {
          x: d.source.data.settings.leftCenter.x + 6,
          y: d.source.data.settings.leftCenter.y,
        };
        p5 = {
          x: d.source === d.target ? d.source.data.settings.leftCenter.x : d.target.data.settings.rightCenter.x,
          y: d.source === d.target ? d.source.data.settings.leftCenter.y : d.target.data.settings.rightCenter.y,
        };
        if (d.source.depth === 0) {
          p2 = {
            x: (d.source.data.settings.leftCenter.x + d.target.data.settings.rightCenter.x) / 2,
            y: d.source.data.settings.leftCenter.y,
          };
          p3 = {
            x: (d.source.data.settings.leftCenter.x + d.target.data.settings.rightCenter.x) / 2,
            y: d.source.data.settings.leftCenter.y,
          };
          p4 = {
            x: (d.target.data.settings.rightCenter.x + d.source.data.settings.leftCenter.x) / 2,
            y: d.target.data.settings.rightCenter.y,
          };
        } else {
          p2 = {
            x: d.source.data.settings.leftCenter.x - (d.source.data.settings.maxSliblings - d.source.data.settings.width),
            y: d.source.data.settings.leftCenter.y,
          };
          p3 = {
            x:
              d.source.data.settings.leftCenter.x -
              (d.source.data.settings.maxSliblings - d.source.data.settings.width) -
              chartSettings.gap / 2,
            y: d.source.data.settings.leftCenter.y,
          };
          p4 = {
            x:
              d.source.data.settings.leftCenter.x -
              (d.source.data.settings.maxSliblings - d.source.data.settings.width) -
              chartSettings.gap / 2,
            y: d.target.data.settings.rightCenter.y,
          };
        }
      } else {
        p1 = {
          x: d.source.data.settings.rightCenter.x - 6,
          y: d.source.data.settings.rightCenter.y,
        };
        p5 = {
          x: d.source === d.target ? d.source.data.settings.rightCenter.x : d.target.data.settings.leftCenter.x,
          y: d.source === d.target ? d.source.data.settings.rightCenter.y : d.target.data.settings.leftCenter.y,
        };
        if (d.source.data.settings.depth === 0) {
          p2 = {
            x: d.source.data.settings.rightCenter.x,
            y: d.source.data.settings.rightCenter.y,
          };
          p3 = {
            x: d.source.data.settings.rightCenter.x + (d.target.data.settings.leftCenter.x - d.source.data.settings.rightCenter.x) / 2,
            y: d.source.data.settings.leftCenter.y,
          };
          p4 = {
            x: d.source.data.settings.rightCenter.x + (d.target.data.settings.leftCenter.x - d.source.data.settings.rightCenter.x) / 2,
            y: d.target.data.settings.rightCenter.y,
          };
        } else {
          p2 = {
            x: d.source.data.settings.rightCenter.x + (d.source.data.settings.maxSliblings - d.source.data.settings.width),
            y: d.source.data.settings.leftCenter.y,
          };
          p3 = {
            x:
              d.source.data.settings.rightCenter.x +
              (d.source.data.settings.maxSliblings - d.source.data.settings.width) +
              chartSettings.gap / 2,
            y: d.source.data.settings.leftCenter.y,
          };
          p4 = {
            x:
              d.source.data.settings.rightCenter.x +
              (d.source.data.settings.maxSliblings - d.source.data.settings.width) +
              chartSettings.gap / 2,
            y: d.target.data.settings.rightCenter.y,
          };
        }
      }
      d.target.data.settings.pointsForLinkIn = [p1, p2, p3, p4, p5];
    });
  }

  updateTree(isLeft, source) {
    const self = this;
    const haveSource = source !== undefined;

    const rootNode = d3.hierarchy(isLeft ? this.data.left : this.data.right, (data) => {
      if (utils.isDataNode(data)) {
        return _.filter(data.children, (o) => _.includes(filerStore.filters, o.nodeType));
      }
      if (utils.isPropertyNode(data)) {
        // if is vip || the node is not vip only
        return data.children;
      }
      return data.children;
    });

    const nodesContainer = isLeft ? this.leftNodesContainer : this.rightNodesContainer;
    const linksContainer = isLeft ? this.leftLinksContainer : this.rightLinksContainer;

    if (!source) {
      source = rootNode;
      rootNode.eachBefore((node) => {
        node.data.settings0 = utils.createSettings0();
      });
    }

    this.tree(rootNode);

    const nodes = rootNode.descendants();

    const links = rootNode.links();

    this.prepareNodes(nodes, isLeft);

    this.prepareLinks(links, isLeft);

    const node = nodesContainer.selectAll('g.node').data(nodes, self.stratify.nodeId);

    const nodeEnter = node
      .enter()
      .append('g')
      .classed('node', true)
      .attr('cursor', function (d) {
        return d.data.settings.cursor;
      })
      .attr('transform', function (d) {
        if (utils.isOperationMoreOrLessNode(source.data)) {
          if (isLeft) {
            return `translate(${
              source.data.settings0.x + source.data.settings0.width / 2 - d.data.settings.width / 2
            },${source.data.settings0.y})`;
          }
          return `translate(${
            source.data.settings0.rightCenter.x + d.data.settings.width / 2 - source.data.settings0.width
          },${source.data.settings0.rightCenter.y})`;
        }
        if (d.depth === 0 && d === source) {
          return 'translate(0,0)';
        }
        if (source.depth === 0) {
          if (isLeft) {
            return `translate(${source.data.settings.leftCenter.x - d.data.settings.width / 2},${source.data.settings.leftCenter.y})`;
          }
          return `translate(${source.data.settings.rightCenter.x + d.data.settings.width / 2},${source.data.settings.rightCenter.y})`;
        }
        if (isLeft) {
          return `translate(${source.data.settings0.x - source.data.settings0.width / 2 - d.data.settings.width / 2},${
            source.data.settings0.y
          })`;
        }
        return `translate(${source.data.settings0.rightCenter.x + d.data.settings.width / 2},${source.data.settings0.rightCenter.y})`;
      })
      .attr('fill-opacity', 0)
      .attr('stroke-opacity', 0)
      .on('click', function (d) {
        if (d.depth === 0) {
          return;
        }
        if (utils.isOperationMoreOrLessNode(d.data)) {
          self.emit('onOperationMoreOrLess', { d, isLeft });
        }

        if (utils.isPropertyNode(d.data)) {
          if (d.data._children) {
            d.data.children = d.data._children;
            d.data._children = null;
          } else {
            d.data._children = d.data.children;
            d.data.children = null;
          }
          self.saveState(nodes);
          self.emit('onRequireUpdate', { d, isLeft });
        }

        if (utils.isDataNode(d.data) && !d.data.settings.noChildren) {
          if (d.data.isLoaded) {
            if (d.data.emptyNode) {
              self.emit('toasted', '该企业/人员暂无更多详细数据');
            }
            if (d.data._children) {
              d.data.children = d.data._children;
              d.data._children = null;
            } else {
              d.data._children = d.data.children;
              d.data.children = null;
            }
            self.emit('onRequireUpdate', { d, isLeft });
          } else {
            self.emit('onRequireData', { d, isLeft });
          }
        }
      });
    // .each(function (d) {
    //   let mc = Hammer(this)
    //   mc.get('swipe').set({ enable: false })
    //   mc
    //     .on('press', function (event) {
    //       console.log('press')
    //       console.log(event)
    //       console.log(d)
    //     })
    //     .on('tap', function (event) {
    //       console.log('tab')
    //       console.log(event)
    //       console.log(d)
    //     })
    // })

    nodeEnter
      .append('rect')
      .attr('fill', function (d) {
        if (!isLeft && d.depth === 0) {
          return 'rgba(255, 255, 255, 0)';
        }
        return d.data.settings.bgColor;
      })
      .attr('fill-opacity', function (d) {
        return d.data.settings.fillOpacity;
      })
      .attr('stroke-width', function (d) {
        return 0;
      })
      .attr('width', function (d) {
        return d.data.settings.width;
      })
      .attr('height', function (d) {
        return d.data.settings.height;
      })
      .attr('x', function (d) {
        return -d.data.settings.width / 2;
      })
      .attr('y', function (d) {
        return -d.data.settings.height / 2;
      })
      .attr('rx', function (d) {
        return d.data.settings.radius;
      })
      .attr('ry', function (d) {
        return d.data.settings.radius;
      });

    nodeEnter
      .append('circle')
      .attr('fill', function (d) {
        return d.data.settings.dotColor;
      })
      .attr('r', function (d) {
        return d.data.settings.dotR;
      })
      .attr('cx', function (d) {
        if (isLeft) {
          return d.data.settings.textSize.width / 2 + chartSettings.padding.h + 2 * d.data.settings.dotR;
        }
        return -d.data.settings.textSize.width / 2 - chartSettings.padding.h - 2 * d.data.settings.dotR;
      })
      .attr('y', function (d) {
        // 也不知道是什么鬼Safari位置有一点偏差
        // if (deviceInfo.isSafari()) {
        //   return 4;
        // }
        return 2;
      });

    nodeEnter
      .append(function (d) {
        if (d.depth === 0) {
          return utils.createText(d.data.name, d.data.settings.fontSize);
          // return utils.createRichNameText(d.data)
        }
        return utils.createRichNameText(d.data);
      })
      .classed('more-or-less', function (d) {
        return utils.isOperationMoreOrLessNode(d.data);
      })
      .style('fill', function (d) {
        return d.data.settings.textColor;
      })
      .attr('x', function (d) {
        if (isLeft) {
          // if (d.data.settings.vipOnly) {
          //   return ((d.data.settings.width - d.data.settings.textSize.width) / 2 - chartSettings.padding.h - 26)
          // } else {
          //   return ((d.data.settings.width - d.data.settings.textSize.width) / 2 - chartSettings.padding.h)
          // }
          return (d.data.settings.width - d.data.settings.textSize.width) / 2 - chartSettings.padding.h;
        }
        return -((d.data.settings.width - d.data.settings.textSize.width) / 2 - chartSettings.padding.h);
      })
      .attr('y', function (d) {
        // 也不知道是什么鬼Safari位置有一点偏差
        // if (deviceInfo.isSafari()) {
        //   return 4;
        // }
        return 2;
      });

    // draw plus circle
    const plusCircle = nodeEnter
      .append('g')
      .classed('plus-circle', true)
      .attr('transform', function (d) {
        return `translate(${
          isLeft
            ? -d.data.settings.width / 2 + chartSettings.padding.h + chartSettings.padding.h
            : d.data.settings.width / 2 - chartSettings.padding.h - chartSettings.padding.h
        },${d.data.settings.plusBorderWidth})`;
      })
      .attr('visibility', function (d) {
        return d.depth === 0 || !d.data.settings.plusEnabled ? 'hidden' : '';
      });

    plusCircle
      .append('circle')
      .classed('plus', true)
      .attr('r', function (d) {
        return d.data.settings.plusR;
      })
      .classed('blink', function (d) {
        return d.data.settings.plusEnableBlink;
      })
      .style('stroke', function (d) {
        return utils.plusIsOpen(d) ? d.data.settings.plusBorderColor2 : d.data.settings.plusLineColor;
      })
      .style('fill', function (d) {
        return d.data.settings.bgColor;
      })
      .style('stroke-width', function (d) {
        return d.data.settings.plusBorderWidth;
      });

    plusCircle
      .append('line')
      .classed('plus', true)
      .style('stroke', function (d) {
        return utils.plusIsOpen(d) ? d.data.settings.plusBorderColor2 : d.data.settings.plusLineColor;
      })
      .style('stroke-width', function (d) {
        return d.data.settings.plusLineWidth;
      })
      .attr('x1', function (d) {
        return 3 - d.data.settings.plusR;
      })
      .attr('y1', 0)
      .attr('x2', function (d) {
        return d.data.settings.plusR - 3;
      })
      .attr('y2', 0);

    plusCircle
      .append('line')
      .classed('plus', true)
      .classed('vertical-line', true)
      .style('stroke', function (d) {
        return utils.plusIsOpen(d) ? d.data.settings.plusBorderColor2 : d.data.settings.plusLineColor;
      })
      .style('stroke-width', function (d) {
        return d.data.settings.plusLineWidth;
      })
      .attr('x1', 0)
      .attr('y1', function (d) {
        return 3 - d.data.settings.plusR;
      })
      .attr('x2', 0)
      .attr('y2', function (d) {
        return d.data.settings.plusR - 3;
      })
      .attr('visibility', function (d) {
        return utils.plusIsOpen(d) ? 'hidden' : '';
      });

    // vip icon
    nodeEnter
      .append(function (d) {
        // if (d.data.settings.vipOnly) {
        //   return utils.createVipIcon()
        // } else {
        //   return utils.createEmptyIcon()
        // }

        return utils.createEmptyIcon();
      })
      .attr('transform', function (d) {
        let x = 0;
        // const y = deviceInfo.isIE() ? -7 : -4;
        const y = -4;
        if (isLeft) {
          x = -d.data.settings.width / 2 + chartSettings.padding.h + chartSettings.padding.h + 12 + d.data.settings.textSize.width;
        } else {
          x = d.data.settings.width / 2 - chartSettings.padding.h - chartSettings.padding.h - 35;
        }
        return `translate(${x}, ${y}) scale(1.2)`;
      });

    nodeEnter
      .append('rect')
      .attr('stroke', function (d) {
        if (!isLeft && d.data.settings.depth === 0) {
          return '#fff';
        }
        return d.data.settings.borderColor;
      })
      .attr('fill-opacity', function (d) {
        return 0;
      })
      .attr('stroke-width', function (d) {
        return d.data.settings.borderWidth;
      })
      .attr('stroke-opacity', function (d) {
        return d.data.settings.borderOpacity;
      })
      .attr('width', function (d) {
        return d.data.settings.width;
      })
      .attr('height', function (d) {
        return d.data.settings.height;
      })
      .attr('x', function (d) {
        return -d.data.settings.width / 2;
      })
      .attr('y', function (d) {
        return -d.data.settings.height / 2;
      })
      .attr('rx', function (d) {
        return d.data.settings.radius;
      })
      .attr('ry', function (d) {
        return d.data.settings.radius;
      })
      .on('mouseover', function (d) {
        if (utils.isDataNode(d.data)) {
          const activeBorderColor = d.data.settings.activeBorderColor || d.data.settings.borderColor;
          d3.select(this).transition().duration(chartSettings.fadeDuration).ease(chartSettings.ease).attr('stroke', activeBorderColor);
        }

        if (utils.isPropertyNode(d.data)) {
          const links = linksContainer.selectAll(`[link-flow-animation-id="${d.data.nodeId}"]`);
          links
            .classed('link-flow-in', function (d) {
              const a = d3.select(this).attr('link-flow-style');
              return a === 'in';
            })
            .classed('link-flow-out', function (d) {
              const a = d3.select(this).attr('link-flow-style');
              return a === 'out';
            })
            .attr('stroke', chartSettings.defaultLineSettings.flowLineColor)
            .attr('stroke-opacity', chartSettings.defaultLineSettings.flowLineOpacity);
        }

        if (utils.isDataNode(d.data)) {
          const $sender = $(window.event.target);

          let position = $sender.position();

          if (position.left === 0 && position.top === 0) {
            // 都是0的情况下，说明没有取到正确值，因此做个补充
            const rect = $sender[0].getBoundingClientRect();
            position = {
              left: rect.x,
              top: rect.y,
            };
          }

          let size = { width: $sender.width(), height: $sender.height() };

          if (size.width === 0 && size.height === 0) {
            // 都是0的情况下，说明没有取到正确值，因此做个补充
            const rect = $sender[0].getBBox();
            size = {
              width: rect.width,
              height: rect.height,
            };
          }

          self.emit('onDataNodeMouseover', {
            data: d.data,
            position,
            size,
            isLeft,
          });
        }
      })
      .on('mouseout', function (d) {
        if (utils.isDataNode(d.data)) {
          d3.select(this)
            .transition()
            .duration(chartSettings.fadeDuration)
            .ease(chartSettings.ease)
            .attr('stroke', d.data.settings.borderColor);

          const $sender = $(window.event.target);
          const position = $sender.position();
          const size = { width: $sender.width(), height: $sender.height() };

          self.emit('onDataNodeMouseout', {
            data: d.data,
            position,
            size,
          });
        }
        if (utils.isPropertyNode(d.data)) {
          const links = linksContainer.selectAll(`[link-flow-animation-id="${d.data.nodeId}"]`);
          links
            .classed('link-flow-in', false)
            .classed('link-flow-out', false)
            .attr('stroke', chartSettings.defaultLineSettings.lineColor)
            .attr('stroke-opacity', chartSettings.defaultLineSettings.lineOpacity);
        }
      });

    const arrow = nodeEnter.append('g').attr('transform', function (d) {
      if (isLeft) {
        return `translate(${d.data.settings.width / 2 + chartSettings.defaultArrowSettings.distance}, 0)`;
      }
      return `translate(${
        -d.data.settings.width / 2 - chartSettings.defaultArrowSettings.distance - chartSettings.defaultArrowSettings.width
      },0)`;
    });

    arrow
      .append('path')
      .attr('fill', function (d) {
        return chartSettings.defaultArrowSettings.fill;
      })
      .attr('stroke-width', 0)
      .attr('d', function (d) {
        if (utils.isPropertyNode(d.data)) {
          if (d.data.settings.arrowInOrOut) {
            if (isLeft) {
              return d.data.settings.arrowInOrOut === 'in' ? chartArrows.right() : chartArrows.left();
            }
            return d.data.settings.arrowInOrOut === 'in' ? chartArrows.left() : chartArrows.right();
          }
          return chartSettings.empty();
        }
        return chartArrows.empty();
      });

    const nodeUpdate = node
      .merge(nodeEnter)
      .transition()
      .duration(chartSettings.duration)
      .ease(chartSettings.ease)
      .attr('transform', function (d) {
        return `translate(${d.data.settings.x},${d.data.settings.y})`;
      })
      .attr('fill-opacity', 1)
      .attr('stroke-opacity', 1);

    nodeUpdate
      .select('text.more-or-less')
      .select('tspan')
      .text(function (d) {
        if (d.parent.data && d.parent.data.restChildren && d.parent.data.restChildren.length > 0) {
          return `${d.data.name}(${d.parent.data.restChildren.length})`;
        }
        return d.data.name;
      });

    nodeUpdate.select('.plus-circle').attr('visibility', function (d) {
      return d.depth === 0 || !d.data.settings.plusEnabled ? 'hidden' : '';
    });

    nodeUpdate.select('.vertical-line').attr('visibility', function (d) {
      return utils.plusIsOpen(d) ? 'hidden' : '';
    });

    nodeUpdate.selectAll('.plus').style('stroke', function (d) {
      return utils.plusIsOpen(d) ? d.data.settings.plusBorderColor2 : d.data.settings.plusLineColor;
    });

    node
      .exit()
      .transition()
      .duration(chartSettings.duration)
      .ease(chartSettings.ease)
      .attr('transform', function (d) {
        if (utils.isOperationMoreOrLessNode(source.data)) {
          if (isLeft) {
            return `translate(${source.data.settings.x + source.data.settings.width / 2 - d.data.settings.width / 2},${
              source.data.settings.y
            })`;
          }
          return `translate(${source.data.settings.x - source.data.settings.width / 2 + d.data.settings.width / 2},${
            source.data.settings.rightCenter.y
          })`;
        }
        if (isLeft) {
          return `translate(${source.data.settings.x - source.data.settings.width / 2 - d.data.settings.width / 2},${
            source.data.settings.y
          })`;
        }
        return `translate(${
          source.data.settings.x + source.data.settings.width / 2 + d.data.settings.width / 2
        },${source.data.settings.rightCenter.y})`;
      })
      .attr('fill-opacity', 0)
      .attr('stroke-opacity', 0)
      .remove();

    const link = linksContainer.selectAll('path.link').data(links, this.stratify.linkId);

    const linkEnter = link
      .enter()
      .append('path')
      .classed('link', true)
      .attr('link-flow-animation-id', function (d) {
        if (!utils.isOperationMoreOrLessNode(d.source.data) && !utils.isOperationMoreOrLessNode(d.target.data)) {
          if (utils.isPropertyNode(d.source.data)) {
            return d.source.data.nodeId;
          }
          if (utils.isPropertyNode(d.target.data)) {
            return d.target.data.nodeId;
          }
        }
        return null;
      })
      .attr('link-flow-style', function (d) {
        if (!utils.isOperationMoreOrLessNode(d.source.data) && !utils.isOperationMoreOrLessNode(d.target.data)) {
          if (utils.isLinkFlowInEnabled(isLeft, d)) {
            return isLeft ? 'out' : 'in';
          }
          if (utils.isLinkFlowOutEnabled(isLeft, d)) {
            return isLeft ? 'in' : 'out';
          }
        }
        return null;
      })
      .attr('fill', function (d) {
        return d.lineFill;
      })
      .attr('stroke', function (d) {
        return d.lineColor;
      })
      .attr('stroke-opacity', function (d) {
        return d.lineOpacity;
      })
      .attr('stroke-width', function (d) {
        return d.lineWidth;
      })
      .attr('d', function (d) {
        if (utils.isOperationMoreOrLessNode(source.data)) {
          return chartLines.createDefault(source.data.settings0.pointsForLinkIn);
        }
        if (source.depth === 0) {
          if (isLeft) {
            return chartLines.createDefault([
              source.data.settings.leftCenter,
              source.data.settings.leftCenter,
              source.data.settings.leftCenter,
              source.data.settings.leftCenter,
              source.data.settings.leftCenter,
            ]);
          }
          return chartLines.createDefault([
            source.data.settings.rightCenter,
            source.data.settings.rightCenter,
            source.data.settings.rightCenter,
            source.data.settings.rightCenter,
            source.data.settings.rightCenter,
          ]);
        }
        if (isLeft) {
          return chartLines.createDefault([
            source.data.settings0.leftCenter,
            source.data.settings0.leftCenter,
            source.data.settings0.leftCenter,
            source.data.settings0.leftCenter,
            source.data.settings0.leftCenter,
          ]);
        }
        return chartLines.createDefault([
          source.data.settings0.rightCenter,
          source.data.settings0.rightCenter,
          source.data.settings0.rightCenter,
          source.data.settings0.rightCenter,
          source.data.settings0.rightCenter,
        ]);
      });

    link
      .merge(linkEnter)
      .transition()
      .duration(chartSettings.duration)
      .ease(chartSettings.ease)
      .attr('d', function (d) {
        return chartLines.createDefault(d.target.data.settings.pointsForLinkIn);
      });

    link
      .exit()
      .transition()
      .duration(chartSettings.duration)
      .ease(chartSettings.ease)
      .remove()
      .attr('d', function (d) {
        if (utils.isOperationMoreOrLessNode(source.data)) {
          return chartLines.createDefault(source.data.settings.pointsForLinkIn);
        }
        if (isLeft) {
          return chartLines.createDefault([
            source.data.settings.leftCenter,
            source.data.settings.leftCenter,
            source.data.settings.leftCenter,
            source.data.settings.leftCenter,
            source.data.settings.leftCenter,
          ]);
        }
        return chartLines.createDefault([
          source.data.settings.rightCenter,
          source.data.settings.rightCenter,
          source.data.settings.rightCenter,
          source.data.settings.rightCenter,
          source.data.settings.rightCenter,
        ]);
      });

    if (chartSettings.debug) {
      self.drawFlag(isLeft, nodesContainer, nodes);
    }

    if (haveSource) {
      this.autoAdjust(source, isLeft);
    }

    this.saveState(nodes);
  }

  saveState(nodes) {
    nodes.forEach(function (d) {
      d.data.settings0 = JSON.parse(JSON.stringify(d.data.settings));
    });
  }

  appendData(data, d) {
    d.data.children = data.children || [];
    d.data.isLoaded = true;
  }

  moreNode(d) {
    const part1 = d.data.children.slice(0, d.data.children.length - 1);
    const nodeOperation = d.data.children.slice(d.data.children.length - 1)[0];
    nodeOperation.name = chartSettings.moreOrLessSettings.textForLess;
    const children = [].concat(part1, d.data.restChildren, nodeOperation);

    _.each(children, (o) => {
      o.parent = d.data;
    });
    d.data.children = children;
    d.data.restChildren = [];
  }

  lessNode(d) {
    const part1 = d.data.children.slice(0, d.data.children.length - 1);
    const nodeOperation = d.data.children.slice(d.data.children.length - 1)[0];

    nodeOperation.name = chartSettings.moreOrLessSettings.textForMore;

    const children = [].concat(part1.slice(0, chartSettings.moreOrLessSettings.maxNumForMore), nodeOperation);
    const restChildren = part1.slice(chartSettings.moreOrLessSettings.maxNumForMore);
    d.data.children = children;
    d.data.restChildren = restChildren;
  }

  drawFlag(isLeft, nodesContainer, nodes) {
    d3.selectAll(isLeft ? '.left-flag' : '.right-flag').remove();
    nodes.forEach(function (d) {
      const points = [
        {
          x: d.data.settings.x,
          y: d.data.settings.y,
        },
        d.data.settings.topLeft,
        d.data.settings.topCenter,
        d.data.settings.topRight,
        d.data.settings.rightCenter,
        d.data.settings.bottomRight,
        d.data.settings.bottomCenter,
        d.data.settings.bottomLeft,
        d.data.settings.leftCenter,
      ];
      _.each(points, function (p) {
        nodesContainer
          .append('circle')
          .classed('node-circle', true)
          .classed(isLeft ? 'left-flag' : 'right-flag', true)
          .attr('cx', function () {
            return p.x;
          })
          .attr('cy', function () {
            return p.y;
          })
          .attr('r', 2);
      });
    });
  }

  applyTransform(transform, [newX, newY, newK]) {
    const x = transform.x;
    const y = transform.y;
    const k = transform.k;

    this.root
      .transition()
      .duration(600)
      .attrTween('transform', () => {
        const interpolator = d3.interpolate([x, y, k], [newX, newY, newK]);
        return (t) => {
          const view = interpolator(t);
          transform.x = view[0];
          transform.y = view[1];
          transform.k = view[2];
          this.emit('onScale', { oldTransform: this.transform, newTransform: { ...transform } });

          return transform;
        };
      })
      .on('end', () => {
        this.zoom.transform(this.root, transform);
        this.emit('onScale', { oldTransform: this.transform, newTransform: { ...transform } });
        this.transform = { ...transform };
      });
  }

  zoomInOrOut(directionOrScale) {
    const transform = d3.zoomTransform(this.svg.node());

    const k = transform.k;
    const x = transform.x;
    const y = transform.y;

    let newK;

    if (_.isNumber(directionOrScale)) {
      newK = directionOrScale;
    } else {
      newK = k + (directionOrScale ? this.scaleStep : -this.scaleStep);
    }

    if (newK < chartSettings.scaleRange[0]) {
      newK = chartSettings.scaleRange[0];
    }

    if (newK > chartSettings.scaleRange[1]) {
      newK = chartSettings.scaleRange[1];
    }

    const center = { x: this.size.width / 2, y: this.size.height / 2 };
    const translate0 = { x: (center.x - x) / k, y: (center.y - y) / k };

    const l = { x: translate0.x * newK + x, y: translate0.y * newK + y };
    const newX = x + center.x - l.x;
    const newY = y + center.y - l.y;

    this.applyTransform(transform, [newX, newY, newK]);
  }

  autoAdjust(d, isLeft) {
    if (chartSettings.autoAdjust === false) {
      return;
    }

    if (utils.isOperationMoreOrLessNode(d.data) || d.data.emptyNode) {
      return;
    }

    let x = d.data.settings.x;
    let y = d.data.settings.y;

    if (d.data._children) {
      if (utils.isDataNode(d.data)) {
        x = d.parent.parent.data.settings.x;
        y = d.parent.parent.data.settings.y;
      }
      if (utils.isPropertyNode(d.data)) {
        x = d.parent.data.settings.x;
        y = d.parent.data.settings.y;
      }
    }

    const transform = d3.zoomTransform(this.svg.node());
    const k = transform.k;
    const newK = k;

    const originOffset = {
      x: this.size.width / 2 - (this.size.width / 2) * newK,
      y: this.size.height / 2 - (this.size.height / 2) * newK,
    };

    const newX = -x * newK + originOffset.x;
    const newY = -y * newK + originOffset.y;
    this.applyTransform(transform, [newX, newY, newK]);
  }
}
