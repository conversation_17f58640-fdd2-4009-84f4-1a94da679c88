import _ from 'lodash';

const nodeTypes = [
  {
    nodeType: 1,
    text: 'MoreOrLess',
    appearance: {
      sequence: 999999,
      textColor: '#999',
      bgColor: '#F1F1F1',
      borderColor: '#F1F1F1',
    },
  },
  {
    nodeType: 101,
    text: '法定代表人',
    appearance: {
      sequence: 1,
      textColor: '#333',
      bgColor: '#fff',
      borderColor: '#fff',
      fillOpacity: 0,
      borderOpacity: 0,
      dotColor: '#D4B106',
      arrowInOrOut: 'in',
    },
  },
  {
    nodeType: 102,
    text: '实际控制人',
    vipOnly: true,
    appearance: {
      sequence: 2,
      textColor: '#333',
      bgColor: '#fff',
      borderColor: '#fff',
      fillOpacity: 0,
      borderOpacity: 0,
      dotColor: '#FF4D4F',
      arrowInOrOut: 'in',
    },
  },
  {
    nodeType: 103,
    text: '最终受益人',
    vipOnly: true,
    appearance: {
      sequence: 3,
      textColor: '#333',
      bgColor: '#fff',
      borderColor: '#fff',
      fillOpacity: 0,
      borderOpacity: 0,
      dotColor: '#FF7A45',
      arrowInOrOut: 'in',
    },
  },
  {
    nodeType: 104,
    text: '股东',
    optionText: '股东',
    appearance: {
      sequence: 4,
      textColor: '#333',
      bgColor: '#fff',
      borderColor: '#fff',
      fillOpacity: 0,
      borderOpacity: 0,
      dotColor: '#13C2C2',
      arrowInOrOut: 'in',
    },
  },
  {
    nodeType: 105,
    text: '高管',
    appearance: {
      sequence: 5,
      textColor: '#333',
      bgColor: '#fff',
      borderColor: '#fff',
      fillOpacity: 0,
      borderOpacity: 0,
      dotColor: '#389E0D',
      arrowInOrOut: 'in',
    },
  },
  {
    nodeType: 106,
    text: '对外投资',
    appearance: {
      sequence: 6,
      textColor: '#333',
      bgColor: '#fff',
      borderColor: '#fff',
      fillOpacity: 0,
      borderOpacity: 0,
      dotColor: '#128BED',
      arrowInOrOut: 'out',
    },
  },
  {
    nodeType: 107,
    text: '控股企业',
    vipOnly: true,
    appearance: {
      sequence: 7,
      textColor: '#333',
      bgColor: '#fff',
      borderColor: '#fff',
      fillOpacity: 0,
      borderOpacity: 0,
      dotColor: '#FF7875',
      arrowInOrOut: 'out',
    },
  },
  {
    nodeType: 108,
    text: '对外担保',
    appearance: {
      sequence: 8,
      textColor: '#333',
      bgColor: '#fff',
      borderColor: '#fff',
      fillOpacity: 0,
      borderOpacity: 0,
      dotColor: '#D9A66B',
      arrowInOrOut: 'out',
    },
  },
  {
    nodeType: 109,
    text: '分支机构',
    appearance: {
      sequence: 9,
      textColor: '#333',
      bgColor: '#fff',
      borderColor: '#fff',
      fillOpacity: 0,
      borderOpacity: 0,
      dotColor: '#F9AD14',
      arrowInOrOut: 'out',
    },
  },
  {
    nodeType: 110,
    text: '供应商',
    appearance: {
      sequence: 10,
      textColor: '#333',
      bgColor: '#fff',
      borderColor: '#fff',
      fillOpacity: 0,
      borderOpacity: 0,
      dotColor: '#85A5FF',
      arrowInOrOut: 'in',
    },
  },
  {
    nodeType: 111,
    text: '客户',
    appearance: {
      sequence: 11,
      textColor: '#333',
      bgColor: '#fff',
      borderColor: '#fff',
      fillOpacity: 0,
      borderOpacity: 0,
      dotColor: '#42B20C',
      arrowInOrOut: 'out',
    },
  },
  {
    nodeType: 112,
    text: '历史对外投资',
    vipOnly: true,
    appearance: {
      sequence: 14,
      textColor: '#333',
      bgColor: '#fff',
      borderColor: '#fff',
      fillOpacity: 0,
      borderOpacity: 0,
      dotColor: '#B37FEB',
      arrowInOrOut: 'out',
    },
  },
  {
    nodeType: 113,
    text: '历史法定代表人',
    vipOnly: true,
    appearance: {
      sequence: 12,
      textColor: '#333',
      bgColor: '#fff',
      borderColor: '#fff',
      fillOpacity: 0,
      borderOpacity: 0,
      dotColor: '#D3ADF7',
      arrowInOrOut: 'in',
    },
  },
  {
    nodeType: 114,
    text: '历史股东',
    vipOnly: true,
    appearance: {
      sequence: 14,
      textColor: '#333',
      bgColor: '#fff',
      borderColor: '#fff',
      fillOpacity: 0,
      borderOpacity: 0,
      dotColor: '#08979C',
      arrowInOrOut: 'in',
    },
  },
  {
    nodeType: 201,
    text: '担任法人',
    vipOnly: true,
    appearance: {
      sequence: 1,
      textColor: '#333',
      bgColor: '#fff',
      borderColor: '#fff',
      fillOpacity: 0,
      borderOpacity: 0,
      dotColor: '#D4B106',
      arrowInOrOut: 'out',
    },
  },
  {
    nodeType: 202,
    text: '对外投资',
    vipOnly: true,
    appearance: {
      sequence: 2,
      textColor: '#333',
      bgColor: '#fff',
      borderColor: '#fff',
      fillOpacity: 0,
      borderOpacity: 0,
      dotColor: '#128BED',
      arrowInOrOut: 'out',
    },
  },
  {
    nodeType: 203,
    text: '控股企业',
    vipOnly: true,
    appearance: {
      sequence: 3,
      textColor: '#333',
      bgColor: '#fff',
      borderColor: '#fff',
      fillOpacity: 0,
      borderOpacity: 0,
      dotColor: '#FF7875',
      arrowInOrOut: 'out',
    },
  },
  {
    nodeType: 204,
    text: '在外任职',
    vipOnly: true,
    appearance: {
      sequence: 4,
      textColor: '#333',
      bgColor: '#fff',
      borderColor: '#fff',
      fillOpacity: 0,
      borderOpacity: 0,
      dotColor: '#128BED',
      arrowInOrOut: 'out',
    },
  },
  {
    nodeType: 999,
    text: '公司实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#128bed',
      activeBorderColor: '#128bed',
    },
  },
  {
    nodeType: 999.101, // 法定代表人
    text: '公司实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#D4B106',
      activeBorderColor: '#D4B106',
    },
  },
  {
    nodeType: 999.102, // 实际控制人
    text: '公司实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#FF4D4F',
      activeBorderColor: '#FF4D4F',
    },
  },
  {
    nodeType: 999.103, // 最终受益人
    text: '公司实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#FF7A45',
      activeBorderColor: '#FF7A45',
    },
  },
  {
    nodeType: 999.104, // 股东
    text: '公司实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#13C2C2',
      activeBorderColor: '#13C2C2',
    },
  },
  {
    nodeType: 999.105, // 高管
    text: '公司实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#389E0D',
      activeBorderColor: '#389E0D',
    },
  },
  {
    nodeType: 999.106, // 对外投资
    text: '公司实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#128bed',
      activeBorderColor: '#128bed',
    },
  },
  {
    nodeType: 999.107, // 控股企业
    text: '公司实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#FF7875',
      activeBorderColor: '#FF7875',
    },
  },
  {
    nodeType: 999.108, // 对外担保
    text: '公司实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#D9A66B',
      activeBorderColor: '#D9A66B',
    },
  },
  {
    nodeType: 999.109,
    text: '公司实体', // 分支机构
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#F9AD14',
      activeBorderColor: '#F9AD14',
    },
  },
  {
    nodeType: 999.11, // 供应商
    text: '公司实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#85A5FF',
      activeBorderColor: '#85A5FF',
    },
  },
  {
    nodeType: 999.111, // 客户
    text: '公司实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#42B20C',
      activeBorderColor: '#42B20C',
    },
  },
  {
    nodeType: 999.112, // 历史对外投资
    text: '公司实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#B37FEB',
      activeBorderColor: '#B37FEB',
    },
  },
  {
    nodeType: 999.113, // 历史法定代表人
    text: '公司实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#D3ADF7',
      activeBorderColor: '#D3ADF7',
    },
  },
  {
    nodeType: 999.114, // 历史股东
    text: '公司实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#08979C',
      activeBorderColor: '#08979C',
    },
  },
  {
    nodeType: 999.201, // 担任法人
    text: '公司实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#D4B106',
      activeBorderColor: '#D4B106',
    },
  },
  {
    nodeType: 999.202, // 对外投资
    text: '公司实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#128bed',
      activeBorderColor: '#128bed',
    },
  },
  {
    nodeType: 999.203, // 控股企业
    text: '公司实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#FF7875',
      activeBorderColor: '#FF7875',
    },
  },
  {
    nodeType: 999.204, // 在外任职
    text: '公司实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#128bed',
      activeBorderColor: '#128bed',
    },
  },
  {
    nodeType: 998,
    text: '人实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#FF7875',
      activeBorderColor: '#FF7875',
    },
  },
  {
    nodeType: 998.101, // 法定代表人
    text: '人实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#D4B106',
      activeBorderColor: '#D4B106',
    },
  },
  {
    nodeType: 998.102, // 实际控制人
    text: '人实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#FF4D4F',
      activeBorderColor: '#FF4D4F',
    },
  },
  {
    nodeType: 998.103, // 最终受益人
    text: '人实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#FF7A45',
      activeBorderColor: '#FF7A45',
    },
  },
  {
    nodeType: 998.104, // 股东
    text: '人实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#13C2C2',
      activeBorderColor: '#13C2C2',
    },
  },
  {
    nodeType: 998.105, // 高管
    text: '人实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#389E0D',
      activeBorderColor: '#389E0D',
    },
  },
  {
    nodeType: 998.113, // 历史法定代表人
    text: '人实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#D3ADF7',
      activeBorderColor: '#D3ADF7',
    },
  },
  {
    nodeType: 998.114, // 历史股东
    text: '人实体',
    appearance: {
      textColor: '#333333',
      bgColor: '#fff',
      borderColor: '#08979C',
      activeBorderColor: '#08979C',
    },
  },
];

const moreOrLessSettings = {
  // 多少个子节点时出查看更多
  maxNumForMore: 10,
  // 多少个子节点时出搜索对话框
  maxNumForSearch: 30,

  textForMore: '展开',
  textForLess: '  收起  ',
  textForSearch: '搜索',
};

const defaultNodeSettings = {
  cursor: 'pointer',
  fontSize: 12,
  textColor: '#fff',
  // control rect style
  borderColor: '#128BED',
  bgColor: '#128BED',
  borderWidth: 0.5,
  fillOpacity: 1,
  borderOpacity: 1,
  radius: 2,
  // control plus style
  plusEnabled: true,
  plusBorderColor: '#666',
  plusBorderColor2: '#999',
  plusLineColor: '#666',
  plusLineWidth: 1,
  plusBorderWidth: 1,
  plusR: 5,
  plusEnableBlink: false,
  arrowInOrOut: 'in',
  dotColor: 'rgba(255,255,255,0)',
  dotR: 4,
};

const nodeSettings = {
  default: defaultNodeSettings,
};

_.each(nodeTypes, (o) => {
  nodeSettings[o.nodeType] = _.assignIn({}, defaultNodeSettings, { ...o.appearance });
  nodeSettings[o.nodeType].vipOnly = o.vipOnly;
});

export default {
  defaultNodeSettings,
  nodeTypes,
  nodeSettings,
  moreOrLessSettings,
};

export { defaultNodeSettings, nodeTypes, nodeSettings, moreOrLessSettings };
