import * as d3 from 'd3';

import defaultArrowSettings from './arrow';
import { defaultNodeSettings, nodeSettings, moreOrLessSettings } from './node';
import defaultLineSettings from './line';

const defaultScale = 1;

export default {
  debug: false,
  // 是否启用自动调整
  autoAdjust: true,
  // 节点内部padding
  padding: {
    h: 6,
    v: 8,
  },
  // 默认缩放
  defaultScale,
  scaleLevel: 5,
  // fade效果
  fadeDuration: 400,
  // 缩放范围
  scaleRange: [defaultScale * 0.1, defaultScale * 3],
  // 节点大小，y无效
  nodeSize: [38, 10],
  // 两层之间最小距离
  gap: 60,
  // 同级节点距离比
  nodeSeparation: 1.6,
  // 动画持续时间
  duration: 300,
  // 缓动参数
  ease: (d3 && d3.easeQuad) || null,
  // 默认节点设置
  defaultNodeSettings,
  // 默认线配置
  defaultLineSettings,
  // 节点类型参数配置
  nodeSettings,
  // 默认箭头配置
  defaultArrowSettings,
  moreOrLessSettings,
};
