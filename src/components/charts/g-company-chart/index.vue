<template labg="vue">
  <div :class="['company-chart-container', containerName]">
    <div :id="containerName"></div>
    <div class="toolbox" v-show="isInit">
      <g-ui-toolbox>
        <g-ui-toolbox-action :action-type="actionTypes.filter" @click="onFilter" :is-active="isFilterOpened"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.zoomIn" @click="onZoomIn"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.zoomOut" @click="onZoomOut"></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.refresh" @click="onRefresh"></g-ui-toolbox-action>
        <g-ui-toolbox-action v-show="!isFullScreen" :action-type="actionTypes.fullScreen" @click="onFullScreen"></g-ui-toolbox-action>
        <g-ui-toolbox-action
          v-show="isFullScreen"
          :action-type="actionTypes.exitFullScreen"
          @click="onExitFullScreen"
        ></g-ui-toolbox-action>
        <g-ui-toolbox-action :action-type="actionTypes.save" :is-busy="isSaving" @click="onSave"></g-ui-toolbox-action>
      </g-ui-toolbox>
    </div>
    <transition name="fade">
      <div class="filter" v-show="isFilterOpened">
        <company-chart-app-filter @changed="onFilterChanged" @close="isFilterOpened = false" @buyVip="buyVip"></company-chart-app-filter>
      </div>
    </transition>
    <!-- 免责 -->
    <g-ui-footer></g-ui-footer>
    <!-- 加载 -->
    <g-ui-loading v-if="isLoading"></g-ui-loading>
    <!-- 暂无数据 -->
    <g-ui-no-data v-if="noData"></g-ui-no-data>
  </div>
</template>
<script src="./component.js"></script>
<style lang="less" src="./style.less" scoped></style>
