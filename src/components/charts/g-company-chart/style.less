.company-chart-container {
  position: absolute;
  inset: 0;
  overflow: hidden;
  background: url("../utils/images/shuiying6.png") repeat;
  background-size: 360px 280px;
  line-height: 1.2;

  >svg {
    user-select: none;
  }

  #company-chart {
    width: 100%;
    height: 100%;
  }
}

.toolbox {
  position: fixed;
  width: 46px;
  right: 20px;
  bottom: 50px;
  font-size: 18px;
  z-index: 20;
}

.filter {
  position: fixed;
  right: 78px;
  bottom: 100px;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity .3s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
