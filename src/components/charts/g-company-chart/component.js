/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable func-names */
/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-expressions */
import moment from 'moment';
import _ from 'lodash';

import dataLoader from './data';
import { actionTypes, gUiToolbox, gUiToolboxAction } from '../g-ui-toolbox';
import gUiFooter from '../g-ui-footer';
import gUiLoading from '../g-ui-loading';
import gUiNoData from '../g-ui-no-data';
import appPerson from '../g-app-person';
import appCompany from '../g-app-company';
import Chart from './chart';
import popoverHelper from '../utils/popoverHelper';
import utils from './chart/utils';
import globalUtils from '../utils/utils';
import saveSvg from '../utils/save-svg';
import settings from './settings';
import appSearch from './components/search';
import appFilter from './components/filter';

const defaultScale = 1;

export default {
  name: 'g-company-chart',
  props: {
    actionTypes: {
      type: Object,
      default: () => actionTypes,
    },
    containerName: {
      type: String,
      default: 'company-chart',
    },
    keyNo: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
  },
  components: {
    [gUiToolbox.name]: gUiToolbox,
    [gUiToolboxAction.name]: gUiToolboxAction,
    [gUiFooter.name]: gUiFooter,
    [gUiLoading.name]: gUiLoading,
    [gUiNoData.name]: gUiNoData,
    [appFilter.name]: appFilter,
  },

  data() {
    return {
      isInit: false,
      isLoading: false,
      noData: false,
      defaultScale,
      isFullScreen: false,
      transform: { x: 0, y: 0, k: defaultScale },
      isFilterOpened: false,
      isVip: true,
      isSaving: false,
    };
  },
  mounted() {
    $(`#${this.containerName}`).on('click', 'svg', () => {
      this.isFilterOpened = false;
    });
    this.isFullScreen = document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement;
    this.$nextTick(() => {
      this.onRefresh();
      document.addEventListener('fullscreenchange', () => {
        this.isFullScreen = !!document.fullscreenElement;
      });
      document.addEventListener('webkitfullscreenchange', () => {
        this.isFullScreen = !!document.webkitFullscreenElement;
      });
      document.addEventListener('mozfullscreenchange', () => {
        this.isFullScreen = !!document.mozFullScreenElement;
      });
      document.addEventListener('MSFullscreenChange', () => {
        this.isFullScreen = !!document.msFullscreenElement;
      });
    });
  },

  methods: {
    cleanup() {
      this.isInit = false;
      this.chart && this.chart.cleanup();
      $('.detail-popover').remove();
      $('.detail-card').remove();
      $('.search-dialg').remove();
    },

    onRefresh() {
      this.isLoading = true;
      this.cleanup();

      this.chart = new Chart(`#${this.containerName}`);
      this.chart.init();

      dataLoader
        .loadCompanyDataById(this.keyNo)
        .then((res) => {
          if (_.isEmpty(res)) {
            return Promise.resolve({});
          }
          return dataLoader.splitCompanyData(res);
        })
        .then((data) => {
          if (_.isEmpty(data)) {
            this.isLoading = false;
            this.noData = true;
            return;
          }
          this.isInit = true;
          this.isLoading = false;
          this.chart.render(data);
          this.chart.zoomInOrOut(defaultScale);
        })
        .catch((err) => {
          this.isLoading = false;
          this.noData = true;
          console.log(err);
        });

      this.chart.on('onRequireData', ({ d, isLeft }) => {
        // 数据中没有ID的情况下，不查数据
        if (!d.data.id) {
          return;
        }

        Promise.resolve()
          .then(() => {
            return utils.isCompanyDataNode(d.data) ? dataLoader.loadCompanyDataById(d.data.id) : dataLoader.loadPersonDataById(d.data.id);
          })
          .then((data) => {
            if (!data.children || data.children.length === 0) {
              d.data.emptyNode = true;
              this.$toasted.error('该企业/人员暂无更多详细数据');
            }

            this.chart.appendData(data, d);
          })
          .then(() => {
            this.$nextTick(function () {
              this.chart.updateTree(isLeft, d);
            });
          })
          .catch(() => {
            this.$toasted.error('查无数据');
          });
      });

      this.chart.on('onRequireUpdate', ({ d, isLeft }) => {
        this.$nextTick(function () {
          this.chart.updateTree(isLeft, d);
        });
      });

      this.chart.on('toasted', (str) => {
        // this.$toasted.error(str);
      });

      this.chart.on('onOperationMoreOrLess', ({ d, isLeft }) => {
        if (d.parent.data.totalChildrenNum > settings.moreOrLessSettings.maxNumForSearch) {
          const items = _.map(d.parent.data.restChildren, (o) => {
            return {
              id: o.id,
              nodeId: o.nodeId,
              nodeType: o.nodeType,
              name: o.name,
              image: o.image,
            };
          });
          const { instance, $instance } = popoverHelper.showSearchDialog({
            component: appSearch,
            data: { items },
            container: $(`.${this.containerName}`),
          });

          instance.$on('onItemAdded', (item) => {
            const nodeId = item.nodeId;
            const o = _.find(d.parent.data.restChildren, (o) => o.nodeId === nodeId);
            if (o) {
              d.parent.data.children.splice(d.parent.data.children.length - 1, 0, o);
              d.parent.data.restChildren = _.filter(d.parent.data.restChildren, (o) => o.nodeId !== nodeId);
              if (d.parent.data.restChildren.length === 0) {
                $instance.close();
                // remove the last operation node
                d.parent.data.children = d.parent.data.children.slice(0, d.parent.data.children.length - 1);
              }

              this.$nextTick(function () {
                // 更新树
                this.chart.updateTree(isLeft, d);
              });
            }
          });
        } else {
          if (d.parent.data.restChildren.length > 0) {
            // 更多操作
            this.chart.moreNode(d.parent);
          } else {
            // 更少操作
            this.chart.lessNode(d.parent);
          }
          this.$nextTick(function () {
            // 更新树
            this.chart.updateTree(isLeft, d);
          });
        }
      });

      this.chart.on('onScale', ({ oldTransform, newTransform }) => {
        this.transform = newTransform;
        $('.detail-popover').remove();
      });

      this.chart.on('onDataNodeMouseover', ({ data, position, size, isLeft }) => {
        data.settings.delayTimer = setTimeout(() => {
          const $instance = $(`#detail-popover-${data.nodeId}`);
          if ($instance.length > 0) {
            $instance.stop(true).fadeIn(0);
            return;
          }
          const vmData = { id: data.id, name: data.name, keyNo: data.id };
          if (data.id && data.id[0] === 'p') {
            vmData.eid = data.parent.parent.id;
          }
          popoverHelper.showPopover({
            component: data.id && data.id[0] === 'p' ? appPerson : appCompany,
            isLeft,
            data: vmData,
            targetPosition: {
              left: this.isFullScreen ? position.left : position.left - (180 + 60),
              top: this.isFullScreen ? position.top : position.top - (42 + 42 + 40),
            },
            container: $(`.${this.containerName}`),
            identity: `detail-popover-${data.nodeId}`,
            targetSize: { width: size.width * this.transform.k, height: size.height * this.transform.k },
          });
        }, settings.fadeDuration);
      });

      this.chart.on('onDataNodeMouseout', ({ data, position, size }) => {
        if (data.settings.delayTimer) {
          clearTimeout(data.settings.delayTimer);
          delete data.settings.delayTimer;
        }

        $(`#detail-popover-${data.nodeId}`).fadeOut(settings.fadeDuration, function () {
          $(this).remove();
        });
      });
    },

    onZoomIn() {
      this.chart.zoomInOrOut(true);
    },

    onZoomOut() {
      this.chart.zoomInOrOut(false);
    },

    onFullScreen() {
      const element = $(`.${this.containerName}`)[0];
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
      this.isFullScreen = true;
    },

    onExitFullScreen() {
      if (this.isFullScreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
        this.isFullScreen = false;
      }
    },

    onFilter() {
      this.isFilterOpened = !this.isFilterOpened;
    },

    onFilterChanged() {
      this.chart.refresh();
    },

    onSave() {
      // 保存过程中，不能再保存
      if (this.isSaving) {
        return;
      }

      this.isSaving = true;

      setTimeout(() => {
        const $svg = $(`#${this.containerName}>svg`);
        const $shadow = $('<div></div>')
          .css({
            width: 0,
            height: 0,
            position: 'fixed',
            top: -1000000,
            let: -1000000,
            zIndex: -1,
            overflow: 'hidden',
          })
          .appendTo('body');

        const $svgClone = $svg.clone().appendTo($shadow);
        const $g = $svgClone.children('g');
        $g.attr('transform', 'translate(0,0) scale(1)');
        const box = $g[0].getBBox();
        const contentSize = { width: box.width, height: box.height };
        const padding = { left: 80, right: 80, top: 30, bottom: 60 };
        const rect = {
          top: box.y - padding.top,
          left: box.x - padding.left,
          width: contentSize.width + padding.left + padding.right,
          height: contentSize.height + padding.top + padding.bottom,
        };

        // add water markter
        const n = Math.ceil(rect.width / 360.0);
        const m = Math.ceil(rect.height / 280.0);
        for (let i = 0; i < n; i++) {
          for (let j = 0; j < m; j++) {
            const x = rect.left + 360 * i;
            const y = rect.top + 280 * j;
            $svgClone.prepend(globalUtils.getWaterMarkLogo().clone().attr('transform', `translate(${x}, ${y})`));
          }
        }

        // add white bg
        const $masker = $('<rect></rect>').attr({
          x: rect.left,
          y: rect.top,
          width: rect.width,
          height: rect.height,
          fill: 'rgba(255, 255, 255, 1)',
        });
        $svgClone.prepend($masker);

        // add bottom text
        if (rect.width < 600) {
          const $textFooter = $('<text></text>')
            .attr({
              x: padding.left + contentSize.width / 2 + rect.left,
              y: rect.top + padding.top + contentSize.height + 20,
              'text-anchor': 'middle',
              fill: '#999',
              'font-size': '12px',
            })
            .text('以上数据是企查查大数据分析引擎基于公开信息挖掘的成果，仅供参考');
          $svgClone.append($textFooter);
          const $textFooter1 = $('<text></text>')
            .attr({
              x: padding.left + contentSize.width / 2 + rect.left,
              y: rect.top + padding.top + contentSize.height + 40,
              'text-anchor': 'middle',
              fill: '#999',
              'font-size': '12px',
            })
            .text('该成果不构成任何明示或暗示的观点或保证');
          $svgClone.append($textFooter1);
        } else {
          const $textFooter = $('<text></text>')
            .attr({
              x: padding.left + contentSize.width / 2 + rect.left,
              y: rect.top + padding.top + contentSize.height + 40,
              'text-anchor': 'middle',
              fill: '#999',
              'font-size': '12px',
            })
            .text('以上数据是企查查大数据分析引擎基于公开信息挖掘的成果，仅供参考。该成果不构成任何明示或暗示的观点或保证。');
          $svgClone.append($textFooter);
        }

        const fileName = `${this.name}-企业图谱-${moment().format('YYYY-MM-DD')}.png`;

        const option = {
          ...rect,
          scale: 2,
        };

        saveSvg
          .saveAsImage($svgClone[0], fileName, option)
          .then(() => {
            $shadow.remove();
            this.isSaving = false;
          })
          .catch((err) => {
            $shadow.remove();
            this.$toasted.error(err);
            this.isSaving = false;
          });
      }, 50);
    },
    buyVip() {
      if (this.isFullScreen) {
        this.onExitFullScreen();
      }
    },
  },
};
