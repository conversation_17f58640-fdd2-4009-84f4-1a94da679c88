.filter-container {
  font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
  width: 500px;
  height: 400px;
  box-shadow: rgba(0, 0, 0, 0.2) 0 2px 4px 0;
  background-color: #fff;
  border-radius: 6px;

  > .filter-header {
    position: relative;
    height: 56px;
    border-bottom: 1px solid #eee;
    padding: 0 15px;
    line-height: 56px;
    font-weight: bold;
    font-size: 16px;

    > .filter-close {
      position: absolute;
      display: inline-block;
      right: 15px;
      color: #128bed;
      line-height: 52px;
      font-size: 30px;
      font-weight: bold;
      cursor: pointer;
    }
  }

  > .filter-body {
    padding-top: 15px;

    .section-title {
      font-size: 14px;
      color: #333;
      padding: 0 15px 15px;
      font-weight: bold;
    }

    .tab {
      text-align: center;

      > span {
        color: #333;
        padding-bottom: 6px;
        font-size: 16px;
        cursor: pointer;
        border-bottom: 2px solid transparent;

        &.active {
          color: #128bed;
          border-bottom: 2px solid #128bed;
        }
      }

      > span + span {
        display: inline-block;
        margin-left: 15px;
      }
    }

    .options {
      padding: 15px;
      color: #666;
      font-size: 14px;

      > span {
        &.item {
          display: inline-block;
          width: 33.33%;
          cursor: pointer;
          user-select: none;
          margin-bottom: 15px;

          > .checker-text {
            line-height: 14px;
            vertical-align: top;
            margin-left: 8px;
          }

          > .checker {
            display: inline-block;
            width: 14px;
            height: 14px;
            background: url('./images/check box.png') 14px 14px;
            background-position: -14px 0;
            background-size: cover;
          }

          > .vip-icon {
            vertical-align: middle;
            display: inline-block;
            height: 8px;
            margin-top: -7px;
          }

          &.active {
            > .checker {
              background-position: 0 0;
            }
          }
        }
      }
    }
  }
}
