/* eslint-disable no-param-reassign */
import _ from 'lodash';

import { nodeTypes } from '../../settings/node';
import filterStore from '../../data/filter';

export default {
  name: 'company-chart-app-filter',
  data() {
    const options = _.chain(nodeTypes)
      .filter((o) => o.nodeType >= 101 && o.nodeType <= 299)
      .map((o) => {
        return {
          nodeType: o.nodeType,
          text: o.text,
          vipOnly: o.vipOnly,
          checked: false,
        };
      })
      .value();
    return {
      selectedTabIndex: 1,
      tabs: [
        {
          index: 1,
          text: '企业模板',
          isActive: true,
        },
        {
          index: 2,
          text: '个人模板',
          isActive: false,
        },
      ],
      options,
    };
  },

  mounted() {
    _.each(this.options, (o) => {
      o.checked = _.includes(filterStore.filters, o.nodeType);
    });
  },
  methods: {
    close() {
      this.$emit('close');
    },
    filteredOptions(index) {
      if (index === 1) {
        return _.filter(this.options, (o) => o.nodeType >= 101 && o.nodeType <= 199);
      }
      return _.filter(this.options, (o) => o.nodeType >= 201 && o.nodeType <= 299);
    },
    switchTab(tab) {
      _.each(this.tabs, (o) => {
        o.isActive = tab === o;

        if (o.isActive) {
          this.selectedTabIndex = o.index;
        }
      });
    },
    switchOption(option) {
      option.checked = !option.checked;
      const selectedFilters = _.chain(this.options)
        .filter((o) => o.checked)
        .map((o) => o.nodeType)
        .value();
      filterStore.save(selectedFilters);
      this.$emit('changed', selectedFilters);
    },
  },
};
