<template>
  <div class="filter-container">
    <div class="filter-header">
      筛选
      <div class="filter-close" @click="close">&times;</div>
    </div>
    <div class="filter-body">
      <div class="section" v-for="(tab, index) in tabs" :key="`tab-${index}`">
        <span class="section-title">{{ tab.text }}</span>
        <div class="options">
          <span
            v-for="(option, i) in filteredOptions(index + 1)"
            :key="`option-${i}`"
            :class="{ item: true, active: option.checked }"
            @click="switchOption(option)"
          >
            <span class="checker"></span>
            <span class="checker-text">{{ option.text }}</span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" src="./style.less" scoped></style>

<script src="./component.js"></script>
