<template>
  <div class="search-container">
    <div class="filter-header">
      搜索
      <span class="tips">当前节点数据过多，请点击您需要展示的数据予以显示</span>
      <span class="search-close" @click="close">&times;</span>
    </div>
    <div class="content-body">
      <div class="search-header">
        <input type="text" class="search" v-model="keyword" placeholder="请筛选您想查询的公司/个人" />
      </div>
      <div class="body" ref="scroller" v-show="filtered.length">
        <div
          :class="{ 'item-row': true, active: item.active }"
          v-for="(item, index) in filtered"
          :key="`filter-${index}`"
          title="点击添加该公司"
          @click="addItem(item, $event)"
        >
          <!--公司或者有image的情况下，出logo-->
          <q-entity-avatar :src="item.image" :name="item.name"></q-entity-avatar>
          <span v-html="item.name"></span>
        </div>
      </div>
      <div style="position: relative" v-if="keyword && !filtered.length" :style="{ height: height + 'px' }">
        <g-ui-no-data></g-ui-no-data>
      </div>
    </div>
  </div>
</template>

<script src="./component.js"></script>

<style lang="less" src="./style.less" scoped></style>

<style>
.app-search-em {
  color: #f04040;
  font-style: normal;
}

::placeholder {
  /* Mozilla Firefox 19+ */
  color: rgb(153, 153, 153);
}
</style>
