.search-container {
  width: 500px;
  height: 400px;
  overflow: hidden;
  font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
  background-color: #fff;
  color: #333;
  border-radius: 6px;

  > .filter-header {
    position: relative;
    height: 56px;
    border-bottom: 1px solid #eee;
    padding: 0 15px;
    line-height: 56px;
    font-weight: bold;
    font-size: 16px;

    .tips {
      font-size: 14px;
      color: #999;
      font-weight: normal;
      display: inline-block;
      margin-left: 3px;
    }

    > .search-close {
      position: absolute;
      right: 15px;
      top: 0;
      color: #128bed;
      line-height: 52px;
      font-size: 30px;
      font-weight: bold;
      cursor: pointer;
    }
  }

  > .content-body {
    > .search-header {
      width: 100%;
      height: 62px;
      padding: 15px 15px 0;
      position: relative;

      .clear-input {
        position: absolute;
        right: 25px;
        top: 23px;
        color: #999;
        cursor: pointer;
      }

      > .search {
        display: inline-block;
        width: 100%;
        line-height: 22px;
        appearance: none;
        -webkit-appearance: none;
        outline: none;
        padding: 0 10px;
        box-sizing: border-box;
        margin: 0 auto;
        height: 32px;
        background: #fff;
        border-radius: 4px;
        border: 1px solid #d6d6d6;
        font-size: 14px;
      }
    }

    > .body {
      overflow: auto;
      max-height: 280px;
      border-radius: 4px;

      .item-row {
        padding: 15px / 2 15px;
        font-size: 14px;
        cursor: pointer;

        .logo {
          width: 30px;
          height: 30px;
          background-size: cover;
          display: inline-block;
          border-radius: 4px;
        }

        &.active,
        &:hover {
          background-color: #f5f9ff;

          > span {
            color: #128bed;
          }
        }

        .img-wrap {
          float: left;
          height: 30px;
          width: 30px;
          background-color: #fff;
          border: 1px solid #eee;
          border-radius: 4px;
        }

        img {
          vertical-align: middle;
          object-fit: contain;
          height: 100%;
          width: 100%;
          border-radius: 4px;
        }

        > span {
          vertical-align: middle;
          line-height: 32px;
          padding-left: 10px;
          color: #333;
        }

        > .avatar-empty {
          display: inline-block;
          width: 32px;
          height: 32px;
          text-align: center;

          &::before {
            content: attr(first-letter);
            font-size: 16px;
            line-height: 32px;
            color: #fff;
            font-weight: bold;
          }

          &.color-1 {
            background-color: #9eb5de;
          }
        }
      }
    }
  }

  > .content-footer {
    font-size: 14px;
    padding-top: 15px / 2;
    padding-left: 15px;
    padding-bottom: 15px / 2;
    color: #999;
    user-select: none;
  }
}
