import _ from 'lodash';

import defaultLogo from './images/default.jpg';
import gUiNoData from '../../../g-ui-no-data';

export default {
  name: 'app-search',
  components: {
    [gUiNoData.name]: gUiNoData,
  },
  data() {
    return {
      keyword: '',
      items: [],
      height: 280,
      activeIndex: 0,
    };
  },

  mounted() {
    this.scroll = this.$refs.scroller.scroll;
  },

  computed: {
    filtered() {
      const items = _.chain(this.items)
        .filter((o) => (o.name || '').indexOf(this.keyword) !== -1 || this.keyword === '')
        .map((o, n) => {
          return {
            id: o.id,
            nodeId: o.nodeId,
            nodeType: o.nodeType,
            name: o.name.replace(this.keyword, `<em class='app-search-em'>${this.keyword}</em>`),
            originalName: o.name,
            image: o.image,
            index: n,
            active: n === this.activeIndex,
          };
        })
        .value();

      const n = items.length;
      let height = n > 5 ? 280 : n * 48 - 1;
      if (height <= 0) {
        height = 280;
      }

      this.height = height;

      return items;
    },
  },

  watch: {
    keyword() {
      this.activeIndex = 0;
      $(this.$refs.scroller).animate({ scrollTop: 0 }, 300);
      // this.scroll.scrollToElement($(this.$el).find('.active')[0], 600, true, true)
    },
  },
  methods: {
    clearInput() {
      this.keyword = '';
    },
    close() {
      $('.search-dialg').remove();
    },
    addItem(item, event) {
      this.items = _.filter(this.items, (o) => o.nodeId !== item.nodeId);
      this.$emit('onItemAdded', item);
    },

    onLoadError(e) {
      $(e.target).attr('src', defaultLogo);
    },

    onKeyUp(e) {
      if (e.key === 'ArrowUp') {
        if (this.activeIndex > 0) {
          this.activeIndex -= 1;
          this.scroll.scrollToElement($(this.$el).find('.active')[0], 600, true, true);
        }
      }
      if (e.key === 'ArrowDown') {
        if (this.activeIndex < this.filtered.length - 1) {
          this.activeIndex += 1;
          this.scroll.scrollToElement($(this.$el).find('.active')[0], 600, true, true);
        }
      }
      if (e.key === 'Enter') {
        if (this.activeIndex >= 0 && this.activeIndex <= this.filtered.length - 1) {
          this.addItem(this.filtered[this.activeIndex]);
          this.$nextTick(() => {
            if (this.activeIndex === this.filtered.length) {
              this.activeIndex -= 1;
            }
          });
        }
      }
    },
  },
};
