import { graph } from '@/shared/services';

import utils from '../chart/utils';
import formater from './formater';

const loadCompanyDataById = (keyNo) => {
  return new Promise((resolve, reject) => {
    graph
      .getEnterpriseGraph({ keyNo, isVIP: true })
      .then((data) => {
        if (data.Result) {
          const nodeData = formater.formatGraph(data.Result);
          utils.patchNode(nodeData);
          resolve(nodeData);
        } else {
          resolve({});
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const loadPersonDataById = (keyNo) => {
  return new Promise((resolve, reject) => {
    graph
      .getPersonGraph({ keyNo, isVIP: true })
      .then((data) => {
        const nodeData = formater.formatGraph(data.Result);
        utils.patchNode(nodeData);
        resolve(nodeData);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const splitCompanyData = (entity) => {
  const data = {
    left: {
      id: entity.id,
      nodeId: entity.nodeId,
      nodeType: 999,
      name: entity.name,
      children: [],
    },
    right: {
      id: entity.id,
      nodeId: entity.nodeId,
      nodeType: 999,
      name: entity.name,
      children: [],
    },
  };

  const half = Math.ceil(entity.children.length / 2.0);
  for (let i = 0; i < entity.children.length; ++i) {
    if (i < half) {
      data.right.children.push(entity.children[i]);
    } else {
      data.left.children.push(entity.children[i]);
    }
  }
  return data;
};

export default {
  loadCompanyDataById,
  loadPersonDataById,
  splitCompanyData,
};
