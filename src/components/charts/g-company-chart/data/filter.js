import _ from 'lodash';

import { nodeTypes } from '../settings/node';

const localStorage = window.localStorage;
const KEY = 'company-chart-filters';

class Filter {
  constructor() {
    this.init();
  }

  init() {
    this.store = localStorage;
    let obj = this.store.getItem(KEY);
    if (!_.isArray(obj)) {
      obj = _.chain(nodeTypes)
        .filter((o) => o.nodeType >= 101 && o.nodeType <= 299)
        .map((o) => o.nodeType)
        .value();
    }
    this.filters = obj;
  }

  save(obj) {
    this.filters = [...obj];
    this.store.setItem(KEY, obj);
  }
}

export default new Filter();
