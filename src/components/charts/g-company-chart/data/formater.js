/* eslint-disable max-len */

const formatGraph = (data) => {
  const nodeData = {};

  if (data.Id && data.Id.indexOf('h') !== 0 && data.Id.indexOf('t') !== 0 && data.Id.indexOf('s') !== 0 && data.Id.indexOf('z') !== 0) {
    nodeData.nGraph = true;
  }
  if (data.Id) {
    nodeData.id = data.Id;
  }

  nodeData.name = data.Name;
  nodeData.image = data.Image;
  nodeData.nodeType = +data.NodeType || 999;
  nodeData.proportionOfShares = data.Percent;
  nodeData.product = data.Product;
  nodeData.location = data.Location;

  if (!nodeData.location) {
    if (data.Id && data.Id.indexOf('h') === 0) {
      nodeData.location = '香港';
    }
    if (data.Id && data.Id.indexOf('t') === 0) {
      nodeData.location = '台湾';
    }
  }

  nodeData.quotedCompanyInfo = data.QuotedCompanyInfo;
  nodeData.jobTitle = data.Job;
  nodeData.children = [];

  if (data.Children) {
    for (let i = 0; i < data.Children.length; ++i) {
      const obj = formatGraph(data.Children[i]);
      if (obj.nodeType === 998 || obj.nodeType === 999) {
        obj.nodeType = parseFloat(`${obj.nodeType}.${nodeData.nodeType}`);
      }
      nodeData.children.push(obj);
    }
  }
  return nodeData;
};

export default {
  formatGraph,
};
