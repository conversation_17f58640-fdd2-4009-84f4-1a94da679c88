import { computed, defineComponent, onMounted, ref } from 'vue';
import { Tooltip } from 'ant-design-vue';

import { formatCompanyLogoUrl } from '@/utils/format/company-format';

import styles from './company-logo.module.less';

const getColorById = (id: string) => {
  const colors = [
    'E59E9E',
    '72C1A0',
    'E5B072',
    'AE9EE5',
    '9EA6E5',
    '8BABE5',
    '72BCCE',
    'E5A687',
    'C7AE8E',
    '97BB72',
    'D294D2',
    'E5BF72',
    'BD97DF',
    '7BB1DD',
  ];
  const index = parseInt(
    String(id)
      .split('')
      .find((s) => s <= 'd') ?? '0',
    16
  );
  return colors[index];
};

export const LogoImg = defineComponent({
  props: {
    id: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      default: '',
    },
    size: {
      type: String,
      default: '32px',
    },
    hasimage: {
      type: Number,
      default: 1,
    },
    src: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const isError = ref(false);

    // 默认它有image，找不到就前端自己渲染
    const url = computed(() => props.src || formatCompanyLogoUrl(props.id, props.hasimage));
    onMounted(() => {
      if (props.hasimage === 0) {
        isError.value = true;
      }
    });
    const logoColor = computed(() => {
      const id = props.id;
      return getColorById(id);
    });

    const getFontSize = (size: number): string => {
      const dszie = size - 2;
      const nameLength = props.name?.length;
      switch (nameLength) {
        case 1:
          return `${Math.ceil(dszie / 1.8)}px`;
        case 2:
          return `${Math.ceil(dszie / 2.6)}px`;
        case 3:
        case 4:
          return `${Math.ceil(dszie / 3)}px`;
        default:
          return `${Math.ceil(dszie / 3.6)}px`;
      }
    };

    return { isError, url, logoColor, getFontSize };
  },
  render() {
    // 纯前端渲染，在没有url的情况下
    if (this.isError) {
      const size = parseInt(this.size, 10);
      const fontSize: string = this.getFontSize(size);
      const scale = parseInt(fontSize, 10) / 12;

      const style: Record<string, any> = {};
      if (parseInt(fontSize, 10) < 12) {
        // 谷歌浏览器12px 限制
        style.fontSize = '12px';
        style.width = `${100 / scale}%`;
        style.height = `${100 / scale}%`;
        style.transform = `scale(${scale})`;
        style.transformOrigin = 'top left';
      } else if (fontSize !== '18px') {
        style.fontSize = fontSize;
      }

      return (
        <div
          style={{
            width: this.size,
            height: this.size,
            backgroundColor: `#${this.logoColor}`,
          }}
        >
          <span data-testid="logo-text" class={styles.textLogo} style={style}>
            <span>{this.name?.slice(0, 2)}</span>
            <span>{this.name?.slice(2, 5)}</span>
          </span>
        </div>
      );
    }
    return (
      <img
        src={this.url}
        onError={() => {
          this.isError = true;
        }}
      />
    );
  },
});

const CompanyLogo = defineComponent({
  props: {
    id: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      required: false,
    },
    hasimage: {
      type: Number,
      default: 1,
    },
    src: {
      type: String,
      default: '',
    },
    size: {
      type: String,
      default: '32px',
    },
    hoverable: {
      type: Boolean,
      default: false,
    },
  },
  render() {
    return (
      <Tooltip overlayClassName={styles.overlay} placement="rightTop" mouseEnterDelay={this.hoverable ? 0 : 99999}>
        <LogoImg
          slot="title"
          id={this.id}
          name={this.name}
          style={{ width: '100px', height: '100px', borderRadius: '4px' }}
          size={'100px'}
          hasimage={this.hasimage}
          src={this.src}
        />
        <LogoImg
          id={this.id}
          name={this.name}
          style={{ width: this.size, height: this.size, borderRadius: '4px', border: '1px solid #eee' }}
          hasimage={this.hasimage}
          src={this.src}
          size={this.size}
        />
      </Tooltip>
    );
  },
});

export default CompanyLogo;
