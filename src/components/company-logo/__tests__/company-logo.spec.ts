import { mount } from '@vue/test-utils';

import { formatCompanyLogoUrl } from '@/utils/format/company-format';

import CompanyLogo, { LogoImg } from '..';
import { Mock } from 'vitest';

vi.mock('@/utils/format/company-format', () => ({
  formatCompanyLogoUrl: vi.fn((id, hasimage) => `http://example.com/logo/${id}_${hasimage}.png`),
}));

describe('LogoImg 组件测试', () => {
  it('正常显示图片', () => {
    const wrapper = mount(LogoImg, {
      propsData: {
        id: '123',
        name: '公司',
        src: 'http://example.com/image.png',
      },
    });
    expect(wrapper.find('img').exists()).toBe(true);
    expect(wrapper.find('img').attributes('src')).toBe('http://example.com/image.png');
  });

  it('hasimage 为 0 时显示纯前端渲染', async () => {
    const wrapper = mount(LogoImg, {
      propsData: {
        id: '123',
        name: '公司',
        hasimage: 0,
        src: '',
      },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.find('img').exists()).toBe(false);
    expect(wrapper.find('div').exists()).toBe(true);
    expect(wrapper.find('div').attributes('style')).toContain('background-color:');
  });

  it('name 长度为 1 时', async () => {
    const wrapper = mount(LogoImg, {
      propsData: {
        id: '123',
        name: '一',
        src: '',
        size: '50px',
        hasimage: 0,
      },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.find('[data-testid="logo-text"]').attributes('style')).toContain('font-size: 27px;');
  });

  it('name 长度为 2 时', async () => {
    const wrapper = mount(LogoImg, {
      propsData: {
        id: '123',
        name: '一二',
        src: '',
        size: '50px',
        hasimage: 0,
      },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.find('[data-testid="logo-text"]').attributes('style')).toContain('font-size: 19px;');
  });

  it('name 长度为 3 时', async () => {
    const wrapper = mount(LogoImg, {
      propsData: {
        id: '123',
        name: '一二三',
        src: '',
        size: '50px',
        hasimage: 0,
      },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.find('[data-testid="logo-text"]').attributes('style')).toContain('font-size: 16px;');
  });

  it('name 长度为 4 时', async () => {
    const wrapper = mount(LogoImg, {
      propsData: {
        id: '123',
        name: '一二三四',
        src: '',
        size: '50px',
        hasimage: 0,
      },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.find('[data-testid="logo-text"]').attributes('style')).toContain('font-size: 16px;');
  });

  it('name 长度大于 4 时', async () => {
    const wrapper = mount(LogoImg, {
      propsData: {
        id: '123',
        name: '一二三四五',
        src: '',
        size: '50px',
        hasimage: 0,
      },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.find('[data-testid="logo-text"]').attributes('style')).toContain('font-size: 14px;');
  });

  it('src 为空时显示默认图片', () => {
    (formatCompanyLogoUrl as Mock).mockReturnValue('default.png');
    const wrapper = mount(LogoImg, {
      propsData: {
        id: '123',
        name: '公司',
        src: '',
      },
    });
    expect(wrapper.find('img').attributes('src')).toBe('default.png');
  });
});

describe('CompanyLogo 组件测试', () => {
  it('正常显示图片', () => {
    const wrapper = mount(CompanyLogo, {
      propsData: {
        id: '123',
        name: '公司',
        src: 'http://example.com/image.png',
      },
    });
    expect(wrapper.find('img').exists()).toBe(true);
    expect(wrapper.find('img').attributes('src')).toBe('http://example.com/image.png');
  });

  it('hoverable 为 true 时 mouseEnterDelay 为 0', () => {
    const wrapper = mount(CompanyLogo, {
      propsData: {
        id: '123',
        name: '公司',
        hoverable: true,
      },
    });
    expect(wrapper.findComponent({ name: 'ATooltip' }).props('mouseEnterDelay')).toBe(0);
  });

  it('hoverable 为 false 时 mouseEnterDelay 为 99999', () => {
    const wrapper = mount(CompanyLogo, {
      propsData: {
        id: '123',
        name: '公司',
        hoverable: false,
      },
    });
    expect(wrapper.findComponent({ name: 'ATooltip' }).props('mouseEnterDelay')).toBe(99999);
  });
});
