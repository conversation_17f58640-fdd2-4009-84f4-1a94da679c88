import { defineComponent } from 'vue';
import { Button, Dropdown, Icon } from 'ant-design-vue';

import styles from './dropdown-button.module.less';

const DropdownButton = defineComponent({
  name: 'DropdownButton',
  props: {
    text: {
      type: String,
      default: '',
    },
    buttonProps: {
      type: Object,
      default: () => ({}),
    },
  },
  emits: ['visibleChange'],
  setup(props, { emit }) {
    const onVisibleChange = (isVisible: boolean) => {
      emit('visibleChange', isVisible);
    };
    return {
      onVisibleChange,
    };
  },
  render() {
    return (
      <Dropdown
        trigger={['hover']}
        {...{
          props: {
            ...this.$attrs,
          },
        }}
        onVisibleChange={this.onVisibleChange}
        destroyPopupOnHide
      >
        <template slot="overlay">{this.$slots.overlay}</template>
        <Button class={styles.container} {...{ props: this.buttonProps }}>
          <span>{this.text}</span>
          <Icon class={styles.icon} type="caret-down" />
        </Button>
      </Dropdown>
    );
  },
});

export default DropdownButton;
