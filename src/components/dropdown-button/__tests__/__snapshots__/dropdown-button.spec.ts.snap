// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`DropdownButton > render 1`] = `
<anonymous-stub prefixcls="ant-dropdown" transitionname="slide-up" overlayclassname="" overlaystyle="[object Object]" placement="bottomLeft" trigger="hover" showaction="" hideaction="" mouseenterdelay="0.15" mouseleavedelay="0.1" destroypopuponhide="true">
  <wave-stub class="container ant-dropdown-trigger"><button type="button" class="ant-btn"><span></span>
      <localereceiver-stub componentname="Icon" class="icon"><i aria-label="undefined: caret-down" class="anticon anticon-caret-down">
          <antdicon-stub type="caret-down-o" focusable="false" class=""></antdicon-stub>
        </i></localereceiver-stub></button></wave-stub><template></template>
</anonymous-stub>
`;
