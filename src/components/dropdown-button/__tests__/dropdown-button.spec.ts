import { shallowMount } from '@vue/test-utils';
import { Dropdown } from 'ant-design-vue';

import DropdownButton from '..';

describe('DropdownButton', () => {
  test('render', () => {
    const wrapper = shallowMount<InstanceType<typeof DropdownButton>>(DropdownButton, {
      propsData: {},
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('actions: visible', () => {
    const wrapper = shallowMount<InstanceType<typeof DropdownButton>>(DropdownButton);
    wrapper.findComponent(Dropdown).vm.$emit('visibleChange', true);
    expect(wrapper.emitted('visibleChange')?.[0]).toEqual([true]);
  });
});
