import { computed, defineComponent } from 'vue';
import QIcon from '../global/q-icon';

const DiligenceRiskTrendScore = defineComponent({
  name: 'DiligenceRiskTrendScore',
  props: {
    score: {
      type: Number,
      default: 0,
    },
    previousScore: {
      type: Number,
      default: 0,
    },
    description: {
      type: String,
      required: false,
    },
    mapping: {
      type: Object,
      default: () => ({
        up: '#00ad65',
        down: '#f04040',
      }),
    },
  },
  setup(props) {
    // 变化评分
    const scoreChange = computed(() => {
      return props.score - props.previousScore;
    });
    const direction = computed<'stable' | 'up' | 'down'>(() => {
      if (scoreChange.value === 0) {
        return 'stable';
      }
      return scoreChange.value > 0 ? 'up' : 'down';
    });

    return {
      direction,
      scoreChange,
    };
  },
  render() {
    const { scoreChange, direction, description, mapping } = this;
    return (
      <div class="flex gap-2 leading-[22px]">
        {description ? <div>{description}</div> : null}
        <div style={{ color: mapping[direction] }} v-show={direction !== 'stable'}>
          <QIcon type="icon-shangsheng1" class={direction === 'down' ? 'rotate-180' : ''} style={{ color: mapping[direction] }} />
          <span>{scoreChange}</span>
        </div>
        <div v-show={direction === 'stable'}>
          <span>无波动</span>
        </div>
      </div>
    );
  },
});

export default DiligenceRiskTrendScore;
