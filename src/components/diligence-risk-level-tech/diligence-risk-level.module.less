.container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .text {
    position: absolute;
    top: 0;
    transform: translateX(-50%);
    width: 140px;
    height: 140px;
    left: 50%;
    right: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: 500;
    line-height: 32px;
  }

  .result {
    padding: 5px 10px;
    width: 182px;
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    box-sizing: border-box;
    color: #666;
    background: #fff8f9;
    border: 1px solid #fcc;
    font-size: 14px;
    line-height: 22px;
  }
}
