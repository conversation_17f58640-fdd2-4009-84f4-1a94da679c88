import { defineComponent } from 'vue';

const FileIcon = defineComponent({
  props: {
    type: {
      type: String,
      default: 'zip',
    },
  },
  render() {
    const getIconType = () => {
      switch (this.type) {
        case 'image':
        case 'png':
          return 'icon-png';
        case 'jpg':
        case 'jpeg':
          return 'icon-jpg';
        case 'docx':
        case 'doc':
        case 'word':
        case 'wps':
          return 'icon-docx';
        case 'excel':
        case 'xlsx':
        case 'xls':
        case 'csv':
          return 'icon-excel';
        case 'pdf':
          return 'icon-pdf';
        case 'zip':
        case 'rar':
        case '7z':
        default:
          return 'icon-zip';
      }
    };
    return <q-icon style="font-size: 22px;" type={getIconType()}></q-icon>;
  },
});

export default FileIcon;
