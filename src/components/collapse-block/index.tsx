import { useToggle } from '@vueuse/core';
import { defineComponent } from 'vue';

import Icon from '@/shared/components/icon';

import styles from './collapse-block.module.less';

const CollapseBlock = defineComponent({
  name: 'CollapseBlock',
  props: {
    defaultCollapse: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      required: false,
    },
    /** 通过条件控制强制渲染 */
    force: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const [isCollapse, setCollapse] = useToggle(props.defaultCollapse);
    return {
      isCollapse,
      setCollapse,
    };
  },
  render() {
    return (
      <div
        class={{
          [styles.container]: true,
          [styles.collapsed]: this.isCollapse,
        }}
      >
        <div
          class={styles.header}
          onClick={() => {
            this.setCollapse(!this.isCollapse);
            this.$emit('toggle', this.isCollapse);
          }}
        >
          <div class={styles.wrapper}>
            <div class={styles.title}>
              <Icon class={styles.icon} type={this.isCollapse ? 'icon-icon_zhank' : 'icon-icon_sqing'} />
              {this.$slots.title || <span domPropsInnerHTML={this.title} />}
            </div>
          </div>
        </div>
        {this.force && this.isCollapse ? null : <div class={styles.body}>{this.$slots.default}</div>}
      </div>
    );
  },
});

export default CollapseBlock;
