import { mount } from '@vue/test-utils';

import CollapseBlock from '../index';

describe('CollapseBlock', () => {
  test('slots: title', () => {
    const expected = {
      title: '<div>TITLE</div>',
    };
    const wrapper = mount(CollapseBlock, {
      slots: {
        title: expected.title,
      },
    });
    expect(wrapper.html()).toContain(expected.title);
  });

  test('slots: body', () => {
    const expected = {
      body: '<div>BODY</div>',
    };
    const wrapper = mount(CollapseBlock, {
      slots: {
        default: expected.body,
      },
    });
    expect(wrapper.html()).toContain(expected.body);
  });
});
