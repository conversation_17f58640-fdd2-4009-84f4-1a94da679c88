@import '@/styles/token.less';

.container {
  background-color: @qcc-color-white;
  border-radius: @qcc-border-radius-middle;
  margin-top: 10px;

  &.collapsed {
    .header .wrapper {
      box-shadow: none;
    }

    .body {
      display: none;
    }
  }

  .header {
    position: relative;
    padding: 0 15px;
    color: @qcc-color-black-600;
    cursor: pointer;
    user-select: none;

    &:hover {
      .title i {
        color: @qcc-color-blue-500;
      }
    }

    .wrapper {
      box-shadow: inset 0 -1px 0 0 @qcc-color-gray-500;
      display: flex;
      align-items: center;
      justify-content: space-between;
      min-height: 52px;
    }

    .title {
      font-size: 15px;
      font-weight: @qcc-font-bold;
      color: @qcc-color-black-600;
      line-height: 24px;

      i {
        font-size: 14px;
        margin-right: 5px;
        color: @qcc-color-black-200;
      }

      em {
        color: @qcc-color-red-500;
      }
    }
  }

  .body {
    padding: 15px;
  }
}
