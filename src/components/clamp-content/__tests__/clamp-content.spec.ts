import { mount, shallowMount } from '@vue/test-utils';

import C<PERSON>Content from '..';

describe('ClampContent', () => {
  test('render', () => {
    const wrapper = shallowMount(ClampContent, {
      slots: {
        default: 'AAA',
      },
    });
    expect(wrapper).toMatchSnapshot();
  });

  test('events: toggle', async () => {
    const wrapper = mount(ClampContent, {
      slots: {
        default: 'A'.repeat(10000),
      },
    });

    expect(wrapper.text()).toContain('展开');

    await wrapper.find('a').trigger('click'); // 点击展开

    expect(wrapper.emitted('change')).toBeTruthy(); // 发送 `change` 事件
    expect(wrapper.text()).toContain('收起');
  });
});
