import { Path, PathConverter, PathEdge, PathNode } from '@/components/relation-path';

/**
 * 简单关系路径转换器
 * 未支持股权等信息显示、合并相同节点等功能
 */
export class PlainRelationPathConverter extends PathConverter {
  covert(path: Path, dataList: any[]) {
    dataList.forEach((item) => {
      let part: PathNode | PathEdge | null = null;
      if (this.isNodeLike(item)) {
        part = this.convertNode(item);
      } else if (this.isEdgeLike(item)) {
        part = this.convertEdge(item);
      }
      if (part) {
        path.nodes.push(part);
      }
    });

    return path;
  }

  convertNode(data) {
    const node = new PathNode();
    node.id = data['Company.keyno'];
    node.name = data['Company.name'];
    node.org = data['Org'];
    return node;
  }

  convertEdge(data) {
    const edge = new PathEdge();
    edge.description = data.stockpercent ? `${data.stockpercent}%` : '';
    edge.id = `${data.startid}-${edge.description}-${data.endid}`;
    edge.startId = data.startid;
    edge.endId = data.endid;
    edge.direction = data.direction;
    return edge;
  }

  isNodeLike(data) {
    return data['Company.name'] && data['Company.keyno'];
  }

  isEdgeLike(data) {
    return data.startid && data.endid;
  }
}
