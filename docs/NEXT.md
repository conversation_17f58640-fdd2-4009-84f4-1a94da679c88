# README

架构调整：通过目录结构隔离不同应用，提高代码的可维护性，使用多页面构建模式，也为后续拆分子应用做准备。

```shell
# 通过 scope 标记 commit 的影响范围，例如：feat(main)、fix(setting)
git commit 'RA-000 feat(scope): commit message'
```

## Apps

* [ ] main 主应用
* [x] setting 设置
* [ ] external 对外嵌入页面
* [x] account 帐号应用
* [x] task 任务列表应用
* [x] embed 工商嵌入页面
* [x] document 文档
* [x] notification 消息中心

## Core 动行主应用的核心代码

* assets
* styles 主应用的基础样式
* app
* store
* router

## Shared 应用之间共享的内容

* components
* features
* composables
* config
* constants
* directives
* layouts
* models
* services
* utils

## Libs 插件/库

* base-ui-style
* base-ui-theme
* user-ability-plugin
* user-tracking-plugin
