{"Jest mount component": {"prefix": "j<PERSON>", "body": ["import { mount } from '@vue/test-utils';", "", "import ${TM_FILENAME_BASE/(.*)\\.(spec|test)$/${1:/pascalcase}/} from '..';", "", "describe('${TM_FILENAME_BASE/(.*)\\.(spec|test)$/${1:/pascalcase}/}', () => {", "  test('render', () => {", "    const wrapper = mount<InstanceType<typeof ${TM_FILENAME_BASE/(.*)\\.(spec|test)$/${1:/pascalcase}/}>>(${TM_FILENAME_BASE/(.*)\\.(spec|test)$/${1:/pascalcase}/}, {", "      propsData: {", "        $0", "      }", "    });", "    expect(wrapper).toMatchSnapshot();", "  });", "});"], "description": "Jest mount component"}, "Jest shallowMount component": {"prefix": "jsmount", "body": ["import { shallowMount } from '@vue/test-utils';", "", "import ${TM_FILENAME_BASE/(.*)\\.(spec|test)$/${1:/pascalcase}/} from '..';", "", "describe('${TM_FILENAME_BASE/(.*)\\.(spec|test)$/${1:/pascalcase}/}', () => {", "  test('render', () => {", "    const wrapper = shallowMount<InstanceType<typeof ${TM_FILENAME_BASE/(.*)\\.(spec|test)$/${1:/pascalcase}/}>>(${TM_FILENAME_BASE/(.*)\\.(spec|test)$/${1:/pascalcase}/}, {", "      propsData: {", "        $0", "      }", "    });", "    expect(wrapper).toMatchSnapshot();", "  });", "});"], "description": "Jest shallowMount component"}, "Jest each": {"prefix": "jeach", "body": ["import { shallowMount } from '@vue/test-utils';", "", "import ${TM_FILENAME_BASE/(.*)\\.(spec|test)$/${1:/pascalcase}/} from '..';", "", "describe('${TM_FILENAME_BASE/(.*)\\.(spec|test)$/${1:/pascalcase}/}', () => {", "  let wrapper: ReturnType<typeof shallowMount>;", "", "  beforeEach(() => {", "    wrapper = shallowMount<InstanceType<typeof ${TM_FILENAME_BASE/(.*)\\.(spec|test)$/${1:/pascalcase}/}>>(${TM_FILENAME_BASE/(.*)\\.(spec|test)$/${1:/pascalcase}/}, {", "      propsData: {", "        $0", "      },", "    });", "  });", "", "  test('render', () => {", "    expect(wrapper).toMatchSnapshot();", "  });", "});", ""], "description": "Jest snapshot"}}