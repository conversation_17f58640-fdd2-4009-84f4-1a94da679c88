{"eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"], "emmet.includeLanguages": {"javascriptreact": "javascriptreact", "typescriptreact": "typescriptreact", "vue-html": "html"}, "[javascriptreact]": {"editor.defaultFormatter": "rvest.vs-code-prettier-eslint"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "rvest.vs-code-prettier-eslint"}, "[typescript]": {"editor.defaultFormatter": "rvest.vs-code-prettier-eslint"}, "cSpell.words": ["unmark"], "i18n-ally.localesPaths": ["src/locales"], "references.preferredLocation": "peek", "typescript.tsdk": "node_modules/typescript/lib", "sonarlint.connectedMode.project": {"connectionId": "http-172-18-102-250-9000-", "projectKey": "qcc-insights-web"}, "vite.autoStart": false}